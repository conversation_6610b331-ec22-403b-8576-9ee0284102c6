const db = require('../config/db');

/**
 * 合伙人端获取顾客订单列表
 * GET /api/partner/customer-orders
 * 根据文档要求，需要显示：
 * 1. 销售人是当前用户的所有订单
 * 2. 当前用户名下所有门店的订单
 */
exports.getCustomerOrders = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    console.log('合伙人端获取顾客订单，用户ID:', userId);

    const { page = 1, limit = 10, status = '', store_no = '', delivery_method = '', date_range = '', keyword = '' } = req.query;

    // 状态映射
    const statusMap = {
      'pending_shipment': 'pending_shipment',
      'shipped': 'shipped', 
      'signed': 'signed',
      'returns': 'returns'
    };

    let whereConditions = [];
    let queryParams = [];

    // 简化查询逻辑：先获取当前用户的门店列表
    const userStoresQuery = `
      SELECT DISTINCT s.store_no 
      FROM stores s
      INNER JOIN partners p ON s.store_no = p.store_no
      WHERE p.user_id = ?
    `;
    const userStores = await db.query(userStoresQuery, [userId]);
    const userStoreNos = userStores.map(store => store.store_no);

    console.log('用户门店列表:', userStoreNos);

    // 基础条件：当前用户是销售员 OR 订单属于当前用户的门店
    if (userStoreNos.length > 0) {
      // 构建门店条件
      const storePlaceholders = userStoreNos.map(() => '?').join(',');
      whereConditions.push(`(
        co.salesman_id = ? OR
        EXISTS (
          SELECT 1 FROM customer_sub_orders cso
          WHERE cso.main_order_id = co.id AND cso.store_no IN (${storePlaceholders})
        )
      )`);
      queryParams.push(userId, ...userStoreNos);
    } else {
      // 如果用户没有门店，只查询销售人为当前用户的订单
      whereConditions.push('co.salesman_id = ?');
      queryParams.push(userId);
    }

    // 状态筛选
    if (status && statusMap[status]) {
      whereConditions.push('co.status = ?');
      queryParams.push(statusMap[status]);
    }

    // 门店筛选
    if (store_no && store_no !== 'all') {
      whereConditions.push(`EXISTS (
        SELECT 1 FROM customer_sub_orders cso
        WHERE cso.main_order_id = co.id AND cso.store_no = ?
      )`);
      queryParams.push(store_no);
    }

    // 配送方式筛选
    if (delivery_method && delivery_method !== 'all') {
      whereConditions.push('co.delivery_method = ?');
      queryParams.push(delivery_method);
    }

    // 日期范围筛选
    if (date_range && date_range !== 'all') {
      let dateCondition = '';
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000 - 1);

      switch (date_range) {
        case 'today':
          dateCondition = 'co.created_at >= ? AND co.created_at <= ?';
          queryParams.push(startOfDay, endOfDay);
          break;
        case 'yesterday':
          const yesterday = new Date(startOfDay.getTime() - 24 * 60 * 60 * 1000);
          const endOfYesterday = new Date(yesterday.getTime() + 24 * 60 * 60 * 1000 - 1);
          dateCondition = 'co.created_at >= ? AND co.created_at <= ?';
          queryParams.push(yesterday, endOfYesterday);
          break;
        case 'week':
          const startOfWeek = new Date(startOfDay.getTime() - (today.getDay() * 24 * 60 * 60 * 1000));
          dateCondition = 'co.created_at >= ?';
          queryParams.push(startOfWeek);
          break;
        case 'month':
          const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
          dateCondition = 'co.created_at >= ?';
          queryParams.push(startOfMonth);
          break;
      }
      if (dateCondition) {
        whereConditions.push(dateCondition);
      }
    }

    // 关键词搜索（订单号或商品名称）
    if (keyword) {
      whereConditions.push(`(
        co.order_no LIKE ? OR
        EXISTS (
          SELECT 1 FROM customer_order_items coi
          WHERE coi.main_order_id = co.id AND coi.product_name LIKE ?
        )
      )`);
      const keywordParam = `%${keyword}%`;
      queryParams.push(keywordParam, keywordParam);
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    console.log('查询条件:', whereClause);
    console.log('查询参数:', queryParams);

    // 计算总数
    const countQuery = `
      SELECT COUNT(DISTINCT co.id) as total
      FROM customer_orders co
      ${whereClause}
    `;
    
    console.log('总数查询SQL:', countQuery);
    const countResult = await db.query(countQuery, queryParams);
    const total = countResult[0] ? countResult[0].total : 0;
    console.log('订单总数:', total);

    // 分页参数
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 主订单查询
    const ordersQuery = `
      SELECT DISTINCT
        co.id, co.order_no, co.user_id, co.total_amount,
        co.total_amount as actual_amount, 0 as discount_amount,
        co.status, co.delivery_method, co.salesman_id,
        co.created_at, co.updated_at
      FROM customer_orders co
      ${whereClause}
      ORDER BY co.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    console.log('订单查询SQL:', ordersQuery);
    const ordersResult = await db.query(ordersQuery, [...queryParams, parseInt(limit), offset]);
    const orders = ordersResult;
    console.log('查询到订单数量:', orders.length);

    // 为每个订单获取子订单信息（包含门店信息）和商品信息
    for (let order of orders) {
      try {
        // 获取子订单信息（门店信息）
        const subOrdersQuery = `
          SELECT cso.id, cso.sub_order_no, cso.store_no, s.name as store_name, cso.status
          FROM customer_sub_orders cso
          LEFT JOIN stores s ON cso.store_no = s.store_no
          WHERE cso.main_order_id = ?
        `;
        const subOrdersResult = await db.query(subOrdersQuery, [order.id]);
        
        // 添加子订单信息到主订单
        order.sub_orders = subOrdersResult;
        
        // 合并门店信息
        if (subOrdersResult.length > 0) {
          order.store_no = subOrdersResult[0].store_no;
          order.store_name = subOrdersResult[0].store_name;
          if (subOrdersResult.length > 1) {
            order.store_name += ` 等${subOrdersResult.length}家门店`;
          }
        } else {
          order.store_no = '';
          order.store_name = '未知门店';
        }

        // 获取商品信息
        const itemsQuery = `
          SELECT
            coi.id, coi.product_id, coi.product_name, coi.product_image,
            coi.product_price as price, coi.quantity, coi.quantity as total_quantity
          FROM customer_order_items coi
          WHERE coi.main_order_id = ?
        `;
        order.items = await db.query(itemsQuery, [order.id]);

        // 获取客户姓名
        if (order.user_id) {
          const userQuery = `SELECT nickname FROM users WHERE user_id = ?`;
          const userResult = await db.query(userQuery, [order.user_id]);
          order.customer_name = userResult.length > 0 ? userResult[0].nickname || '顾客' : '顾客';
        } else {
          order.customer_name = '顾客';
        }

        // 获取销售人姓名
        if (order.salesman_id) {
          const salesmanQuery = `SELECT nickname FROM users WHERE user_id = ?`;
          const salesmanResult = await db.query(salesmanQuery, [order.salesman_id]);
          order.salesman_name = salesmanResult.length > 0 ? salesmanResult[0].nickname || '销售员' : '销售员';
        } else {
          order.salesman_name = '平台';
        }

        // 格式化日期
        order.created_at = new Date(order.created_at).toLocaleDateString('zh-CN');
        order.updated_at = new Date(order.updated_at).toLocaleDateString('zh-CN');
      } catch (error) {
        console.error('处理订单数据时出错:', error);
        // 设置默认值
        order.sub_orders = [];
        order.items = [];
        order.customer_name = '顾客';
        order.salesman_name = '销售员';
        order.store_name = '未知门店';
      }
    }

    console.log('返回订单数据:', orders.length, '条');

    res.json({
      success: true,
      data: {
        orders: orders,
        total: total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('合伙人端获取顾客订单失败:', error);
    res.status(500).json({ success: false, message: '获取订单失败，请重试' });
  }
}; 

/**
 * 合伙人端获取门店订单列表
 * GET /api/partner/store-orders
 * 根据文档要求，需要显示：
 * 1. 门店采购订单
 * 2. 门店移库订单
 */
exports.getStoreOrders = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    console.log('合伙人端获取门店订单，用户ID:', userId);

    const { 
      page = 1, 
      limit = 10, 
      storeNo = '', 
      orderType = '', 
      subType = '', 
      sortBy = 'date_desc', 
      keyword = '',
      productKeyword = '',
      startDate = '',
      endDate = ''
    } = req.query;

    console.log('门店订单查询参数:', req.query);

    // 先获取当前用户的门店列表
    const userStoresQuery = `
      SELECT DISTINCT s.store_no 
      FROM stores s
      INNER JOIN partners p ON s.store_no = p.store_no
      WHERE p.user_id = ?
    `;
    const userStores = await db.query(userStoresQuery, [userId]);
    const userStoreNos = userStores.map(store => store.store_no);

    console.log('用户门店列表:', userStoreNos);

    // 构建查询条件
    let whereConditions = [];
    let queryParams = [];

    // 基础条件：当前用户门店的订单
    if (storeNo && storeNo !== 'all') {
      // 如果指定了门店，验证用户是否有权限查看该门店订单
      if (!userStoreNos.includes(storeNo)) {
        return res.status(403).json({ success: false, message: '您没有权限查看该门店订单' });
      }
      whereConditions.push('so.store_no = ?');
      queryParams.push(storeNo);
    } else {
      // 如果没有指定门店，获取当前用户所有门店的订单
      if (userStoreNos.length > 0) {
        // 构建门店条件
        const storePlaceholders = userStoreNos.map(() => '?').join(',');
        whereConditions.push(`so.store_no IN (${storePlaceholders})`);
        queryParams.push(...userStoreNos);
      } else {
        // 如果用户没有门店，返回空结果
        return res.json({
          success: true,
          data: {
            orders: [],
            total: 0,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: 0
          }
        });
      }
    }

    // 订单类型筛选
    if (orderType && orderType !== 'all') {
      whereConditions.push('so.type = ?');
      queryParams.push(orderType);
    }

    // 子类型（状态）筛选
    if (subType && subType !== 'all') {
      whereConditions.push('so.status = ?');
      queryParams.push(subType);
    }

    // 日期范围筛选
    if (startDate && endDate) {
      whereConditions.push('so.created_at >= ? AND so.created_at <= ?');
      queryParams.push(new Date(startDate), new Date(endDate));
    }

    // 关键词搜索（订单号）
    if (keyword) {
      whereConditions.push('so.order_no LIKE ?');
      queryParams.push(`%${keyword}%`);
    }

    // 商品关键词搜索（store_orders表没有product_name字段，暂时跳过）
    if (productKeyword) {
      // TODO: 如果需要商品搜索，需要JOIN products表
      console.log('商品关键词搜索功能暂未实现');
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    console.log('门店订单查询条件:', whereClause);
    console.log('门店订单查询参数:', queryParams);

    // 排序条件
    let orderByClause = 'ORDER BY so.created_at DESC';
    switch (sortBy) {
      case 'date_asc':
        orderByClause = 'ORDER BY so.created_at ASC';
        break;
      case 'amount_desc':
        orderByClause = 'ORDER BY so.total_amount DESC';
        break;
      case 'amount_asc':
        orderByClause = 'ORDER BY so.total_amount ASC';
        break;
      default:
        orderByClause = 'ORDER BY so.created_at DESC';
    }

    // 计算总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM store_orders so
      ${whereClause}
    `;
    
    console.log('门店订单总数查询SQL:', countQuery);
    const countResult = await db.query(countQuery, queryParams);
    const total = countResult[0] ? countResult[0].total : 0;
    console.log('门店订单总数:', total);

    // 分页参数
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 订单查询
    const ordersQuery = `
      SELECT
        so.id, so.order_no, so.store_no, so.type as order_type,
        so.status, so.total_amount, so.created_at,
        so.updated_at, so.product_id, so.quantity
      FROM store_orders so
      ${whereClause}
      ${orderByClause}
      LIMIT ? OFFSET ?
    `;
    
    console.log('门店订单查询SQL:', ordersQuery);
    const ordersResult = await db.query(ordersQuery, [...queryParams, parseInt(limit), offset]);
    const orders = ordersResult;
    console.log('查询到门店订单数量:', orders.length);

    // 为每个订单获取商品信息和状态文本
    for (let order of orders) {
      try {
        // 获取商品信息（从products表获取）
        if (order.product_id) {
          const productQuery = `
            SELECT id, name as product_name, image as product_image, price
            FROM products
            WHERE id = ?
          `;
          const products = await db.query(productQuery, [order.product_id]);
          if (products.length > 0) {
            const product = products[0];
            order.items = [{
              id: order.id,
              product_id: order.product_id,
              product_name: product.product_name || '商品',
              product_image: product.product_image || '/images/icons2/商品.png',
              price: product.price || (order.total_amount / order.quantity) || 0,
              quantity: order.quantity
            }];
          } else {
            order.items = [{
              id: order.id,
              product_id: order.product_id,
              product_name: '商品',
              product_image: '/images/icons2/商品.png',
              price: order.total_amount / order.quantity || 0,
              quantity: order.quantity
            }];
          }
        } else {
          order.items = [{
            id: order.id,
            product_id: '',
            product_name: '商品',
            product_image: '/images/icons2/商品.png',
            price: order.total_amount / order.quantity || 0,
            quantity: order.quantity
          }];
        }
        
        order.items_count = 1;

        // 设置状态文本
        switch (order.status) {
          case 'pending_review':
            order.status_text = '已下单';
            break;
          case 'reviewed':
            order.status_text = '已审核';
            break;
          case 'pending_shipment':
            order.status_text = '已下单';
            break;
          case 'shipped':
            order.status_text = '已发货';
            break;
          case 'received':
            order.status_text = '已到店';
            break;
          default:
            order.status_text = '未知状态';
        }

        // 格式化日期
        order.created_at = new Date(order.created_at).toLocaleDateString('zh-CN');
        order.updated_at = new Date(order.updated_at).toLocaleDateString('zh-CN');
      } catch (error) {
        console.error('处理门店订单数据时出错:', error);
        // 设置默认值
        order.items = [];
        order.items_count = 0;
        order.status_text = '未知状态';
      }
    }

    console.log('返回门店订单数据:', orders.length, '条');

    res.json({
      success: true,
      data: {
        orders: orders,
        total: total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('合伙人端获取门店订单失败:', error);
    res.status(500).json({ success: false, message: '获取订单失败，请重试' });
  }
}; 