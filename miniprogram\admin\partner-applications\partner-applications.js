// admin/partner-applications/partner-applications.js
const { partnerApi } = require('../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    applications: [],
    total: 0,
    loading: false,
    currentPage: 1,
    pageSize: 10,
    statusFilter: '',
    statusText: '全部',
    statusOptions: [
      { value: '', text: '全部' },
      { value: 'pending', text: '待审核' },
      { value: 'approved', text: '已通过' },
      { value: 'rejected', text: '已拒绝' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('合伙人申请管理页面加载');
    this.loadApplications();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({ currentPage: 1 });
    this.loadApplications(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.applications.length < this.data.total) {
      this.setData({
        currentPage: this.data.currentPage + 1
      });
      this.loadApplications();
    }
  },

  /**
   * 加载申请列表
   */
  loadApplications: function (callback) {
    const { currentPage, pageSize, statusFilter } = this.data;
    
    this.setData({ loading: true });
    
    // 构建查询参数
    const params = {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize
    };
    
    if (statusFilter && statusFilter !== '') {
      params.status = statusFilter;
    }
    
    console.log('加载申请列表，参数:', params);
    console.log('当前用户信息:', getApp().globalData);
    
    partnerApi.getAllApplications(params)
      .then(res => {
        console.log('API响应:', res);
        if (res.success) {
          // 如果是第一页，直接替换数据，否则追加数据
          const applications = currentPage === 1 
            ? res.data 
            : [...this.data.applications, ...res.data];
          
          console.log('处理后的申请列表:', applications);
          
          this.setData({
            applications,
            total: res.total,
            loading: false
          });
        } else {
          console.error('API返回失败:', res);
          wx.showToast({
            title: res.message || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
        
        if (callback && typeof callback === 'function') {
          callback();
        }
      })
      .catch(err => {
        console.error('加载申请列表失败:', err);
        console.error('错误详情:', {
          code: err.code,
          message: err.message,
          url: '/api/partner/applications'
        });
        
        wx.showToast({
          title: err.message || '加载失败，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
        
        if (callback && typeof callback === 'function') {
          callback();
        }
      });
  },

  /**
   * 切换状态筛选
   */
  onStatusChange: function (e) {
    const index = e.detail.value;
    const statusFilter = this.data.statusOptions[index].value;
    const statusText = this.data.statusOptions[index].text;
    
    console.log('状态筛选变更:', { index, statusFilter, statusText });
    
    this.setData({
      statusFilter,
      statusText,
      currentPage: 1
    });
    this.loadApplications();
  },

  /**
   * 查看申请详情
   */
  viewDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `./application-detail?id=${id}`
    });
  },

  /**
   * 格式化时间戳
   */
  formatDate: function (timestamp) {
    if (!timestamp) return '';
    const date = new Date(parseInt(timestamp));
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  }
});