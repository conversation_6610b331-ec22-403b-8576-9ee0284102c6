/* pages/profile/profile.wxss */
.profile-container {
  min-height: 100vh;
  background-color: #FFFFFF;
}

/* 顶部个人信息 */
.profile-header {
  position: relative;
  padding: 20px 16px;
  background-color: #FFFFFF;
  color: #333333;
  border-bottom: 1px solid #EEEEEE;
}

.user-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 2px solid #FFFFFF;
  background-color: #1E6A9E;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 16px;
}

.user-info-content {
  flex: 1;
  margin-left: 15px;
}

.user-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333333;
}

.user-id {
  font-size: 14px;
  color: #666666;
}

.user-info-flex {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.user-info-main-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.user-name-id-col {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.user-role-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  gap: 4px;
}
.user-id-switch-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: 2px;
}
.user-id {
  font-size: 14px;
  color: #666666;
}
.user-role-switch-row {
  margin-top: 4px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.switch-role-icon {
  width: 16px;
  height: 16px;
  margin-left: 0;
  cursor: pointer;
  opacity: 0.85;
  transition: opacity 0.2s;
}
.switch-role-icon:active {
  opacity: 0.6;
}

.vip-icon {
  width: 20px;
  height: 20px;
  margin-left: 5px;
}

.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.login-text {
  font-size: 16px;
  margin: 10px 0;
  color: #333333;
}

.login-btn {
  padding: 6px 20px;
  background-color: #E74C3C;
  color: #FFFFFF;
  border-radius: 18px;
  font-size: 14px;
  font-weight: bold;
}

/* 数据统计 */
.stats-bar {
  display: flex;
  justify-content: space-between;
  padding: 10px 0 5px;
  border-top: 1px solid #EEEEEE;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stats-num {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 2px;
  color: #333333;
}

.stats-label {
  font-size: 12px;
  color: #666666;
}

/* 钱包统计 */
.wallet-section {
  background-color: #FFFFFF;
  border-radius: 12px;
  margin: 0 15px 3px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.wallet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px 6px;
  border-bottom: 1px solid #F0F0F0;
}

.wallet-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.wallet-detail {
  font-size: 14px;
  color: #1E6A9E;
  cursor: pointer;
}

.wallet-stats {
  display: flex;
  padding: 10px 0;
}

.wallet-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.wallet-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background-color: #F0F0F0;
}

.wallet-num {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 4px;
}

.wallet-label {
  font-size: 12px;
  color: #666666;
}

/* 统计分隔线 */
.stats-divider {
  height: 1px;
  background-color: #EEEEEE;
  margin: 2px 15px;
  opacity: 0.6;
}

/* 内容统计 */
.content-stats {
  display: flex;
  justify-content: space-around;
  padding: 5px 15px 0;
  background-color: #FFFFFF;
  border-bottom: none;
}

.content-stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.content-stats-num {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 0;
  line-height: 1.2;
}

.content-stats-label {
  font-size: 12px;
  color: #666666;
  line-height: 1.2;
}

/* 轮播图 */
.banner-section {
  padding: 0 15px 3px;
  background-color: #FFFFFF;
  margin-top: 2px;
  margin-bottom: 8px;
}

.banner-swiper {
  width: 100%;
  height: 130px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  display: block; /* 确保图片正确显示 */
}

.banner-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.4);
  color: #FFFFFF;
  font-size: 14px;
  line-height: 1.4;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

/* 内容卡片 */
.content-card {
  margin-top: 0;
  padding: 0 15px;
  background-color: #FFFFFF;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #EEEEEE;
}

.order-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.view-all {
  font-size: 14px;
  color: #666666;
}

/* 订单网格 */
.order-grid {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
}

.order-item {
  position: relative;
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.order-icon {
  width: 28px;
  height: 28px;
  margin-bottom: 5px;
}

.order-name {
  font-size: 13px;
  color: #333333;
}

.order-badge {
  position: absolute;
  top: -5px;
  right: 15px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  background-color: #FF4D4F;
  color: #FFFFFF;
  border-radius: 8px;
  font-size: 10px;
  padding: 0 4px;
}

/* 分隔线 */
.divider {
  height: 6px;
  background-color: #F5F5F5;
}

/* 其他功能 */
.other-functions {
  background-color: #FFFFFF;
  margin: 0 15px 15px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.other-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  padding: 15px 20px 10px;
  border-bottom: 1px solid #F0F0F0;
}

.function-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 15px 10px;
}

.function-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 5px;
  box-sizing: border-box;
}

.function-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
}

.function-name {
  font-size: 12px;
  color: #333333;
  text-align: center;
  line-height: 1.2;
}

/* 原有的菜单样式保留作为备用 */
.menu-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #F0F0F0;
  background-color: #FFFFFF;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
}

.menu-name {
  flex: 1;
  font-size: 16px;
  color: #333333;
}

.arrow-right {
  font-size: 14px;
  color: #CCCCCC;
}

.logout-text {
  color: #FF4444;
}

.arrow-right {
  font-size: 14px;
  color: #999999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 20px;
}

/* 开发者选项 */
.dev-options {
  background-color: #f5f5f5;
  padding: 15px;
  margin-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.dev-options-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.dev-option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 0;
}

.dev-option-label {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
  width: 80px;
}

/* 开发者选项触发区域 - 隐藏的点击区域 */
.dev-trigger {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 50px;
  height: 50px;
  z-index: 100;
  /* 透明不可见 */
  opacity: 0;
}

.switch-role-btn {
  margin-left: 10rpx;
  font-size: 22rpx;
  padding: 0 16rpx;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 24rpx;
  background: #f5f5f5;
  color: #1E6A9E;
  border: 1px solid #1E6A9E;
}

.role-switch-modal {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.role-switch-mask {
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
}
.role-switch-content {
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 32rpx 24rpx 32rpx;
  min-width: 400rpx;
  z-index: 1001;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.12);
}
.role-switch-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  text-align: center;
}
.role-list {
  margin-bottom: 24rpx;
}
.role-item {
  padding: 18rpx 0;
  font-size: 28rpx;
  border-bottom: 1px solid #f0f0f0;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.role-item:last-child {
  border-bottom: none;
}
.close-btn {
  width: 100%;
  margin-top: 12rpx;
  background: #f5f5f5;
  color: #1E6A9E;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 16rpx 0;
}

.user-id-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  margin-top: 2px;
}
.user-id {
  margin-right: 8px;
}
.user-role-switch-row {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.user-role-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
}
.vip-badge-switch-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  gap: 6px;
}
.user-info-align-bottom {
  align-items: flex-end;
}
.user-id-bottom {
  margin-top: 4px;
}
.user-info-align-center {
  align-items: center;
}
.switch-badge-group {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.vip-badge {
  display: flex;
  align-items: center;
  color: #1E6A9E;
  margin-right: 0;
}
