Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    storeData: {
      type: Object,
      value: {}
    }
  },

  data: {
    editData: {
      name: '',
      level: 3,
      level_title: 'L3',
      contact_person: '',
      phone: '',
      address: '',
      image: ''
    },
    storeImageTemp: '', // 门店形象照临时路径
    customImageMode: false, // 标记是否使用自定义图片
    levelOptions: [
      { value: 1, title: 'L1', label: '一星门店' },
      { value: 2, title: 'L2', label: '二星门店' },
      { value: 3, title: 'L3', label: '三星门店' },
      { value: 4, title: 'L4', label: '四星门店' },
      { value: 5, title: 'L5', label: '五星门店' }
    ],
    selectedLevelIndex: 2
  },

  observers: {
    'visible, storeData': function(visible, storeData) {
      if (visible && storeData) {
        this.loadStoreData(storeData);
      }
    },
    'visible': function(visible) {
      if (visible) {
        // 不重置图片相关字段（参考门店创建成功实现）
        // 图片数据由 pageLifetimes.show 处理
      }
    }
  },

  pageLifetimes: {
    show() {
      console.log('编辑门店抽屉组件 show 生命周期触发');
      // 从裁剪页面返回时处理门店图片（参考公司Logo成功实现）
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      // 处理门店图片裁剪后的结果
      if (currentPage.data.croppedStoreImage) {
        console.log('检测到门店图片裁剪结果:', currentPage.data.croppedStoreImage);
        console.log('当前storeImageTemp值:', this.data.storeImageTemp);

        // 立即设置裁剪后的图片，确保页面能够显示
        this.setData({
          storeImageTemp: currentPage.data.croppedStoreImage,
          customImageMode: true
        }, () => {
          // 设置完成后的回调，确保数据已更新
          console.log('门店图片设置完成，当前storeImageTemp值:', this.data.storeImageTemp);
        });

        // 清除页面临时数据
        currentPage.setData({
          croppedStoreImage: '',
          selectedStoreImage: '',
          customStoreImageMode: false
        });
      }
    }
  },

  methods: {
    // 加载门店数据
    loadStoreData(storeData) {
      console.log('加载门店数据:', storeData);
      const levelIndex = this.data.levelOptions.findIndex(item => item.value === storeData.level);
      
      this.setData({
        editData: {
          name: storeData.name || '',
          level: storeData.level || 3,
          level_title: storeData.level_title || 'L3',
          contact_person: storeData.contact_person || '',
          phone: storeData.phone || '',
          address: storeData.address || '',
          image: storeData.image || ''
        },
        selectedLevelIndex: levelIndex >= 0 ? levelIndex : 2
        // 不重置 storeImageTemp，保留裁剪后的图片（参考门店创建成功实现）
      });
    },

    // 输入框变化
    onInputChange(e) {
      const { field } = e.currentTarget.dataset;
      const { value } = e.detail;
      this.setData({
        [`editData.${field}`]: value
      });
    },

    // 门店级别选择
    onLevelChange(e) {
      const index = e.detail.value;
      const levelOption = this.data.levelOptions[index];
      this.setData({
        selectedLevelIndex: index,
        'editData.level': levelOption.value,
        'editData.level_title': levelOption.title
      });
    },

    // 选择门店图片
    onChooseImage() {
      console.log('开始选择门店图片');
      // 先尝试使用chooseMedia API
      try {
        wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: ['album', 'camera'],
          success: res => {
            console.log('选择门店图片成功:', res);
            const file = res.tempFiles[0];
            if (file.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
              wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
              return;
            }
            // 跳转到裁剪页面 - 门店形象照使用16:9比例
            wx.navigateTo({
              url: `/admin/cropper/cropper?src=${encodeURIComponent(file.tempFilePath)}&type=storeImage`
            });
          },
          fail: err => {
            console.error('chooseMedia 失败:', err);
            // 检查是否是用户取消操作
            if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
              console.log('用户取消选择门店图片');
              return; // 用户取消，直接返回
            }
            // 其他错误情况才使用备选方案
            this.onChooseImageFallback();
          }
        });
      } catch (error) {
        console.error('chooseMedia 异常:', error);
        // 如果chooseMedia出现异常，尝试使用chooseImage作为备选方案
        this.onChooseImageFallback();
      }
    },

    // 选择门店图片备选方案
    onChooseImageFallback() {
      console.log('使用chooseImage作为备选方案选择门店图片');
      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: res => {
          console.log('chooseImage选择门店图片成功:', res);
          const tempFilePath = res.tempFilePaths[0];
          const tempFile = res.tempFiles[0];
          if (tempFile && tempFile.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
            wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
            return;
          }
          // 跳转到裁剪页面 - 门店形象照使用16:9比例
          wx.navigateTo({
            url: `/admin/cropper/cropper?src=${encodeURIComponent(tempFilePath)}&type=storeImage`
          });
        },
        fail: err => {
          console.error('chooseImage 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消选择门店图片');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才显示提示
          wx.showToast({
            title: '选择图片失败，请检查相关权限',
            icon: 'none'
          });
        }
      });
    },

    // 删除门店图片
    deleteStoreImage() {
      console.log('删除门店图片');
      this.setData({ 
        storeImageTemp: '',
        'editData.image': ''
      });
    },

    // 预览图片
    onPreviewImage() {
      const imageUrl = this.data.storeImageTemp || this.data.editData.image;
      if (imageUrl) {
        wx.previewImage({
          urls: [imageUrl]
        });
      }
    },

    // 确认保存
    async onConfirm() {
      const { editData } = this.data;
      
      console.log('开始保存门店信息:', editData);
      console.log('临时图片路径:', this.data.storeImageTemp);
      
      // 只验证门店名称为必填字段
      if (!editData.name.trim()) {
        wx.showToast({ title: '请输入门店名称', icon: 'none' });
        return;
      }

      wx.showLoading({ title: '保存中...' });
      try {
        const { storeApi, uploadFile } = require('../../../utils/api');
        
        // 1. 上传图片（如果有新选择的图片）
        let imageUrl = this.data.storeImageTemp || editData.image;

        // 判断是否需要上传新图片 - 检查是否为临时文件路径（参考公司Logo实现）
        const isTempFilePath = this.data.storeImageTemp &&
                              (this.data.storeImageTemp.startsWith('wxfile://') ||
                               this.data.storeImageTemp.startsWith('http://tmp') ||
                               this.data.storeImageTemp.includes('tmp'));

        const needUploadImage = isTempFilePath;

        if (needUploadImage) {
          console.log('检测到新门店图片需要上传:', this.data.storeImageTemp);
          try {
            wx.showLoading({ title: '正在上传图片...' });
            imageUrl = await uploadFile(this.data.storeImageTemp, 'store_images');
            wx.hideLoading();
            console.log('门店图片上传成功，新URL:', imageUrl);
          } catch (uploadError) {
            console.error('门店图片上传失败:', uploadError);
            wx.hideLoading();
            wx.showToast({ title: '门店图片上传失败，请重试', icon: 'none' });
            return;
          }
        } else {
          console.log('无需上传门店图片，使用现有URL:', imageUrl);
        }
        
        // 2. 构建更新数据，只包含有值的字段
        const updateData = {
          name: editData.name.trim() // 门店名称是必填的
        };
        
        // 门店级别（picker选择器，有默认值，不需要检查空值）
        updateData.level = editData.level;
        updateData.level_title = editData.level_title;
        
        // 其他字段只有在有值时才添加到更新数据中
        if (editData.contact_person !== undefined && editData.contact_person !== null) {
          updateData.contact_person = editData.contact_person.trim();
        }
        
        if (editData.phone !== undefined && editData.phone !== null) {
          updateData.phone = editData.phone.trim();
        }
        
        if (editData.address !== undefined && editData.address !== null) {
          updateData.address = editData.address.trim();
        }
        
        // 图片字段，如果有新上传的图片或原有图片，则包含
        if (imageUrl) {
          updateData.image = imageUrl;
        }
        
        console.log('更新门店数据（部分字段）:', updateData);
        const res = await storeApi.updateStore(this.data.storeData.id, updateData);
        wx.hideLoading();
        
        if (res.success) {
          wx.showToast({ title: '保存成功', icon: 'success' });
          this.triggerEvent('confirm', updateData);
          this.onCancel();
        } else {
          wx.showToast({ title: res.message || '保存失败', icon: 'none' });
        }
      } catch (err) {
        console.error('保存门店失败:', err);
        wx.hideLoading();
        wx.showToast({ title: '网络异常', icon: 'none' });
      }
    },

    // 取消
    onCancel() {
      this.triggerEvent('cancel');
    },

    // 阻止冒泡
    preventBubble() {
      // 阻止事件冒泡
    }
  }
});