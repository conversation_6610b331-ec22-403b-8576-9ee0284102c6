# MoreBuy-X 项目开发规范

## 项目架构
本项目采用前后端分离模式，后端使用node.js开发，前端使用微信小程序原生语法开发。
本项目后端部署在【微信云托管】，数据库使用【微信云托管】的MySQL数据库，资源存储可以使用【微信云托管】的"对象存储"。
如果有必要，本项目可以使用【微信云托管】的"函数"。
你可以根据需要，使用微信官方组件及UI图标（联网自动下载）。

## 代码修改规范
1. 修改代码时，应尽可能详细地添加注释，说明修改的目的和实现方式
2. 在解决问题时，应自动检查关联问题，必要时一并修改，但不能破坏原有正常功能
3. 如果修改需要清空数据库或物理删除文件，必须获得明确指令才能继续
4. 修改代码时应考虑安全性，不应在代码中硬编码敏感信息（如API密钥）
5. 修改代码时应保持与原有代码风格一致，包括命名规范、缩进方式等

## 代码组织与文件结构
1. **目录结构规范**
   - 顾客端代码主要在 `/miniprogram/pages/` 目录下
   - 合伙人端代码主要在 `/miniprogram/partner/` 目录下
   - 管理端代码主要在 `/miniprogram/admin/` 目录下
   - 公共组件放在 `/miniprogram/components/` 目录下
   - 工具函数放在 `/miniprogram/utils/` 目录下

2. **命名规范**
   - 文件名使用小写字母，多个单词用连字符（-）连接
   - 变量和函数名使用驼峰命名法（camelCase）
   - 常量使用全大写，多个单词用下划线（_）连接

## 错误处理规范
1. API请求应有适当的错误处理机制，包括网络错误、服务器错误和业务逻辑错误
2. 用户操作失败时应给予友好的提示，避免使用技术术语
3. 关键操作应有日志记录，便于问题排查
4. 敏感操作应有适当的权限验证和安全检查

## 重要部署信息
**项目后端部署在微信云托管，不在本地！**
- 后端服务运行在微信云托管环境
- 数据库使用微信云托管的MySQL数据库
- 不要在本地启动后端服务器
- 所有后端相关的测试和验证都应该通过远程API进行

## 项目界面结构
**本项目包含三个完全不同的界面，不能用相同的逻辑修改：**

1. **顾客端** - 普通用户使用的购物界面
   - 路径：主要在 `/miniprogram/pages/` 目录下
   - 功能：浏览商品、下单、支付、查看订单等
   - 特点：面向终端消费者，注重用户体验和购物流程

2. **合伙人端** - 合伙人使用的管理界面
   - 路径：主要在 `/miniprogram/partner/` 目录下
   - 功能：管理商品、查看订单、处理佣金等
   - 特点：面向商家合作伙伴，注重业务管理功能

3. **管理端** - 系统管理员使用的后台管理界面
   - 路径：主要在 `/miniprogram/admin/` 目录下
   - 功能：系统配置、用户管理、数据统计等
   - 特点：面向系统管理员，注重全局控制和数据分析

**重要规则：** 修改代码时必须严格区分这三个端的需求和逻辑，不能将一个端的修改逻辑直接应用到另一个端。每个端的用户角色、权限和业务流程都不同，必须分别考虑。

## API请求与登录验证规范

1. **API请求规范**
   - 所有API请求应通过 `utils/request.js` 中的统一请求方法进行
   - API接口定义应在 `utils/api.js` 文件中集中管理
   - 接口调用时必须明确指定是否需要登录验证（requireAuth参数）

2. **登录验证规则**
   - 顾客端的公共浏览功能（如首页、商品列表、商品详情等）应设置 `requireAuth: false`
   - 涉及用户个人信息或操作的功能（如下单、支付、个人中心等）应设置 `requireAuth: true`
   - 合伙人端和管理端的所有功能默认需要登录验证
   - 修改API调用时，必须考虑该功能的用户场景和安全需求

## 性能优化规范
1. **小程序性能优化**
   - 减少不必要的数据请求和页面渲染
   - 合理使用缓存机制，避免重复请求相同数据
   - 图片资源应进行适当压缩，避免过大文件影响加载速度
   - 避免频繁触发setData，合并多次数据更新

2. **后端性能优化**
   - 合理设计数据库索引，优化查询性能
   - 对高频访问的数据考虑使用缓存
   - 大量数据查询应实现分页机制
   - API响应应尽可能精简，只返回必要数据

## 测试与调试规范
1. 代码修改后应在微信开发者工具中进行充分测试
2. 测试应覆盖不同的用户角色和使用场景
3. 修复bug时应验证修复是否彻底，避免引入新问题
4. 重要功能更新应进行回归测试，确保不影响其他功能

## 文档与注释规范
1. 重要功能应有相应的文档说明
2. 复杂的业务逻辑应添加详细注释
3. API接口应有清晰的参数和返回值说明
4. 代码修改应在注释中说明修改原因和影响范围

## 总结
以上规范旨在确保MoreBuy-X项目的代码质量和可维护性。开发过程中应严格遵循这些规范，特别注意区分三个不同端（顾客端、合伙人端、管理端）的业务逻辑和用户需求。所有代码修改都应经过充分测试，确保不破坏现有功能，并且应该考虑性能、安全性和用户体验。

这些规范将随着项目的发展不断完善和更新。开发人员应定期查阅最新版本，确保开发工作符合项目要求。