.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 顶部操作按钮 */
.top-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
  margin-bottom: 0; /* 确保与分类标签分开 */
  gap: 20rpx; /* 调整按钮之间的间距 */
  position: fixed; /* 固定定位 */
  top: 0; /* 固定在顶部 */
  left: 0; /* 左对齐 */
  right: 0; /* 右对齐 */
  z-index: 1000; /* 确保在最上层 */
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center; /* 水平居中 */
  border-radius: 8rpx;
  font-size: 26rpx; /* 统一字体大小 */
  transition: all 0.2s ease;
  width: 180rpx !important; /* 强制统一宽度 */
  height: 72rpx !important; /* 强制统一高度 */
  text-align: center; /* 文字居中对齐 */
  box-sizing: border-box; /* 确保padding不会影响总宽度 */
  flex-shrink: 0; /* 防止按钮被压缩 */
  border: 1rpx solid transparent; /* 统一边框，避免边框差异 */
  min-width: 180rpx; /* 最小宽度 */
  max-width: 180rpx; /* 最大宽度 */
  min-height: 72rpx; /* 最小高度 */
  max-height: 72rpx; /* 最大高度 */
}

.select-all-btn {
  color: #fff;
  background-color: #FF6B35;
  border-color: #FF6B35; /* 橙红色边框 */
}

.select-all-btn:active {
  background-color: #e55a2b; /* 按下时稍微深一点的橙红色 */
}

/* 移除复选框图标样式，因为不再使用 */

/* 确保按钮内文字完全居中 */
.action-btn text {
  text-align: center;
  white-space: nowrap; /* 防止文字换行 */
  font-size: 26rpx; /* 统一字体大小 */
  line-height: 1.2; /* 统一行高 */
  flex-shrink: 0; /* 防止文字被压缩 */
  font-weight: 400; /* 统一字重 */
}

.remove-btn {
  color: #fff;
  background-color: #ccc;
  border-color: #ccc; /* 只改变边框颜色 */
}

.remove-btn.active {
  background-color: #FF6B35;
  border-color: #FF6B35; /* 激活状态边框颜色 */
}

.remove-btn.disabled {
  background-color: #ccc;
  color: #999;
  border-color: #ccc; /* 禁用状态边框颜色 */
}

/* 分类标签 */
.category-tabs {
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
  padding: 20rpx 24rpx; /* 增加上下内边距，与顶部按钮分开 */
  margin-top: 0; /* 确保与顶部按钮分开 */
  position: fixed; /* 固定定位 */
  top: 112rpx; /* 固定在顶部按钮下方，112rpx = 20rpx(padding) + 72rpx(按钮高度) + 20rpx(间距) */
  left: 0; /* 左对齐 */
  right: 0; /* 右对齐 */
  z-index: 999; /* 确保在按钮下方但在内容上方 */
}

.tabs-scroll {
  white-space: nowrap;
}

.tab-item {
  display: inline-block;
  padding: 16rpx 24rpx; /* 减小内边距 */
  margin-right: 20rpx;
  font-size: 24rpx; /* 缩小字号 */
  color: #666;
  border-radius: 32rpx;
  transition: all 0.2s ease;
  background-color: transparent; /* 去掉背景色 */
  border: 2rpx solid transparent; /* 添加透明边框，为选中状态做准备 */
}

.tab-item.active {
  color: #FF6B35; /* 选中状态文字颜色 */
  background-color: transparent; /* 去掉背景色 */
  border: 2rpx solid #FF6B35; /* 选中状态加边框 */
}

.tab-item:last-child {
  margin-right: 0;
}

.content {
  padding: 20rpx;
  padding-top: 200rpx; /* 为固定的头部留出空间，200rpx = 112rpx(按钮区域) + 88rpx(分类标签区域) */
}

.loading {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 40rpx;
}

.empty button {
  background-color: #FF6B35;
  color: #fff;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
}

.list {
  padding: 20rpx 0;
}

.item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  width: 100%; /* 确保容器B占满宽度 */
  box-sizing: border-box;
}

/* 复选框样式 */
.select-box {
  width: 44rpx;
  height: 44rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  border: 3rpx solid #333;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  box-sizing: border-box;
  position: relative;
  padding: 8rpx;
  margin-left: -8rpx;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.select-box.selected {
  border-color: #FF6B35;
  background-color: rgba(255, 107, 53, 0.1);
  transform: scale(1.05);
}

.select-box image {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.8;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.select-box.selected image {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

.image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 160rpx;
  min-width: 0; /* 允许内容收缩 */
}

.name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.price {
  font-size: 32rpx;
  color: #FF6B35;
  font-weight: 600;
}

/* 加入购物车按钮样式 */
.add-to-cart-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  flex-shrink: 0;
  transition: all 0.2s ease;
  background-color: transparent; /* 确保背景透明 */
}

.add-to-cart-btn:active {
  transform: scale(0.95);
}

.plus-icon {
  width: 48rpx;
  height: 48rpx;
  /* 确保图标可见 */
}