# Users表结构迁移指南

## 📋 **迁移概述**

本次迁移将users表的现有"id"字段改为"user_id"，并添加新的自增"id"字段作为主键。这样可以更好地关联数据表，提供更清晰的字段语义。

## 🏗️ **迁移前后对比**

### 迁移前
```sql
CREATE TABLE users (
  id VARCHAR(32) NOT NULL PRIMARY KEY,  -- 业务ID（如：8405116576）
  username VARCHAR(50),
  nickname VARCHAR(50),
  -- 其他字段...
);
```

### 迁移后
```sql
CREATE TABLE users (
  id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,  -- 自增主键ID
  user_id VARCHAR(32) NOT NULL UNIQUE,         -- 业务ID（如：8405116576）
  username VARCHAR(50),
  nickname VARCHAR(50),
  -- 其他字段...
);
```

## 🔄 **迁移步骤**

### 第一步：备份数据库
```sql
-- 强烈建议在执行迁移前备份整个数据库
CREATE TABLE users_backup AS SELECT * FROM users;
```

### 第二步：执行迁移脚本
```bash
# 使用Node.js脚本执行迁移
node backend/scripts/migrate_users_table.js

# 或者手动执行SQL脚本
mysql -u username -p database_name < backend/scripts/migrate_users_table_structure.sql
```

### 第三步：验证迁移结果
```sql
-- 检查表结构
DESCRIBE users;

-- 检查索引
SHOW INDEX FROM users;

-- 验证数据完整性
SELECT COUNT(*) as total_users FROM users;
SELECT COUNT(*) as users_with_user_id FROM users WHERE user_id IS NOT NULL;
SELECT COUNT(*) as users_with_id FROM users WHERE id IS NOT NULL;

-- 检查user_id唯一性
SELECT user_id, COUNT(*) as count 
FROM users 
GROUP BY user_id 
HAVING count > 1;
```

## 📝 **代码修改清单**

### 后端修改

#### 1. 模型层 (Models)
- ✅ `backend/models/User.js` - 添加`findByUserId`方法，更新现有方法
- ✅ `backend/models/PartnerApplication.js` - 更新JOIN查询
- ✅ `backend/models/Partner.js` - 更新JOIN查询
- ✅ `backend/models/Message.js` - 更新JOIN查询
- ✅ `backend/models/Group.js` - 更新JOIN查询

#### 2. 服务层 (Services)
- ✅ `backend/services/userService.js` - 更新所有用户相关方法

#### 3. 控制器层 (Controllers)
- ✅ `backend/controllers/userController.js` - 更新用户列表和批量操作
- ✅ `backend/controllers/partnerOrderController.js` - 更新用户查询
- ✅ `backend/controllers/shareController.js` - 更新用户查询
- ✅ `backend/controllers/storeController.js` - 更新用户查询
- ✅ `backend/controllers/vipController.js` - 更新用户查询
- ✅ `backend/controllers/adminMessageController.js` - 更新JOIN查询
- ✅ `backend/controllers/adminController.js` - 更新JOIN查询

#### 4. 路由层 (Routes)
- ✅ `backend/routes/quickMenus.js` - 更新用户查询
- ✅ `backend/routes/group.js` - 更新用户查询

### 前端修改

#### 1. API调用
- ✅ `miniprogram/utils/api.js` - 更新API调用逻辑

#### 2. 页面逻辑
- ✅ `miniprogram/pages/profile/profile.js` - 更新用户信息处理
- ✅ `miniprogram/admin/users/users.js` - 更新用户管理逻辑

## 🔧 **兼容性处理**

### 向后兼容策略
1. **双重查询**：优先通过`user_id`查询，如果失败则通过自增`id`查询
2. **字段映射**：在API响应中同时返回`id`（业务ID）和`_id`（自增ID）
3. **渐进迁移**：分阶段更新代码，确保系统稳定运行

### 数据一致性
1. **唯一性约束**：为`user_id`添加唯一索引
2. **数据验证**：迁移后验证所有数据的完整性
3. **外键关系**：更新相关表的外键引用

## ⚠️ **注意事项**

### 迁移前准备
1. **备份数据**：务必在迁移前备份整个数据库
2. **测试环境**：先在测试环境验证迁移脚本
3. **停机时间**：预估迁移所需时间，安排合适的维护窗口
4. **回滚计划**：准备回滚脚本以应对意外情况

### 迁移中注意
1. **数据量**：如果用户数据量很大，迁移可能需要较长时间
2. **并发访问**：迁移期间避免对users表的写操作
3. **监控日志**：密切关注迁移过程中的错误日志

### 迁移后验证
1. **功能测试**：全面测试所有用户相关功能
2. **性能测试**：验证查询性能是否受到影响
3. **数据验证**：确保所有数据都正确迁移

## 🚀 **执行命令**

### 1. 执行迁移
```bash
cd backend
node scripts/migrate_users_table.js
```

### 2. 验证迁移结果
```bash
# 检查迁移日志
tail -f logs/migration.log

# 验证数据完整性
mysql -u username -p database_name -e "
SELECT 'Total users' as metric, COUNT(*) as count FROM users
UNION ALL
SELECT 'Users with user_id', COUNT(*) FROM users WHERE user_id IS NOT NULL
UNION ALL
SELECT 'Users with id', COUNT(*) FROM users WHERE id IS NOT NULL;
"
```

### 3. 回滚（如果需要）
```sql
-- 删除新添加的id字段
ALTER TABLE users DROP COLUMN id;

-- 将user_id重命名回id
ALTER TABLE users CHANGE COLUMN user_id id VARCHAR(32) NOT NULL;

-- 恢复主键
ALTER TABLE users ADD PRIMARY KEY (id);
```

## 📊 **迁移状态检查**

### 检查项目
- [ ] 数据库备份完成
- [ ] 迁移脚本执行成功
- [ ] 表结构验证通过
- [ ] 数据完整性验证通过
- [ ] 代码修改完成
- [ ] 功能测试通过
- [ ] 性能测试通过
- [ ] 生产环境部署完成

### 验证清单
- [ ] 用户注册功能正常
- [ ] 用户登录功能正常
- [ ] 用户信息查询正常
- [ ] 用户信息更新正常
- [ ] 订单关联正常
- [ ] 消息系统正常
- [ ] 群组功能正常
- [ ] 管理端功能正常

## 🆘 **故障排除**

### 常见问题

#### 1. 迁移失败
**症状**：迁移脚本执行失败
**解决方案**：
- 检查数据库连接
- 验证用户权限
- 查看错误日志
- 执行回滚脚本

#### 2. 数据不一致
**症状**：某些用户数据丢失或重复
**解决方案**：
- 对比备份数据
- 手动修复数据
- 重新执行迁移

#### 3. 功能异常
**症状**：某些功能无法正常工作
**解决方案**：
- 检查代码修改
- 验证API调用
- 查看错误日志
- 回滚到稳定版本

### 联系支持
如果在迁移过程中遇到问题，请联系技术支持团队。 