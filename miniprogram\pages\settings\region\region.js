const { userApi } = require('../../../utils/api');

Page({
  data: {
    loading: false,
    region: ['北京市', '北京市', '东城区']
  },

  onLoad() {
    // 获取传递过来的当前地区
    const eventChannel = this.getOpenerEventChannel();
    eventChannel.on('currentRegion', (data) => {
      if (data.region && data.region.length === 3) {
        this.setData({
          region: data.region
        });
      }
    });
    
    // 如果没有传递地区，则使用用户默认地区
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.region) {
      // 将地区字符串分割成数组
      const regionArray = userInfo.region.split(' ');
      if (regionArray.length === 3) {
        this.setData({
          region: regionArray
        });
      }
    }
  },

  // 地区选择改变
  onRegionChange(e) {
    this.setData({
      region: e.detail.value
    });
  },

  // 保存地区设置
  async saveRegion() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    try {
      const regionString = this.data.region.join(' ');
      
      // 检查是否是从地址编辑页面跳转过来的
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      
      if (prevPage && prevPage.route === 'pages/address/edit') {
        // 如果是地址编辑页面，直接返回选择结果
        const eventChannel = this.getOpenerEventChannel();
        eventChannel.emit('selectRegion', {
          province: this.data.region[0],
          city: this.data.region[1],
          district: this.data.region[2]
        });
        
        wx.showToast({
          title: '地区选择成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        // 如果是用户设置页面，更新用户信息
        const res = await userApi.updateUserInfo({ region: regionString });
        
        if (res.success) {
          // 更新本地存储的用户信息
          const userInfo = wx.getStorageSync('userInfo');
          userInfo.region = regionString;
          wx.setStorageSync('userInfo', userInfo);
          
          // 更新全局用户信息
          const app = getApp();
          app.globalData.userInfo = userInfo;
          
          wx.showToast({
            title: '地区设置成功',
            icon: 'success'
          });
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: '地区设置失败',
            icon: 'none'
          });
        }
      }
    } catch (error) {
      console.error('保存地区失败:', error);
      wx.showToast({
        title: '地区设置失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  }
}); 