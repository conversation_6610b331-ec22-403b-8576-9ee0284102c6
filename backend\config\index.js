/**
 * 主配置文件
 */
const config = require('./config');

module.exports = {
  port: process.env.PORT || 3001,
  env: process.env.NODE_ENV || 'production',
  apiPrefix: '/api',
  jwtSecret: process.env.JWT_SECRET || 'morebuy-secret-key',
  jwtExpiration: '24h',
  database: {
    // 在云环境中使用持久化存储路径
    path: process.env.DB_PATH || 'data/morebuy.db'
  },
  db: config.db,
  jwt: config.jwt,
  wechat: config.wechat
};
