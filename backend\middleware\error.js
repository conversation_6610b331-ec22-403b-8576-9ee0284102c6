/**
 * 错误处理中间件
 */
module.exports = (err, req, res, next) => {
  console.error('错误处理中间件捕获到错误:');
  console.error('错误堆栈:', err.stack);
  console.error('请求路径:', req.path);
  console.error('请求方法:', req.method);
  console.error('用户ID:', req.userData ? req.userData.userId : '未登录');
  
  const statusCode = err.statusCode || 500;
  const message = err.message || '服务器内部错误';
  
  // 记录错误详情
  console.error('错误详情:', {
    statusCode,
    message,
    url: req.url,
    method: req.method,
    body: req.body,
    params: req.params,
    query: req.query
  });
  
  res.status(statusCode).json({
    success: false,
    message,
    error: process.env.NODE_ENV === 'production' ? {} : {
      stack: err.stack,
      details: err.message
    }
  });
};
