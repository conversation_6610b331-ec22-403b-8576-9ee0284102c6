-- 添加微信登录相关字段到users表
-- 微信openid（如果不存在则添加）
ALTER TABLE users ADD COLUMN openid VARCHAR(100) DEFAULT NULL COMMENT '微信openid';

-- 微信unionid（如果不存在则添加）
ALTER TABLE users ADD COLUMN unionid VARCHAR(100) DEFAULT NULL COMMENT '微信unionid';

-- 添加索引以提高查询性能（如果不存在则添加）
CREATE INDEX idx_users_openid ON users(openid);
CREATE INDEX idx_users_unionid ON users(unionid);

-- 确保openid字段唯一性（可选，根据业务需求决定）
-- ALTER TABLE users ADD UNIQUE INDEX uk_users_openid (openid);