-- 为轮播图表添加页面类型字段的升级脚本
-- 如果page_type字段不存在，则添加该字段
ALTER TABLE `banners` 
ADD COLUMN IF NOT EXISTS `page_type` varchar(50) DEFAULT 'customer_home' COMMENT '页面类型：customer_home-顾客首页, customer_category-顾客分类页, customer_profile-顾客我的页, partner_products-合伙人选品页, partner_center-合伙人中心页' 
AFTER `link_url`;

-- 添加索引
ALTER TABLE `banners` 
ADD INDEX IF NOT EXISTS `idx_page_type` (`page_type`);

-- 更新现有数据，如果page_type为空则设置为默认值
UPDATE `banners` SET `page_type` = 'customer_home' WHERE `page_type` IS NULL OR `page_type` = '';

-- 显示更新结果
SELECT COUNT(*) as total_banners, page_type, COUNT(*) as count_by_type 
FROM `banners` 
GROUP BY page_type; 