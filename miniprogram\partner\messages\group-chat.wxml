<view class="chat-container">
  <!-- 消息列表 -->
  <scroll-view class="message-list"
               scroll-y="true"
               scroll-into-view="{{scrollToView}}"
               bindscrolltoupper="onLoadMore">
    <view class="loading" wx:if="{{loading}}">
      <view class="loading-icon"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <view class="message-item {{item.senderId === userInfo.id ? 'self' : 'other'}}"
          wx:for="{{messages}}"
          wx:key="id"
          id="msg-{{item.id}}">
      <image class="avatar" src="{{item.senderId === userInfo.id ? userInfo.avatar || userInfo.avatarUrl || '/images/icons2/男头像.png' : item.avatar || '/images/icons2/男头像.png'}}"></image>
      <view class="message-content">
        <!-- 群聊显示发送者昵称（仅对方消息显示） -->
        <view class="sender-name" wx:if="{{item.senderId !== userInfo.id}}">{{item.nickname}}</view>
        <view class="bubble {{item.type === 'image' ? 'image-bubble' : ''}}">
          <text wx:if="{{item.type === 'text' || !item.type}}">{{item.content}}</text>
          <image wx:if="{{item.type === 'image'}}" src="{{item.content}}" mode="widthFix" bindtap="previewImage" data-url="{{item.content}}"></image>
        </view>
        <view class="time">{{item.displayTime}}</view>
      </view>
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="input-area">
    <view class="input-box">
      <!-- 图片选择按钮 - 左侧 -->
      <view class="action-btn image-btn" bindtap="chooseImage">
        <image src="../../images/icons2/添加.png" mode="aspectFit"></image>
      </view>

      <input class="message-input"
             value="{{inputMessage}}"
             bindinput="onInput"
             placeholder="输入消息..."
             confirm-type="send"
             bindconfirm="sendMessage"
      />

      <view class="action-buttons">
        <view class="action-btn send-btn" bindtap="sendMessage">
          <text>发送</text>
        </view>
      </view>
    </view>
  </view>
</view>