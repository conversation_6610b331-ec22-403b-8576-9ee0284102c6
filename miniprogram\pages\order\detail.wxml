<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>

  <block wx:if="{{!loading && orderDetail}}">
    <!-- 订单状态 -->
    <view class="status-section">
      <view class="status-header">
        <text class="status-text">{{orderDetail.status_text}}</text>
        <text class="status-desc" wx:if="{{orderDetail.status_desc}}">{{orderDetail.status_desc}}</text>
      </view>
      
      <!-- 订单进度条 -->
      <view class="status-steps" wx:if="{{statusSteps.length > 1}}">
        <block wx:for="{{statusSteps}}" wx:key="title">
          <view class="step-item {{item.active ? 'active' : ''}} {{item.current ? 'current' : ''}}">
            <view class="step-dot"></view>
            <view class="step-line" wx:if="{{index < statusSteps.length - 1}}"></view>
            <view class="step-title">{{item.title}}</view>
            <view class="step-desc" wx:if="{{item.desc}}">{{item.desc}}</view>
          </view>
        </block>
      </view>
    </view>

    <!-- 收货信息 -->
    <view class="info-section" wx:if="{{orderDetail.delivery_type === 'express'}}">
      <view class="section-title">收货信息</view>
      <view class="address-info">
        <view class="address-user">
          <text class="name">{{orderDetail.receiver_name}}</text>
          <text class="phone">{{orderDetail.receiver_phone}}</text>
        </view>
        <view class="address-detail">{{orderDetail.receiver_address}}</view>
      </view>
    </view>

    <!-- 自提信息 -->
    <view class="info-section" wx:if="{{orderDetail.delivery_type === 'self'}}">
      <view class="section-title">自提信息</view>
      <view class="store-info">
        <view class="store-name">{{orderDetail.store_name}}</view>
        <view class="store-address">{{orderDetail.store_address}}</view>
        <view class="store-contact">
          <text>联系电话: {{orderDetail.store_phone}}</text>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="info-section">
      <view class="section-title">订单信息</view>
      <view class="order-info">
        <view class="info-item">
          <text class="info-label">订单编号</text>
          <view class="info-value-copy">
            <text>{{orderDetail.order_no}}</text>
            <view class="copy-btn" bindtap="copyOrderNo">复制</view>
          </view>
        </view>
        <view class="info-item">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{orderDetail.created_at}}</text>
        </view>
        <view class="info-item" wx:if="{{orderDetail.payment_time}}">
          <text class="info-label">支付时间</text>
          <text class="info-value">{{orderDetail.payment_time}}</text>
        </view>
        <view class="info-item" wx:if="{{orderDetail.shipping_time}}">
          <text class="info-label">发货时间</text>
          <text class="info-value">{{orderDetail.shipping_time}}</text>
        </view>
        <view class="info-item" wx:if="{{orderDetail.completion_time}}">
          <text class="info-label">完成时间</text>
          <text class="info-value">{{orderDetail.completion_time}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">支付方式</text>
          <text class="info-value">{{orderDetail.payment_method}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">配送方式</text>
          <text class="info-value">{{orderDetail.delivery_type === 'express' ? '快递配送' : '门店自提'}}</text>
        </view>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="info-section">
      <view class="section-title">商品信息</view>
      <view class="products-list">
        <block wx:for="{{orderDetail.items}}" wx:key="id">
          <view class="product-item">
            <image class="product-image" src="{{item.image || '/images/icons2/默认商品.png'}}"></image>
            <view class="product-info">
              <view class="product-name">{{item.name}}</view>
              <view class="product-specs" wx:if="{{item.specs}}">{{item.specs}}</view>
              <view class="product-price-qty">
                <text class="product-price">¥{{item.price}}</text>
                <text class="product-qty">x{{item.quantity}}</text>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 金额信息 -->
    <view class="info-section">
      <view class="section-title">金额信息</view>
      <view class="price-info">
        <view class="price-item">
          <text class="price-label">商品金额</text>
          <text class="price-value">¥{{orderDetail.goods_amount}}</text>
        </view>
        <view class="price-item">
          <text class="price-label">运费</text>
          <text class="price-value">¥{{orderDetail.shipping_fee}}</text>
        </view>
        <view class="price-item" wx:if="{{orderDetail.discount_amount > 0}}">
          <text class="price-label">优惠金额</text>
          <text class="price-value">-¥{{orderDetail.discount_amount}}</text>
        </view>
        <view class="price-item total">
          <text class="price-label">实付金额</text>
          <text class="price-value">¥{{orderDetail.total_amount}}</text>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="footer-actions">
      <!-- 待付款状态 -->
      <block wx:if="{{orderDetail.status === 'pending_payment'}}">
        <view class="action-btn" bindtap="contactService">联系客服</view>
        <view class="action-btn" bindtap="cancelOrder">取消订单</view>
        <view class="action-btn primary" bindtap="goToPay">去支付</view>
      </block>
      
      <!-- 待发货状态 -->
      <block wx:elif="{{orderDetail.status === 'pending_shipment'}}">
        <view class="action-btn" bindtap="contactService">联系客服</view>
        <view class="action-btn" bindtap="applyRefund">申请退款</view>
      </block>
      
      <!-- 待收货状态 -->
      <block wx:elif="{{orderDetail.status === 'shipped'}}">
        <view class="action-btn" bindtap="contactService">联系客服</view>
        <view class="action-btn" bindtap="viewLogistics">查看物流</view>
        <view class="action-btn primary" bindtap="confirmReceipt">确认收货</view>
      </block>
      
      <!-- 已完成状态 -->
      <block wx:elif="{{orderDetail.status === 'completed'}}">
        <view class="action-btn" bindtap="contactService">联系客服</view>
        <view class="action-btn" bindtap="viewLogistics">查看物流</view>
        <view wx:if="{{!orderDetail.is_rated}}" class="action-btn primary" bindtap="goToRate">去评价</view>
      </block>
      
      <!-- 已取消状态 -->
      <block wx:elif="{{orderDetail.status === 'cancelled'}}">
        <view class="action-btn" bindtap="contactService">联系客服</view>
      </block>
      
      <!-- 退款/售后状态 -->
      <block wx:elif="{{orderDetail.status === 'refund'}}">
        <view class="action-btn" bindtap="contactService">联系客服</view>
        <view class="action-btn primary" bindtap="viewRefundDetail">查看退款详情</view>
      </block>
    </view>
  </block>

  <!-- 订单不存在 -->
  <view class="empty-container" wx:if="{{!loading && !orderDetail}}">
    <image class="empty-icon" src="/images/icons/empty-order.svg"></image>
    <text class="empty-text">订单不存在或已被删除</text>
  </view>
</view>