/**
 * 消息模型
 */
const db = require('../utils/db');

class Message {
  static async findAll(options = {}) {
    try {
      let { userId, page = 1, pageSize = 20 } = options;
      console.log('[消息模型调试] 原始参数 userId:', userId, '类型:', typeof userId, 'page:', page, 'pageSize:', pageSize);

      if (!userId) {
        // 未传userId，直接返回空
        console.log('[消息模型调试] 未提供userId，返回空列表');
        return { list: [], total: 0, page, pageSize };
      }

      // 保留原始ID，不做类型转换
      const id = userId;
      console.log('[消息模型调试] 使用的id:', id, '类型:', typeof id);

      // 检查id是否有效
      if (!id) {
        console.error('[消息模型调试] 无效的userId:', userId);
        return { list: [], total: 0, page, pageSize };
      }

      const offset = (page - 1) * pageSize;
      console.log('[消息模型调试] 查询参数 offset:', offset, 'limit:', pageSize);

      // 获取消息列表 - 使用简单查询避免字符集问题
      console.log('[消息模型调试] 执行SQL查询消息列表');
      const simpleSql = `
        SELECT * FROM messages
        WHERE senderId = ? OR receiverId = ?
        ORDER BY createTime DESC
        LIMIT ? OFFSET ?
      `;
      console.log('[消息模型调试] SQL:', simpleSql);

      // 确保参数类型正确
      const safePageSize = parseInt(pageSize, 10);
      const safeOffset = parseInt(offset, 10);
      console.log('[消息模型调试] 参数:', [id, id, safePageSize, safeOffset]);

      const messages = await db.query(simpleSql, [id, id, safePageSize, safeOffset]);
      console.log('[消息模型调试] 查询结果:', messages ? `获取到${messages.length}条消息` : '查询结果为空');

      // 如果有消息，获取用户信息
      if (messages && messages.length > 0) {
        console.log('[消息模型调试] 第一条消息:', JSON.stringify(messages[0]));

        // 获取所有相关用户ID
        const userIds = new Set();
        messages.forEach(msg => {
          userIds.add(msg.senderId);
          userIds.add(msg.receiverId);
        });

        // 查询所有相关用户信息
        const users = await db.query(
          `SELECT id, nickname, avatar FROM users WHERE id IN (?)`,
          [Array.from(userIds)]
        );

        // 创建用户信息映射
        const userMap = {};
        users.forEach(user => {
          userMap[user.id] = user;
        });

        // 为每条消息添加用户信息
        messages.forEach(msg => {
          const sender = userMap[msg.senderId] || { nickname: `用户${msg.senderId.substring(0, 4)}`, avatar: '/images/icons2/男头像.png' };
          const receiver = userMap[msg.receiverId] || { nickname: `用户${msg.receiverId.substring(0, 4)}`, avatar: '/images/icons2/男头像.png' };

          msg.sender_nickname = sender.nickname;
          msg.sender_avatar = sender.avatar;
          msg.receiver_nickname = receiver.nickname;
          msg.receiver_avatar = receiver.avatar;
        });
      }

      // 获取总消息数
      console.log('[消息模型调试] 执行SQL查询消息总数');
      const countSql = `
        SELECT COUNT(*) as count
        FROM messages
        WHERE senderId = ? OR receiverId = ?
      `;
      console.log('[消息模型调试] 总数SQL:', countSql);
      console.log('[消息模型调试] 总数参数:', [id, id]);

      const total = await db.query(countSql, [id, id]);
      console.log('[消息模型调试] 总消息数:', total && total[0] ? total[0].count : 0);

      return {
        list: messages || [],
        total: total && total[0] ? total[0].count : 0,
        page,
        pageSize
      };
    } catch (error) {
      console.error('[消息模型调试] 获取消息列表失败:', error);
      throw error;
    }
  }

  static async findById(id) {
    const result = await db.query('SELECT * FROM messages WHERE id = ?', [id]);
    return result.length > 0 ? result[0] : null;
  }

  static async create(data) {
    try {
      const { senderId, receiverId, content, type = 'text' } = data;
      const createTime = Date.now();

      const result = await db.query(
        'INSERT INTO messages (senderId, receiverId, content, type, createTime) VALUES (?, ?, ?, ?, ?)',
        [senderId, receiverId, content, type, createTime]
      );

      return {
        id: result.insertId,
        senderId,
        receiverId,
        content,
        type,
        createTime
      };
    } catch (error) {
      console.error('创建消息失败:', error);
      throw error;
    }
  }

  static async update(id, messageData) {
    await db.update('messages', { id }, messageData);
    return await this.findById(id);
  }

  static async markAsRead(id) {
    await db.query('UPDATE messages SET isRead = 1 WHERE id = ?', [id]);
    return await this.findById(id);
  }

  static async markAllAsRead(userId, type) {
    let sql = 'UPDATE messages SET isRead = 1 WHERE userId = ?';
    const params = [userId];

    if (type) {
      sql += ' AND type = ?';
      params.push(type);
    }

    await db.query(sql, params);
    return { success: true };
  }

  static async count(options = {}) {
    const { userId, type, isRead } = options;

    let sql = 'SELECT COUNT(*) as total FROM messages WHERE 1=1';
    const params = [];

    if (userId) {
      sql += ' AND userId = ?';
      params.push(userId);
    }

    if (type) {
      sql += ' AND type = ?';
      params.push(type);
    }

    if (isRead !== undefined) {
      sql += ' AND isRead = ?';
      params.push(isRead);
    }

    const result = await db.query(sql, params);
    return result[0].total;
  }

  static async getUnreadCount(userId) {
    try {
      const result = await db.query(
        'SELECT COUNT(*) as count FROM messages WHERE receiverId = ? AND isRead = FALSE',
        [userId]
      );
      return result[0].count || 0;
    } catch (error) {
      console.error('获取未读消息数失败:', error);
      throw error;
    }
  }

  static async getMessages(userId, targetUserId, page = 1, pageSize = 20) {
    try {
      const offset = (page - 1) * pageSize;

      // 记录参数信息
      console.log('[消息模型调试] getMessages - 参数信息:');
      console.log('[消息模型调试] userId:', userId, '类型:', typeof userId);
      console.log('[消息模型调试] targetUserId:', targetUserId, '类型:', typeof targetUserId);
      console.log('[消息模型调试] page:', page, '类型:', typeof page);
      console.log('[消息模型调试] pageSize:', pageSize, '类型:', typeof pageSize);

      // 获取消息列表 - 使用简单查询避免字符集问题
      console.log('[消息模型调试] getMessages - 执行SQL查询');
      const simpleSql = `
        SELECT * FROM messages
        WHERE (senderId = ? AND receiverId = ?)
        OR (senderId = ? AND receiverId = ?)
        ORDER BY createTime DESC
        LIMIT ? OFFSET ?
      `;
      console.log('[消息模型调试] getMessages - SQL:', simpleSql);

      // 确保参数类型正确
      const safePageSize = parseInt(pageSize, 10);
      const safeOffset = parseInt(offset, 10);
      console.log('[消息模型调试] getMessages - 参数:', [userId, targetUserId, targetUserId, userId, safePageSize, safeOffset]);

      const messages = await db.query(simpleSql, [userId, targetUserId, targetUserId, userId, safePageSize, safeOffset]);
      console.log('[消息模型调试] getMessages - 查询结果:', messages ? `获取到${messages.length}条消息` : '查询结果为空');

      // 如果有消息，获取用户信息
      if (messages && messages.length > 0) {
        // 获取所有相关用户ID
        const userIds = new Set();
        messages.forEach(msg => {
          userIds.add(msg.senderId);
          userIds.add(msg.receiverId);
        });

        // 查询所有相关用户信息
        const users = await db.query(
          `SELECT id, nickname, avatar FROM users WHERE id IN (?)`,
          [Array.from(userIds)]
        );

        // 创建用户信息映射
        const userMap = {};
        users.forEach(user => {
          userMap[user.id] = user;
        });

        // 为每条消息添加用户信息
        messages.forEach(msg => {
          const sender = userMap[msg.senderId] || { nickname: `用户${msg.senderId.substring(0, 4)}`, avatar: '/images/icons2/男头像.png' };
          const receiver = userMap[msg.receiverId] || { nickname: `用户${msg.receiverId.substring(0, 4)}`, avatar: '/images/icons2/男头像.png' };

          msg.sender_nickname = sender.nickname;
          msg.sender_avatar = sender.avatar;
          msg.receiver_nickname = receiver.nickname;
          msg.receiver_avatar = receiver.avatar;
        });
      }

      // 获取总消息数
      console.log('[消息模型调试] getMessages - 执行SQL查询消息总数');
      const countSql = `
        SELECT COUNT(*) as count FROM messages
        WHERE (senderId = ? AND receiverId = ?)
        OR (senderId = ? AND receiverId = ?)
      `;
      console.log('[消息模型调试] getMessages - 总数SQL:', countSql);
      console.log('[消息模型调试] getMessages - 总数参数:', [userId, targetUserId, targetUserId, userId]);

      // 确保参数类型正确
      const total = await db.query(countSql, [userId, targetUserId, targetUserId, userId]);
      console.log('[消息模型调试] getMessages - 总消息数:', total && total[0] ? total[0].count : 0);

      // 标记消息为已读
      try {
        await db.query(
          `UPDATE messages SET isRead = TRUE
           WHERE senderId = ? AND receiverId = ? AND isRead = FALSE`,
          [targetUserId, userId]
        );
        console.log('[消息模型调试] getMessages - 已标记消息为已读');
      } catch (error) {
        console.error('[消息模型调试] getMessages - 标记消息为已读失败:', error.message);
        // 继续执行，不影响消息获取
      }

      return {
        list: messages,
        total: total[0].count,
        page,
        pageSize
      };
    } catch (error) {
      console.error('获取消息失败:', error);
      throw error;
    }
  }

  static async getRecentChats(userId) {
    try {
      console.log('[消息模型调试] getRecentChats - 参数信息:');
      console.log('[消息模型调试] userId:', userId, '类型:', typeof userId);

      // 直接获取最近聊天的消息，不使用GROUP BY
      console.log('[消息模型调试] getRecentChats - 执行SQL查询最近消息');

      // 先检查消息表中是否有数据
      const allMessages = await db.query('SELECT COUNT(*) as count FROM messages');
      console.log('[消息模型调试] getRecentChats - 消息表总数:', allMessages[0].count);

      // 检查与当前用户相关的消息
      const userMessages = await db.query('SELECT COUNT(*) as count FROM messages WHERE senderId = ? OR receiverId = ?', [userId, userId]);
      console.log('[消息模型调试] getRecentChats - 用户相关消息数:', userMessages[0].count);

      // 直接获取最近的消息
      const recentMessagesSql = `
        SELECT
          m.*,
          CASE
            WHEN m.senderId = ? THEN m.receiverId
            ELSE m.senderId
          END as targetUserId
        FROM messages m
        WHERE m.senderId = ? OR m.receiverId = ?
        ORDER BY m.createTime DESC
        LIMIT 20
      `;
      console.log('[消息模型调试] getRecentChats - SQL:', recentMessagesSql);
      console.log('[消息模型调试] getRecentChats - 参数:', [userId, userId, userId]);

      const messages = await db.query(recentMessagesSql, [userId, userId, userId]);
      console.log('[消息模型调试] getRecentChats - 获取到消息数:', messages ? messages.length : 0);

      if (!messages || messages.length === 0) {
        console.log('[消息模型调试] getRecentChats - 没有最近聊天');
        return [];
      }

      // 获取所有相关用户ID
      const userIds = new Set();
      messages.forEach(msg => {
        userIds.add(msg.senderId);
        userIds.add(msg.receiverId);
      });

      // 查询所有相关用户信息
      console.log('[消息模型调试] getRecentChats - 查询用户信息，用户ID:', Array.from(userIds));

      let users = [];
      if (userIds.size > 0) {
        // 构建查询条件
        const placeholders = Array(userIds.size).fill('?').join(',');
        const userIdArray = Array.from(userIds);

        const usersSql = `SELECT id, nickname, avatar FROM users WHERE id IN (${placeholders})`;
        console.log('[消息模型调试] getRecentChats - 用户查询SQL:', usersSql);
        console.log('[消息模型调试] getRecentChats - 用户查询参数:', userIdArray);

        users = await db.query(usersSql, userIdArray);
        console.log('[消息模型调试] getRecentChats - 获取到用户数:', users ? users.length : 0);
      }

      // 创建用户信息映射
      const userMap = {};
      users.forEach(user => {
        userMap[user.id] = user;
      });

      // 为每条消息添加用户信息
      const chats = messages.map(msg => {
        const targetId = msg.senderId === userId ? msg.receiverId : msg.senderId;
        const targetUser = userMap[targetId] || { nickname: `用户${targetId.substring(0, 4)}`, avatar: '/images/icons2/男头像.png' };

        return {
          ...msg,
          targetNickname: targetUser.nickname,
          targetAvatar: targetUser.avatar
        };
      });

      console.log('[消息模型调试] getRecentChats - 查询结果:', chats ? `获取到${chats.length}条聊天` : '查询结果为空');
      if (chats && chats.length > 0) {
        console.log('[消息模型调试] getRecentChats - 第一条聊天:', JSON.stringify(chats[0]));
      }

      return chats;
    } catch (error) {
      console.error('获取最近聊天列表失败:', error);
      throw error;
    }
  }

  static async getMessagesByUser(userId, limit = 20, offset = 0) {
    try {
      const db = require('../config/db');
      
      // 查询用户的消息
      const sql = `
        SELECT m.*, 
               s.nickname as sender_name, s.avatar as sender_avatar,
               r.nickname as receiver_name, r.avatar as receiver_avatar
        FROM messages m
        LEFT JOIN users s ON m.senderId = s.user_id
        LEFT JOIN users r ON m.receiverId = r.user_id
        WHERE m.senderId = ? OR m.receiverId = ?
        ORDER BY m.createTime DESC
        LIMIT ? OFFSET ?
      `;
      
      const messages = await db.query(sql, [userId, userId, limit, offset]);
      
      return {
        success: true,
        data: messages
      };
    } catch (error) {
      console.error('获取用户消息失败:', error);
      return {
        success: false,
        message: '获取用户消息失败'
      };
    }
  }

  static async getConversation(userId1, userId2, limit = 50, offset = 0) {
    try {
      const db = require('../config/db');
      
      const sql = `
        SELECT m.*, 
               s.nickname as sender_name, s.avatar as sender_avatar,
               r.nickname as receiver_name, r.avatar as receiver_avatar
        FROM messages m
        LEFT JOIN users s ON m.senderId = s.user_id
        LEFT JOIN users r ON m.receiverId = r.user_id
        WHERE (m.senderId = ? AND m.receiverId = ?) 
           OR (m.senderId = ? AND m.receiverId = ?)
        ORDER BY m.createTime ASC
        LIMIT ? OFFSET ?
      `;
      
      const messages = await db.query(sql, [userId1, userId2, userId2, userId1, limit, offset]);
      
      return {
        success: true,
        data: messages
      };
    } catch (error) {
      console.error('获取对话记录失败:', error);
      return {
        success: false,
        message: '获取对话记录失败'
      };
    }
  }

  static async getUnreadCount(userId) {
    try {
      const db = require('../config/db');
      
      const sql = `
        SELECT COUNT(*) as count
        FROM messages 
        WHERE receiverId = ? AND isRead = 0
      `;
      
      const [result] = await db.query(sql, [userId]);
      
      return {
        success: true,
        data: result.count
      };
    } catch (error) {
      console.error('获取未读消息数量失败:', error);
      return {
        success: false,
        message: '获取未读消息数量失败'
      };
    }
  }

  static async markAsRead(messageId, userId) {
    try {
      const db = require('../config/db');
      
      const sql = `
        UPDATE messages 
        SET isRead = 1, readAt = ?
        WHERE id = ? AND receiverId = ?
      `;
      
      await db.query(sql, [Date.now(), messageId, userId]);
      
      return {
        success: true,
        message: '消息已标记为已读'
      };
    } catch (error) {
      console.error('标记消息为已读失败:', error);
      return {
        success: false,
        message: '标记消息为已读失败'
      };
    }
  }

  static async getUsersWithMessages(userId) {
    try {
      const db = require('../config/db');
      
      // 获取与当前用户有消息往来的所有用户ID
      const sql = `
        SELECT DISTINCT 
          CASE 
            WHEN senderId = ? THEN receiverId
            ELSE senderId
          END as other_user_id
        FROM messages 
        WHERE senderId = ? OR receiverId = ?
      `;
      
      const results = await db.query(sql, [userId, userId, userId]);
      const userIdArray = results.map(r => r.other_user_id);
      
      if (userIdArray.length === 0) {
        return {
          success: true,
          data: []
        };
      }
      
      // 获取用户信息
      const placeholders = userIdArray.map(() => '?').join(',');
      const usersSql = `SELECT id, nickname, avatar FROM users WHERE id IN (${placeholders})`;
      const users = await db.query(usersSql, userIdArray);
      
      return {
        success: true,
        data: users
      };
    } catch (error) {
      console.error('获取消息用户列表失败:', error);
      return {
        success: false,
        message: '获取消息用户列表失败'
      };
    }
  }
}

module.exports = Message;
