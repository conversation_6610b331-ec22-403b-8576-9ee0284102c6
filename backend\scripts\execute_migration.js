const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'molI2505$',
  database: 'morebuy'
};

async function executeMigration() {
  let connection;
  
  try {
    console.log('🔗 连接到数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 读取SQL迁移脚本
    const sqlPath = path.join(__dirname, 'users_table_migration.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf-8');
    
    // 分割SQL语句，只保留ALTER TABLE语句
    const sqlStatements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt && stmt.toUpperCase().startsWith('ALTER TABLE'));
    
    console.log(`📋 准备执行 ${sqlStatements.length} 条ALTER TABLE语句`);
    
    // 执行每个SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const sql = sqlStatements[i];
      if (!sql) continue;
      
      console.log(`\n🔄 执行第 ${i + 1} 条SQL语句:`);
      console.log(sql);
      
      try {
        await connection.execute(sql);
        console.log(`✅ 第 ${i + 1} 条SQL执行成功`);
      } catch (error) {
        console.error(`❌ 第 ${i + 1} 条SQL执行失败:`, error.message);
        throw error;
      }
    }
    
    // 验证迁移结果
    console.log('\n🔍 验证迁移结果...');
    
    // 检查表结构
    const [columns] = await connection.execute('DESCRIBE users');
    console.log('📊 users表结构:');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Key ? `(${col.Key})` : ''}`);
    });
    
    // 检查数据
    const [users] = await connection.execute('SELECT id, user_id, nickname, phone FROM users LIMIT 5');
    console.log('\n📋 用户数据示例:');
    users.forEach(user => {
      console.log(`  - ID: ${user.id}, UserID: ${user.user_id}, 昵称: ${user.nickname}`);
    });
    
    console.log('\n🎉 数据库迁移完成！');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行迁移
executeMigration()
  .then(() => {
    console.log('✅ 所有操作完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 操作失败:', error);
    process.exit(1);
  }); 