/**
 * 路由索引
 */
const express = require('express');
const productRoutes = require('./products');
const userRoutes = require('./users');

const cartRoutes = require('./cart');
const messageRoutes = require('./messages');
const messageRoutesSingular = require('./message');
const systemRoutes = require('./system');
const uploadRoutes = require('./upload');
const groupRoutes = require('./group');
const adminRoutes = require('./admin');
const companyRoutes = require('./company');
const shareRoutes = require('./share');
const pointsRoutes = require('./points');
const vipRoutes = require('./vip');
const quickMenusRoutes = require('./quickMenus');
const regionRoutes = require('./region');
const partnerRoutes = require('./partner');
const favoriteRoutes = require('./favorites');
const faqRoutes = require('./faq');
const storeInventoryRoutes = require('./storeInventory');
const storeController = require('../controllers/storeController');
const orderController = require('../controllers/orderController');
const storeFundController = require('../controllers/storeFundController');
const bannerController = require('../controllers/bannerController');
const { checkAuth } = require('../middleware/auth');

const router = express.Router();

router.use('/products', productRoutes);
router.use('/users', userRoutes);

router.use('/cart', cartRoutes);
router.use('/messages', messageRoutes);
router.use('/message', messageRoutesSingular);
router.use('/system', systemRoutes);
router.use('/upload', uploadRoutes);
router.use('/group', groupRoutes);
router.use('/admin', adminRoutes);
router.use('/company', companyRoutes);
router.use('/share', shareRoutes);
router.use('/points', pointsRoutes);
router.use('/vip', vipRoutes);
router.use('/quick-menus', quickMenusRoutes);
router.use('/region', regionRoutes);
router.use('/partner', partnerRoutes);
router.use('/favorites', favoriteRoutes);
router.use('/faq', faqRoutes);
router.use('/store/inventory', storeInventoryRoutes);

// 轮播图前端API路由（无需认证）
router.get('/banners', bannerController.getActiveBanners);

// 订单相关路由
router.post('/orders/create', checkAuth, (req, res) => {
  // 根据请求体判断是哪种类型的订单创建
  if (req.body.cartItemIds) {
    // 购物车订单创建
    return orderController.createCartOrder(req, res);
  } else {
    // 原有的订单创建（采购、移库等）
    return orderController.createOrder(req, res);
  }
});

// 订单支付路由
router.post('/orders/:id/pay', checkAuth, orderController.payOrder);

// 更新订单状态路由
router.put('/orders/:id/status', checkAuth, orderController.updateOrderStatus);

// 获取订单列表
router.get('/orders', checkAuth, orderController.getOrders);

// 获取订单详情
router.get('/orders/:id', checkAuth, orderController.getOrderDetail);

// 取消订单
router.post('/orders/:id/cancel', checkAuth, orderController.cancelOrder);

// 确认收货
router.post('/orders/:id/confirm', checkAuth, orderController.confirmReceipt);

// 删除订单
router.delete('/orders/:id', checkAuth, orderController.deleteOrder);

// 申请退款
router.post('/orders/:id/refund', checkAuth, orderController.applyRefund);

// 查看退款进度
router.get('/orders/:id/refund', checkAuth, orderController.getRefundDetail);

// 门店相关路由
router.get('/store/by-no', checkAuth, storeController.getStoreByNo);

// 门店资金相关路由
router.get('/store/funds/:storeNo', checkAuth, storeFundController.getStoreFunds);
router.post('/store/funds/record', checkAuth, storeFundController.createFundRecord);
router.get('/store/funds/records/:storeNo', checkAuth, storeFundController.getFundRecords);

module.exports = router;
