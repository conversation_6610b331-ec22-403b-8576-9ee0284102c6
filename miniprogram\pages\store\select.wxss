.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

/* 门店列表 */
.store-list {
  margin-bottom: 20rpx;
}

.store-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.store-item:active {
  transform: scale(0.98);
  background: #f8f8f8;
}

/* 门店形象照 */
.store-image {
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.store-image image {
  width: 100%;
  height: 100%;
}

/* 门店信息 */
.store-info {
  flex: 1;
  margin-right: 20rpx;
}

.store-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.store-no {
  font-size: 28rpx;
  color: #e74c3c;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: flex;
  align-items: flex-start;
  font-weight: 500;
}

.store-address {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: flex;
  align-items: flex-start;
}

.store-hours {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: flex;
  align-items: flex-start;
}

.store-phone {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  display: flex;
  align-items: flex-start;
}

.address-icon,
.time-icon,
.phone-icon,
.no-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

/* 营业状态 */
.store-status {
  display: flex;
  align-items: flex-start;
  flex-shrink: 0;
}

.status-icon {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-icon.open {
  background: #e8f5e8;
  color: #52c41a;
}

.status-icon.closed {
  background: #f5f5f5;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #ccc;
}