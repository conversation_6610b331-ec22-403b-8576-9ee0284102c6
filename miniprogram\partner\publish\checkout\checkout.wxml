<!--partner/publish/checkout/checkout.wxml-->
<view class="checkout-container">
  <!-- 门店信息 -->
  <view class="store-section">
    <view class="store-header">
      <view class="section-title">当前门店</view>
      <view class="store-info">
        <view class="store-name">{{selectedStore.name}}</view>
        <view class="store-no">{{selectedStore.store_no}}</view>
      </view>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="products-section">
    <view class="section-title">商品列表</view>
    <view class="products-list">
      <view class="product-item" wx:for="{{selectedItems}}" wx:key="id">
        <image class="product-image" src="{{item.image}}" mode="aspectFill" binderror="onImageError" data-index="{{index}}"></image>
        
        <view class="product-info">
          <view class="product-name">{{item.name}}</view>
          <view class="product-price">采购价: ¥{{item.purchasePrice}}</view>
          <view class="product-quantity">数量: {{item.quantity}}</view>
        </view>
        
        <view class="product-subtotal">
          <text class="subtotal-label">小计:</text>
          <text class="subtotal-price">¥{{item.subtotal}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 支付方式 -->
  <view class="payment-section">
    <view class="section-title">支付方式</view>
    
    <!-- 门店股本金支付 -->
    <view class="payment-item {{paymentMethods.equityPayment.selected ? 'selected' : ''}} {{!paymentMethods.equityPayment.available ? 'disabled' : ''}}">
      <view class="payment-info">
        <view class="payment-name">{{paymentMethods.equityPayment.name}}</view>
        <view class="payment-amount">可用: ¥{{paymentMethods.equityPayment.amount}}</view>
      </view>
      <view class="payment-checkbox">
        <image src="{{paymentMethods.equityPayment.selected ? '/images/icons2/勾选.png' : '/images/icons/checkbox.png'}}"></image>
      </view>
    </view>
    
    <!-- 门店公积金支付 -->
    <view class="payment-item {{paymentMethods.reservePayment.selected ? 'selected' : ''}} {{!paymentMethods.reservePayment.available ? 'disabled' : ''}}" bindtap="togglePaymentMethod" data-method="reservePayment">
      <view class="payment-info">
        <view class="payment-name">{{paymentMethods.reservePayment.name}}</view>
        <view class="payment-amount">可用: ¥{{paymentMethods.reservePayment.amount}}</view>
      </view>
      <view class="payment-switch">
        <view class="switch-container {{paymentMethods.reservePayment.selected ? 'switch-on' : 'switch-off'}}">
          <view class="switch-button"></view>
        </view>
      </view>
    </view>
    
    <!-- 红包（占位） -->
    <view class="payment-item disabled">
      <view class="payment-info">
        <view class="payment-name">{{paymentMethods.redPacket.name}}</view>
        <view class="payment-amount">暂未开放</view>
      </view>
      <view class="payment-checkbox">
        <image src="/images/icons/checkbox.png"></image>
      </view>
    </view>
    
    <!-- 优惠券（占位） -->
    <view class="payment-item disabled">
      <view class="payment-info">
        <view class="payment-name">{{paymentMethods.coupon.name}}</view>
        <view class="payment-amount">暂未开放</view>
      </view>
      <view class="payment-checkbox">
        <image src="/images/icons/checkbox.png"></image>
      </view>
    </view>
  </view>

  <!-- 订单提交栏 -->
  <view class="submit-bar">
    <view class="total-info">
      <view class="total-label">合计:</view>
      <view class="total-price">¥{{orderInfo.totalAmount}}</view>
    </view>
    
    <view class="submit-btn" bindtap="submitOrder">
      提交订单
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view> 