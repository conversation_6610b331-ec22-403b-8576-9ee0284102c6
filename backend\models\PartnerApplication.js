/**
 * 合伙人申请模型
 */
const db = require('../config/db');

class PartnerApplication {
  /**
   * 创建合伙人申请
   * @param {Object} applicationData 申请数据
   * @returns {Promise<Object>} 创建结果
   */
  static async create({ user_id, name, phone, province, city, district }) {
    const now = Date.now();
    const sql = `INSERT INTO partner_applications 
      (user_id, name, phone, province, city, district, status, created_at, updated_at) 
      VALUES (?, ?, ?, ?, ?, ?, 'pending', ?, ?)`;
    const params = [user_id, name, phone, province, city, district, now, now];
    
    try {
      const result = await db.query(sql, params);
      return { success: true, id: result.insertId };
    } catch (error) {
      console.error('创建合伙人申请失败:', error);
      throw new Error('创建合伙人申请失败: ' + error.message);
    }
  }

  /**
   * 获取用户的申请记录
   * @param {string} userId 用户ID
   * @returns {Promise<Array>} 申请记录列表
   */
  static async getByUserId(userId) {
    const sql = `SELECT * FROM partner_applications WHERE user_id = ? ORDER BY created_at DESC`;
    
    try {
      const applications = await db.query(sql, [userId]);
      return applications;
    } catch (error) {
      console.error('获取用户申请记录失败:', error);
      throw new Error('获取用户申请记录失败: ' + error.message);
    }
  }

  /**
   * 获取所有申请记录（支持分页和筛选）
   * @param {Object} options 查询选项
   * @returns {Promise<Object>} 申请记录和总数
   */
  static async getAll({ status = null, limit = 20, offset = 0 }) {
    let whereClause = '';
    const params = [];
    
    if (status && status !== '') {
      whereClause = 'WHERE pa.status = ?';
      params.push(status);
    }
    
    const countSql = `SELECT COUNT(*) as total FROM partner_applications pa ${whereClause}`;
    const dataSql = `
      SELECT 
        pa.*,
        u.nickname,
        u.avatar,
        u.phone as user_phone 
      FROM partner_applications pa
      LEFT JOIN users u ON pa.user_id = u.user_id
      ${whereClause}
      ORDER BY pa.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    try {
      console.log('SQL查询:', { countSql, dataSql, params: [...params, limit, offset] });
      
      const countResult = await db.query(countSql, params);
      const total = countResult[0].total;
      
      const dataParams = [...params, limit, offset];
      const applications = await db.query(dataSql, dataParams);
      
      console.log('查询结果:', { total, applicationsCount: applications.length });
      
      return { applications, total };
    } catch (error) {
      console.error('获取申请记录列表失败:', error);
      throw new Error('获取申请记录列表失败: ' + error.message);
    }
  }

  /**
   * 更新申请状态
   * @param {number} id 申请ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  static async updateStatus(id, { status, admin_id, admin_remark }) {
    const now = Date.now();
    const sql = `
      UPDATE partner_applications 
      SET status = ?, admin_id = ?, admin_remark = ?, updated_at = ? 
      WHERE id = ?
    `;
    const params = [status, admin_id, admin_remark || '', now, id];
    
    try {
      await db.query(sql, params);
      return { success: true };
    } catch (error) {
      console.error('更新申请状态失败:', error);
      throw new Error('更新申请状态失败: ' + error.message);
    }
  }

  /**
   * 获取申请详情
   * @param {number} id 申请ID
   * @returns {Promise<Object>} 申请详情
   */
  static async getById(id) {
    const sql = `
      SELECT 
        pa.*,
        u.nickname,
        u.avatar,
        u.phone as user_phone 
      FROM partner_applications pa
      LEFT JOIN users u ON pa.user_id = u.user_id
      WHERE pa.id = ?
    `;
    
    try {
      const applications = await db.query(sql, [id]);
      return applications.length > 0 ? applications[0] : null;
    } catch (error) {
      console.error('获取申请详情失败:', error);
      throw new Error('获取申请详情失败: ' + error.message);
    }
  }
}

module.exports = PartnerApplication;