/**
 * 合伙人相关路由
 */
const express = require('express');
const router = express.Router();
const partnerController = require('../controllers/partnerController');
const partnerStatsController = require('../controllers/partnerStatsController');
const partnerApplicationController = require('../controllers/partnerApplicationController');
const partnerOrderController = require('../controllers/partnerOrderController');
const storeController = require('../controllers/storeController');
const { checkAuth } = require('../middleware/auth');

// 原有合伙人功能
router.post('/join', partnerController.joinPartner);
router.get('/list', partnerController.getPartnersByStoreId);
router.get('/store-partners', checkAuth, partnerController.getStorePartners);

// 合伙人统计相关路由
router.get('/stats', checkAuth, partnerStatsController.getPartnerStats);
router.get('/fund-records', checkAuth, partnerStatsController.getFundRecords);
router.get('/order-stats', checkAuth, partnerStatsController.getOrderStats);
// 旧的获取合伙人门店接口（保留以兼容旧版本）
router.get('/stores', checkAuth, partnerStatsController.getPartnerStores);
// 新的合伙人端专用门店接口
router.get('/joined-stores', checkAuth, storeController.getPartnerJoinedStores);

// 合伙人申请相关路由
router.post('/apply', checkAuth, partnerApplicationController.applyPartner);
router.get('/applications/my', checkAuth, partnerApplicationController.getMyApplications);
router.get('/applications', checkAuth, partnerApplicationController.getAllApplications);
router.get('/applications/:id', checkAuth, partnerApplicationController.getApplicationDetail);
router.put('/applications/:id/review', checkAuth, partnerApplicationController.reviewApplication);

// 合伙人端顾客订单相关路由
router.get('/customer-orders', checkAuth, partnerOrderController.getCustomerOrders);

// 合伙人端门店订单相关路由
router.get('/store-orders', checkAuth, partnerOrderController.getStoreOrders);

module.exports = router;