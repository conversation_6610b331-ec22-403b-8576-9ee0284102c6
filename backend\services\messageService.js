/**
 * 消息服务
 */
const Message = require('../models/Message');
const User = require('../models/User');
const db = require('../utils/db');

class MessageService {
  async sendMessage(senderId, receiverId, content, type = 'text') {
    try {
      // 检查接收者是否存在
      let receiver = await User.findById(receiverId);

      // 如果接收者不存在，但ID格式正确，则创建一个默认用户
      if (!receiver && receiverId && receiverId.length > 5) {
        console.log('接收者不存在，创建默认用户:', receiverId);
        try {
          // 创建默认用户
          const defaultUserData = {
            id: receiverId,
            username: `user_${receiverId.substring(0, 5)}`,
            nickname: `用户${receiverId.substring(0, 4)}`,
            avatar: '/images/icons2/男头像.png',
            createTime: Date.now(),
            updateTime: Date.now()
          };

          receiver = await User.create(defaultUserData);
          console.log('创建默认用户成功:', receiver);
        } catch (createError) {
          console.error('创建默认用户失败:', createError);
          // 即使创建用户失败，也继续发送消息
        }
      }

      if (!receiver) {
        console.warn('接收者不存在且无法创建默认用户');
      }

      // 创建消息
      const message = await Message.create({
        senderId,
        receiverId,
        content,
        type
      });

      return {
        success: true,
        data: message,
        message: '发送成功'
      };
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }

  async getMessages(id, targetUserId = null, page = 1, pageSize = 20) {
    try {
      console.log('[消息服务调试] id:', id, '类型:', typeof id, 'targetUserId:', targetUserId, 'page:', page, 'pageSize:', pageSize);

      // 确保id有值
      if (!id) {
        console.error('[消息服务调试] 用户ID为空');
        return {
          success: false,
          data: [],
          message: '获取消息失败: 用户ID为空'
        };
      }

      // 保留原始ID格式，不强制转换为数字
      const userId = id;
      console.log('[消息服务调试] 使用的userId:', userId, '类型:', typeof userId);

      let result;
      if (targetUserId) {
        // 获取与特定用户的对话
        console.log('[消息服务调试] 获取与特定用户的对话');
        result = await Message.getMessages(userId, targetUserId, page, pageSize);
      } else {
        // 获取所有消息
        console.log('[消息服务调试] 获取所有消息');
        result = await Message.findAll({ userId: userId, page, pageSize });
      }

      console.log('[消息服务调试] 查询结果:', JSON.stringify(result));

      // 检查结果是否有list属性
      if (!result || !result.list) {
        console.error('[消息服务调试] 查询结果异常:', result);
        return {
          success: false,
          data: [],
          message: '获取消息失败: 查询结果异常'
        };
      }

      // 检查消息列表是否为空
      if (result.list.length === 0) {
        console.log('[消息服务调试] 消息列表为空');
        return {
          success: true,
          data: [],
          message: '暂无消息'
        };
      }

      console.log('[消息服务调试] 返回消息数量:', result.list.length);
      return {
        success: true,
        data: result.list,
        message: '获取消息成功'
      };

    } catch (error) {
      console.error('[消息服务调试] 获取消息失败:', error);
      return {
        success: false,
        data: [],
        message: '获取消息失败: ' + error.message
      };
    }
  }

  // 管理员查询消息（支持自定义SQL查询）
  async queryMessages(query, params = []) {
    try {
      console.log('[消息服务] 执行自定义查询:', query);
      console.log('[消息服务] 查询参数:', params);

      const result = await db.query(query, params);
      console.log('[消息服务] 查询结果数量:', result.length);

      return result;
    } catch (error) {
      console.error('[消息服务] 自定义查询失败:', error);
      throw error;
    }
  }

  // 切换消息屏蔽状态
  async toggleMessageBlock(id, isBlocked) {
    try {
      console.log('[消息服务] 切换消息屏蔽状态:', id, isBlocked);

      // 检查消息是否存在
      const checkQuery = 'SELECT id FROM messages WHERE id = ?';
      const existingMessage = await db.query(checkQuery, [id]);

      if (existingMessage.length === 0) {
        return {
          success: false,
          message: '消息不存在'
        };
      }

      const currentTime = Date.now();
      const query = 'UPDATE messages SET is_blocked = ?, updateTime = ? WHERE id = ?';
      
      await db.query(query, [isBlocked ? 1 : 0, currentTime, id]);

      console.log('[消息服务] 消息屏蔽状态更新成功');

      return {
        success: true,
        message: isBlocked ? '消息已屏蔽' : '消息已取消屏蔽'
      };

    } catch (error) {
      console.error('[消息服务] 切换消息屏蔽状态失败:', error);
      return {
        success: false,
        message: '操作失败: ' + error.message
      };
    }
  }

  // 删除消息
  async deleteMessage(id) {
    try {
      console.log('[消息服务] 删除消息:', id);

      // 检查消息是否存在
      const checkQuery = 'SELECT id FROM messages WHERE id = ?';
      const existingMessage = await db.query(checkQuery, [id]);

      if (existingMessage.length === 0) {
        return {
          success: false,
          message: '消息不存在'
        };
      }

      const query = 'DELETE FROM messages WHERE id = ?';
      await db.query(query, [id]);

      console.log('[消息服务] 消息删除成功');

      return {
        success: true,
        message: '消息删除成功'
      };

    } catch (error) {
      console.error('[消息服务] 删除消息失败:', error);
      return {
        success: false,
        message: '删除失败: ' + error.message
      };
    }
  }

  // 获取消息统计信息
  async getMessageStats() {
    try {
      console.log('[消息服务] 获取消息统计信息');

      const statsQuery = `
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN is_blocked = 1 THEN 1 ELSE 0 END) as blocked,
          SUM(CASE WHEN is_blocked = 0 THEN 1 ELSE 0 END) as active,
          SUM(CASE WHEN type = 'text' THEN 1 ELSE 0 END) as textMessages,
          SUM(CASE WHEN type = 'image' THEN 1 ELSE 0 END) as imageMessages,
          SUM(CASE WHEN type = 'group' THEN 1 ELSE 0 END) as groupMessages
        FROM messages
      `;

      const stats = await db.query(statsQuery);
      const result = stats[0] || {
        total: 0,
        blocked: 0,
        active: 0,
        textMessages: 0,
        imageMessages: 0,
        groupMessages: 0
      };

      console.log('[消息服务] 统计结果:', result);

      return result;

    } catch (error) {
      console.error('[消息服务] 获取消息统计失败:', error);
      throw error;
    }
  }

  async getMessageById(id) {
    try {
      const message = await Message.findById(id);
      return {
        success: true,
        data: message
      };
    } catch (error) {
      return {
        success: false,
        message: '消息不存在'
      };
    }
  }

  async createMessage(data) {
    try {
      const message = await Message.create(data);
      return {
        success: true,
        data: message
      };
    } catch (error) {
      return {
        success: false,
        message: '创建消息失败'
      };
    }
  }

  async markAsRead(id) {
    try {
      const result = await Message.markAsRead(id);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        message: '标记已读失败'
      };
    }
  }

  async markAllAsRead(userId, type) {
    try {
      const result = await Message.markAllAsRead(userId, type);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        message: '标记全部已读失败'
      };
    }
  }

  async getUnreadCount(userId) {
    try {
      const count = await Message.getUnreadCount(userId);
      return {
        success: true,
        data: { count }
      };
    } catch (error) {
      return {
        success: false,
        message: '获取未读消息数失败'
      };
    }
  }

  async getRecentChats(userId) {
    try {
      const chats = await Message.getRecentChats(userId);
      return {
        success: true,
        data: chats
      };
    } catch (error) {
      return {
        success: false,
        message: '获取最近聊天列表失败'
      };
    }
  }
}

module.exports = new MessageService();
