/* pages/home/<USER>/
/* 新版首页 - 电商风格样式 */

.home-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏区域 */
.search-section {
  position: fixed;
  top: 2px;
  left: 0;
  right: 0;
  background-color: #f8f8f8;
  border-radius: 18px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  padding: 2px;
  margin: 2px 16px 6px;
  z-index: 999;
}

.search-input-wrapper {
  background-color: transparent;
  border-radius: 14px;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  height: 28px;
  width: 100%;
  position: relative;
}

.search-icon {
  width: 22px;
  height: 22px;
  position: absolute;
  right: 28px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0.7;
  cursor: pointer;
  z-index: 1;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 14px;
  color: #333;
  padding-right: 35px;
  min-width: 0;
}

.search-input-placeholder {
  color: #999;
  font-size: 14px;
}



/* 导航栏占位 */
.nav-placeholder {
  width: 100%;
}

/* 为固定搜索栏添加占位 */
.search-placeholder {
  height: 25px; /* 调整高度，与轮播图上边距30px匹配 */
  width: 100%;
}

/* ==================== 轮播图区域 ==================== */
.banner-section {
  margin: 30px 16px 8px 16px; /* 调整上边距为30px */
}

.banner-swiper {
  height: 140px; /* 减小高度 */
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 20px 16px 16px;
  color: #FFFFFF;
}

.banner-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.banner-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

/* ==================== 快捷菜单 ==================== */
.quick-menu-section {
  background-color: #FFFFFF;
  margin: 0 16px 8px; /* 减小下边距 */
  border-radius: 12px;
  padding: 12px 16px 4px 16px; /* 减小上下内边距 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.quick-menu-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.quick-menu-item {
  width: 22%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px; /* 进一步减小底部间距 */
  transition: transform 0.2s ease;
}

.quick-menu-item:active {
  transform: scale(0.95);
}

/* 快捷菜单按钮悬停和点击效果 */
.quick-menu-icon-wrapper:active {
  /* 点击时增强立体效果 */
  box-shadow: 
    0 1px 4px rgba(0, 0, 0, 0.15), /* 减小外阴影 */
    inset 0 2px 4px rgba(0, 0, 0, 0.1), /* 增强内阴影 */
    inset 0 1px 0 rgba(255, 255, 255, 0.6); /* 减弱内部高光 */
  transform: translateY(1px); /* 轻微下沉效果 */
}

.quick-menu-icon-wrapper {
  width: 48px;
  height: 48px;
  background-color: #FFFFFF; /* 修改背景色为白色 */
  border: 1px solid #E0E0E0; /* 添加灰色边框 */
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px; /* 减小底部间距 */
  /* 立体效果：外阴影和内阴影结合 */
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.1), /* 外阴影 */
    inset 0 1px 0 rgba(255, 255, 255, 0.8), /* 内部高光 */
    inset 0 -1px 0 rgba(0, 0, 0, 0.05); /* 内部阴影 */
  transition: all 0.2s ease; /* 添加过渡动画 */
}

.quick-menu-icon {
  width: 28px;
  height: 28px;
}

.quick-menu-name {
  font-size: 12px;
  color: #333;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

/* ==================== 推荐商品区域 ==================== */
.recommend-section {
  background-color: #FFFFFF;
  margin: 0 16px 8px; /* 减小下边距 */
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}



.recommend-tabs-scroll {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 6px; /* 减小底部间距 */
  padding-top: 16px; /* 增加顶部内边距，补偿移除标题后的间距 */
}
.recommend-tabs {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 16px;
}
.recommend-tab {
  min-width: 56px;
  padding: 4px 10px;
  font-size: 13px;
  color: #666;
  border-radius: 16px;
  background: transparent;
  border: 1px solid #e0e0e0;
  margin-right: 8px;
  text-align: center;
  transition: all 0.2s ease;
}
.recommend-tab.active {
  background: transparent;
  color: #FF6B35;
  border: 1px solid #FF6B35;
}

/* 热门商品横向滚动 */
.hot-products-section {
  padding-bottom: 16px;
}

.hot-products-scroll {
  white-space: nowrap;
}

.hot-products-list {
  display: inline-flex;
  padding: 0 16px;
}

.hot-product-item {
  width: 120px;
  margin-right: 12px;
  flex-shrink: 0;
}

.hot-product-image-wrapper {
  position: relative;
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 8px;
}

.hot-product-image {
  width: 100%;
  height: 100%;
}

.hot-badge {
  position: absolute;
  top: 6px;
  left: 6px;
  background-color: #FF4444;
  color: #FFFFFF;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
}

.hot-product-info {
  text-align: center;
}

.hot-product-name {
  font-size: 12px;
  color: #333;
  display: block;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hot-product-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.price-symbol {
  font-size: 10px;
  color: #FF6B35;
}

.price-value {
  font-size: 14px;
  font-weight: bold;
  color: #FF6B35;
}

.new-product-original-price {
  font-size: 12px;
  color: #bbb;
  text-decoration: line-through;
  text-align: left;
  margin-bottom: 0px;
  padding-left: 4px;
  line-height: 1;
}

/* 新品推荐样式（与热门商品类似） */
.new-products-section {
  background: #fff;
  margin: 0 16px 8px; /* 减小下边距 */
  border-radius: 12px;
  padding: 10px 0 6px 0; /* 减小上下内边距 */
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden; /* 确保内容不会超出容器 */
}
/* 新品上市标题栏 */
.new-products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  margin-bottom: 6px; /* 减小底部间距 */
}

.new-products-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

/* 更多按钮样式 - 移除背景颜色，增大字号与标题一致 */
.new-products-more {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  background-color: transparent; /* 移除背景颜色 */
  transition: opacity 0.2s ease; /* 改为透明度过渡效果 */
}

.new-products-more:active {
  opacity: 0.7; /* 点击时降低透明度 */
}

.more-text {
  font-size: 16px; /* 增大字号，与新品上市标题一致 */
  color: #666;
  margin-right: 2px;
}

.more-icon {
  font-size: 18px; /* 相应增大箭头图标字号 */
  color: #666;
  font-weight: bold;
}
.new-products-scroll {
  white-space: nowrap;
  overflow-x: auto;
  padding-left: 16px;
  padding-right: 16px; /* 添加右侧内边距，防止最后一个卡片超出容器 */
  width: 100%; /* 确保滚动容器占满父容器宽度 */
  box-sizing: border-box; /* 确保内边距计算正确 */
  -webkit-overflow-scrolling: touch; /* 在iOS上提供更流畅的滚动体验 */
}
.new-products-list {
  display: flex;
  align-items: flex-start;
  padding-right: 0; /* 确保右侧没有额外的内边距 */
}
.new-product-card {
  width: 110px;
  margin-right: 6px; /* 减小右边距 */
  background: #f8f8f8;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  display: inline-block;
  vertical-align: top;
  padding-bottom: 6px; /* 减小底部内边距 */
  flex-shrink: 0; /* 防止卡片被压缩 */
}
.new-product-image {
  width: 100%;
  height: 80px; /* 减小高度 */
  border-radius: 10px 10px 0 0;
  object-fit: cover;
}
.new-product-name {
  font-size: 13px;
  color: #333;
  margin: 4px 8px 2px 8px; /* 减小上边距 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}
.new-product-price {
  font-size: 16px;
  color: #FF6B35;
  font-weight: bold;
  text-align: left;
  padding-left: 4px;
  letter-spacing: 0.8px;
  line-height: 1;
}

/* ==================== 商品列表区域 ==================== */
.products-section {
  background-color: #FFFFFF;
  margin: 0 16px 8px; /* 减小下边距 */
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 12px; /* 减小上下内边距 */
  border-bottom: 1px solid #f0f0f0;
}

.products-title {
  display: flex;
  flex-direction: column;
}

.view-more-btn {
  display: flex;
  align-items: center;
  color: #FF6B35;
  font-size: 14px;
}

.arrow-icon {
  width: 12px;
  height: 12px;
  margin-left: 4px;
}

.products-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 8px; /* 进一步减小内边距 */
}

.product-item {
  width: calc(50% - 3px); /* 进一步减小间距 */
  background-color: #FFFFFF;
  border-radius: 8px;
  margin-bottom: 6px; /* 进一步减小底部间距 */
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.product-item:nth-child(odd) {
  margin-right: 6px; /* 进一步减小右边距 */
}

.product-image-wrapper {
  position: relative;
  width: 100%;
  height: 120px; /* 减小高度 */
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-tags {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  flex-direction: column;
}

.product-tag {
  background-color: rgba(255, 107, 53, 0.9);
  color: #FFFFFF;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-bottom: 4px;
}

.product-info {
  padding: 10px; /* 减小内边距 */
}

.product-name {
  font-size: 16px; /* 与分类页保持一致，从13px调整为16px */
  color: #333;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: 3px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-spec {
  font-size: 11px;
  color: #999;
  line-height: 1.3;
  margin-bottom: 4px; /* 进一步减小底部间距 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px; /* 进一步减小底部间距 */
}

.product-price {
  display: flex;
  align-items: baseline;
}

.price-unit {
  font-size: 10px;
  color: #999;
  margin-left: 2px;
}

.product-original-price {
  color: #bbb;
  font-size: 14px;
  text-decoration: line-through;
  margin-bottom: 0px;
  line-height: 1;
}

.price-symbol {
  font-size: 13px;
  color: #ff4757;
  font-weight: 500;
  letter-spacing: 0.5px;
  line-height: 1;
}

.price-value {
  font-size: 15px;
  color: #ff4757;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: 1;
}

.product-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* 右对齐按钮组 */
  gap: 12px; /* 增加50%：从8px调整到12px */
  flex-shrink: 0; /* 防止按钮被压缩 */
  margin-right: -10px; /* 与商品卡片右边缘对齐，抵消product-info的右内边距 */
}

.action-btn {
  width: 26px; /* 减小15%：从30px调整到26px */
  height: 26px; /* 减小15%：从30px调整到26px */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: transparent;
  border: none;
  cursor: pointer;
}

.favorite-btn {
  background: transparent;
  border: none;
}

.favorite-btn:active {
  transform: scale(0.9);
}

.cart-btn {
  background: transparent;
  border: none;
}

.cart-btn:active {
  transform: scale(0.9);
}

.action-icon {
  width: 21px; /* 减小15%：从25px调整到21px */
  height: 21px; /* 减小15%：从25px调整到21px */
}

.favorite-btn .action-icon {
  /* 收藏图标使用红色调 */
  filter: hue-rotate(-10deg) saturate(1.5) brightness(1.1);
  color: #FF3333;
}

.favorite-btn:active .action-icon {
  opacity: 0.8;
}

.cart-btn .action-icon {
  /* 购物车图标使用红色调 */
  filter: hue-rotate(10deg) saturate(1.3) brightness(1.2);
  color: #FF0000;
}

.cart-btn:active .action-icon {
  opacity: 0.8;
}



/* ==================== 加载和空状态 ==================== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px; /* 减小上下内边距 */
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #FF6B35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999;
}

.no-more-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px; /* 减小内边距 */
}

.no-more-line {
  flex: 1;
  height: 1px;
  background-color: #e0e0e0;
}

.no-more-text {
  font-size: 12px;
  color: #999;
  margin: 0 16px;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px; /* 减小上下内边距 */
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

.retry-btn {
  background-color: #FF6B35;
  color: #FFFFFF;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
}

.bottom-spacing {
  height: 16px; /* 减小底部间距 */
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 375px) {
  .category-item {
    width: 23%;
  }
  
  .product-item {
    width: calc(50% - 4px);
  }
  
  .product-item:nth-child(odd) {
    margin-right: 8px;
  }
}

.back-to-top {
  position: fixed;
  right: 24px;
  bottom: 100px;
  width: 40px; /* 减小按钮容器尺寸，从48px改为40px */
  height: 40px; /* 减小按钮容器尺寸，从48px改为40px */
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.2s;
}
.back-to-top-icon {
  width: 28px;
  height: 28px;
}

/* ==================== 加载动画样式 ==================== */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #FF6B35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 收藏按钮特殊样式 ==================== */
.favorite-btn {
  position: relative;
}

.favorite-btn .action-icon {
  transition: all 0.3s ease;
}

.favorite-btn:active .action-icon {
  transform: scale(0.9);
}

/* 已收藏状态的按钮样式 */
.favorite-btn.favorited .action-icon {
  filter: drop-shadow(0 2px 4px rgba(255, 107, 53, 0.3));
}
