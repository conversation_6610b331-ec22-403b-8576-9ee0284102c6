/**
 * 门店控制器
 */
const db = require('../config/db');

// 获取用户订阅门店
exports.getSubscribedStore = async (req, res) => {
  try {
    const userId = req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    // 查询用户的订阅门店（通过用户的subscribe_store_no字段查找）
    const result = await db.query(`
      SELECT s.* 
      FROM stores s
      INNER JOIN users u ON s.store_no = u.subscribe_store_no
      WHERE u.id = ?
      LIMIT 1
    `, [userId]);

    if (result.length === 0) {
      return res.json({ 
        success: true, 
        data: null, 
        message: '暂无订阅门店' 
      });
    }

    res.json({
      success: true,
      data: result[0],
      message: '获取订阅门店成功'
    });

  } catch (error) {
    console.error('获取订阅门店失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '获取订阅门店失败', 
      error: error.message 
    });
  }
};

// 获取销售人名下的门店列表
exports.getSalesmanStores = async (req, res) => {
  try {
    const userId = req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    // 先获取用户的销售人ID
    const userResult = await db.query(
      'SELECT salesman_id FROM users WHERE user_id = ?',
      [userId]
    );

    if (userResult.length === 0) {
      return res.status(404).json({ 
        success: false, 
        message: '用户不存在' 
      });
    }

    const salesmanId = userResult[0].salesman_id;
    
    // 如果用户没有关联销售人，返回空列表
    if (!salesmanId) {
      return res.json({ 
        success: true, 
        data: [], 
        message: '用户未关联销售人，暂无可用门店' 
      });
    }

    // 查询销售人名下的所有门店
    // 销售人名下的门店 = 销售人作为合伙人参与的所有门店
    // 通过partners表查询销售人作为合伙人参与的所有门店
    const result = await db.query(`
      SELECT DISTINCT s.* 
      FROM stores s
      INNER JOIN partners p ON s.store_no = p.store_no
      WHERE p.user_id = ?
      ORDER BY s.create_time DESC
    `, [salesmanId || userId]);

    res.json({
      success: true,
      data: result,
      message: '获取门店列表成功'
    });

  } catch (error) {
    console.error('获取销售人门店失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '获取门店列表失败', 
      error: error.message 
    });
  }
};

// 创建门店
exports.createStore = async (req, res) => {
  try {
    const { name, level, level_title, province, city, district, address, image, contact_person, phone, capital } = req.body;
    
    // 验证必填字段
    if (!name || !level || !province || !city || !district) {
      return res.status(400).json({ success: false, message: '缺少必要参数' });
    }

    // 使用Store模型创建门店，自动生成正确的门店编号
    const Store = require('../models/Store');
    const result = await Store.create({
      name,
      level,
      level_title,
      province,
      city,
      district,
      contact_person: contact_person || null,
      phone: phone || null,
      image: image || null
    });

    // 如果有股本金，需要额外更新
    if (capital && capital > 0) {
      await Store.addCapital(result.id, capital);
    }

    res.json({
      success: true,
      data: { id: result.id, store_no: result.store_no },
      message: '创建门店成功'
    });
  } catch (error) {
    console.error('创建门店失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '创建门店失败', 
      error: error.message 
    });
  }
};

// 更新门店信息
exports.updateStore = async (req, res) => {
  try {
    const storeId = req.params.id;
    const updateData = req.body;
    
    console.log('更新门店请求，ID:', storeId, '数据:', updateData);
    
    // 只验证门店名称为必填字段
    if (!updateData.name || !updateData.name.trim()) {
      return res.status(400).json({ success: false, message: '门店名称不能为空' });
    }

    // 先查询当前门店信息
    const currentStore = await db.query('SELECT * FROM stores WHERE id = ?', [storeId]);
    if (currentStore.length === 0) {
      return res.status(404).json({ 
        success: false, 
        message: '门店不存在' 
      });
    }

    const current = currentStore[0];
    
    // 构建更新字段和值
    const updateFields = [];
    const updateValues = [];
    
    // 门店名称（必填）
    updateFields.push('name = ?');
    updateValues.push(updateData.name.trim());
    
    // 门店级别（picker选择器，有默认值，直接更新）
    updateFields.push('level = ?');
    updateValues.push(updateData.level);
    
    // 门店级别标题（picker选择器，有默认值，直接更新）
    updateFields.push('level_title = ?');
    updateValues.push(updateData.level_title);
    
    // 联系人（可选）
    if (updateData.contact_person !== undefined && updateData.contact_person !== null) {
      updateFields.push('contact_person = ?');
      updateValues.push(updateData.contact_person.trim() || null);
    }
    
    // 联系电话（可选）
    if (updateData.phone !== undefined && updateData.phone !== null) {
      updateFields.push('phone = ?');
      updateValues.push(updateData.phone.trim() || null);
    }
    
    // 详细地址（可选）
    if (updateData.address !== undefined && updateData.address !== null) {
      updateFields.push('address = ?');
      updateValues.push(updateData.address.trim() || null);
    }
    
    // 门店图片（可选）
    if (updateData.image !== undefined && updateData.image !== null) {
      updateFields.push('image = ?');
      updateValues.push(updateData.image.trim() || null);
    }
    
    // 添加更新时间
    updateFields.push('update_time = ?');
    updateValues.push(Date.now());
    
    // 添加门店ID
    updateValues.push(storeId);
    
    // 构建SQL语句
    const sql = `UPDATE stores SET ${updateFields.join(', ')} WHERE id = ?`;
    
    console.log('更新门店SQL:', sql);
    console.log('更新参数:', updateValues);
    
    const result = await db.query(sql, updateValues);

    if (result.affectedRows === 0) {
      return res.status(404).json({ 
        success: false, 
        message: '门店不存在或更新失败' 
      });
    }

    res.json({
      success: true,
      data: { id: storeId },
      message: '更新门店成功'
    });
  } catch (error) {
    console.error('更新门店失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '更新门店失败', 
      error: error.message 
    });
  }
};

// 获取当前用户作为合伙人加入的门店列表（合伙人端专用接口）
exports.getPartnerJoinedStores = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    console.log('获取合伙人加入的门店列表 - 用户ID:', userId);

    // 查询当前用户作为合伙人加入的所有门店
    const stores = await db.query(`
      SELECT DISTINCT 
        s.id,
        s.store_no,
        s.name,
        s.level,
        s.level_title,
        s.province,
        s.city,
        s.district,
        s.address,
        s.image,
        s.contact_person,
        s.phone as contact_phone,
        s.create_time,
        p.type as partner_type,
        p.amount as investment_amount,
        p.percent as share_percent,
        p.created_at
      FROM partners p
      JOIN stores s ON p.store_no = s.store_no
      WHERE p.user_id = ?
      ORDER BY p.created_at ASC
    `, [userId]);

    console.log('查询到的合伙人门店数量:', stores.length);
    if (stores.length > 0) {
      console.log('第一条合伙人门店记录:', stores[0]);
    }

    res.json({
      success: true,
      data: stores,
      message: '获取合伙人门店列表成功'
    });
  } catch (error) {
    console.error('获取合伙人门店列表失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '获取合伙人门店列表失败',
      error: error.message
    });
  }
};

// 根据门店编号获取门店信息
exports.getStoreByNo = async (req, res) => {
  try {
    const { store_no } = req.query;
    
    if (!store_no) {
      return res.status(400).json({ success: false, message: '缺少门店编号参数' });
    }

    console.log('根据门店编号获取门店信息:', store_no);

    const store = await db.query('SELECT * FROM stores WHERE store_no = ?', [store_no]);
    
    if (store.length === 0) {
      return res.status(404).json({ success: false, message: '门店不存在' });
    }

    res.json({
      success: true,
      data: store[0]
    });
  } catch (error) {
    console.error('根据门店编号获取门店信息失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '获取门店信息失败',
      error: error.message
    });
  }
};