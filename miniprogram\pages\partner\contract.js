// pages/partner/contract.js
Page({
  data: {
    contractTitle: '合伙人协议',
    contractContent: [
      {
        title: '第一条 合作方式',
        content: '甲方与乙方建立合伙人合作关系，乙方在甲方平台开展经营活动。'
      },
      {
        title: '第二条 权益保障',
        content: '乙方享有现金推荐奖、席位赠送奖、销售高分佣等九项核心权益。'
      },
      {
        title: '第三条 收益分配',
        content: '按照平台规则进行收益分配，具体比例以系统显示为准。'
      },
      {
        title: '第四条 退出机制',
        content: '乙方可随时申请退出，零风险保障，无任何违约金。'
      },
      {
        title: '第五条 争议解决',
        content: '双方发生争议时，应友好协商解决，协商不成的可向平台客服申请调解。'
      }
    ]
  },

  onLoad: function (options) {
    // 页面加载时的逻辑
  },

  onShow: function () {
    // 页面显示时的逻辑
  },

  // 联系客服
  contactService: function() {
    wx.navigateTo({
      url: '/pages/service/index'
    });
  }
}); 