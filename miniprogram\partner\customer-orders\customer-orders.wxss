/* 合伙人端顾客订单页面样式 */
.customer-orders-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  margin: 0;
  background-color: #fff;
  gap: 20rpx;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 25rpx;
  padding: 15rpx 20rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  background-color: #ff4757;
  color: #fff;
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 筛选器 */
.filter-container {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  gap: 20rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.filter-picker {
  flex: 1;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f8f8;
  padding: 15rpx 20rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #333;
}

.picker-arrow,
.calendar-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 状态标签栏 */
.status-tabs {
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-container {
  display: flex;
  padding: 0 30rpx;
}

.tab-item {
  position: relative;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #22a2c3;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #22a2c3;
  border-radius: 2rpx;
}

/* 订单列表容器 */
.order-list-container {
  padding: 20rpx 30rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 订单列表 */
.order-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 我的销售订单特殊样式 */
.order-item.my-sales-order {
  border-left: 6rpx solid #22a2c3;
  background-color: #fafeff;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.store-info {
  flex: 1;
}

.store-name {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.order-no {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.order-status {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff4757;
}

/* 订单日期和销售人信息 */
.order-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-date {
  font-size: 26rpx;
  color: #999;
}

.salesman-info {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.salesman-info.my-sales {
  color: #22a2c3;
}

.my-sales-tag {
  background-color: #e6f7ff;
  color: #22a2c3;
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  font-size: 22rpx;
}

/* 订单状态和配送方式 */
.order-status-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.delivery-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.delivery-tag.express {
  background-color: #e6f7ff;
  color: #22a2c3;
}

.delivery-tag.pickup {
  background-color: #f6ffed;
  color: #52c41a;
}

.customer-name {
  font-size: 26rpx;
  color: #666;
}

/* 子订单信息 */
.sub-orders-container {
  margin-bottom: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 15rpx;
}

.sub-orders-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.sub-orders-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.sub-order-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 10rpx 0;
  border-bottom: 1rpx dashed #eee;
}

.sub-order-item:last-child {
  border-bottom: none;
}

.sub-order-store {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.sub-order-no {
  font-size: 24rpx;
  color: #666;
}

.sub-order-status {
  font-size: 24rpx;
  color: #22a2c3;
  padding: 4rpx 10rpx;
  background-color: #e6f7ff;
  border-radius: 8rpx;
}

/* 商品列表 */
.product-list {
  margin-bottom: 20rpx;
}

.product-item {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background-color: #f8f8f8;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.product-specs {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.product-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.product-price {
  font-size: 28rpx;
  color: #ff4757;
  font-weight: 600;
}

.product-qty {
  font-size: 24rpx;
  color: #666;
}

.product-total {
  font-size: 24rpx;
  color: #999;
}

/* 订单金额 */
.order-total {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.total-row,
.discount-row,
.actual-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.total-row:last-child,
.discount-row:last-child,
.actual-row:last-child {
  margin-bottom: 0;
}

.total-label,
.discount-label,
.actual-label {
  font-size: 26rpx;
  color: #666;
}

.total-amount,
.discount-amount,
.actual-amount {
  font-size: 26rpx;
  font-weight: 600;
}

.total-amount {
  color: #333;
}

.discount-amount {
  color: #ff4757;
}

.actual-amount {
  color: #ff4757;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.no-more text {
  font-size: 26rpx;
  color: #999;
} 