/* pages/faq/faq.wxss */
.faq-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0;
}

.faq-header {
  background: linear-gradient(135deg, #FF6B35 0%, #FF8C42 100%);
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  color: #fff;
}

.faq-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.faq-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.4;
}

.faq-list {
  padding: 30rpx 20rpx;
}

.faq-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.faq-item:active {
  transform: scale(0.98);
}

.faq-question {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
}

.faq-arrow {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s ease;
}

.faq-arrow.active {
  transform: rotate(180deg);
  color: #FF6B35;
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  background: #fafafa;
}

.faq-answer.active {
  max-height: 300rpx;
  padding: 30rpx;
}

.faq-answer text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.faq-footer {
  padding: 40rpx 20rpx 60rpx;
  text-align: center;
}

.faq-notice {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.contact-btn {
  background: linear-gradient(135deg, #FF6B35 0%, #FF8C42 100%);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;
}

.contact-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
} 