/**
 * 执行创建门店库存表的SQL脚本
 */
const fs = require('fs');
const path = require('path');
const db = require('../config/db');

async function executeSQL() {
  try {
    // 读取SQL文件内容
    const sqlFilePath = path.join(__dirname, 'create_store_inventory_table.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // 执行SQL语句
    await db.query(sqlContent);
    
    console.log('门店库存表创建成功！');
    process.exit(0);
  } catch (error) {
    console.error('执行SQL脚本失败:', error);
    process.exit(1);
  }
}

// 执行脚本
executeSQL();