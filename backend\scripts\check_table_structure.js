const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'molI2505$',
  database: 'morebuy'
};

async function checkTableStructure() {
  let connection;
  
  try {
    console.log('🔗 连接到数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 检查表结构
    console.log('\n📊 users表结构:');
    const [columns] = await connection.execute('DESCRIBE users');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Key ? `(${col.Key})` : ''} ${col.Extra ? `[${col.Extra}]` : ''}`);
    });
    
    // 检查主键信息
    console.log('\n🔑 主键信息:');
    const [keys] = await connection.execute("SHOW KEYS FROM users WHERE Key_name = 'PRIMARY'");
    keys.forEach(key => {
      console.log(`  - 主键字段: ${key.Column_name}`);
    });
    
    // 检查自增字段
    console.log('\n🔄 自增字段信息:');
    const autoIncrementColumns = columns.filter(col => col.Extra && col.Extra.includes('auto_increment'));
    if (autoIncrementColumns.length > 0) {
      autoIncrementColumns.forEach(col => {
        console.log(`  - 自增字段: ${col.Field} (${col.Type})`);
      });
    } else {
      console.log('  - 没有自增字段');
    }
    
    // 查看一些示例数据
    console.log('\n📋 用户数据示例:');
    const [users] = await connection.execute('SELECT id, nickname, phone FROM users LIMIT 5');
    users.forEach(user => {
      console.log(`  - ID: ${user.id}, 昵称: ${user.nickname}, 电话: ${user.phone}`);
    });
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行检查
checkTableStructure()
  .then(() => {
    console.log('✅ 检查完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 检查失败:', error);
    process.exit(1);
  }); 