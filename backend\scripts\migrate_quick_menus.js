const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// 数据库配置
const dbConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: process.env.MYSQL_PORT || 3306,
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || 'molI2505$',
  database: process.env.MYSQL_DATABASE || 'morebuy',
  charset: 'utf8mb4'
};

async function migrateQuickMenus() {
  let connection;
  
  try {
    console.log('🔄 开始合伙人端快捷菜单数据表迁移...');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 检查当前表结构
    console.log('📋 检查当前表结构...');
    const [tableInfo] = await connection.execute('DESCRIBE quick_menus');
    console.log('当前字段:', tableInfo.map(field => field.Field).join(', '));
    
    // 2. 检查target_platform字段是否存在
    const hasTargetPlatform = tableInfo.some(field => field.Field === 'target_platform');
    
    if (hasTargetPlatform) {
      console.log('⚠️  target_platform字段已存在，跳过表结构扩展');
    } else {
      console.log('➕ 开始扩展表结构...');
      
      // 添加target_platform字段
      await connection.execute(`
        ALTER TABLE quick_menus 
        ADD COLUMN target_platform ENUM('customer', 'partner', 'admin', 'all') NOT NULL DEFAULT 'customer' COMMENT '目标平台' AFTER is_active
      `);
      console.log('✅ 添加target_platform字段成功');
      
      // 添加required_roles字段
      await connection.execute(`
        ALTER TABLE quick_menus 
        ADD COLUMN required_roles JSON NULL COMMENT '所需角色权限' AFTER target_platform
      `);
      console.log('✅ 添加required_roles字段成功');
      
      // 添加时间戳字段
      await connection.execute(`
        ALTER TABLE quick_menus 
        ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER required_roles
      `);
      console.log('✅ 添加created_at字段成功');
      
      await connection.execute(`
        ALTER TABLE quick_menus 
        ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER created_at
      `);
      console.log('✅ 添加updated_at字段成功');
    }
    
    // 3. 添加索引
    console.log('📊 添加索引...');
    try {
      await connection.execute(`
        ALTER TABLE quick_menus 
        ADD INDEX idx_target_platform (target_platform)
      `);
      console.log('✅ 添加target_platform索引成功');
    } catch (err) {
      if (err.code === 'ER_DUP_KEYNAME') {
        console.log('⚠️  target_platform索引已存在');
      } else {
        throw err;
      }
    }
    
    try {
      await connection.execute(`
        ALTER TABLE quick_menus 
        ADD INDEX idx_is_active_platform (is_active, target_platform)
      `);
      console.log('✅ 添加组合索引成功');
    } catch (err) {
      if (err.code === 'ER_DUP_KEYNAME') {
        console.log('⚠️  组合索引已存在');
      } else {
        throw err;
      }
    }
    
    // 4. 检查是否已有合伙人端菜单
    const [existingPartnerMenus] = await connection.execute(
      'SELECT COUNT(*) as count FROM quick_menus WHERE target_platform = ?',
      ['partner']
    );
    
    if (existingPartnerMenus[0].count > 0) {
      console.log('⚠️  合伙人端菜单已存在，跳过数据插入');
    } else {
      console.log('📝 插入合伙人端默认菜单...');
      
      const partnerMenus = [
        ['门店合伙人', '/images/icons2/门店合伙人.png', 'function', 'viewStorePartners', 1, 1, 'partner', JSON.stringify(['partner'])],
        ['门店库存', '/images/icons2/门店库存.png', 'function', 'viewStoreInventory', 2, 1, 'partner', JSON.stringify(['partner'])],
        ['在线客服', '/images/icons2/在线客服.png', 'function', 'contactService', 3, 1, 'partner', JSON.stringify(['partner'])],
        ['分享门店', '/images/icons2/分享门店.png', 'function', 'shareStore', 4, 1, 'partner', JSON.stringify(['partner'])]
      ];
      
      for (const menu of partnerMenus) {
        await connection.execute(`
          INSERT INTO quick_menus (name, icon, link_type, link_url, sort_order, is_active, target_platform, required_roles)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, menu);
        console.log(`✅ 插入菜单: ${menu[0]}`);
      }
    }
    
    // 5. 验证结果
    console.log('🔍 验证迁移结果...');
    
    // 查看更新后的表结构
    const [newTableInfo] = await connection.execute('DESCRIBE quick_menus');
    console.log('✅ 更新后的表结构:');
    newTableInfo.forEach(field => {
      console.log(`   ${field.Field}: ${field.Type} ${field.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${field.Default ? 'DEFAULT ' + field.Default : ''}`);
    });
    
    // 查看合伙人端菜单
    const [partnerMenus] = await connection.execute('SELECT * FROM quick_menus WHERE target_platform = ?', ['partner']);
    console.log('✅ 合伙人端菜单列表:');
    partnerMenus.forEach(menu => {
      console.log(`   ${menu.name} (${menu.link_type}: ${menu.link_url})`);
    });
    
    // 查看按平台分类的统计
    const [stats] = await connection.execute(`
      SELECT target_platform, COUNT(*) as menu_count 
      FROM quick_menus 
      WHERE is_active = 1 
      GROUP BY target_platform
    `);
    console.log('✅ 菜单统计:');
    stats.forEach(stat => {
      console.log(`   ${stat.target_platform}: ${stat.menu_count} 个菜单`);
    });
    
    console.log('🎉 合伙人端快捷菜单迁移完成！');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    console.error('详细错误:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('📪 数据库连接已关闭');
    }
  }
}

// 执行迁移
if (require.main === module) {
  migrateQuickMenus();
}

module.exports = { migrateQuickMenus }; 