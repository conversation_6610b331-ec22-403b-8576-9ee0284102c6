const device = wx.getSystemInfoSync();
// 计算裁剪窗口实际尺寸：95vw，以屏幕宽度为准，不限制最大像素
const width = device.windowWidth * 0.95;
const height = width; // 1:1裁剪

Page({
  data: {
    src: '',
    cropWidth: width,
    cropHeight: width,
    imgInfo: null,
    scale: 1,
    minScale: 1,
    maxScale: 4,
    offsetX: 0,
    offsetY: 0,
    lastX: 0,
    lastY: 0,
    lastDist: 0,
    mode: '' // 'move' or 'scale'
  },

  onLoad(options) {
    console.log('裁剪页面加载，参数:', options);
    if (options && options.src) {
      let src = decodeURIComponent(options.src);
      console.log('解码后的图片路径:', src);
      this.setData({ src });
      this.initImage();
    } else {
      console.error('裁剪页面缺少图片路径参数');
      wx.showToast({ title: '缺少图片参数', icon: 'none' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  initImage() {
    const { src, cropWidth, cropHeight } = this.data;
    if (!src) return;

    console.log('开始初始化图片，路径:', src);

    // 直接使用适配算法，确保图片完整显示
    this.setupImageFitDisplay(src, cropWidth, cropHeight);
  },

  // 设置图片适配显示
  setupImageFitDisplay(src, cropWidth, cropHeight) {
    console.log('设置图片适配显示');

    // 先尝试获取图片信息，如果失败则使用默认策略
    wx.getImageInfo({
      src: src,
      success: (res) => {
        console.log('获取图片信息成功:', res);
        this.calculateImageFit(src, res.width, res.height, cropWidth, cropHeight);
      },
      fail: (err) => {
        console.log('获取图片信息失败，使用默认策略:', err);
        // 使用常见的图片比例作为默认值
        this.calculateImageFit(src, 1080, 1080, cropWidth, cropHeight);
      }
    });
  },

  // 计算图片适配参数
  calculateImageFit(src, imgWidth, imgHeight, cropWidth, cropHeight) {
    console.log('计算图片适配参数:', {
      imgWidth,
      imgHeight,
      cropWidth,
      cropHeight
    });

    const imgInfo = {
      width: imgWidth,
      height: imgHeight,
      path: src
    };

    // 计算缩放比例，保持图片原始比例
    const scaleX = cropWidth / imgWidth;
    const scaleY = cropHeight / imgHeight;

    // 使用较小的缩放比例，让图片完整显示在裁剪区域内
    const scale = Math.min(scaleX, scaleY);

    // 计算缩放后的尺寸
    const scaledWidth = imgWidth * scale;
    const scaledHeight = imgHeight * scale;

    // 计算居中偏移
    const offsetX = (cropWidth - scaledWidth) / 2;
    const offsetY = (cropHeight - scaledHeight) / 2;

    console.log('图片完整显示适配参数:', {
      scaleX: scaleX.toFixed(3),
      scaleY: scaleY.toFixed(3),
      selectedScale: scale.toFixed(3),
      scaledWidth: scaledWidth.toFixed(1),
      scaledHeight: scaledHeight.toFixed(1),
      offsetX: offsetX.toFixed(1),
      offsetY: offsetY.toFixed(1)
    });

    this.setData({
      imgInfo,
      scale,
      minScale: scale * 0.5,  // 最小缩放到完整显示的50%
      maxScale: scale * 3,   // 最大放大3倍
      offsetX,
      offsetY,
      isInitialized: true
    }, () => {
      // 延迟绘制，确保数据设置完成
      setTimeout(() => {
        this.drawToCanvas();
      }, 100);
    });
  },

  drawToCanvas() {
    const { src, cropWidth, cropHeight, imgInfo, scale, offsetX, offsetY, isInitialized } = this.data;
    if (!imgInfo || !isInitialized) {
      console.log('绘制画布跳过，缺少必要数据:', { imgInfo: !!imgInfo, isInitialized });
      return;
    }

    console.log('绘制画布，参数:', {
      scale,
      offsetX,
      offsetY,
      imgInfo,
      cropWidth,
      cropHeight
    });

    const ctx = wx.createCanvasContext('cropperCanvas', this);

    // 清空画布，使用白色背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, cropWidth, cropHeight);

    // 计算绘制尺寸，保持原始比例
    const scaledWidth = imgInfo.width * scale;
    const scaledHeight = imgInfo.height * scale;

    console.log('绘制图片参数:', {
      原始尺寸: `${imgInfo.width}x${imgInfo.height}`,
      缩放比例: scale.toFixed(3),
      绘制尺寸: `${scaledWidth.toFixed(1)}x${scaledHeight.toFixed(1)}`,
      绘制位置: `(${offsetX.toFixed(1)}, ${offsetY.toFixed(1)})`,
      Canvas尺寸: `${cropWidth}x${cropHeight}`
    });

    try {
      // 确保图片路径有效
      if (src && src.length > 0) {
        // 按原始比例绘制图片，完整显示在裁剪区域内
        ctx.drawImage(src, offsetX, offsetY, scaledWidth, scaledHeight);
        console.log('图片绘制完成，已完整显示在裁剪区域内');
      } else {
        console.error('图片路径无效:', src);
      }

      ctx.draw(false, () => {
        console.log('Canvas绘制完成');
      });
    } catch (error) {
      console.error('Canvas绘制失败:', error);
      // 如果绘制失败，尝试重新绘制
      setTimeout(() => {
        console.log('尝试重新绘制');
        this.retryDraw();
      }, 500);
    }
  },

  // 重试绘制
  retryDraw() {
    const { src, cropWidth, cropHeight, imgInfo, scale, offsetX, offsetY } = this.data;
    const ctx = wx.createCanvasContext('cropperCanvas', this);

    console.log('重试绘制图片，使用当前参数');

    // 设置画布背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, cropWidth, cropHeight);

    if (imgInfo && scale && offsetX !== undefined && offsetY !== undefined) {
      // 使用当前的缩放和偏移参数
      const scaledWidth = imgInfo.width * scale;
      const scaledHeight = imgInfo.height * scale;
      ctx.drawImage(src, offsetX, offsetY, scaledWidth, scaledHeight);
      console.log('重试绘制使用参数:', { scaledWidth, scaledHeight, offsetX, offsetY });
    } else {
      // 如果参数不完整，使用简化的居中绘制
      const size = Math.min(cropWidth, cropHeight) * 0.9;
      const x = (cropWidth - size) / 2;
      const y = (cropHeight - size) / 2;
      ctx.drawImage(src, x, y, size, size);
      console.log('重试绘制使用简化参数:', { size, x, y });
    }

    ctx.draw();
  },



  touchStart(e) {
    console.log('触摸开始:', e.touches.length);
    if (e.touches.length === 1) {
      this.setData({
        mode: 'move',
        lastX: e.touches[0].clientX,
        lastY: e.touches[0].clientY
      });
    } else if (e.touches.length === 2) {
      this.setData({
        mode: 'scale',
        lastDist: this.getDistance(e.touches[0], e.touches[1])
      });
    }
  },

  touchMove(e) {
    const { mode, lastX, lastY, lastDist, scale, minScale, maxScale, offsetX, offsetY, imgInfo, cropWidth, cropHeight } = this.data;

    if (mode === 'move' && e.touches.length === 1) {
      const deltaX = e.touches[0].clientX - lastX;
      const deltaY = e.touches[0].clientY - lastY;

      let newOffsetX = offsetX + deltaX;
      let newOffsetY = offsetY + deltaY;

      // 限制边界，确保图片不会完全移出裁剪区
      const scaledWidth = imgInfo.width * scale;
      const scaledHeight = imgInfo.height * scale;
      const maxOffsetX = cropWidth - scaledWidth;
      const maxOffsetY = cropHeight - scaledHeight;

      // 如果图片比裁剪区小，则不允许移动
      if (scaledWidth <= cropWidth) {
        newOffsetX = (cropWidth - scaledWidth) / 2;
      } else {
        newOffsetX = Math.max(maxOffsetX, Math.min(0, newOffsetX));
      }

      if (scaledHeight <= cropHeight) {
        newOffsetY = (cropHeight - scaledHeight) / 2;
      } else {
        newOffsetY = Math.max(maxOffsetY, Math.min(0, newOffsetY));
      }

      this.setData({
        offsetX: newOffsetX,
        offsetY: newOffsetY,
        lastX: e.touches[0].clientX,
        lastY: e.touches[0].clientY
      }, this.drawToCanvas);
    } else if (mode === 'scale' && e.touches.length === 2) {
      const currentDist = this.getDistance(e.touches[0], e.touches[1]);
      const scaleRatio = currentDist / lastDist;

      let newScale = scale * scaleRatio;
      newScale = Math.max(minScale, Math.min(maxScale, newScale));

      // 缩放时保持图片居中
      const oldScaledWidth = imgInfo.width * scale;
      const oldScaledHeight = imgInfo.height * scale;
      const newScaledWidth = imgInfo.width * newScale;
      const newScaledHeight = imgInfo.height * newScale;

      const newOffsetX = offsetX + (oldScaledWidth - newScaledWidth) / 2;
      const newOffsetY = offsetY + (oldScaledHeight - newScaledHeight) / 2;

      this.setData({
        scale: newScale,
        offsetX: newOffsetX,
        offsetY: newOffsetY,
        lastDist: currentDist
      }, this.drawToCanvas);
    }
  },

  touchEnd() {
    console.log('触摸结束');
    this.setData({ mode: '' });
  },

  getDistance(touch1, touch2) {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  },

  onCancel() {
    wx.navigateBack();
  },

  doCrop() {
    const { cropWidth, cropHeight } = this.data;

    // 显示加载中提示
    wx.showLoading({
      title: '正在裁剪...',
      mask: true
    });

    // 准备裁剪图片
    wx.canvasToTempFilePath({
      canvasId: 'cropperCanvas',
      width: cropWidth,
      height: cropHeight,
      destWidth: cropWidth,
      destHeight: cropHeight,
      fileType: 'jpg',  // 指定保存格式为JPG，压缩文件大小
      quality: 0.8,     // 设置较高的质量，但不是最高，减小文件大小
      success: res => {
        // 隐藏加载中提示
        wx.hideLoading();

        console.log('图片裁剪成功，临时路径：', res.tempFilePath);

        // 获取上一个页面实例
        const pages = getCurrentPages();
        const prevPage = pages[pages.length - 2];

        // 检查上一个页面是否存在
        if (prevPage) {
          try {
            // 使用croppedAvatar字段存储裁剪后的图片路径
            // 这样在avatar页面的onShow中可以检测到新的头像
            prevPage.setData({
              croppedAvatar: res.tempFilePath,
              selectedAvatar: res.tempFilePath,
              customAvatarMode: true  // 设置为自定义头像模式
            });

            wx.showToast({
              title: '裁剪成功',
              icon: 'success',
              duration: 1000
            });

            setTimeout(() => {
              wx.navigateBack();
            }, 1000);
          } catch (error) {
            console.error('设置上一页数据失败:', error);
            this.handleCropError('返回数据失败');
          }
        } else {
          console.error('找不到上一页面实例');
          this.handleCropError('找不到上一页面');
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('裁剪失败:', err);
        this.handleCropError('裁剪失败');
      }
    }, this);
  },
  // 处理裁剪错误
  handleCropError(message) {
    wx.showToast({
      title: message || '裁剪失败',
      icon: 'none'
    });

    // 尝试返回上一页
    setTimeout(() => {
      try {
        wx.navigateBack();
      } catch (e) {
        console.error('返回上一页失败:', e);
      }
    }, 1500);
  }
});