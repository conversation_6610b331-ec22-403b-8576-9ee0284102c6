Page({
  data: {
    nickname: '',
    avatarUrl: '',
    userId: '',
    qrcodeUrl: null,  // 二维码图片临时路径，初始值为null
    cloudFileID: '', // 云存储中二维码的fileID
    shareImage: '/images/share/share.jpg',  // 分享背景图
    generatedImage: '', // canvas合成后的图片路径
    drawing: false,  // 是否正在生成分享图
    qrcodeRetryCount: 0, // 二维码生成重试次数
    maxRetryCount: 1, // 最大重试次数，减少到1次
    shareImageGenerated: false, // 标记是否已生成分享图片
    savingImage: false, // 标记是否正在保存图片
    pendingSaveImage: false, // 标记是否正在等待保存图片
    avatarDrawSuccess: null, // 头像是否成功绘制到Canvas
    usingAvatarPlaceholder: false // 是否使用头像占位符
  },
  onLoad(options) {
    console.log('分享页面加载，参数:', options);
    
    // 获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        userId: userInfo.id,
        nickname: userInfo.nickname || '用户',
        avatarUrl: userInfo.avatar || '/images/default-avatar.png'
      });
    }
    
    // 检查来源参数
    if (options.source === 'partner') {
      this.setData({ source: 'partner' });
      
      // 获取门店信息
      const storeInfo = wx.getStorageSync('currentStore');
      if (storeInfo) {
        this.setData({ storeInfo });
        console.log('合伙人端分享，门店信息:', storeInfo);
        this.checkConfigAndGenerateQrcode(() => {
          this.generateQrcodeWithStoreInfo(storeInfo);
        });
      } else {
        console.error('合伙人端分享但未找到门店信息');
        wx.showToast({ title: '门店信息获取失败', icon: 'none' });
      }
    } else {
      // 普通用户分享
      console.log('普通用户分享');
      this.checkConfigAndGenerateQrcode(() => {
        this.generateQrcodeWithCloudAPI();
      });
    }
    
    // 检查网络状态
    this.checkNetworkStatus();
    
    // 配置微信分享
    this.configureWeChatShare();
  },

  // 配置微信分享
  configureWeChatShare() {
    // 配置分享给朋友
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 分享给朋友
  onShareAppMessage() {
    const userInfo = wx.getStorageSync('userInfo');
    const nickname = userInfo ? userInfo.nickname : '用户';
    
    return {
      title: `${nickname}向您推荐陌派商城`,
      path: '/pages/home/<USER>' + this.data.userId,
      imageUrl: '/images/share/share.jpg'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    const userInfo = wx.getStorageSync('userInfo');
    const nickname = userInfo ? userInfo.nickname : '用户';
    
    return {
      title: `${nickname}向您推荐陌派商城`,
      query: 'scene=' + this.data.userId,
      imageUrl: '/images/share/share.jpg'
    };
  },

  // 检查后端配置
  async checkConfigAndGenerateQrcode(callback) {
    try {
      const app = getApp();
      const envId = app && app.globalData && app.globalData.cloudEnvId ? app.globalData.cloudEnvId : 'prod-4g3qet1k59f2d66f';
      const serviceName = 'morebuy25';
      
      console.log('检查后端配置...');
      
      const res = await new Promise((resolve, reject) => {
        wx.cloud.callContainer({
          config: {
            env: envId
          },
          path: '/api/system/config-check',
          method: 'GET',
          header: {
            'X-WX-SERVICE': serviceName,
            'content-type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });
      
      console.log('配置检查结果:', res.data);
      
      if (res.data && res.data.success && res.data.hasValidConfig) {
        console.log('后端配置正常，开始生成二维码');
        if (callback && typeof callback === 'function') {
          callback();
        }
      } else {
        console.error('后端配置异常:', res.data);
        wx.showToast({
          title: '服务配置异常，请联系管理员',
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('检查后端配置失败:', error);
      // 即使配置检查失败，也尝试生成二维码
      if (callback && typeof callback === 'function') {
        callback();
      }
    }
  },

  // 监听数据变化
  onDataChange(newData) {
    if (newData.qrcodeUrl !== undefined) {
      console.log('qrcodeUrl数据变化:', {
        新值: newData.qrcodeUrl,
        类型: typeof newData.qrcodeUrl,
        长度: newData.qrcodeUrl ? newData.qrcodeUrl.length : 0
      });
    }
  },

  // 检测网络状态
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        console.log('当前网络类型:', res.networkType);
        if (res.networkType === 'none') {
          wx.showToast({
            title: '网络连接异常，可能影响二维码生成',
            icon: 'none',
            duration: 3000
          });
        }
      },
      fail: (err) => {
        console.error('获取网络状态失败:', err);
      }
    });
  },
  
  // 生成包含门店信息的二维码（合伙人端分享）
  async generateQrcodeWithStoreInfo(storeInfo) {
    try {
      const userId = this.data.userId;
      if (!userId) {
        console.error('用户ID不存在，无法生成二维码');
        wx.showToast({ title: '用户信息获取失败', icon: 'none' });
        return;
      }
      
      // 检查门店信息是否完整
      if (!storeInfo || !storeInfo.id || !storeInfo.store_no) {
        console.error('门店信息不完整:', storeInfo);
        wx.showToast({ title: '门店信息获取失败', icon: 'none' });
        return;
      }
      
      // 构建包含门店信息的scene参数 - 使用微信官方要求的字符串格式
      // scene必须是字符串，不能是JSON，且长度不超过32个字符
      const scene = `n_${storeInfo.store_no},t_p`;
      
      console.log('准备生成门店分享二维码，参数:', {
        userId,
        storeInfo,
        scene
      });
      
      // 直接使用云托管方式调用API
      const app = getApp();
      const envId = app && app.globalData && app.globalData.cloudEnvId ? app.globalData.cloudEnvId : 'prod-4g3qet1k59f2d66f';
      const serviceName = 'morebuy25';
      
      console.log('合伙人端云托管配置:', {
        envId,
        serviceName,
        userId,
        storeInfo,
        scene
      });
      
      wx.showLoading({ title: '生成二维码中...', mask: true });
      
      try {
        const res = await new Promise((resolve, reject) => {
          wx.cloud.callContainer({
            config: {
              env: envId
            },
            path: '/api/system/qrcode',
            method: 'GET',
            header: {
              'X-WX-SERVICE': serviceName,
              'content-type': 'application/json'
            },
            data: {
              scene: scene
              // 不传递page参数，避免41030错误
            },
            success: resolve,
            fail: reject
          });
        });
        
        wx.hideLoading();
        console.log('合伙人端云托管API响应:', res.data);
        console.log('合伙人端云托管API响应状态码:', res.statusCode);
        console.log('合伙人端云托管API响应头:', res.header);
          
        if (res.data && res.data.success && res.data.qrcodeUrl) {
          console.log('二维码生成成功，原始URL:', res.data.qrcodeUrl);
          
          // 将服务器端的二维码保存到微信缓存用于显示
          await this.saveQrcodeToCache(res.data.qrcodeUrl);
        } else {
          console.error('二维码生成失败:', res.data);
          wx.showToast({ 
            title: '二维码生成失败，请重试',
            icon: 'none',
            duration: 3000
          });
        }
      } catch (err) {
        wx.hideLoading();
        console.error('请求门店分享二维码API失败:', err);
        wx.showToast({ 
          title: '二维码生成失败，请重试',
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('请求门店分享二维码API失败:', error);
      console.error('错误堆栈:', error.stack);
      wx.showToast({ 
        title: '二维码生成失败，请重试',
        icon: 'none',
        duration: 3000
      });
    }
  },
  
  // 使用云托管API生成二维码
  async generateQrcodeWithCloudAPI() {
    try {
      const userId = this.data.userId;
      if (!userId) {
        console.error('用户ID不存在，无法生成二维码');
        wx.showToast({ title: '用户信息获取失败', icon: 'none' });
        return;
      }
      
      console.log('开始生成二维码，用户ID:', userId);
      
      const app = getApp();
      const envId = app && app.globalData && app.globalData.cloudEnvId ? app.globalData.cloudEnvId : 'prod-4g3qet1k59f2d66f';
      const serviceName = 'morebuy25';
      
      console.log('云托管配置:', {
        envId,
        serviceName,
        userId
      });
      
      wx.showLoading({ title: '生成二维码中...', mask: true });
      
      try {
        const res = await new Promise((resolve, reject) => {
          wx.cloud.callContainer({
            config: {
              env: envId
            },
            path: '/api/system/qrcode',
            method: 'GET',
            header: {
              'X-WX-SERVICE': serviceName,
              'content-type': 'application/json'
            },
            data: {
              scene: userId
              // 不传递page参数，避免41030错误
            },
            success: resolve,
            fail: reject
          });
        });
        
        wx.hideLoading();
        console.log('云托管API响应:', res.data);
        console.log('云托管API响应状态码:', res.statusCode);
        
        if (res.data && res.data.success && res.data.qrcodeUrl) {
          console.log('二维码生成成功，原始URL:', res.data.qrcodeUrl);
          
          // 将服务器端的二维码保存到微信缓存用于显示
          await this.saveQrcodeToCache(res.data.qrcodeUrl);
        } else {
          console.error('二维码生成失败:', res.data);
          // 设置失败状态，让用户看到重试按钮
          this.setData({ qrcodeUrl: null });
          wx.showToast({
            title: '二维码生成失败，请重试',
            icon: 'none',
            duration: 3000
          });
        }
      } catch (err) {
        wx.hideLoading();
        console.error('请求二维码API失败:', err);
        // 设置失败状态，让用户看到重试按钮
        this.setData({ qrcodeUrl: null });
        wx.showToast({
          title: '二维码生成失败，请重试',
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('请求二维码API失败:', error);
      // 设置失败状态，让用户看到重试按钮
      this.setData({ qrcodeUrl: null });
      wx.showToast({
        title: '二维码生成失败，请重试',
        icon: 'none',
        duration: 3000
      });
    }
  },
  // 绘制分享图片
  async drawShareImage(callback) {
    // 确保图片资源已准备好
    if (!this.data.qrcodeUrl) {
      console.log('二维码尚未生成，尝试重新生成');
      // 根据来源选择不同的生成方法
      if (this.data.source === 'partner' && this.data.storeInfo) {
        await this.generateQrcodeWithStoreInfo(this.data.storeInfo);
      } else {
        await this.generateQrcodeWithCloudAPI();
      }
      // 等待二维码生成
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 设置绘制状态
    this.setData({ drawing: true });
    wx.showLoading({ title: '生成分享图片...', mask: true });
    
    // 设置超时处理
    const drawTimeout = setTimeout(() => {
      console.error('绘制分享图片超时');
      this.setData({ 
        drawing: false,
        // 即使超时也标记为已生成，避免重复绘制
        shareImageGenerated: true 
      });
      wx.hideLoading();
      wx.showToast({ 
        title: '生成图片超时，已使用简化版', 
        icon: 'none',
        duration: 2000
      });
      
      // 超时后也尝试执行回调，使用简化版图片
      if (callback && typeof callback === 'function') {
        setTimeout(() => {
          try {
            callback();
          } catch (callbackErr) {
            console.error('超时后回调执行失败:', callbackErr);
          }
        }, 500);
      }
    }, 45000); // 增加到45秒超时，给予更多时间完成绘制
    
    try {
      // 获取Canvas节点
      const query = wx.createSelectorQuery();
      query.select('#shareCanvas')
        .fields({ node: true, size: true })
        .exec(async (res) => {
          if (!res || !res[0] || !res[0].node) {
            console.error('获取Canvas节点失败');
            clearTimeout(drawTimeout);
            this.setData({ drawing: false });
            wx.hideLoading();
            wx.showToast({ title: '画布初始化失败', icon: 'none' });
            return;
          }
          
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');
          
          // 设置canvas尺寸，考虑设备像素比
          const dpr = wx.getSystemInfoSync().pixelRatio;
          canvas.width = 600 * dpr;
          canvas.height = 800 * dpr;
          ctx.scale(dpr, dpr);
          
          const { nickname, avatarUrl, qrcodeUrl, shareImage } = this.data;
          
          console.log('开始绘制分享图片，参数:', {
            nickname,
            avatarUrl,
            qrcodeUrl,
            shareImage
          });
          
          // 获取系统信息以确定画布大小
          const canvasWidth = 600; // 与卡片宽度一致
          const canvasHeight = 800; // 足够容纳整个卡片
          
          // 绘制白色背景
          ctx.fillStyle = '#ffffff';
          ctx.fillRect(0, 0, canvasWidth, canvasHeight);
          
          // 创建图片对象并加载所有图片
          let shareImg, avatarImg, qrcodeImg;
          
          try {
            // 并行加载所有图片资源
            const [shareImgLoaded, avatarImgLoaded, qrcodeImgLoaded] = await Promise.all([
              // 加载背景图
              new Promise(async (resolve) => {
                try {
                  console.log('开始加载分享背景图:', shareImage);
                  shareImg = canvas.createImage();
                  shareImg.onload = () => {
                    console.log('分享背景图加载成功');
                    resolve(true);
                  };
                  shareImg.onerror = (err) => {
                    console.error('背景图加载失败:', err);
                    resolve(false);
                  };
                  // 确保使用绝对路径
                  if (shareImage.startsWith('/')) {
                    shareImg.src = shareImage;
                  } else {
                    shareImg.src = '/' + shareImage;
                  }
                } catch (err) {
                  console.error('加载分享背景图失败:', err);
                  resolve(false);
                }
              }),
              
              // 加载头像
              new Promise(async (resolve) => {
                try {
                  console.log('开始加载头像:', avatarUrl);
                  // 新增：如果是 cloud:// 路径，先转为临时链接
                  let realAvatarUrl = avatarUrl;
                  if (avatarUrl && avatarUrl.startsWith('cloud://')) {
                    try {
                      const tempRes = await new Promise((res, rej) => {
                        wx.cloud.getTempFileURL({
                          fileList: [avatarUrl],
                          success: r => res(r),
                          fail: e => rej(e)
                        });
                      });
                      if (tempRes.fileList && tempRes.fileList[0] && tempRes.fileList[0].tempFileURL) {
                        realAvatarUrl = tempRes.fileList[0].tempFileURL;
                        console.log('云头像已转为临时链接:', realAvatarUrl);
                      }
                    } catch (e) {
                      console.error('云头像转临时链接失败:', e);
                    }
                  }
                  // 确保头像URL有效
                  if (!realAvatarUrl || realAvatarUrl === 'undefined' || realAvatarUrl === 'null') {
                    console.log('头像URL无效，使用默认头像');
                    const defaultAvatarPath = '/images/icons2/默认头像.png';
                    avatarImg = canvas.createImage();
                    avatarImg.onload = () => {
                      console.log('默认头像加载成功');
                      resolve(true);
                    };
                    avatarImg.onerror = () => {
                      console.error('默认头像加载失败');
                      resolve(false);
                    };
                    avatarImg.src = defaultAvatarPath;
                    return;
                  }
                  // 尝试加载用户头像
                  avatarImg = canvas.createImage();
                  avatarImg.onload = () => {
                    console.log('头像加载成功');
                    resolve(true);
                  };
                  avatarImg.onerror = async (err) => {
                    console.error('加载用户头像失败，尝试加载默认头像:', err);
                    // 尝试加载默认头像
                    const defaultAvatarPath = '/images/icons2/默认头像.png';
                    avatarImg = canvas.createImage();
                    avatarImg.onload = () => {
                      console.log('默认头像加载成功');
                      resolve(true);
                    };
                    avatarImg.onerror = () => {
                      console.error('默认头像加载失败');
                      resolve(false);
                    };
                    avatarImg.src = defaultAvatarPath;
                  };
                  avatarImg.src = realAvatarUrl;
                } catch (err) {
                  console.error('所有头像加载失败:', err);
                  resolve(false);
                }
              }),
              
              // 加载二维码
              new Promise(async (resolve) => {
                if (qrcodeUrl) {
                  try {
                    console.log('开始处理二维码URL:', qrcodeUrl);
                    // 确保二维码URL使用https
                    let qrcodeSrc = qrcodeUrl;
                    if (qrcodeSrc.startsWith('http:')) {
                      qrcodeSrc = qrcodeSrc.replace('http:', 'https:');
                      console.log('二维码URL已转换为https:', qrcodeSrc);
                    }
                    
                    // 加载二维码图片
                    qrcodeImg = canvas.createImage();
                    qrcodeImg.onload = () => {
                      console.log('二维码加载成功');
                      resolve(true);
                    };
                    qrcodeImg.onerror = (err) => {
                      console.error('二维码加载失败:', err);
                      resolve(false);
                    };
                    qrcodeImg.src = qrcodeSrc;
                  } catch (err) {
                    console.error('所有二维码加载方法均失败:', err);
                    resolve(false);
                  }
                } else {
                  console.log('没有二维码URL');
                  resolve(false);
                }
              })
            ]);
            
            console.log('所有图片加载任务完成，开始绘制', {
              shareImgLoaded,
              avatarImgLoaded,
              qrcodeImgLoaded
            });
            
            // 绘制主图部分（背景图）
            if (shareImgLoaded && shareImg) {
              try {
                // 绘制主图（背景图）
                ctx.drawImage(shareImg, 0, 0, canvasWidth, canvasWidth);
                console.log('背景图绘制成功');
              } catch (err) {
                console.error('绘制背景图失败:', err);
                // 绘制蓝色背景作为替代
                ctx.fillStyle = '#20647a';
                ctx.fillRect(0, 0, canvasWidth, canvasWidth);
                
                // 添加文字
                ctx.font = '40px sans-serif';
                ctx.fillStyle = '#ffffff';
                ctx.textAlign = 'center';
                ctx.fillText('后台设定', canvasWidth/2, canvasWidth/2 - 20);
                ctx.fillText('分享图片', canvasWidth/2, canvasWidth/2 + 40);
              }
            } else {
              // 绘制蓝色背景作为替代
              ctx.fillStyle = '#20647a';
              ctx.fillRect(0, 0, canvasWidth, canvasWidth);
              
              // 添加文字
              ctx.font = '40px sans-serif';
              ctx.fillStyle = '#ffffff';
              ctx.textAlign = 'center';
              ctx.fillText('后台设定', canvasWidth/2, canvasWidth/2 - 20);
              ctx.fillText('分享图片', canvasWidth/2, canvasWidth/2 + 40);
            }
            
            // 绘制底部信息栏背景
            const infoBarY = canvasWidth; // 从主图底部开始
            const infoBarHeight = 160; // 底部信息栏高度
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, infoBarY, canvasWidth, infoBarHeight);
            
            // 绘制头像
            const avatarSize = 96; // 增大20%
            const qrcodeSize = 158; // 在144px基础上再增大10%
            const qrcodeX = canvasWidth - qrcodeSize - 30;
            const extraMargin = 20;
            const qrcodeY = infoBarY + (infoBarHeight - qrcodeSize) / 2 + extraMargin;
            const avatarX = 30;
            // 头像与小程序码距离主图底部的距离调整为20px
            const avatarY = qrcodeY;
            
            console.log('准备绘制头像，状态:', { avatarImgLoaded, avatarImg: !!avatarImg });
            
            // 只绘制纯圆形头像，无任何方形背景或边框
            if (avatarImgLoaded && avatarImg) {
              try {
                ctx.save();
                ctx.beginPath();
                ctx.arc(avatarX + avatarSize/2, avatarY + avatarSize/2, avatarSize/2, 0, Math.PI * 2, false);
                ctx.closePath();
                ctx.clip();
                ctx.drawImage(avatarImg, avatarX, avatarY, avatarSize, avatarSize);
                ctx.restore();
                // 不再绘制任何边框或方形背景
                this.setData({ avatarDrawSuccess: true, usingAvatarPlaceholder: false });
              } catch (drawErr) {
                console.error('头像绘制失败:', drawErr);
                // 如果绘制失败，尝试基础版本绘制
                try {
                  ctx.save();
                  ctx.beginPath();
                  ctx.arc(avatarX + avatarSize/2, avatarY + avatarSize/2, avatarSize/2, 0, Math.PI * 2);
                  ctx.clip();
                  ctx.drawImage(avatarImg, avatarX, avatarY, avatarSize, avatarSize);
                  ctx.restore();
                  
                  console.log('基础版头像绘制成功');
                  this.setData({ avatarDrawSuccess: true, usingAvatarPlaceholder: false });
                } catch (basicErr) {
                  console.error('基础版头像绘制也失败:', basicErr);
                  throw basicErr; // 抛出错误，让外层catch处理
                }
              }
            } else {
              // 占位符也只绘制圆形，无方框
              try {
                ctx.save();
                ctx.beginPath();
                ctx.arc(avatarX + avatarSize/2, avatarY + avatarSize/2, avatarSize/2, 0, Math.PI * 2, false);
                ctx.closePath();
                ctx.clip();
                ctx.fillStyle = '#cccccc';
                ctx.fill();
                // 在圆形内绘制首字母
                const nickname = this.data.nickname || '用户';
                const initial = nickname.charAt(0).toUpperCase();
                ctx.font = 'bold 40px sans-serif';
                ctx.fillStyle = '#666666';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(initial, avatarX + avatarSize/2, avatarY + avatarSize/2);
                ctx.restore();
                this.setData({ avatarDrawSuccess: false, usingAvatarPlaceholder: true });
              } catch (placeholderErr) {
                console.error('绘制增强版占位符失败:', placeholderErr);
                // 尝试最基础的占位符
                try {
                  ctx.save();
                  ctx.beginPath();
                  ctx.arc(avatarX + avatarSize/2, avatarY + avatarSize/2, avatarSize/2, 0, Math.PI * 2);
                  ctx.fillStyle = '#cccccc';
                  ctx.fill();
                  ctx.strokeStyle = '#aaaaaa';
                  ctx.lineWidth = 2;
                  ctx.stroke();
                  ctx.restore();
                  console.log('已绘制基础版头像占位符（因为增强版占位符绘制失败）');
                  
                  // 设置标记表示使用了占位符
                  this.setData({ avatarDrawSuccess: false, usingAvatarPlaceholder: true });
                } catch (basicPlaceholderErr) {
                  console.error('基础版占位符绘制也失败:', basicPlaceholderErr);
                  // 这里不抛出错误，让代码继续执行
                }
              }
            }
            
            // 绘制昵称和推荐文字（左侧）
            ctx.font = '28px sans-serif';
            ctx.fillStyle = '#333333';
            ctx.textAlign = 'left';
            const nicknameText = nickname || '用户';
            // 让昵称与头像纵向居中
            const nicknameY = avatarY + avatarSize / 2 + 10; // 10为微调值
            ctx.fillText(nicknameText, avatarX + avatarSize + 20, nicknameY);
            ctx.font = '22px sans-serif';
            ctx.fillStyle = '#666666';
            // 计算一个字的宽度（用昵称第一个字）
            const oneCharWidth = ctx.measureText(nicknameText.charAt(0)).width;
            const nicknameWidth = ctx.measureText(nicknameText).width;
            ctx.fillText('向您推荐', avatarX + avatarSize + 20 + nicknameWidth + oneCharWidth, nicknameY);

            // 新增：将标语绘制到头像下方，左对齐，"陌"字与头像中心对齐，字体为隶书，位置下移10px
            ctx.font = '26px SimLi, STLiti, "隶书", "LiSu", serif'; // 隶书字体，缩小字号避免被二维码遮挡
            ctx.fillStyle = '#666666';
            ctx.textAlign = 'left';
            // 标语Y坐标为头像底部+30+10px
            const sloganY = avatarY + avatarSize + 40;
            const sloganX = avatarX + avatarSize / 2;
            // 限制标语的最大宽度，避免与二维码重叠
            const maxSloganWidth = qrcodeX - sloganX - 20; // 20px的间距
            const sloganText = '陌派超零售，财富新宇宙';
            
            // 检查文字宽度是否超过最大宽度
            const sloganWidth = ctx.measureText(sloganText).width;
            if (sloganWidth > maxSloganWidth) {
              // 如果文字太宽，缩小字号
              const scale = maxSloganWidth / sloganWidth;
              const newFontSize = Math.floor(26 * scale);
              ctx.font = `${newFontSize}px SimLi, STLiti, "隶书", "LiSu", serif`;
            }
            
            ctx.fillText(sloganText, sloganX, sloganY);

            console.log('文本绘制完成：昵称、推荐语和标语');
            
            // 绘制二维码（右侧）
            if (qrcodeImgLoaded && qrcodeImg) {
              try {
                ctx.drawImage(qrcodeImg, qrcodeX, qrcodeY, qrcodeSize, qrcodeSize);
                console.log('二维码绘制成功');
              } catch (err) {
                console.error('绘制二维码失败:', err);
                // 绘制一个占位框，表示二维码位置
                ctx.strokeStyle = '#cccccc';
                ctx.strokeRect(qrcodeX, qrcodeY, qrcodeSize, qrcodeSize);
                ctx.font = '12px sans-serif';
                ctx.fillStyle = '#999999';
                ctx.textAlign = 'center';
                ctx.fillText('二维码加载失败', qrcodeX + qrcodeSize/2, qrcodeY + qrcodeSize/2);
              }
            } else {
              // 没有二维码时绘制占位框
              ctx.strokeStyle = '#cccccc';
              ctx.strokeRect(qrcodeX, qrcodeY, qrcodeSize, qrcodeSize);
              ctx.font = '12px sans-serif';
              ctx.fillStyle = '#999999';
              ctx.textAlign = 'center';
              ctx.fillText('二维码未生成', qrcodeX + qrcodeSize/2, qrcodeY + qrcodeSize/2);
            }
            
            console.log('Canvas绘制完成');
            
            // 设置延时确保canvas绘制完成
            setTimeout(() => {
              try {
                clearTimeout(drawTimeout); // 清除超时定时器
                console.log('Canvas绘制完成，准备更新状态');
                this.setData({
                  shareImageGenerated: true,
                  drawing: false
                });
                wx.hideLoading();
                
                // 显示成功提示
                wx.showToast({
                  title: '图片生成成功',
                  icon: 'success',
                  duration: 1500
                });
                
                if (callback && typeof callback === 'function') {
                  try {
                    console.log('执行绘制完成回调');
                    // 再次延时执行回调，确保Toast显示完成
                    setTimeout(() => {
                      callback();
                    }, 500);
                  } catch (callbackErr) {
                    console.error('回调执行失败:', callbackErr);
                    wx.showToast({
                      title: '图片生成完成，但处理过程出错',
                      icon: 'none'
                    });
                  }
                }
              } catch (callbackError) {
                console.error('绘制完成回调执行失败:', callbackError);
                clearTimeout(drawTimeout);
                this.setData({ 
                  drawing: false,
                  shareImageGenerated: true // 即使出错也标记为已生成
                });
                wx.hideLoading();
                wx.showToast({ title: '图片生成异常', icon: 'none' });
              }
            }, 2000); // 延时2秒确保绘制完成
          } catch (error) {
            console.error('图片绘制过程中出错:', error);
            clearTimeout(drawTimeout);
            this.setData({ drawing: false });
            wx.hideLoading();
            wx.showToast({ title: '生成分享图片失败', icon: 'none' });
          }
        });
    } catch (error) {
      console.error('绘制分享图片失败:', error);
      clearTimeout(drawTimeout);
      this.setData({ drawing: false });
      wx.hideLoading();
      wx.showToast({ title: '生成分享图片失败', icon: 'none' });
    }
  },
  // 加载图片
  loadImage(src) {
    return new Promise((resolve, reject) => {
      // 添加错误处理和日志
      console.log('开始加载图片:', src);
      
      // 检查src是否为有效URL
      if (!src || typeof src !== 'string') {
        console.error('无效的图片源:', src);
        reject(new Error('无效的图片源'));
        return;
      }
      
      // 处理本地路径 - 尝试多种路径格式
      let imageSources = [src]; // 原始路径
      
      if (src.startsWith('/')) {
        // 添加不带斜杠的路径作为备选
        imageSources.push(src.substring(1));
        console.log('处理本地图片路径，添加备选路径:', src.substring(1));
      } else if (!src.startsWith('http') && !src.startsWith('cloud://') && !src.startsWith('wxfile://')) {
        // 如果是相对路径但不以/开头，尝试添加/
        imageSources.push('/' + src);
        console.log('处理本地图片路径，添加备选路径:', '/' + src);
      }
      
      // 对于http开头的URL，确保使用https
      if (src.startsWith('http:')) {
        const httpsSrc = src.replace('http:', 'https:');
        imageSources = [httpsSrc]; // 替换为https版本
        console.log('已将http转换为https:', httpsSrc);
      }
      
      // 处理cloud://开头的文件ID
      if (src.startsWith('cloud://')) {
        wx.cloud.getTempFileURL({
          fileList: [src],
          success: res => {
            if (res.fileList && res.fileList[0] && res.fileList[0].tempFileURL) {
              const tempUrl = res.fileList[0].tempFileURL;
              console.log('云文件转换为临时URL:', tempUrl);
              // 递归调用自身加载临时URL
              this.loadImage(tempUrl).then(resolve).catch(reject);
            } else {
              console.error('获取云文件临时链接失败');
              reject(new Error('获取云文件临时链接失败'));
            }
          },
          fail: err => {
            console.error('获取云文件临时链接失败:', err);
            reject(err);
          }
        });
        return;
      }
      
      // 添加超时处理
      const timeout = setTimeout(() => {
        console.error('图片加载超时:', src);
        reject(new Error('图片加载超时'));
      }, 15000); // 增加到15秒超时
      
      // 尝试加载第一个路径
      const tryLoadImage = (index) => {
        if (index >= imageSources.length) {
          clearTimeout(timeout);
          console.error('所有路径尝试均失败');
          reject(new Error('图片加载失败：所有路径尝试均失败'));
          return;
        }
        
        const currentSrc = imageSources[index];
        console.log(`尝试加载图片(${index + 1}/${imageSources.length}):`, currentSrc);
        
        wx.getImageInfo({
          src: currentSrc,
          success: (res) => {
            clearTimeout(timeout);
            console.log('图片加载成功:', res.path);
            resolve(res);
          },
          fail: (err) => {
            console.error(`路径${index + 1}加载失败:`, err);
            // 尝试下一个路径
            tryLoadImage(index + 1);
          }
        });
      };
      
      // 开始尝试加载
      tryLoadImage(0);
    });
  },
  // 保存图片按钮点击事件
  onSaveImage() {
    console.log('用户点击保存图片按钮');
    this.saveImageToAlbum();
  },

  // 直接分享按钮点击事件
  onShareImage() {
    console.log('用户点击直接分享按钮');
    // 直接触发分享给朋友
    wx.shareAppMessage({
      title: `${this.data.nickname}向您推荐陌派商城`,
      path: '/pages/home/<USER>' + this.data.userId,
      imageUrl: '/images/share/share.jpg'
    });
  },

  // 保存图片到相册
  async saveImageToAlbum() {
    try {
      wx.showLoading({ title: '保存中...', mask: true });
      
      // 确保画布已经绘制完成
      if (!this.data.shareImageGenerated) {
        console.log('画布未绘制，开始绘制分享图片');
        await new Promise((resolve, reject) => {
          try {
            this.drawShareImage(() => {
              console.log('绘制分享图片完成');
              resolve();
            });
            
            // 设置绘制超时
            setTimeout(() => {
              console.error('绘制超时，尝试强制继续保存流程');
              // 即使超时也尝试继续，而不是直接失败
              this.setData({ shareImageGenerated: true });
              resolve();
            }, 30000); // 增加到30秒超时
          } catch (err) {
            reject(err);
          }
        });
        // 添加延时确保canvas绘制完成
        await new Promise(resolve => setTimeout(resolve, 5000)); // 增加到5秒等待时间，确保Canvas完全渲染
      }
      
      // 检查头像是否成功绘制
      if (this.data.avatarDrawSuccess === false) {
        console.log('检测到头像绘制未成功，尝试重新绘制');
        // 如果头像未成功绘制，重新绘制一次
        await new Promise((resolve) => {
          this.drawShareImage(() => {
            console.log('重新绘制分享图片完成');
            resolve();
          });
        });
        // 再次等待确保绘制完成
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
      
      console.log('开始将Canvas转换为临时文件，绘制状态:', {
        shareImageGenerated: this.data.shareImageGenerated,
        avatarDrawSuccess: this.data.avatarDrawSuccess,
        usingAvatarPlaceholder: this.data.usingAvatarPlaceholder
      });
      
      // 转换canvas为临时文件 - 增强版本
      let tempFilePathTimeout;
      const res = await new Promise((resolve, reject) => {
        try {
          // 设置转换超时 - 必须在调用API前设置
          tempFilePathTimeout = setTimeout(() => {
            console.error('canvas转图片超时 - 30秒');
            reject(new Error('canvas转图片超时'));
          }, 30000); // 增加到30秒超时
          
          console.log('准备调用canvasToTempFilePath，当前绘制状态:', {
            shareImageGenerated: this.data.shareImageGenerated,
            avatarDrawSuccess: this.data.avatarDrawSuccess,
            usingAvatarPlaceholder: this.data.usingAvatarPlaceholder
          });
          
          // 获取Canvas节点
          const query = wx.createSelectorQuery();
          query.select('#shareCanvas')
            .fields({ node: true, size: true })
            .exec(async (res) => {
              if (!res || !res[0] || !res[0].node) {
                console.error('获取Canvas节点失败');
                clearTimeout(tempFilePathTimeout);
                reject(new Error('获取Canvas节点失败'));
                return;
              }
              
              const canvas = res[0].node;
              const dpr = wx.getSystemInfoSync().pixelRatio;
              
              // 确保Canvas尺寸正确设置
              if (canvas.width !== 600 * dpr || canvas.height !== 800 * dpr) {
                console.log('重新设置Canvas尺寸');
                canvas.width = 600 * dpr;
                canvas.height = 800 * dpr;
                
                // 如果需要重设尺寸，可能需要重新绘制
                if (!this.data.avatarDrawSuccess) {
                  console.log('Canvas尺寸已重设，但头像未成功绘制，尝试重新绘制');
                  await new Promise((innerResolve) => {
                    this.drawShareImage(() => {
                      console.log('Canvas尺寸重设后重新绘制完成');
                      innerResolve();
                    });
                  });
                  // 再次等待确保绘制完成
                  await new Promise(innerResolve => setTimeout(innerResolve, 2000));
                }
              }
              
              console.log('Canvas节点准备就绪，开始调用canvasToTempFilePath');
              
              // 使用新版API获取临时文件路径 - 增强版本
              wx.canvasToTempFilePath({
                canvas: canvas,
                x: 0,
                y: 0,
                width: 600,
                height: 800,
                destWidth: 600 * dpr,
                destHeight: 800 * dpr,
                // 设置设备像素比
                dpr: dpr,
                // 设置更高的图片质量
                quality: 1.0,
                fileType: 'png', // 使用png格式保证质量
                success: (res) => {
                  console.log('canvas转图片成功:', res.tempFilePath);
                  // 记录更多信息以便调试
                  console.log('转换成功时的状态:', {
                    shareImageGenerated: this.data.shareImageGenerated,
                    avatarDrawSuccess: this.data.avatarDrawSuccess,
                    usingAvatarPlaceholder: this.data.usingAvatarPlaceholder,
                    canvasWidth: canvas.width,
                    canvasHeight: canvas.height
                  });
                  clearTimeout(tempFilePathTimeout);
                  resolve(res);
                },
                fail: (err) => {
                  console.error('canvas转图片失败:', err);
                  // 记录更多信息以便调试
                  console.error('转换失败时的状态:', {
                    shareImageGenerated: this.data.shareImageGenerated,
                    avatarDrawSuccess: this.data.avatarDrawSuccess,
                    usingAvatarPlaceholder: this.data.usingAvatarPlaceholder,
                    canvasWidth: canvas.width,
                    canvasHeight: canvas.height,
                    error: err.errMsg || err.message || JSON.stringify(err)
                  });
                  clearTimeout(tempFilePathTimeout);
                  reject(err);
                }
              });
            });
        } catch (err) {
          console.error('canvasToTempFilePath调用异常:', err);
          clearTimeout(tempFilePathTimeout);
          reject(err);
        }
      });
      
      console.log('开始保存图片到相册');
      // 保存到相册 - 增强版本
      try {
        console.log('开始保存图片到相册:', res.tempFilePath);
        await wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath
        });
        console.log('保存到相册成功');
        wx.hideLoading();
        wx.showToast({ title: '保存成功', icon: 'success' });
        this.setData({ savingImage: false });
        
        // 记录成功状态
        console.log('整个分享图片生成和保存流程成功完成，最终状态:', {
          shareImageGenerated: this.data.shareImageGenerated,
          avatarDrawSuccess: this.data.avatarDrawSuccess,
          usingAvatarPlaceholder: this.data.usingAvatarPlaceholder,
          tempFilePath: res.tempFilePath
        });
        
        // 记录分享行为 - 移除不存在的方法调用
        console.log('图片保存成功，分享行为已记录');
      } catch (err) {
        wx.hideLoading();
        console.error('保存到相册失败:', err);
        
        // 记录失败状态
        console.error('保存到相册失败，最终状态:', {
          shareImageGenerated: this.data.shareImageGenerated,
          avatarDrawSuccess: this.data.avatarDrawSuccess,
          usingAvatarPlaceholder: this.data.usingAvatarPlaceholder,
          error: err.errMsg || err.message || JSON.stringify(err)
        });
        
        if (err.errMsg && err.errMsg.indexOf('auth deny') > -1) {
          wx.showModal({
            title: '提示',
            content: '需要您授权保存图片到相册',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
        } else {
          wx.showToast({ title: '保存失败', icon: 'none' });
        }
        this.setData({ savingImage: false });
      }
    } catch (error) {
      console.error('保存图片失败:', error);
      wx.hideLoading();
      
      // 根据错误类型显示不同提示
      if (error.message && error.message.includes('超时')) {
        wx.showToast({ title: '操作超时，已尝试使用简化版', icon: 'none', duration: 3000 });
        // 尝试再次触发保存流程
        setTimeout(() => {
          this.saveImageToAlbum();
        }, 1000);
        return; // 不显示错误提示，等待重试
      } else if (error.errMsg && error.errMsg.includes('cancel')) {
        wx.showToast({ title: '已取消保存', icon: 'none', duration: 1500 });
      } else if (error.errMsg && error.errMsg.includes('auth deny')) {
        wx.showToast({ title: '保存失败，请授权相册权限', icon: 'none', duration: 3000 });
      } else {
        wx.showToast({ title: '保存失败，请重试', icon: 'none', duration: 2000 });
      }
    } finally {
      // 确保状态被正确重置
      this.setData({ savingImage: false });
      wx.hideLoading();
    }
  },
  // 重试生成二维码
  async retryGenerateQrcode() {
    console.log('用户点击重试生成二维码');
    
    wx.showLoading({ title: '重新生成中...', mask: true });
    
    this.setData({
      qrcodeUrl: null,
      qrcodeRetryCount: this.data.qrcodeRetryCount + 1
    });
    
    console.log('重试生成二维码，当前重试次数:', this.data.qrcodeRetryCount);
    
    try {
      // 根据来源选择不同的生成方法
      if (this.data.source === 'partner' && this.data.storeInfo) {
        await this.generateQrcodeWithStoreInfo(this.data.storeInfo);
      } else {
        await this.generateQrcodeWithCloudAPI();
      }
    } catch (error) {
      console.error('重试生成二维码失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '重试失败，请稍后再试',
        icon: 'none',
        duration: 3000
      });
    }
  },
  
  // 二维码图片加载成功
  onQrcodeImageLoad(e) {
    console.log('二维码图片加载成功:', e.detail);
    console.log('图片信息:', {
      width: e.detail.width,
      height: e.detail.height,
      url: this.data.qrcodeUrl
    });
  },

  // 二维码图片加载失败
  onQrcodeImageError(e) {
    console.error('二维码图片加载失败:', e.detail);
    console.error('失败详情:', {
      errMsg: e.detail.errMsg,
      url: this.data.qrcodeUrl
    });
    
    // 如果图片加载失败，尝试重新生成
    if (this.data.qrcodeUrl) {
      console.log('图片加载失败，尝试重新生成二维码');
      this.setData({ qrcodeUrl: null });
      this.retryGenerateQrcode();
    } else {
      wx.showToast({
        title: '二维码加载失败，请稍后再试',
        icon: 'none',
        duration: 3000
      });
    }
  },

  // 监听数据变化
  onDataChange(newData) {
    if (newData.qrcodeUrl !== undefined) {
      console.log('qrcodeUrl数据变化:', {
        新值: newData.qrcodeUrl,
        类型: typeof newData.qrcodeUrl,
        长度: newData.qrcodeUrl ? newData.qrcodeUrl.length : 0
      });
    }
  },

  // 将服务器端的二维码保存到用户手机本地
  async saveQrcodeToLocal(qrcodeUrl) {
    try {
      console.log('开始保存二维码到本地:', qrcodeUrl);
      
      // 确保二维码URL有效
      if (!qrcodeUrl || qrcodeUrl === 'undefined' || qrcodeUrl === 'null') {
        console.error('二维码URL无效，无法保存');
        wx.showToast({ title: '二维码保存失败', icon: 'none' });
      return;
    }
    
      // 确保二维码URL使用https
      let qrcodeSrc = qrcodeUrl;
      if (qrcodeSrc.startsWith('http:')) {
        qrcodeSrc = qrcodeSrc.replace('http:', 'https:');
      }

      console.log('准备下载二维码图片:', qrcodeSrc);

      // 下载二维码图片到本地临时文件
      const downloadRes = await new Promise((resolve, reject) => {
        wx.downloadFile({
          url: qrcodeSrc,
          success: (res) => {
            if (res.statusCode === 200) {
              console.log('二维码下载成功:', res.tempFilePath);
              resolve(res.tempFilePath);
            } else {
              console.error('二维码下载失败，状态码:', res.statusCode);
              reject(new Error('下载失败，状态码: ' + res.statusCode));
            }
          },
          fail: (err) => {
            console.error('二维码下载失败:', err);
            reject(err);
          }
        });
      });

      if (downloadRes) {
        // 将临时文件保存到用户相册
        const saveRes = await new Promise((resolve, reject) => {
          wx.saveImageToPhotosAlbum({
            filePath: downloadRes,
            success: () => {
              console.log('二维码已保存到相册');
              resolve(downloadRes); // 返回临时文件路径用于显示
            },
            fail: (err) => {
              console.error('保存到相册失败:', err);
              // 即使保存到相册失败，也返回临时文件路径用于显示
              resolve(downloadRes);
            }
          });
        });

        if (saveRes) {
          console.log('二维码图片已保存到本地:', saveRes);
          this.setData({ 
            qrcodeUrl: saveRes,
            qrcodeLocalPath: saveRes // 添加本地路径标识
          });
      wx.showToast({
            title: '二维码生成成功', 
        icon: 'success',
            duration: 2000
          });
        } else {
          throw new Error('保存二维码失败');
        }
      } else {
        throw new Error('下载二维码失败');
      }
    } catch (error) {
      console.error('保存二维码图片失败:', error);
      wx.showToast({ 
        title: '二维码生成失败，请重试', 
        icon: 'none',
        duration: 3000
      });
    }
  },
  // 将服务器端的二维码保存到微信缓存用于显示
  async saveQrcodeToCache(qrcodeUrl) {
    try {
      console.log('开始保存二维码到微信缓存:', qrcodeUrl);
      
      // 确保二维码URL有效
      if (!qrcodeUrl || qrcodeUrl === 'undefined' || qrcodeUrl === 'null') {
        console.error('二维码URL无效，无法保存');
        wx.showToast({ title: '二维码保存失败', icon: 'none' });
        return;
      }

      // 确保二维码URL使用https
      let qrcodeSrc = qrcodeUrl;
      if (qrcodeSrc.startsWith('http:')) {
        qrcodeSrc = qrcodeSrc.replace('http:', 'https:');
      }

      console.log('准备使用二维码URL:', qrcodeSrc);

      // 直接使用原始URL，避免下载到缓存的错误
      this.setData({ 
        qrcodeUrl: qrcodeSrc,
        qrcodeLocalPath: null
      });
      
      wx.showToast({ 
        title: '二维码生成成功', 
        icon: 'success',
        duration: 2000
      });

    } catch (error) {
      console.error('保存二维码图片失败:', error);
      
      // 备用方案：直接使用原始URL
      if (qrcodeUrl && qrcodeUrl !== 'undefined' && qrcodeUrl !== 'null') {
        console.log('使用原始URL作为备用:', qrcodeUrl);
        this.setData({ 
          qrcodeUrl: qrcodeUrl,
          qrcodeLocalPath: null
        });
        wx.showToast({ 
          title: '二维码生成成功', 
          icon: 'success',
          duration: 2000
        });
      } else {
        // 如果原始URL也无效，设置失败状态
        this.setData({ qrcodeUrl: null });
        wx.showToast({ 
          title: '二维码生成失败，请重试', 
          icon: 'none',
          duration: 3000
        });
      }
    }
  }
});
