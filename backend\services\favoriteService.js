/**
 * 收藏服务
 */
const Favorite = require('../models/Favorite');
const Product = require('../models/Product');

class FavoriteService {
  async getFavorites(userId) {
    try {
      const favorites = await Favorite.findAll(userId);
      
      // 处理收藏数据
      const processedItems = favorites.map(item => {
        // 如果images是JSON字符串，转换为数组
        if (item.images && typeof item.images === 'string') {
          try {
            item.images = JSON.parse(item.images);
            // 只取第一张图片
            item.image = item.images.length > 0 ? item.images[0] : '';
          } catch (e) {
            item.images = [];
            item.image = '';
          }
        }
        
        return item;
      });
      
      return {
        success: true,
        data: processedItems,
        message: '获取收藏列表成功'
      };
    } catch (error) {
      console.error('获取收藏列表失败:', error);
      throw error;
    }
  }
  
  async addToFavorites(userId, productId) {
    try {
      // 检查商品是否存在
      const product = await Product.findById(productId);
      if (!product) {
        return {
          success: false,
          message: '商品不存在'
        };
      }
      
      // 检查是否已经收藏
      const existingFavorite = await Favorite.findByUserAndProduct(userId, productId);
      
      if (existingFavorite) {
        return {
          success: false,
          message: '该商品已在收藏夹中'
        };
      }
      
      // 添加收藏
      const favoriteId = 'favorite_' + userId + '_' + productId;
      const newFavorite = await Favorite.create({
        id: favoriteId,
        userId,
        productId,
        name: product.name || '',
        price: product.price != null ? product.price : 0,
        image: (product.images && Array.isArray(product.images) && product.images[0]) ? product.images[0] : (product.image || '')
      });
      
      return {
        success: true,
        data: newFavorite,
        message: '添加到收藏夹成功'
      };
    } catch (error) {
      console.error('添加到收藏夹失败:', error);
      throw new Error('添加到收藏夹失败，请稍后再试');
    }
  }
  
  async removeFromFavorites(userId, productId) {
    try {
      const result = await Favorite.deleteByUserAndProduct(userId, productId);
      
      if (!result) {
        return {
          success: false,
          message: '收藏记录不存在'
        };
      }
      
      return {
        success: true,
        message: '已从收藏夹移除'
      };
    } catch (error) {
      console.error('从收藏夹移除失败:', error);
      throw error;
    }
  }
  
  async checkFavoriteStatus(userId, productId) {
    try {
      const favorite = await Favorite.findByUserAndProduct(userId, productId);
      
      return {
        success: true,
        data: { isFavorited: !!favorite },
        message: '获取收藏状态成功'
      };
    } catch (error) {
      console.error('获取收藏状态失败:', error);
      throw error;
    }
  }
  
  async getFavoriteCount(userId) {
    try {
      const count = await Favorite.count(userId);
      
      return {
        success: true,
        data: { count },
        message: '获取收藏数量成功'
      };
    } catch (error) {
      console.error('获取收藏数量失败:', error);
      throw error;
    }
  }
}

module.exports = new FavoriteService(); 