const db = require('../config/db');
const User = require('../models/User');
const Cart = require('../models/Cart');
const { CustomerOrder, CustomerSubOrder, CustomerOrderItem } = require('../models/CustomerOrder');

/**
 * 安全解析支付方式JSON
 */
function parsePaymentMethods(paymentMethodsStr) {
  if (!paymentMethodsStr) {
    return [];
  }
  
  try {
    // 如果已经是数组，直接返回
    if (Array.isArray(paymentMethodsStr)) {
      return paymentMethodsStr;
    }
    
    // 尝试解析JSON
    const parsed = JSON.parse(paymentMethodsStr);
    return Array.isArray(parsed) ? parsed : [parsed];
  } catch (error) {
    console.warn('支付方式JSON解析失败:', paymentMethodsStr, error.message);
    // 如果解析失败，返回默认值
    return ['微信支付'];
  }
}

/**
 * 获取支付方式文本
 */
function getPaymentMethodText(paymentMethodsStr) {
  const methods = parsePaymentMethods(paymentMethodsStr);
  return methods.length > 0 ? methods.join(', ') : '微信支付';
}

/**
 * 创建订单（普通/采购/移库）
 * POST /api/orders/create
 * body: { user_id, product_id, quantity, type, store_no, total_amount, ... }
 */
exports.createOrder = async (req, res) => {
  try {
    const { user_id, items, address_id, delivery_method, store_id, payment_methods } = req.body;
    
    if (!user_id || !items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ success: false, message: '参数错误' });
    }
    
    // 验证用户是否存在
    const userRows = await db.query('SELECT * FROM users WHERE user_id = ?', [user_id]);
    if (userRows.length === 0) {
      return res.status(404).json({ success: false, message: '用户不存在' });
    }
    const user = userRows[0];
    
    // 验证销售人是否存在（如果有的话）
    if (user.salesman_id) {
      const salesmanRows = await db.query('SELECT * FROM users WHERE user_id = ?', [user.salesman_id]);
      if (salesmanRows.length === 0) {
        console.warn('销售人不存在:', user.salesman_id);
      }
    }
    
    // 创建订单逻辑...
    // 这里省略具体的订单创建逻辑，因为需要根据实际的订单表结构来实现
    
    res.json({ success: true, message: '订单创建成功' });
  } catch (error) {
    console.error('创建购物车订单失败:', error);
    
    // 根据错误类型返回不同的错误信息
    let errorMessage = '创建订单失败，请稍后重试';
    
    if (error.message) {
      if (error.message.includes('库存不足')) {
        errorMessage = error.message;
      } else if (error.message.includes('用户不存在')) {
        errorMessage = '用户信息异常，请重新登录';
      } else if (error.message.includes('数据库')) {
        errorMessage = '系统繁忙，请稍后重试';
      }
    }
    
    res.status(500).json({ success: false, message: errorMessage });
  }
};

/**
 * 创建购物车订单
 * POST /api/orders/create
 * body: { cartItemIds, deliveryMethod, addressId, paymentMethods, totalAmount }
 */
exports.createCartOrder = async (req, res) => {
  try {
    console.log('接收到创建订单请求:', req.body);
    const { cartItemIds, deliveryMethod, addressId, storeId, paymentMethods } = req.body;
    const userId = req.userData && req.userData.userId;
    let totalAmount = 0; // 后端重新计算总金额，不依赖前端传递

    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }

    if (!cartItemIds || !Array.isArray(cartItemIds) || cartItemIds.length === 0) {
      return res.status(400).json({ success: false, message: '购物车商品不能为空' });
    }

    console.log('查询购物车商品, userId:', userId, 'cartItemIds:', cartItemIds);
    
    // 验证cartItemIds格式
    if (!Array.isArray(cartItemIds)) {
      console.error('cartItemIds不是数组:', cartItemIds);
      return res.status(400).json({ success: false, message: '购物车商品ID格式错误' });
    }
    
    // 过滤无效的ID
    const validCartItemIds = cartItemIds.filter(id => id && (typeof id === 'string' || typeof id === 'number'));
    if (validCartItemIds.length === 0) {
      console.error('没有有效的购物车商品ID:', cartItemIds);
      return res.status(400).json({ success: false, message: '购物车商品ID无效' });
    }
    
    // 获取购物车商品信息
    let cartItems;
    try {
      cartItems = await Cart.findByIds(userId, validCartItemIds);
      console.log('查询到的购物车商品:', cartItems);
    } catch (error) {
      console.error('查询购物车商品失败:', error);
      return res.status(500).json({ success: false, message: '查询购物车商品失败，请稍后重试' });
    }

    if (cartItems.length === 0) {
      console.error('未找到任何购物车商品, userId:', userId, 'cartItemIds:', validCartItemIds);
      return res.status(400).json({ success: false, message: '购物车商品不存在或已被删除' });
    }
    
    // 验证查询到的商品数据完整性
    const validCartItems = cartItems.filter(item => {
      if (!item.productId || !item.price || !item.quantity || item.quantity <= 0) {
        console.warn('发现无效的购物车商品数据:', item);
        return false;
      }
      return true;
    });
    
    if (validCartItems.length === 0) {
      console.error('所有购物车商品数据都无效');
      return res.status(400).json({ success: false, message: '购物车商品数据异常' });
    }
    
    if (validCartItems.length !== cartItems.length) {
      console.warn(`过滤了${cartItems.length - validCartItems.length}个无效商品`);
    }
    
    // 重新计算总金额，确保数据准确性
    totalAmount = validCartItems.reduce((sum, item) => {
      const itemTotal = parseFloat(item.price) * parseInt(item.quantity);
      return sum + itemTotal;
    }, 0);
    
    console.log('重新计算的总金额:', totalAmount);
    
    if (totalAmount <= 0) {
      console.error('计算的总金额无效:', totalAmount);
      return res.status(400).json({ success: false, message: '订单金额计算错误' });
    }

    // 获取用户信息，确定订单归属门店
    const User = require('../models/User');
    // 修复：使用findByUserId而不是findById，因为userId是业务ID（user_id）而不是自增ID
    const user = await User.findByUserId(userId);
    if (!user) {
      return res.status(404).json({ success: false, message: '用户不存在' });
    }

    console.log('用户信息:', {
      userId,
      salesman_id: user.salesman_id,
      subscribe_store_no: user.subscribe_store_no
    });

    // 生成订单号（购物车订单）
    const orderNo = generateOrderNo();
    console.log('生成订单号:', orderNo);

    // 确定支付方式和初始状态
    let initialStatus = '待支付';
    let shouldProcessPayment = false;
    let paymentMethod = null;

    // 检查支付方式，如果启用了余额支付，则尝试直接支付
    if (paymentMethods && paymentMethods.includes('balance')) {
      shouldProcessPayment = true;
      paymentMethod = 'balance'; // 使用余额支付
    }

    // 按业务逻辑进行订单拆分
    const subOrders = await splitOrderByStock(validCartItems, user);
    console.log('订单拆分结果:', subOrders);

    if (subOrders.length === 0) {
      return res.status(400).json({ success: false, message: '库存不足，无法创建订单' });
    }

    // 创建主订单记录（使用新的顾客订单表）
    const mainOrderData = {
      order_no: orderNo,
      user_id: userId,
      salesman_id: user.salesman_id,
      delivery_method: deliveryMethod,
      address_id: addressId || null,
      store_id: storeId || null,
      payment_methods: paymentMethods,
      total_amount: totalAmount,
      total_quantity: validCartItems.reduce((sum, item) => sum + item.quantity, 0),
      sub_order_count: subOrders.length,
      status: initialStatus
    };

    console.log('创建主订单数据:', mainOrderData);

    // 插入主订单记录
    const mainOrderResult = await CustomerOrder.create(mainOrderData);
    const mainOrderId = mainOrderResult.id;
    console.log('主订单创建成功, mainOrderId:', mainOrderId);

    // 创建子订单和商品明细记录
    const subOrderIds = [];
    for (let i = 0; i < subOrders.length; i++) {
      const subOrder = subOrders[i];
      const subOrderNo = `${orderNo}-${String(i + 1).padStart(2, '0')}`;

      // 确定门店类型
      let storeType = 'platform';
      if (subOrder.type === 'subscribe_store') {
        storeType = 'subscribe';
      } else if (subOrder.type === 'salesman_store') {
        storeType = 'salesman';
      }

      // 创建子订单
      const subOrderData = {
        main_order_id: mainOrderId,
        sub_order_no: subOrderNo,
        store_no: subOrder.store_no,
        store_type: storeType,
        sub_total_amount: subOrder.total_amount,
        sub_total_quantity: subOrder.items.reduce((sum, item) => sum + item.quantity, 0),
        status: initialStatus
      };

      const subOrderResult = await CustomerSubOrder.create(subOrderData);
      const subOrderId = subOrderResult.id;
      subOrderIds.push(subOrderId);

      console.log(`子订单创建成功: ${subOrderNo}, 门店: ${subOrder.store_no}, 类型: ${storeType}`);

      // 创建商品明细
      for (const item of subOrder.items) {
        const itemData = {
          main_order_id: mainOrderId,
          sub_order_id: subOrderId,
          product_id: item.productId,
          product_name: item.name || '商品名称',
          product_image: item.image || '',
          product_price: item.price,
          quantity: item.quantity,
          subtotal: item.amount
        };

        await CustomerOrderItem.create(itemData);
        console.log(`商品明细创建成功: 商品${item.productId}, 数量${item.quantity}`);
      }
    }

    // 扣减库存（按子订单结果）
    for (const subOrder of subOrders) {
      for (const item of subOrder.items) {
        await db.query(
          'UPDATE store_cloud_stock SET quantity = quantity - ? WHERE store_no = ? AND product_id = ?',
          [item.quantity, subOrder.store_no, item.productId]
        );
        console.log(`扣减库存: 门店${subOrder.store_no}, 商品${item.productId}, 数量${item.quantity}`);
      }
    }

    // 如果需要处理支付，则立即进行支付处理
    if (shouldProcessPayment && paymentMethod) {
      console.log('开始处理订单支付, 支付方式:', paymentMethod, 'mainOrderId:', mainOrderId);

      try {
        // 调用支付处理逻辑（使用主订单ID和总金额）
        const paymentResult = await processOrderPayment(mainOrderId, userId, totalAmount, paymentMethod);

        if (paymentResult.success) {
          console.log('订单支付成功');
          initialStatus = '待发货';

          // 更新主订单状态
          await CustomerOrder.updateStatus(mainOrderId, initialStatus, { paid_at: Date.now() });

          // 更新所有子订单状态
          await CustomerSubOrder.updateStatusByMainOrderId(mainOrderId, initialStatus);
        } else {
          console.log('订单支付失败，保持待支付状态:', paymentResult.message);
          // 支付失败时回滚库存
          for (const subOrder of subOrders) {
            for (const item of subOrder.items) {
              await db.query(
                'UPDATE store_cloud_stock SET quantity = quantity + ? WHERE store_no = ? AND product_id = ?',
                [item.quantity, subOrder.store_no, item.productId]
              );
            }
          }
          return res.status(400).json({
            success: false,
            message: paymentResult.message || '支付失败'
          });
        }
      } catch (paymentError) {
        console.error('订单支付处理失败:', paymentError);
        // 支付失败时回滚库存
        for (const subOrder of subOrders) {
          for (const item of subOrder.items) {
            await db.query(
              'UPDATE store_cloud_stock SET quantity = quantity + ? WHERE store_no = ? AND product_id = ?',
              [item.quantity, subOrder.store_no, item.productId]
            );
          }
        }
        return res.status(500).json({
          success: false,
          message: '支付处理失败: ' + paymentError.message
        });
      }
    }

    // 删除已结算的购物车商品
    console.log('删除购物车商品...');
    for (const cartItemId of cartItemIds) {
      await Cart.remove(cartItemId);
    }

    // 获取完整的订单详情
    const orderDetail = await CustomerOrder.getOrderDetail(mainOrderId);

    res.json({
      success: true,
      data: {
        orderId: mainOrderId,  // 前端期望的字段名
        mainOrderId: mainOrderId,
        orderNo: orderNo,
        subOrderIds: subOrderIds,
        subOrderCount: subOrders.length,
        status: initialStatus,
        paid: initialStatus === '待发货',
        orderDetail: orderDetail,
        splitSummary: subOrders.map((subOrder, index) => ({
          store_no: subOrder.store_no,
          store_type: subOrder.type,
          items_count: subOrder.items.length,
          total_amount: subOrder.total_amount,
          total_quantity: subOrder.items.reduce((sum, item) => sum + item.quantity, 0)
        }))
      },
      message: initialStatus === '待发货' ? '订单创建并支付成功' : '订单创建成功'
    });

  } catch (error) {
    console.error('创建购物车订单失败:', error);
    
    // 根据错误类型返回不同的错误信息
    let errorMessage = '创建订单失败';
    
    if (error.message) {
      if (error.message.includes('库存不足')) {
        errorMessage = error.message;
      } else if (error.message.includes('用户不存在')) {
        errorMessage = '用户信息异常，请重新登录';
      } else if (error.message.includes('数据库')) {
        errorMessage = '系统繁忙，请稍后重试';
      }
    }
    
    res.status(500).json({ success: false, message: errorMessage });
  }
};

// 生成顾客订单号
// 顾客订单编号规则：字母"XS"+年月日时分（12位数）+顺序号（4位数）
function generateOrderNo() {
  const now = new Date();

  // 格式化年月日时分：YYYYMMDDHHMM
  const year = now.getFullYear().toString();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hour = now.getHours().toString().padStart(2, '0');
  const minute = now.getMinutes().toString().padStart(2, '0');
  const dateTimeStr = year + month + day + hour + minute; // 12位数

  // 生成4位顺序号（基于毫秒数的后4位）
  const sequence = (now.getMilliseconds() + Math.floor(Math.random() * 1000)).toString().slice(-4).padStart(4, '0');

  return `XS${dateTimeStr}${sequence}`;
}

// 处理订单支付
async function processOrderPayment(orderId, userId, orderAmount, paymentMethod) {
  try {
    console.log('处理订单支付:', { orderId, userId, orderAmount, paymentMethod });

    if (paymentMethod === 'balance') {
      // 余额支付：使用余额支付

      // 查询用户余额
      const balanceQuery = 'SELECT account_balance FROM user_fund_accounts WHERE user_id = ?';
      const balanceResult = await db.query(balanceQuery, [userId]);

      let balance = 0;
      if (balanceResult.length === 0) {
        // 创建用户资金账户
        const now = Date.now();
        await db.query(
          'INSERT INTO user_fund_accounts (user_id, account_balance, pending_commission, total_commission, total_withdrawal, total_dividend, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
          [userId, 0.00, 0.00, 0.00, 0.00, 0.00, now, now]
        );
      } else {
        balance = parseFloat(balanceResult[0].account_balance || 0);
      }

      console.log('用户余额:', balance, '订单金额:', orderAmount);

      if (balance >= orderAmount) {
        // 余额充足，使用余额支付
        console.log('余额充足，使用余额支付');

        // 开始事务
        const connection = await db.getConnection();
        try {
          await connection.beginTransaction();

          // 扣减余额
          await connection.execute('UPDATE user_fund_accounts SET account_balance = account_balance - ?, updated_at = ? WHERE user_id = ?',
            [orderAmount, Date.now(), userId]);

          // 添加资金变动记录
          await connection.execute(
            'INSERT INTO user_fund_records (user_id, type, amount, balance, description, order_id, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
            [userId, 'payment', -orderAmount, balance - orderAmount, '订单支付（余额）', orderId, 'completed', Date.now()]
          );

          // 提交事务
          await connection.commit();

          return {
            success: true,
            message: '余额支付成功',
            paymentMethod: 'balance',
            balanceUsed: orderAmount,
            remainingBalance: balance - orderAmount
          };
        } catch (error) {
          // 回滚事务
          await connection.rollback();
          console.error('余额支付失败:', error);
          return {
            success: false,
            message: '支付处理失败',
            error: error.message
          };
        } finally {
          connection.release();
        }
      } else {
        // 余额不足
        console.log('余额不足，无法完成支付');
        return {
          success: false,
          message: '余额不足',
          balance: balance,
          required: orderAmount
        };
      }
    } else {
      return {
        success: false,
        message: '不支持的支付方式'
      };
    }
  } catch (error) {
    console.error('支付处理异常:', error);
    return {
      success: false,
      message: '支付处理异常',
      error: error.message
    };
  }
}

/**
 * 获取订单列表（顾客端）
 * GET /api/orders
 * query: { page, limit, status }
 * 注意：顾客端只显示顾客订单，不显示门店采购/移库订单
 */
exports.getOrders = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }

    const { page = 1, limit = 10, status } = req.query;
    const offset = (page - 1) * limit;

    console.log('顾客订单列表查询参数:', { userId, page, limit, status });

    // 状态映射（前端传入的状态码到数据库状态文本）
    const statusMap = {
      'pending_payment': '待支付',
      'pending_shipment': '待发货',
      'shipped': '待收货',
      'completed': '已完成',
      'cancelled': '已取消',
      'refund': '退款/售后'
    };

    // 构建查询条件
    let statusCondition = '';
    const queryParams = [userId];

    if (status && status !== 'all' && statusMap[status]) {
      statusCondition = ' AND status = ?';
      queryParams.push(statusMap[status]);
    }

    console.log('状态筛选:', status, '->', statusMap[status] || 'all');

    // 只查询顾客订单总数
    const countQuery = `SELECT COUNT(*) as total FROM customer_orders WHERE user_id = ?${statusCondition}`;
    const countResult = await db.query(countQuery, queryParams);
    const total = countResult[0].total;

    console.log('顾客订单总数:', total);

    // 只查询顾客订单列表
    const ordersQuery = `
      SELECT
        co.id,
        co.order_no,
        co.user_id,
        co.total_amount,
        co.total_quantity,
        co.status,
        co.delivery_method,
        co.created_at,
        co.updated_at
      FROM customer_orders co
      WHERE co.user_id = ?${statusCondition}
      ORDER BY co.created_at DESC
      LIMIT ? OFFSET ?
    `;

    // 执行查询
    const orders = await db.query(ordersQuery, [...queryParams, parseInt(limit), offset]);

    console.log('查询到的顾客订单数量:', orders.length);

    // 处理订单数据
    const orderList = await Promise.all(orders.map(async order => {
      // 查询顾客订单的商品明细
      const itemsQuery = `
        SELECT
          coi.product_id as id,
          coi.product_name as name,
          coi.product_image as image,
          coi.product_price as price,
          coi.quantity
        FROM customer_order_items coi
        WHERE coi.main_order_id = ?
      `;
      const items = await db.query(itemsQuery, [order.id]);

      // 状态文本映射（数据库状态文本到前端状态码）
      const statusCodeMap = {
        '待支付': 'pending_payment',
        '待发货': 'pending_shipment',
        '待收货': 'shipped',
        '已完成': 'completed',
        '已取消': 'cancelled',
        '退款/售后': 'refund'
      };

      return {
        id: order.id,
        order_no: order.order_no,
        status: statusCodeMap[order.status] || order.status,
        status_text: order.status,
        type: 'customer',
        total_amount: order.total_amount,
        total_quantity: order.total_quantity,
        created_at: formatDate(order.created_at),
        updated_at: formatDate(order.updated_at),
        items: items
      };
    }));

    // 打印最终返回的数据
    console.log('顾客订单列表API返回数据:', {
      total: total,
      count: orderList.length,
      page: parseInt(page),
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: {
        list: orderList,
        total: total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取顾客订单列表失败:', error);
    res.status(500).json({ success: false, message: '获取订单列表失败', error: error.message });
  }
};

/**
 * 取消订单（顾客端）
 * POST /api/orders/:id/cancel
 */
exports.cancelOrder = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }

    const orderId = req.params.id;
    if (!orderId) {
      return res.status(400).json({ success: false, message: '订单ID不能为空' });
    }

    // 只查询顾客订单
    const orderQuery = 'SELECT * FROM customer_orders WHERE id = ? AND user_id = ?';
    const orders = await db.query(orderQuery, [orderId, userId]);

    if (orders.length === 0) {
      return res.status(404).json({ success: false, message: '订单不存在' });
    }

    const order = orders[0];

    // 只有待支付的订单可以取消
    if (order.status !== '待支付') {
      return res.status(400).json({ success: false, message: '只有待支付的订单可以取消' });
    }

    // 更新订单状态为已取消
    await db.query('UPDATE customer_orders SET status = ?, updated_at = ? WHERE id = ?', ['已取消', Date.now(), orderId]);

    res.json({
      success: true,
      message: '订单已取消'
    });
  } catch (error) {
    console.error('取消订单失败:', error);
    res.status(500).json({ success: false, message: '取消订单失败', error: error.message });
  }
};

/**
 * 确认收货（顾客端）
 * POST /api/orders/:id/confirm
 */
exports.confirmReceipt = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }

    const orderId = req.params.id;
    if (!orderId) {
      return res.status(400).json({ success: false, message: '订单ID不能为空' });
    }

    // 只查询顾客订单
    const orderQuery = 'SELECT * FROM customer_orders WHERE id = ? AND user_id = ?';
    const orders = await db.query(orderQuery, [orderId, userId]);

    if (orders.length === 0) {
      return res.status(404).json({ success: false, message: '订单不存在' });
    }

    const order = orders[0];

    // 只有待收货的订单可以确认收货
    if (order.status !== '待收货') {
      return res.status(400).json({ success: false, message: '只有待收货的订单可以确认收货' });
    }

    // 更新订单状态为已完成
    await db.query('UPDATE customer_orders SET status = ?, updated_at = ?, completed_at = ? WHERE id = ?',
      ['已完成', Date.now(), Date.now(), orderId]);

    res.json({
      success: true,
      message: '确认收货成功'
    });
  } catch (error) {
    console.error('确认收货失败:', error);
    res.status(500).json({ success: false, message: '确认收货失败', error: error.message });
  }
};

/**
 * 申请退款
 * POST /api/orders/:id/refund
 */
exports.applyRefund = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }
    
    const orderId = req.params.id;
    if (!orderId) {
      return res.status(400).json({ success: false, message: '订单ID不能为空' });
    }
    
    const { reason, description, images } = req.body;
    if (!reason) {
      return res.status(400).json({ success: false, message: '退款原因不能为空' });
    }
    
    // 查询订单信息
    const orderQuery = 'SELECT * FROM customer_orders WHERE id = ? AND user_id = ?';
    const orders = await db.query(orderQuery, [orderId, userId]);
    
    if (orders.length === 0) {
      return res.status(404).json({ success: false, message: '订单不存在' });
    }
    
    const order = orders[0];
    
    // 只有待发货或待收货的订单可以申请退款
    if (order.status !== '待发货' && order.status !== '待收货') {
      return res.status(400).json({ success: false, message: '只有待发货或待收货的订单可以申请退款' });
    }
    
    // 创建退款记录
    const now = new Date();
    await db.query(
      'INSERT INTO order_refunds (order_id, user_id, reason, description, images, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [orderId, userId, reason, description || '', JSON.stringify(images || []), '处理中', now, now]
    );
    
    // 更新订单状态为退款/售后
    await db.query('UPDATE customer_orders SET status = ?, updated_at = ? WHERE id = ?', ['退款/售后', now, orderId]);
    
    res.json({
      success: true,
      message: '退款申请已提交'
    });
  } catch (error) {
    console.error('申请退款失败:', error);
    res.status(500).json({ success: false, message: '申请退款失败', error: error.message });
  }
};

/**
 * 查看退款进度
 * GET /api/orders/:id/refund
 */
exports.getRefundDetail = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }
    
    const orderId = req.params.id;
    if (!orderId) {
      return res.status(400).json({ success: false, message: '订单ID不能为空' });
    }
    
    // 查询退款记录
    const refundQuery = `
      SELECT 
        r.id, 
        r.order_id, 
        r.user_id, 
        r.reason, 
        r.description, 
        r.images, 
        r.status, 
        r.admin_remark,
        r.created_at, 
        r.updated_at,
        o.status as order_status
      FROM order_refunds r
      LEFT JOIN customer_orders o ON r.order_id = o.id
      WHERE r.order_id = ? AND r.user_id = ?
      ORDER BY r.created_at DESC
      LIMIT 1
    `;
    
    const refunds = await db.query(refundQuery, [orderId, userId]);
    
    if (refunds.length === 0) {
      return res.status(404).json({ success: false, message: '退款记录不存在' });
    }
    
    const refund = refunds[0];
    
    // 处理图片数据
    try {
      refund.images = JSON.parse(refund.images || '[]');
    } catch (e) {
      refund.images = [];
    }
    
    // 格式化日期
    refund.created_at = formatDate(refund.created_at);
    refund.updated_at = formatDate(refund.updated_at);
    
    res.json({
      success: true,
      data: refund
    });
  } catch (error) {
    console.error('获取退款进度失败:', error);
    res.status(500).json({ success: false, message: '获取退款进度失败', error: error.message });
  }
};

/**
 * 删除订单（顾客端）
 * DELETE /api/orders/:id
 */
exports.deleteOrder = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }

    const orderId = req.params.id;
    if (!orderId) {
      return res.status(400).json({ success: false, message: '订单ID不能为空' });
    }

    // 只查询顾客订单
    const orderQuery = 'SELECT * FROM customer_orders WHERE id = ? AND user_id = ?';
    const orders = await db.query(orderQuery, [orderId, userId]);

    if (orders.length === 0) {
      return res.status(404).json({ success: false, message: '订单不存在' });
    }

    const order = orders[0];

    // 只有已取消或已完成的订单可以删除
    if (order.status !== '已取消' && order.status !== '已完成') {
      return res.status(400).json({ success: false, message: '只有已取消或已完成的订单可以删除' });
    }

    // 删除顾客订单（会级联删除子订单和商品明细）
    await db.query('DELETE FROM customer_orders WHERE id = ?', [orderId]);

    res.json({
      success: true,
      message: '订单已删除'
    });
  } catch (error) {
    console.error('删除订单失败:', error);
    res.status(500).json({ success: false, message: '删除订单失败', error: error.message });
  }
};

/**
 * 获取订单详情
 * GET /api/orders/:id
 */
exports.getOrderDetail = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }

    const orderId = req.params.id;
    if (!orderId) {
      return res.status(400).json({ success: false, message: '订单ID不能为空' });
    }

    console.log('查询订单详情, orderId:', orderId, 'userId:', userId);

    // 先尝试查询顾客订单
    const customerOrderQuery = `
      SELECT
        co.id,
        co.order_no,
        co.user_id,
        co.salesman_id,
        co.delivery_method,
        co.address_id,
        co.store_id,
        co.payment_methods,
        co.total_amount,
        co.total_quantity,
        co.sub_order_count,
        co.status,
        co.paid_at,
        co.shipped_at,
        co.completed_at,
        co.created_at,
        co.updated_at,
        'customer' as order_source
      FROM customer_orders co
      WHERE co.id = ? AND co.user_id = ?
    `;

    const customerOrders = await db.query(customerOrderQuery, [orderId, userId]);

    if (customerOrders.length > 0) {
      // 顾客订单详情
      const order = customerOrders[0];
      
      // 查询子订单信息
      const subOrdersQuery = `
        SELECT
          cso.id,
          cso.sub_order_no,
          cso.store_no,
          cso.store_type,
          cso.sub_total_amount,
          cso.sub_total_quantity,
          cso.status,
          cso.shipped_at,
          cso.completed_at,
          cso.created_at,
          cso.updated_at
        FROM customer_sub_orders cso
        WHERE cso.main_order_id = ?
      `;
      const subOrders = await db.query(subOrdersQuery, [orderId]);

      // 查询商品明细
      const itemsQuery = `
        SELECT
          coi.id,
          coi.product_id,
          coi.product_name,
          coi.product_image,
          coi.product_price,
          coi.quantity,
          coi.subtotal,
          coi.created_at
        FROM customer_order_items coi
        WHERE coi.main_order_id = ?
      `;
      const items = await db.query(itemsQuery, [orderId]);

      // 状态映射
      const statusCodeMap = {
        '待支付': 'pending_payment',
        '待发货': 'pending_shipment',
        '待收货': 'shipped',
        '已完成': 'completed',
        '已取消': 'cancelled',
        '退款/售后': 'refund'
      };

      return res.json({
        success: true,
        data: {
        id: order.id,
        order_no: order.order_no,
        status: statusCodeMap[order.status] || order.status,
        status_text: order.status,
        type: 'customer',
        total_amount: order.total_amount,
        total_quantity: order.total_quantity,
          delivery_method: order.delivery_method,
        created_at: formatDate(order.created_at),
        updated_at: formatDate(order.updated_at),
          paid_at: order.paid_at ? formatDate(order.paid_at) : null,
          shipped_at: order.shipped_at ? formatDate(order.shipped_at) : null,
          completed_at: order.completed_at ? formatDate(order.completed_at) : null,
          sub_orders: subOrders,
          items: items
        }
      });
    }

    // 如果不是顾客订单，尝试查询门店订单
    const storeOrderQuery = `
      SELECT
        so.id,
        so.order_no,
        so.user_id,
        so.store_no,
        so.salesman_id,
        so.product_id,
        so.quantity,
        so.total_amount,
        so.status,
        so.type,
        so.created_at,
        so.updated_at,
        'store' as order_source
      FROM store_orders so
      WHERE so.id = ? AND so.user_id = ?
    `;

    const storeOrders = await db.query(storeOrderQuery, [orderId, userId]);

    if (storeOrders.length > 0) {
      // 门店订单详情
      const order = storeOrders[0];
      
      // 查询商品信息
      let productInfo = null;
      if (order.product_id) {
        const productQuery = `
          SELECT id, name, image, price
          FROM products
          WHERE id = ?
        `;
        const products = await db.query(productQuery, [order.product_id]);
        if (products.length > 0) {
          productInfo = products[0];
        }
      }

      return res.json({
        success: true,
        data: {
      id: order.id,
      order_no: order.order_no,
          status: order.status,
      type: order.type,
      total_amount: order.total_amount,
          quantity: order.quantity,
          store_no: order.store_no,
      created_at: formatDate(order.created_at),
      updated_at: formatDate(order.updated_at),
          product: productInfo
        }
      });
    }

    return res.status(404).json({ success: false, message: '订单不存在' });
  } catch (error) {
    console.error('获取订单详情失败:', error);
    res.status(500).json({ success: false, message: '获取订单详情失败', error: error.message });
  }
};

/**
 * 取消订单
 * POST /api/orders/:id/cancel
 */
exports.cancelOrder = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }
    
    const orderId = req.params.id;
    if (!orderId) {
      return res.status(400).json({ success: false, message: '订单ID不能为空' });
    }
    
    // 查询订单信息
    const orderQuery = 'SELECT * FROM customer_orders WHERE id = ? AND user_id = ?';
    const orders = await db.query(orderQuery, [orderId, userId]);
    
    if (orders.length === 0) {
      return res.status(404).json({ success: false, message: '订单不存在' });
    }
    
    const order = orders[0];
    
    // 只有待支付状态的订单可以取消
    if (order.status !== '待支付') {
      return res.status(400).json({ success: false, message: '只有待支付的订单可以取消' });
    }
    
    // 更新订单状态
    await db.query('UPDATE customer_orders SET status = ?, updated_at = ? WHERE id = ?', ['已取消', new Date(), orderId]);
    
    res.json({
      success: true,
      message: '订单已取消'
    });
  } catch (error) {
    console.error('取消订单失败:', error);
    res.status(500).json({ success: false, message: '取消订单失败', error: error.message });
  }
};

/**
 * 确认收货
 * POST /api/orders/:id/confirm
 */
exports.confirmReceipt = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }
    
    const orderId = req.params.id;
    if (!orderId) {
      return res.status(400).json({ success: false, message: '订单ID不能为空' });
    }
    
    // 查询订单信息
    const orderQuery = 'SELECT * FROM customer_orders WHERE id = ? AND user_id = ?';
    const orders = await db.query(orderQuery, [orderId, userId]);
    
    if (orders.length === 0) {
      return res.status(404).json({ success: false, message: '订单不存在' });
    }
    
    const order = orders[0];
    
    // 只有待收货状态的订单可以确认收货
    if (order.status !== '待收货') {
      return res.status(400).json({ success: false, message: '只有待收货的订单可以确认收货' });
    }
    
    // 更新订单状态
    await db.query('UPDATE customer_orders SET status = ?, updated_at = ? WHERE id = ?', ['已完成', new Date(), orderId]);
    
    res.json({
      success: true,
      message: '确认收货成功'
    });
  } catch (error) {
    console.error('确认收货失败:', error);
    res.status(500).json({ success: false, message: '确认收货失败', error: error.message });
  }
};

/**
 * 申请退款
 * POST /api/orders/:id/refund
 */
exports.applyRefund = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }
    
    const orderId = req.params.id;
    const { reason } = req.body;
    
    if (!orderId) {
      return res.status(400).json({ success: false, message: '订单ID不能为空' });
    }
    
    if (!reason) {
      return res.status(400).json({ success: false, message: '退款原因不能为空' });
    }
    
    // 查询订单信息
    const orderQuery = 'SELECT * FROM customer_orders WHERE id = ? AND user_id = ?';
    const orders = await db.query(orderQuery, [orderId, userId]);
    
    if (orders.length === 0) {
      return res.status(404).json({ success: false, message: '订单不存在' });
    }
    
    const order = orders[0];
    
    // 只有待发货或待收货状态的订单可以申请退款
    if (order.status !== '待发货' && order.status !== '待收货') {
      return res.status(400).json({ success: false, message: '只有待发货或待收货的订单可以申请退款' });
    }
    
    // 更新订单状态
    await db.query('UPDATE customer_orders SET status = ?, updated_at = ? WHERE id = ?', ['退款/售后', new Date(), orderId]);
    
    // 记录退款申请
    await db.query('INSERT INTO refunds (order_id, user_id, reason, status, created_at) VALUES (?, ?, ?, ?, ?)', 
      [orderId, userId, reason, '处理中', new Date()]);
    
    res.json({
      success: true,
      message: '退款申请已提交'
    });
  } catch (error) {
    console.error('申请退款失败:', error);
    res.status(500).json({ success: false, message: '申请退款失败', error: error.message });
  }
};

/**
 * 支付订单
 * POST /api/orders/:id/pay
 * body: { payment_method }
 */
exports.payOrder = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }
    
    const orderId = req.params.id;
    const { payment_method } = req.body;
    
    if (!payment_method) {
      return res.status(400).json({ success: false, message: '支付方式不能为空' });
    }
    
    console.log('订单支付请求:', { orderId, userId, payment_method });
    
    // 先查询顾客订单
    const customerOrderQuery = 'SELECT * FROM customer_orders WHERE id = ? AND user_id = ?';
    const customerOrders = await db.query(customerOrderQuery, [orderId, userId]);
    
    let order = null;
    let orderType = null;
    
    if (customerOrders.length > 0) {
      order = customerOrders[0];
      orderType = 'customer';
    } else {
      // 查询门店订单
      const storeOrderQuery = 'SELECT * FROM store_orders WHERE id = ? AND user_id = ?';
      const storeOrders = await db.query(storeOrderQuery, [orderId, userId]);
      
      if (storeOrders.length > 0) {
        order = storeOrders[0];
        orderType = 'store';
      }
    }

    if (!order) {
      return res.status(404).json({ success: false, message: '订单不存在' });
    }

    if (order.status !== '待支付') {
      return res.status(400).json({ success: false, message: '订单状态不允许支付' });
    }

    // 处理不同的支付方式
    if (payment_method === 'balance') {
      // 余额支付
      console.log('余额支付模式');
      
      // 查询用户余额
      const balanceQuery = 'SELECT account_balance FROM user_fund_accounts WHERE user_id = ?';
      const balanceResult = await db.query(balanceQuery, [userId]);
      
      let balance = 0;
      if (balanceResult.length === 0) {
        // 如果用户没有资金账户，创建一个
        await db.query('INSERT INTO user_fund_accounts (user_id, account_balance, created_at, updated_at) VALUES (?, ?, ?, ?)', 
          [userId, 0, Date.now(), Date.now()]);
      } else {
        balance = parseFloat(balanceResult[0].account_balance || 0);
      }
      
      const orderAmount = parseFloat(order.total_amount || 0);
      
      // 检查余额是否足够
      if (balance < orderAmount) {
        return res.status(400).json({ success: false, message: '余额不足' });
      }
      
      // 开始事务
      const connection = await db.getConnection();
      try {
        await connection.beginTransaction();

        // 扣减余额
        await connection.execute('UPDATE user_fund_accounts SET account_balance = account_balance - ?, updated_at = ? WHERE user_id = ?',
          [orderAmount, Date.now(), userId]);

        // 添加资金变动记录
        await connection.execute(
          'INSERT INTO user_fund_records (user_id, type, amount, balance, description, order_id, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
          [userId, 'payment', -orderAmount, balance - orderAmount, '订单支付', orderId, 'completed', Date.now()]
        );

        // 更新订单状态
        if (orderType === 'customer') {
          await connection.execute('UPDATE customer_orders SET status = ?, updated_at = ?, paid_at = ? WHERE id = ?',
            ['待发货', Date.now(), Date.now(), orderId]);
          
          // 同时更新子订单状态
          await connection.execute('UPDATE customer_sub_orders SET status = ?, updated_at = ? WHERE main_order_id = ?',
            ['待发货', Date.now(), orderId]);
        } else {
          await connection.execute('UPDATE store_orders SET status = ?, updated_at = ? WHERE id = ?',
            ['待发货', Date.now(), orderId]);
        }

        // 提交事务
        await connection.commit();
      } catch (error) {
        // 回滚事务
        await connection.rollback();
        console.error('余额支付失败:', error);
        return res.status(500).json({ success: false, message: '支付处理失败', error: error.message });
      } finally {
        connection.release();
      }
      
      return res.json({
        success: true,
        message: '支付成功'
      });
    } else if (payment_method === 'balance_priority') {
      // 余额优先支付：优先使用余额，不足时使用微信支付
      console.log('余额优先支付模式');

      // 查询用户余额
      const balanceQuery = 'SELECT account_balance FROM user_fund_accounts WHERE user_id = ?';
      const balanceResult = await db.query(balanceQuery, [userId]);

      let balance = 0;
      if (balanceResult.length > 0) {
        balance = parseFloat(balanceResult[0].account_balance || 0);
      }

      const orderAmount = parseFloat(order.total_amount || 0);

      if (balance >= orderAmount) {
        // 余额足够，直接支付
        return exports.payOrder(req, res);
      } else {
        // 余额不足，使用微信支付
        console.log('余额不足，使用微信支付');
        // TODO: 调用微信支付接口
        return res.status(400).json({ success: false, message: '微信支付功能暂未实现' });
      }
    } else {
      return res.status(400).json({ success: false, message: '不支持的支付方式' });
    }
  } catch (error) {
    console.error('订单支付失败:', error);
    res.status(500).json({ success: false, message: '支付失败', error: error.message });
  }
};

// 格式化日期
function formatDate(timestamp) {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
}

// 补零
function padZero(num) {
  return num < 10 ? `0${num}` : num;
}

/**
 * 按业务逻辑进行订单拆分和分配
 * 根据《订单详细规则》文档实现：
 * 1. 优先分配给用户的订阅门店
 * 2. 其次分配给销售人名下的其他门店
 * 3. 最后分配给平台总部
 *
 * 拆分原则：
 * - 同一门店的多个商品归到同一个分订单
 * - 按门店维度拆分，而不是按商品维度
 * - 每个门店只生成一个分订单
 *
 * @param {Array} cartItems - 购物车商品列表
 * @param {Object} user - 用户信息
 * @returns {Array} 订单拆分结果
 */
async function splitOrderByStock(cartItems, user) {
  const subOrders = [];

  console.log('开始订单拆分，用户信息:', {
    userId: user.id,
    salesman_id: user.salesman_id,
    subscribe_store_no: user.subscribe_store_no
  });

  // 首先检查所有商品的总库存是否充足
  for (const item of cartItems) {
    const productId = item.productId;
    const requestQuantity = item.quantity;

    const totalStockQuery = 'SELECT SUM(quantity) as total FROM store_cloud_stock WHERE product_id = ?';
    const totalStockResult = await db.query(totalStockQuery, [productId]);
    const totalStock = totalStockResult[0]?.total || 0;

    console.log(`商品 ${productId} 平台总库存: ${totalStock}，需求数量: ${requestQuantity}`);

    if (totalStock < requestQuantity) {
      throw new Error(`商品库存不足，商品ID: ${productId}，需求: ${requestQuantity}件，库存: ${totalStock}件`);
    }
  }

  // 按商品逐个处理拆分，但同一门店的商品会合并到同一个分订单
  for (const item of cartItems) {
    const productId = item.productId;
    const requestQuantity = item.quantity;
    let remainingQuantity = requestQuantity;

    console.log(`开始拆分商品 ${productId}，需求数量: ${requestQuantity}`);

    // 分配优先级：订阅门店 -> 销售人门店 -> 平台总部
    const allocationStores = [];

    // 1. 添加订阅门店（如果存在）
    if (user.subscribe_store_no) {
      allocationStores.push({
        store_no: user.subscribe_store_no,
        type: 'subscribe_store',
        priority: 1
      });
    }

    // 2. 添加销售人名下的其他门店
    if (user.salesman_id) {
      const salesmanStoresQuery = `
        SELECT DISTINCT p.store_no
        FROM partners p
        WHERE p.user_id = ? AND p.store_no != ?
      `;
      const salesmanStores = await db.query(salesmanStoresQuery, [
        user.salesman_id,
        user.subscribe_store_no || ''
      ]);

      for (const store of salesmanStores) {
        allocationStores.push({
          store_no: store.store_no,
          type: 'salesman_store',
          priority: 2
        });
      }
    }

    // 3. 添加平台总部
    allocationStores.push({
      store_no: 'PLAT',
      type: 'platform',
      priority: 3
    });

    // 按优先级分配库存
    for (const storeInfo of allocationStores) {
      if (remainingQuantity <= 0) break;

      // 查询该门店对该商品的库存
      const storeStockQuery = 'SELECT quantity FROM store_cloud_stock WHERE store_no = ? AND product_id = ?';
      const storeStockResult = await db.query(storeStockQuery, [storeInfo.store_no, productId]);
      const storeStock = storeStockResult[0]?.quantity || 0;

      // 对于平台总部，如果没有设置库存记录，视为无限量
      const availableStock = (storeInfo.store_no === 'PLAT' && storeStock === 0) ? remainingQuantity : storeStock;

      if (availableStock > 0) {
        const allocatedQuantity = Math.min(remainingQuantity, availableStock);

        // 查找是否已有该门店的子订单
        let subOrder = subOrders.find(order => order.store_no === storeInfo.store_no);
        if (!subOrder) {
          subOrder = {
            store_no: storeInfo.store_no,
            items: [],
            total_amount: 0,
            type: storeInfo.type
          };
          subOrders.push(subOrder);
        }

        // 添加商品到该门店的分订单
        subOrder.items.push({
          productId: productId,
          name: item.name || '商品名称',
          image: item.images || item.image || '',
          quantity: allocatedQuantity,
          price: item.price,
          amount: allocatedQuantity * item.price
        });
        subOrder.total_amount += allocatedQuantity * item.price;

        remainingQuantity -= allocatedQuantity;

        console.log(`分配给门店 ${storeInfo.store_no} (${storeInfo.type}): ${allocatedQuantity}件，剩余: ${remainingQuantity}件`);
      }
    }

    // 如果仍有剩余数量，说明库存分配失败
    if (remainingQuantity > 0) {
      throw new Error(`商品 ${productId} 库存分配失败，剩余未分配: ${remainingQuantity}件`);
    }
  }

  console.log('订单拆分完成，子订单数量:', subOrders.length);
  console.log('拆分结果详情:', subOrders.map(order => ({
    store_no: order.store_no,
    type: order.type,
    items_count: order.items.length,
    total_amount: order.total_amount
  })));

  return subOrders;
}

/**
 * 更新订单状态
 * PUT /api/orders/:id/status
 * body: { status }
 */
exports.updateOrderStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const userId = req.user.id;

    console.log('更新订单状态请求:', { orderId: id, status, userId });

    // 验证状态值
    const validStatuses = ['pending_payment', 'paid', 'processing', 'shipped', 'delivered', 'completed', 'cancelled', 'refunded'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的订单状态'
      });
    }

    // 检查订单是否存在且属于当前用户
    const orderQuery = 'SELECT * FROM customer_orders WHERE id = ? AND user_id = ?';
    const orderRows = await db.query(orderQuery, [id, userId]);
    
    if (orderRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '订单不存在或无权限访问'
      });
    }

    const order = orderRows[0];
    console.log('找到订单:', order);

    // 更新订单状态
    const updateQuery = 'UPDATE customer_orders SET status = ?, updated_at = ? WHERE id = ?';
    await db.query(updateQuery, [status, new Date(), id]);

    // 如果是支付状态，同时更新支付时间
    if (status === 'paid') {
      const updatePaidQuery = 'UPDATE customer_orders SET paid_at = ? WHERE id = ?';
      await db.query(updatePaidQuery, [new Date(), id]);
    }

    // 同时更新子订单状态
    const updateSubOrderQuery = 'UPDATE customer_sub_orders SET status = ?, updated_at = ? WHERE main_order_id = ?';
    await db.query(updateSubOrderQuery, [status, new Date(), id]);

    console.log('订单状态更新成功:', { orderId: id, newStatus: status });

    res.json({
      success: true,
      message: '订单状态更新成功',
      data: {
        orderId: id,
        status: status,
        updatedAt: new Date()
      }
    });

  } catch (error) {
    console.error('更新订单状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新订单状态失败',
      error: error.message
    });
  }
};