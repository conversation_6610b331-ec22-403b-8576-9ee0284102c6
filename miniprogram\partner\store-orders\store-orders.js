// partner/store-orders/store-orders.js
// 合伙人端门店订单页面
const { orderApi, partnerApi, storeApi } = require('../../utils/api');
const app = getApp();

Page({
  data: {
    // 门店相关
    storeList: [],
    selectedStore: null,
    
    // 搜索相关
    searchKeyword: '',
    
    // 排序筛选相关
    sortOptions: [
      { id: 'date', name: '日期', direction: 'desc' },
      { id: 'amount', name: '金额', direction: 'desc' }
    ],
    currentSort: 'date',
    currentSortDirection: 'desc',
    showFilterDrawer: false,
    filterOptions: {
      dateRange: [null, null],
      productKeyword: '',
    },
    
    // 订单分类标签
    orderTypes: [
      { key: 'purchase', name: '门店采购订单' },
      { key: 'transfer', name: '门店移库订单' }
    ],
    currentOrderType: 'purchase',
    
    // 二级分类标签
    purchaseSubTypes: [
      { key: 'all', name: '全部' },
      { key: 'pending_review', name: '已下单' },
      { key: 'reviewed', name: '已审核' }
    ],
    transferSubTypes: [
      { key: 'all', name: '全部' },
      { key: 'pending_shipment', name: '已下单' },
      { key: 'shipped', name: '已发货' },
      { key: 'received', name: '已到店' }
    ],
    currentSubType: 'all',
    
    // 订单列表
    orderList: [],
    loading: true,
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    isEmpty: false,
    
    // 用户信息
    userInfo: null
  },

  onLoad: function(options) {
    console.log('门店订单页面加载，参数:', options);
    
    // 获取用户信息
    this.setData({
      userInfo: app.globalData.userInfo || {}
    });
    
    // 从本地存储同步门店数据
    this.syncStoreData();
    
    // 加载订单数据
    this.loadOrders();
  },

  onShow: function() {
    console.log('门店订单页面显示');
    
    // 检查是否从其他页面切换过来，需要刷新数据
    const lastPage = wx.getStorageSync('lastPage');
    const isFromOtherPage = lastPage && !lastPage.includes('store-orders');
    
    if (isFromOtherPage) {
      console.log('从其他页面切换过来，刷新订单数据');
      this.refreshOrders();
    }
    
    // 记录当前页面路径
    wx.setStorageSync('lastPage', 'partner/store-orders/store-orders');
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.refreshOrders().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreOrders();
    }
  },

  /**
   * 同步门店数据
   */
  syncStoreData: function() {
    // 从本地存储获取门店数据
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    console.log('同步门店数据，当前本地存储:', { storedSelectedStore, storedStoreList });
    
    // 如果本地存储中有门店信息，使用本地存储数据
    if (storedStoreList && storedStoreList.length > 0) {
      this.setData({
        storeList: storedStoreList,
        selectedStore: storedSelectedStore || storedStoreList[0]
      });
      
      console.log('从本地存储同步门店信息:', {
        selectedStore: storedSelectedStore || storedStoreList[0]
      });
      return;
    }
    
    // 否则重新获取门店数据
    console.log('本地存储中没有门店信息，重新获取');
    this.getPartnerStores();
  },

  /**
   * 获取合伙人门店列表
   */
  getPartnerStores: function() {
    // 同时调用两个获取门店的API
    return Promise.all([
      partnerApi.getPartnerStores(),
      partnerApi.getPartnerJoinedStores()
    ]).then(([storesRes, joinedStoresRes]) => {
      // 合并两个API的门店数据，并去重
      let allStores = [];
      
      if (storesRes && storesRes.success && storesRes.data) {
        allStores = [...storesRes.data];
      }
      
      if (joinedStoresRes && joinedStoresRes.success && joinedStoresRes.data) {
        // 将joinedStores中的门店添加到allStores中，避免重复
        joinedStoresRes.data.forEach(store => {
          // 检查是否已存在相同store_no的门店
          const existingIndex = allStores.findIndex(s => s.store_no === store.store_no);
          if (existingIndex === -1) {
            // 不存在则添加
            allStores.push(store);
          }
        });
      }
      
      console.log('合并后的门店列表数据:', allStores);
      
      // 选择第一个门店
      let selectedStore = null;
      
      // 获取本地存储的选中门店，用于后续保持用户选择
      const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
      
      // 尝试保持之前选择的门店
      if (storedSelectedStore) {
        selectedStore = allStores.find(store => 
          store.id === storedSelectedStore.id || store.store_no === storedSelectedStore.store_no
        );
      }
      
      // 如果没有找到匹配的门店，选择第一个
      if (!selectedStore && allStores.length > 0) {
        selectedStore = allStores[0];
      }
      
      this.setData({
        storeList: allStores,
        selectedStore: selectedStore
      });
      
      // 保存到本地存储
      wx.setStorageSync('partnerStoreList', allStores);
      wx.setStorageSync('partnerSelectedStore', selectedStore);
      
      // 门店数据加载完成后，重新加载订单数据
      if (selectedStore) {
        this.loadOrders();
      }
      
      return selectedStore;
    }).catch(err => {
      console.error('获取门店列表失败:', err);
      wx.showToast({
        title: '获取门店列表失败',
        icon: 'none'
      });
    });
  },

  /**
   * 门店选择
   */
  onStoreChange: function(e) {
    const index = e.detail.value;
    const selectedStore = this.data.storeList[index];
    
    this.setData({ 
      selectedStore,
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    
    // 保存到本地存储，供其他页面读取
    wx.setStorageSync('partnerSelectedStore', selectedStore);
    
    // 重新加载订单数据
    this.loadOrders();
  },

  /**
   * 加载订单数据
   */
  loadOrders: function() {
    this.setData({
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    return this.getOrders();
  },

  /**
   * 刷新订单数据
   */
  refreshOrders: function() {
    this.setData({
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    return this.getOrders();
  },

  /**
   * 加载更多订单
   */
  loadMoreOrders: function() {
    if (!this.data.hasMore || this.data.loading) {
      return Promise.resolve();
    }
    
    this.setData({
      loading: true
    });
    
    return this.getOrders(true).then(res => {
      if (res && res.success) {
        this.setData({
          pageNum: this.data.pageNum + 1
        });
      }
      return res;
    });
  },

  /**
   * 获取订单数据
   */
  getOrders: function(isLoadMore = false) {
    if (!isLoadMore) {
      this.setData({
        loading: true,
        isEmpty: false
      });
    }
    
    const params = {
      page: isLoadMore ? this.data.pageNum + 1 : 1,
      limit: this.data.pageSize,
      keyword: this.data.searchKeyword,
      sortBy: this.data.currentSort + '_' + this.data.currentSortDirection,
      orderType: this.data.currentOrderType !== 'all' ? this.data.currentOrderType : '',
      subType: this.data.currentSubType !== 'all' ? this.data.currentSubType : ''
    };
    
    // 如果选择了特定门店，添加门店参数
    if (this.data.selectedStore && this.data.selectedStore.store_no) {
      params.storeNo = this.data.selectedStore.store_no;
    }
    
    // 添加筛选条件
    if (this.data.filterOptions.dateRange && 
        this.data.filterOptions.dateRange[0] && 
        this.data.filterOptions.dateRange[1]) {
      params.startDate = this.data.filterOptions.dateRange[0];
      params.endDate = this.data.filterOptions.dateRange[1];
    }
    
    if (this.data.filterOptions.productKeyword) {
      params.productKeyword = this.data.filterOptions.productKeyword;
    }

    console.log('获取门店订单参数:', params);

    // 调用门店订单API
    return orderApi.getStoreOrders(params).then(res => {
      console.log('门店订单数据:', res);
      
      if (res && res.success && res.data) {
        const newOrders = this.processOrderData(res.data.orders || []);
        const total = res.data.total || 0;
        
        if (isLoadMore) {
          this.setData({
            orderList: [...this.data.orderList, ...newOrders],
            hasMore: this.data.orderList.length + newOrders.length < total,
            loading: false
          });
        } else {
          this.setData({
            orderList: newOrders,
            hasMore: newOrders.length < total,
            isEmpty: newOrders.length === 0,
            loading: false
          });
        }
      } else {
        console.error('获取门店订单失败:', res ? res.message : '未知错误');
        this.setData({
          loading: false,
          isEmpty: true
        });
        wx.showToast({
          title: res && res.message ? res.message : '获取订单失败',
          icon: 'none'
        });
      }
      
      return res;
    }).catch(err => {
      console.error('获取门店订单异常:', err);
      this.setData({
        loading: false,
        isEmpty: true
      });
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
      return err;
    });
  },

  /**
   * 处理订单数据，添加显示所需的字段
   */
  processOrderData: function(orders) {
    if (!orders || !Array.isArray(orders)) return [];
    
    return orders.map(order => {
      // 处理订单状态文本
      let statusText = '';
      if (order.order_type === 'purchase') {
        // 采购订单状态
        switch(order.status) {
          case 'pending_review': statusText = '未审核'; break;
          case 'reviewed': statusText = '已审核'; break;
          default: statusText = order.status || '未知状态';
        }
      } else {
        // 移库订单状态
        switch(order.status) {
          case 'pending_shipment': statusText = '未发货'; break;
          case 'shipped': statusText = '已发货'; break;
          case 'received': statusText = '已到店'; break;
          default: statusText = order.status || '未知状态';
        }
      }
      
      // 处理订单项目，计算金额
      const items = order.items || [];
      items.forEach(item => {
        item.amount = (parseFloat(item.price) * parseInt(item.quantity)).toFixed(2);
      });
      
      // 格式化日期时间
      const createdAt = order.created_at ? new Date(order.created_at) : null;
      const reviewTime = order.review_time ? new Date(order.review_time) : null;
      
      return {
        ...order,
        status_text: statusText,
        items: items,
        created_at: createdAt ? this.formatDateTime(createdAt) : '未知时间',
        review_time: reviewTime ? this.formatDateTime(reviewTime) : '',
        // 确保门店名称和操作人信息存在
        store_name: order.store_name || '未知门店',
        operator_name: order.operator_name || '未知用户'
      };
    });
  },
  
  /**
   * 格式化日期时间
   */
  formatDateTime: function(date) {
    if (!date) return '';
    
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  /**
   * 搜索订单
   */
  onSearch: function() {
    console.log('搜索订单:', this.data.searchKeyword);
    this.loadOrders();
  },

  /**
   * 搜索输入
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },
  
  /**
   * 搜索确认
   */
  onSearchConfirm: function() {
    this.onSearch();
  },

  /**
   * 排序方式切换
   */
  onSortChange: function(e) {
    const sortType = e.currentTarget.dataset.sort;
    console.log('切换排序方式:', sortType);
    
    // 如果点击的是当前排序方式，则切换排序方向
    if (sortType === this.data.currentSort) {
      const newDirection = this.data.currentSortDirection === 'desc' ? 'asc' : 'desc';
      console.log('切换排序方向:', newDirection);
      this.setData({
        currentSortDirection: newDirection
      });
    } else {
      // 如果点击的是不同的排序方式，则更新排序方式并默认为降序
      this.setData({
        currentSort: sortType,
        currentSortDirection: 'desc'
      });
    }
    
    // 重新加载订单数据
    this.refreshOrders();
  },

  /**
   * 显示筛选抽屉
   */
  onShowFilter: function() {
    this.setData({
      showFilterDrawer: true
    });
  },

  /**
   * 隐藏筛选抽屉
   */
  onHideFilter: function() {
    this.setData({
      showFilterDrawer: false
    });
  },

  /**
   * 应用筛选
   */
  onApplyFilter: function() {
    console.log('应用筛选:', this.data.filterOptions);
    this.setData({
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    
    this.loadOrders();
    this.onHideFilter();
  },

  /**
   * 重置筛选
   */
  onResetFilter: function() {
    this.setData({
      filterOptions: {
        dateRange: [null, null],
        productKeyword: '',
      },
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    
    this.loadOrders();
    this.onHideFilter();
  },
  
  /**
   * 日期选择
   */
  onDateChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const date = e.detail.value;
    
    if (field === 'start') {
      this.setData({
        'filterOptions.dateRange[0]': date
      });
    } else if (field === 'end') {
      this.setData({
        'filterOptions.dateRange[1]': date
      });
    }
  },
  
  /**
   * 商品关键词输入
   */
  onProductKeywordInput: function(e) {
    this.setData({
      'filterOptions.productKeyword': e.detail.value
    });
  },

  /**
   * 切换订单类型
   */
  switchOrderType: function(e) {
    const type = e.currentTarget.dataset.type;
    console.log('切换订单类型:', type);
    
    if (type === this.data.currentOrderType) {
      return;
    }
    
    this.setData({
      currentOrderType: type,
      currentSubType: 'all',
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    
    this.loadOrders();
  },

  /**
   * 切换二级分类
   */
  switchSubType: function(e) {
    const subType = e.currentTarget.dataset.subtype;
    console.log('切换二级分类:', subType);
    
    if (subType === this.data.currentSubType) return;
    
    this.setData({
      currentSubType: subType,
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    
    this.loadOrders();
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail: function(e) {
    const orderId = e.currentTarget.dataset.id;
    const orderType = e.currentTarget.dataset.type || this.data.currentOrderType;
    console.log('查看订单详情:', orderId, '类型:', orderType);
    
    wx.navigateTo({
      url: `/partner/store-orders/detail?id=${orderId}&type=${orderType}`
    });
  }
}); 