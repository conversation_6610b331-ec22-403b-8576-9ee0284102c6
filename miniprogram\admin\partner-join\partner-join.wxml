<view class="partner-join-container">
  <view class="form-group">
    <view class="form-label">搜索选择会员</view>
    <input class="form-input" placeholder="输入用户名/ID/手机号" value="{{userSearchValue}}" bindinput="onUserSearchInput" />
    <view wx:if="{{showUserDropdown}}" class="dropdown-list">
      <block wx:for="{{filteredUserList}}" wx:key="id">
        <view class="dropdown-item" bindtap="selectUser" data-index="{{index}}">{{item.nickname}}（ID:{{item.id}}，{{item.phone}}）</view>
      </block>
      <view wx:if="{{filteredUserList.length === 0}}" class="dropdown-empty">无匹配用户</view>
    </view>
  </view>
  <view class="form-group">
    <view class="form-label">搜索选择门店</view>
    <input class="form-input" placeholder="输入门店名称/编号" value="{{storeSearchValue}}" bindinput="onStoreSearchInput" />
    <view wx:if="{{showStoreDropdown}}" class="dropdown-list">
      <block wx:for="{{filteredStoreList}}" wx:key="id">
        <view class="dropdown-item" bindtap="selectStore" data-index="{{index}}">{{item.name}}（编号:{{item.code}}）</view>
      </block>
      <view wx:if="{{filteredStoreList.length === 0}}" class="dropdown-empty">无匹配门店</view>
    </view>
  </view>
  <view class="form-group join-type-group">
    <view class="form-label">选择加入类型</view>
    <view class="form-input" style="background:#f5f5f5;" bindtap="openTypeSelector">{{selectedType || 'A、B、C、M四个选项（M为总部派出代表）'}}</view>
  </view>
  <view class="form-group">
    <view class="form-label">投资金额</view>
    <input class="form-input" type="number" placeholder="请输入金额" value="{{amount}}" bindinput="onAmountInput" />
  </view>
  <view class="form-group">
    <view class="form-label">股份占比</view>
    <view style="position:relative;">
      <input class="form-input percent-input" type="number" placeholder="输入百分比" value="{{percent}}" bindinput="onPercentInput" />
      <text class="percent-suffix">%</text>
    </view>
  </view>
  <view class="form-btn-row">
    <button class="form-btn" bindtap="onSubmit">确定</button>
  </view>

  <!-- 加入类型自定义弹窗 -->
  <view wx:if="{{showTypeSelector}}" class="selector-modal">
    <view class="selector-mask" bindtap="closeTypeSelector"></view>
    <view class="selector-popup type-popup">
      <block wx:for="{{joinTypeList}}" wx:key="*this">
        <view class="type-item" bindtap="selectType" data-index="{{index}}">{{item}}</view>
      </block>
    </view>
  </view>
</view>
<view class="partner-join-bg"></view> 