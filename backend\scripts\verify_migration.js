const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'molI2505$',
  database: 'morebuy'
};

async function verifyMigration() {
  let connection;
  
  try {
    console.log('🔗 连接到数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    console.log('\n🔍 验证迁移结果...');
    
    // 检查表结构
    const [columns] = await connection.execute('DESCRIBE users');
    console.log('📊 users表结构:');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Key ? `(${col.Key})` : ''} ${col.Extra ? `[${col.Extra}]` : ''}`);
    });
    
    // 检查索引
    const [indexes] = await connection.execute('SHOW INDEX FROM users');
    console.log('\n🔑 索引信息:');
    indexes.forEach(index => {
      console.log(`  - ${index.Key_name}: ${index.Column_name} (${index.Non_unique === 0 ? 'UNIQUE' : 'NON-UNIQUE'})`);
    });
    
    // 验证数据完整性
    const [totalUsers] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [usersWithUserId] = await connection.execute('SELECT COUNT(*) as count FROM users WHERE user_id IS NOT NULL');
    const [usersWithId] = await connection.execute('SELECT COUNT(*) as count FROM users WHERE id IS NOT NULL');
    
    console.log('\n📋 数据完整性验证:');
    console.log(`  - 总用户数: ${totalUsers[0].count}`);
    console.log(`  - 有user_id的用户数: ${usersWithUserId[0].count}`);
    console.log(`  - 有id的用户数: ${usersWithId[0].count}`);
    
    // 检查user_id唯一性
    const [duplicates] = await connection.execute(`
      SELECT user_id, COUNT(*) as count 
      FROM users 
      GROUP BY user_id 
      HAVING count > 1
    `);
    
    if (duplicates.length > 0) {
      console.log('\n⚠️  发现重复的user_id:');
      duplicates.forEach(dup => {
        console.log(`  - ${dup.user_id}: ${dup.count}条记录`);
      });
    } else {
      console.log('\n✅ user_id唯一性验证通过');
    }
    
    // 查看示例数据
    const [users] = await connection.execute('SELECT id, user_id, nickname, phone FROM users LIMIT 5');
    console.log('\n📋 用户数据示例:');
    users.forEach(user => {
      console.log(`  - ID: ${user.id}, UserID: ${user.user_id}, 昵称: ${user.nickname}`);
    });
    
    console.log('\n🎉 迁移验证完成！');
    
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行验证
verifyMigration()
  .then(() => {
    console.log('✅ 验证完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 验证失败:', error);
    process.exit(1);
  }); 