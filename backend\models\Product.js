/**
 * 商品模型
 */
const db = require('../config/db');

class Product {
  static async findAll(options = {}) {
    const { categoryId, subCategoryId, keyword, page = 1, pageSize = 10, sortType = 'default', category, storeLevel, isNew } = options;

    console.log('[Product.findAll] 接收到的参数:', options);
    console.log('[Product.findAll] 解构后参数 - page:', page, 'pageSize:', pageSize, 'sortType:', sortType, 'isNew:', isNew);

    let sql = 'SELECT * FROM products WHERE 1=1';
    const params = [];

    if (typeof categoryId !== 'undefined' && categoryId !== null && String(categoryId).trim() !== '') {
      sql += ' AND categoryId = ?';
      params.push(Number(categoryId));
    }

    if (typeof subCategoryId !== 'undefined' && subCategoryId !== null && String(subCategoryId).trim() !== '') {
      sql += ' AND subCategoryId = ?';
      params.push(Number(subCategoryId));
    }

    if (keyword) {
      sql += ' AND (name LIKE ? OR description LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 处理新品筛选
    if (isNew) {
      // 新品定义：创建时间在30天内的商品，或者有isNew字段且为1的商品
      // 注意：数据库中的字段名是createTime（时间戳格式），不是created_at
      sql += ' AND (FROM_UNIXTIME(createTime) >= DATE_SUB(NOW(), INTERVAL 30 DAY) OR isNew = 1)';
    }

    // 处理新的分类参数
    if (category) {
      if (category === '性价比') {
        // 性价比：价格较低且销量较好的商品
        sql += ' AND price <= 100 AND salesCount >= 10';
      } else if (category === '大品牌') {
        // 大品牌：通过商品名称或店铺名称识别品牌商品
        sql += ' AND (name LIKE ? OR shopName LIKE ? OR description LIKE ?)';
        params.push('%品牌%', '%品牌%', '%品牌%');
      }
    }

    // 排序，兼容所有前端写法
    if (sortType === 'price_asc' || sortType === 'price-asc') {
      sql += ' ORDER BY price ASC';
    } else if (sortType === 'price_desc' || sortType === 'price-desc') {
      sql += ' ORDER BY price DESC';
    } else if (sortType === 'sales') {
      sql += ' ORDER BY salesCount DESC';
    } else if (sortType === 'price') {
      // 性价比排序：按价格升序
      sql += ' ORDER BY price ASC';
    } else if (sortType === 'brand') {
      // 大品牌排序：按销量和价格排序
      sql += ' ORDER BY salesCount DESC, price ASC';
    } else {
      sql += ' ORDER BY id DESC';
    }

    // 分页
    const safePage = parseInt(page, 10) > 0 ? parseInt(page, 10) : 1;
    const safePageSize = parseInt(pageSize, 10) > 0 ? parseInt(pageSize, 10) : 10;
    const offset = (safePage - 1) * safePageSize;
    sql += ` LIMIT ${Number(safePageSize)} OFFSET ${Number(offset)}`;
    
    console.log('[Product.findAll] 分页计算结果:');
    console.log('  - 原始page:', page, '类型:', typeof page);
    console.log('  - 安全page:', safePage);
    console.log('  - 原始pageSize:', pageSize, '类型:', typeof pageSize);
    console.log('  - 安全pageSize:', safePageSize);
    console.log('  - offset:', offset);
    console.log('[Product.findAll] 最终SQL:', sql);
    console.log('Product SQL params:', params);

    const products = await db.query(sql, params);
    
    // 为每个商品添加采购价信息
    if (storeLevel) {
      console.log('开始为商品添加采购价信息，门店级别:', storeLevel);
      const productsWithPurchasePrice = products.map(product => {
        const purchasePrice = this.getPurchasePriceByLevel(product, storeLevel);
        console.log(`商品 ${product.id} (${product.name}) 采购价:`, purchasePrice);
        return {
          ...product,
          purchasePrice: purchasePrice
        };
      });
      console.log('商品采购价处理完成');
      return productsWithPurchasePrice;
    }
    
    return products;
  }

  /**
   * 根据门店级别获取商品采购价
   * @param {Object} product 商品对象
   * @param {String} storeLevel 门店级别 (L1, L2, L3, L4, L5)
   * @returns {Number|null} 采购价，如果没有则返回门店基准价
   */
  static getPurchasePriceByLevel(product, storeLevel) {
    // 根据门店级别获取对应的采购价字段
    const purchasePriceField = `purchase_price_${storeLevel.toLowerCase()}`;
    console.log(`检查采购价字段: ${purchasePriceField}, 值:`, product[purchasePriceField]);
    
    // 如果对应级别的采购价存在且不为空，则使用该价格
    if (product[purchasePriceField] && product[purchasePriceField] > 0) {
      console.log(`使用${storeLevel}级别采购价:`, product[purchasePriceField]);
      return product[purchasePriceField];
    }
    
    // 否则使用门店基准价
    console.log(`使用门店基准价:`, product.store_price);
    return product.store_price;
  }

  static async findById(id) {
    console.log('=====================================');
    console.log('查询商品详情, 原始id:', id, '原始类型:', typeof id);

    // ID类型转换
    let queryId = id;
    if (typeof id === 'string' && !isNaN(id)) {
      queryId = parseInt(id);
    }
    console.log('转换后的queryId:', queryId, '转换后类型:', typeof queryId);

    const sql = 'SELECT * FROM products WHERE id = ?';
    console.log('SQL:', sql, 'params:', [queryId]);
    const result = await db.query(sql, [queryId]);
    console.log('查询结果:', JSON.stringify(result, null, 2));
    console.log('=====================================');
    return result.length > 0 ? result[0] : null;
  }

  static async getCategories() {
    return await db.query('SELECT * FROM categories ORDER BY id');
  }

  static async getShopCategories() {
    return await db.query('SELECT * FROM categories ORDER BY id');
  }

  static async getShopSubCategories(parentId) {
    try {
      const sql = 'SELECT * FROM subCategories WHERE parentId = ?';
      const params = [parentId];
      return await db.query(sql, params);
    } catch (error) {
      console.error('执行SQL查询失败:', error);
      throw error;
    }
  }

  static async getBanners() {
    // 修复：使用id排序而不是sort字段，因为数据库中banners表没有sort字段
    try {
      const banners = await db.query('SELECT * FROM banners ORDER BY id');
      return banners.map(banner => ({
        id: banner.id,
        imageUrl: banner.imageUrl,
        linkUrl: banner.linkUrl,
        title: banner.title,
        type: banner.type || 'page'
      }));
    } catch (error) {
      console.error('查询banners表失败:', error.message);
      // 如果banners表不存在或查询失败，返回空数组
      return [];
    }
  }

  static async count(options = {}) {
    const { categoryId, subCategoryId, keyword, category, isNew } = options;

    let sql = 'SELECT COUNT(*) as total FROM products WHERE 1=1';
    const params = [];

    if (typeof categoryId !== 'undefined' && categoryId !== null && String(categoryId).trim() !== '') {
      sql += ' AND categoryId = ?';
      params.push(Number(categoryId));
    }

    if (typeof subCategoryId !== 'undefined' && subCategoryId !== null && String(subCategoryId).trim() !== '') {
      sql += ' AND subCategoryId = ?';
      params.push(Number(subCategoryId));
    }

    if (keyword) {
      sql += ' AND (name LIKE ? OR description LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 处理新品筛选（与findAll保持一致）
    if (isNew) {
      // 新品定义：创建时间在30天内的商品，或者有isNew字段且为1的商品
      // 注意：数据库中的字段名是createTime（时间戳格式），不是created_at
      sql += ' AND (FROM_UNIXTIME(createTime) >= DATE_SUB(NOW(), INTERVAL 30 DAY) OR isNew = 1)';
    }

    // 处理新的分类参数（与findAll保持一致）
    if (category) {
      if (category === '性价比') {
        // 性价比：价格较低且销量较好的商品
        sql += ' AND price <= 100 AND salesCount >= 10';
      } else if (category === '大品牌') {
        // 大品牌：通过商品名称或店铺名称识别品牌商品
        sql += ' AND (name LIKE ? OR shopName LIKE ? OR description LIKE ?)';
        params.push('%品牌%', '%品牌%', '%品牌%');
      }
    }

    console.log('[Product.count] SQL查询:', sql);
    console.log('[Product.count] 参数:', params);
    
    const result = await db.query(sql, params);
    const total = result[0].total;
    
    console.log('[Product.count] 查询结果总数:', total);
    return total;
  }
}

module.exports = Product;
