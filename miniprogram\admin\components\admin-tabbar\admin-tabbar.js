Component({
  properties: {
    current: {
      type: String,
      value: 'console'
    }
  },
  methods: {
    goConsole() {
      if (this.data.current !== 'console') {
        wx.redirectTo({ url: '/admin/console/console' });
      }
    },
    goStore() {
      if (this.data.current !== 'store') {
        wx.redirectTo({ url: '/admin/store/store' });
      }
    },
    goProducts() {
      if (this.data.current !== 'store') {
        wx.redirectTo({ url: '/admin/store/store' });
      }
    },
    goProducts() {
      if (this.data.current !== 'products') {
        wx.redirectTo({ url: '/admin/products/products' });
      }
    },
    goMaterials() {
      if (this.data.current !== 'materials') {
        wx.redirectTo({ url: '/admin/materials/materials' });
      }
    },
    goUsers() {
      // 跳转到用户管理页
      if (this.data.current !== 'users') {
        wx.redirectTo({ url: '/admin/users/users' });
      }
    }
  }
});