# 门店订单数据表改名操作说明

## 概述
为了避免顾客订单与门店订单的数据表造成混乱，将门店订单数据表从 `orders` 改名为 `store_orders`。

## 修改内容

### 1. 数据库表改名
- 原表名：`orders`
- 新表名：`store_orders`
- 表结构和数据保持不变

### 2. 后端代码修改
已修改以下文件中的相关代码：
- `backend/controllers/orderController.js` - 订单控制器
- `backend/controllers/partnerOrderController.js` - 合伙人订单控制器
- `backend/controllers/dashboardController.js` - 管理端统计控制器
- `backend/controllers/partnerStatsController.js` - 合伙人统计控制器
- `backend/controllers/adminController.js` - 管理端控制器

### 3. 前端代码
前端代码无需修改，因为前端调用的是API接口，接口路径保持不变。

## 执行步骤

### 第一步：备份数据库
```sql
-- 在微信云托管数据库管理工具中执行
-- 备份orders表（可选）
CREATE TABLE orders_backup AS SELECT * FROM orders;
```

### 第二步：执行表改名
```sql
-- 执行改名脚本
RENAME TABLE orders TO store_orders;
```

### 第三步：验证改名结果
```sql
-- 检查表是否存在
SHOW TABLES LIKE 'store_orders';

-- 检查表结构
DESCRIBE store_orders;

-- 检查数据
SELECT COUNT(*) FROM store_orders;
```

### 第四步：测试功能
1. 测试门店采购订单创建
2. 测试门店移库订单创建
3. 测试合伙人端门店订单查询
4. 测试管理端统计数据

## 回滚方案

如果出现问题，可以使用回滚脚本：
```sql
-- 执行回滚脚本
RENAME TABLE store_orders TO orders;
```

## 注意事项

1. **执行前务必备份数据库**
2. **在业务低峰期执行**
3. **确保所有代码修改已部署**
4. **执行后立即测试相关功能**

## 影响范围

### 不受影响的功能
- 顾客订单相关功能（使用 `customer_orders` 表）
- 前端用户界面
- API接口路径

### 受影响的功能
- 门店采购订单
- 门店移库订单
- 合伙人端门店订单查询
- 管理端订单统计

## 验证清单

- [ ] 数据库表改名成功
- [ ] 门店采购订单创建正常
- [ ] 门店移库订单创建正常
- [ ] 合伙人端门店订单列表显示正常
- [ ] 管理端统计数据正常
- [ ] 订单详情查询正常
- [ ] 订单状态更新正常

## 联系信息

如有问题，请联系开发团队。