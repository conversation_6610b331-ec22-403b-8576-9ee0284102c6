.store-fund-page {
  min-height: 100vh;
  background: #f7f7f7;
  padding-bottom: 32rpx;
}
.fund-header {
  background: #fff;
  padding: 24rpx 24rpx 0 24rpx;
}
.fund-store-picker {
  height: 64rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #222;
}
.fund-tabs {
  display: flex;
  background: #fff;
  padding: 0 24rpx;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 12rpx;
}
.fund-tab {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #888;
  padding: 24rpx 0 16rpx 0;
  border-bottom: 4rpx solid transparent;
}
.fund-tab.active {
  color: #ff4d4f;
  border-bottom: 4rpx solid #ff4d4f;
}
.fund-record-list {
  margin: 0 24rpx;
}
.fund-record-item {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx #eee;
  margin-bottom: 24rpx;
  padding: 18rpx 24rpx;
}
.voucher-no {
  font-size: 28rpx;
  color: #222;
  font-weight: bold;
  margin-right: 16rpx;
}
.fund-type-tag {
  display: inline-block;
  background: #f5f5f5;
  color: #ff4d4f;
  font-size: 22rpx;
  border-radius: 8rpx;
  padding: 2rpx 12rpx;
  margin-right: 8rpx;
  vertical-align: middle;
}
.fund-record-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.fund-record-title {
  font-size: 30rpx;
  color: #222;
  font-weight: bold;
}
.fund-record-amount {
  font-size: 32rpx;
  color: #1bc47d;
  font-weight: bold;
}
.fund-record-meta {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 4rpx;
  margin-left: 4rpx;
}
.fund-record-desc {
  font-size: 26rpx;
  color: #888;
  margin-left: 4rpx;
}
.fund-record-voucher {
  display: flex;
  align-items: center;
}
.form-group {
  margin-bottom: 32rpx;
  position: relative;
  padding: 0 24rpx;
  background: #fff;
  display: flex;
  align-items: center;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx #f0f0f0;
  min-height: 112rpx;
  /* 新增：用于下拉定位 */
  z-index: 2;
}
.form-label {
  font-size: 34rpx;
  color: #222;
  margin-bottom: 0;
  margin-right: 18rpx;
  white-space: nowrap;
  font-weight: bold;
}
.form-input {
  flex: 1;
  width: auto;
  height: 64rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
  color: #222;
  padding: 0 24rpx;
}
.partner-actions {
  display: flex;
  gap: 24rpx;
  padding: 10rpx 24rpx 10rpx 24rpx;
  background: #fff;
  min-height: 100rpx;
  align-items: center;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx #f0f0f0;
  margin-bottom: 8rpx;
}
.partner-action-btn {
  flex: 1;
  height: 64rpx;
  background: #ff4d4f;
  color: #fff;
  border-radius: 12rpx;
  font-size: 24rpx;
  border: none;
  padding: 0 8rpx;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 64rpx;
  letter-spacing: 2rpx;
  font-weight: 500;
}
.partner-action-btn:nth-child(4) {
  letter-spacing: 2rpx;
}
.fund-sort-bar {
  display: flex;
  align-items: center;
  gap: 32rpx;
  padding: 0 24rpx 16rpx 24rpx;
  background: #fff;
  font-size: 28rpx;
  color: #888;
  margin-bottom: 8rpx;
}
.sort-btn {
  cursor: pointer;
  margin-right: 12rpx;
}
.sort-btn.active {
  color: #ff4d4f;
  font-weight: bold;
}
.filter-btn {
  margin-left: auto;
  color: #ff4d4f;
  cursor: pointer;
}
.dropdown-list {
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  background: #fff;
  border-radius: 0 0 12rpx 12rpx;
  box-shadow: 0 8rpx 32rpx #ccc;
  z-index: 10;
  max-height: 320rpx;
  overflow-y: auto;
  /* 移除margin-top，确保紧贴输入框 */
  margin-top: 0;
}
.dropdown-item {
  padding: 18rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fff;
}
.dropdown-item:last-child {
  border-bottom: none;
}
.dropdown-item:active {
  background: #f5f5f5;
}
.dropdown-empty {
  text-align: center;
  color: #aaa;
  font-size: 26rpx;
  padding: 24rpx 0;
  background: #fff;
}
.partner-action-btn-disabled {
  background: #e0e0e0 !important;
  color: transparent !important;
  pointer-events: none;
  border: none;
}
.capital-drawer-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 1000;
}
.capital-drawer {
  position: fixed;
  left: 0; right: 0; bottom: 0;
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
  z-index: 1001;
  min-height: 600rpx;
  box-shadow: 0 -4rpx 32rpx #ccc;
  padding: 32rpx 32rpx 24rpx 32rpx;
  animation: slideUpDrawer 0.2s;
}
@keyframes slideUpDrawer {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}
.capital-drawer-header {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 32rpx;
}
.capital-drawer-body {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.capital-form-row {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}
.capital-form-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #222;
  font-weight: bold;
}
.capital-form-input {
  flex: 1;
  height: 56rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
  color: #222;
  padding: 0 18rpx;
  margin-left: 12rpx;
}
.capital-form-switch-row {
  margin-top: 12rpx;
  align-items: center;
}
.capital-form-switch-label {
  font-size: 26rpx;
  color: #222;
  margin-left: 18rpx;
}
.capital-form-switch-tip {
  font-size: 22rpx;
  color: #888;
  margin-left: 12rpx;
}
.capital-drawer-footer {
  display: flex;
  justify-content: space-between;
  gap: 32rpx;
  margin-top: 48rpx;
}
.capital-cancel-btn, .capital-confirm-btn {
  flex: 1;
  height: 64rpx;
  font-size: 28rpx;
  border-radius: 12rpx;
  border: none;
  font-weight: bold;
}
.capital-cancel-btn {
  background: #fff;
  color: #ff4d4f;
  border: 2rpx solid #ff4d4f;
}
.capital-confirm-btn {
  background: #ff4d4f;
  color: #fff;
}
.voucher-image-list {
  display: flex;
  align-items: center;
  gap: 18rpx;
  flex-wrap: wrap;
  margin-left: 12rpx;
}
.voucher-image-item {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f5f5f5;
}
.voucher-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12rpx;
}
.add-voucher-image-btn {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #eee;
}
.delete-icon {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(255,255,255,0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}
.delete-icon image {
  width: 24rpx;
  height: 24rpx;
}
.add-voucher-image-btn image {
  width: 60rpx;
  height: 60rpx;
  object-fit: contain;
  display: block;
  margin: 0 auto;
} 