const qrcodeService = require('../services/qrcodeService');

exports.getQrcode = async (req, res) => {
  try {
    console.log('收到二维码生成请求，参数:', req.query);

    const { scene, noPage, page } = req.query;
    if (!scene) {
      console.error('缺少scene参数');
      return res.status(400).json({
        success: false,
        message: '缺少scene参数，请提供推荐人ID'
      });
    }    
    console.log('开始生成二维码，scene:', scene, 'page:', page, '是否不使用页面参数:', noPage);
    
    // 处理页面参数
    let pageParam = undefined;
    if (noPage === 'true' || noPage === true) {
      // 客户端明确要求不使用页面参数
      pageParam = null;
    } else if (page) {
      // 客户端指定了页面参数
      pageParam = page;
    } else {
      // 使用默认页面，确保扫码能正确跳转
      pageParam = 'pages/home/<USER>';
    }
    
    let qrcodeUrl;
    try {
      qrcodeUrl = await qrcodeService.generateQrcode(scene, pageParam);
      console.log('二维码生成成功:', qrcodeUrl);
    } catch (qrcodeError) {
      console.error('二维码生成失败:', qrcodeError.message);
      throw qrcodeError; // 直接抛出错误，让上层处理
    }
    
    // 检查是否成功生成二维码URL
    if (!qrcodeUrl) {
      throw new Error('生成二维码失败，无法获取二维码URL');
    }
    
    // 构造完整URL - 针对云托管环境优化
    let fullQrcodeUrl;
    if (qrcodeUrl.startsWith('http')) {
      // 已经是完整URL
      fullQrcodeUrl = qrcodeUrl;
    } else if (qrcodeUrl.startsWith('/public/')) {
      // 云托管环境中的静态文件路径
      // 使用云托管的域名构造完整URL
      const host = req.get('host');
      const protocol = req.protocol;
      fullQrcodeUrl = `${protocol}://${host}${qrcodeUrl}`;
      console.log('构造云托管二维码URL:', fullQrcodeUrl);
    } else {
      // 其他情况，使用默认方式
      const baseUrl = process.env.BASE_URL || req.protocol + '://' + req.get('host');
      fullQrcodeUrl = baseUrl + qrcodeUrl;
    }
    
    console.log('最终返回的二维码URL:', fullQrcodeUrl);
    
    res.json({
      success: true,
      qrcodeUrl: fullQrcodeUrl,
      scene: scene
    });
  } catch (error) {
    console.error('生成二维码失败:', error.message);
    console.error('错误堆栈:', error.stack);

    // 根据错误类型返回不同的状态码和消息
    let statusCode = 500;
    let message = '生成二维码失败';

    if (error.message.includes('微信小程序配置缺失')) {
      statusCode = 500;
      message = '服务配置错误，请联系管理员';
    } else if (error.message.includes('access_token')) {
      statusCode = 500;
      message = '微信服务认证失败，请稍后重试';
    } else if (error.message.includes('超时')) {
      statusCode = 504;
      message = '请求超时，请稍后重试';
    }

    res.status(statusCode).json({
      success: false,
      message: message,
      error: error.message,
      scene: req.query.scene
    });
  }
};