// pages/order/pay.js
const { orderApi } = require('../../utils/api');

Page({
  data: {
    orderId: null,
    orderDetail: null,
    loading: true,
    paymentMethods: [
      { id: 'balance_priority', name: '余额优先支付', icon: '/images/icons/balance.svg', selected: true, description: '优先使用余额，不足时使用微信支付' },
      { id: 'wxpay', name: '微信支付', icon: '/images/icons/wxpay.svg', selected: false },
      { id: 'balance', name: '余额支付', icon: '/images/icons/balance.svg', selected: false }
    ],
    selectedPayment: 'balance_priority',
    userBalance: 0,
    balanceEnough: false
  },

  onLoad: function(options) {
    console.log('订单支付页面加载，参数:', options);
    if (options && options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail(options.id);
      this.loadUserBalance();
    } else {
      wx.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载订单详情
  loadOrderDetail: function(orderId) {
    this.setData({ loading: true });
    
    console.log('开始加载订单详情, orderId:', orderId);
    
    orderApi.getOrderById(orderId).then(res => {
      console.log('订单详情API响应:', res);
      
      if (res.success && res.data) {
        const orderDetail = res.data;
        
        // 检查订单状态是否为待支付
        if (orderDetail.status !== 'pending_payment') {
          wx.showToast({
            title: '该订单不需要支付',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }
        
        this.setData({
          orderDetail,
          loading: false
        });
      } else {
        this.setData({ loading: false });
        
        // 根据错误类型显示不同的提示
        let errorMessage = '获取订单详情失败';
        if (res && res.message) {
          if (res.message.includes('订单不存在')) {
            errorMessage = '订单号不存在或已被删除';
          } else {
            errorMessage = res.message;
          }
        }
        
        wx.showModal({
          title: '订单加载失败',
          content: errorMessage,
          showCancel: true,
          cancelText: '返回',
          confirmText: '重试',
          success: (modalRes) => {
            if (modalRes.confirm) {
              // 重试加载
              this.loadOrderDetail(orderId);
            } else {
              // 返回上一页
              wx.navigateBack();
            }
          }
        });
      }
    }).catch(err => {
      console.error('获取订单详情失败:', err);
      this.setData({ loading: false });
      
      let errorMessage = '网络异常，请重试';
      if (err && err.message) {
        if (err.message.includes('订单不存在')) {
          errorMessage = '订单号不存在或已被删除';
        } else {
          errorMessage = err.message;
        }
      }
      
      wx.showModal({
        title: '订单加载失败',
        content: errorMessage,
        showCancel: true,
        cancelText: '返回',
        confirmText: '重试',
        success: (modalRes) => {
          if (modalRes.confirm) {
            // 重试加载
            this.loadOrderDetail(orderId);
          } else {
            // 返回上一页
            wx.navigateBack();
          }
        }
      });
    });
  },

  // 加载用户余额
  loadUserBalance: function() {
    const { userApi } = require('../../utils/api');
    userApi.getUserWallet().then(res => {
      if (res.success && res.data) {
        const userBalance = res.data.balance || 0;
        this.setData({
          userBalance,
          balanceEnough: userBalance >= (this.data.orderDetail?.total_amount || 0)
        });
      }
    }).catch(err => {
      console.error('获取用户余额失败:', err);
    });
  },

  // 选择支付方式
  selectPayment: function(e) {
    const paymentId = e.currentTarget.dataset.id;

    // 如果选择纯余额支付但余额不足，提示用户
    if (paymentId === 'balance' && !this.data.balanceEnough) {
      wx.showToast({
        title: '余额不足，无法使用纯余额支付',
        icon: 'none'
      });
      return;
    }

    const paymentMethods = this.data.paymentMethods.map(item => {
      return {
        ...item,
        selected: item.id === paymentId
      };
    });

    this.setData({
      paymentMethods,
      selectedPayment: paymentId
    });
  },

  // 确认支付
  confirmPayment: function() {
    const { orderId, selectedPayment, orderDetail } = this.data;
    
    if (!orderId || !orderDetail) {
      wx.showToast({
        title: '订单信息不完整',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载中
    wx.showLoading({
      title: '处理中',
      mask: true
    });
    
    // 根据不同的支付方式调用不同的接口
    if (selectedPayment === 'wxpay') {
      // 调用微信支付
      this.wxPayment(orderId);
    } else if (selectedPayment === 'balance') {
      // 调用余额支付
      this.balancePayment(orderId);
    } else if (selectedPayment === 'balance_priority') {
      // 调用余额优先支付
      this.balancePriorityPayment(orderId);
    }
  },

  // 微信支付
  wxPayment: function(orderId) {
    // 开发阶段模拟支付成功
    console.log('微信支付（开发模拟）');
    
    // 模拟网络延迟
    setTimeout(() => {
      wx.hideLoading();
      
      // 模拟支付成功后，更新订单状态为已支付
      orderApi.updateOrderStatus(orderId, 'paid').then(res => {
        console.log('更新订单状态成功:', res);
        
        wx.showToast({
          title: '微信支付成功（开发模拟）',
          icon: 'success',
          duration: 2000
        });
        
        // 支付成功后返回购物车页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/cart/cart'
          });
        }, 2000);
      }).catch(err => {
        console.error('更新订单状态失败:', err);
        wx.showToast({
          title: '支付成功但状态更新失败',
          icon: 'none',
          duration: 2000
        });
        
        // 即使状态更新失败，也返回购物车页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/cart/cart'
          });
        }, 2000);
      });
    }, 1000);
    
    // 注释掉真实API调用
    /*
    orderApi.payOrder({
      order_id: orderId,
      payment_method: 'wxpay'
    }).then(res => {
      wx.hideLoading();
      
      if (res.success && res.data) {
        const payParams = res.data;
        
        // 调用微信支付
        wx.requestPayment({
          ...payParams,
          success: () => {
            wx.showToast({
              title: '支付成功',
              icon: 'success'
            });
            
            // 支付成功后返回购物车页面
            setTimeout(() => {
              wx.switchTab({
                url: '/pages/cart/cart'
              });
            }, 1500);
          },
          fail: (err) => {
            console.error('微信支付失败:', err);
            wx.showToast({
              title: '支付已取消',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: res.message || '支付失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('发起支付失败:', err);
      wx.showToast({
        title: '发起支付失败',
        icon: 'none'
      });
    });
    */
  },

  // 余额支付
  balancePayment: function(orderId) {
    // 开发阶段模拟支付成功
    console.log('余额支付（开发模拟）');
    
    // 模拟网络延迟
    setTimeout(() => {
      wx.hideLoading();

      // 模拟支付成功后，更新订单状态为已支付
      orderApi.updateOrderStatus(orderId, 'paid').then(res => {
        console.log('更新订单状态成功:', res);
        
        wx.showToast({
          title: '余额支付成功（开发模拟）',
          icon: 'success',
          duration: 2000
        });

        // 支付成功后返回购物车页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/cart/cart'
          });
        }, 2000);
      }).catch(err => {
        console.error('更新订单状态失败:', err);
        wx.showToast({
          title: '支付成功但状态更新失败',
          icon: 'none',
          duration: 2000
        });
        
        // 即使状态更新失败，也返回购物车页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/cart/cart'
          });
        }, 2000);
      });
    }, 1000);
    
    // 注释掉真实API调用
    /*
    orderApi.payOrder({
      order_id: orderId,
      payment_method: 'balance'
    }).then(res => {
      wx.hideLoading();

      if (res.success) {
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });

        // 支付成功后返回购物车页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/cart/cart'
          });
        }, 1500);
      } else {
        wx.showToast({
          title: res.message || '支付失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('余额支付失败:', err);
      wx.showToast({
        title: '支付失败',
        icon: 'none'
      });
    });
    */
  },

  // 余额优先支付
  balancePriorityPayment: function(orderId) {
    // 开发阶段模拟支付成功
    console.log('余额优先支付（开发模拟）');
    
    // 模拟网络延迟
    setTimeout(() => {
      wx.hideLoading();

      // 模拟支付成功后，更新订单状态为已支付
      orderApi.updateOrderStatus(orderId, 'paid').then(res => {
        console.log('更新订单状态成功:', res);
        
        // 模拟余额不足，使用微信支付的情况
        wx.showToast({
          title: '余额优先支付成功（开发模拟）',
          icon: 'success',
          duration: 2000
        });

        // 支付成功后返回购物车页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/cart/cart'
          });
        }, 2000);
      }).catch(err => {
        console.error('更新订单状态失败:', err);
        wx.showToast({
          title: '支付成功但状态更新失败',
          icon: 'none',
          duration: 2000
        });
        
        // 即使状态更新失败，也返回购物车页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/cart/cart'
          });
        }, 2000);
      });
    }, 1000);
    
    // 注释掉真实API调用
    /*
    orderApi.payOrder({
      order_id: orderId,
      payment_method: 'balance_priority'
    }).then(res => {
      wx.hideLoading();

      if (res.success) {
        const paymentData = res.data;

        if (paymentData.paymentMethod === 'balance') {
          // 使用了余额支付
          wx.showToast({
            title: `余额支付成功，剩余余额¥${paymentData.remainingBalance}`,
            icon: 'success',
            duration: 2000
          });

          // 支付成功后返回购物车页面
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/cart/cart'
            });
          }, 2000);
        } else if (paymentData.paymentMethod === 'wxpay') {
          // 余额不足，使用微信支付（开发阶段模拟成功）
          console.log('余额不足，使用微信支付（开发模拟）');

          // 开发阶段直接模拟支付成功
          wx.showToast({
            title: '微信支付成功（开发模拟）',
            icon: 'success',
            duration: 2000
          });

          // 支付成功后返回购物车页面
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/cart/cart'
            });
          }, 2000);
        }
      } else {
        wx.showToast({
          title: res.message || '支付失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('余额优先支付失败:', err);
      wx.showToast({
        title: '支付失败',
        icon: 'none'
      });
    });
    */
  }
});