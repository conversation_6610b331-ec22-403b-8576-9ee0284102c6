/**
 * 测试公司信息更新功能
 * 模拟前端保存公司简介的操作
 */

const CompanyInfo = require('../models/CompanyInfo');

async function testCompanyUpdate() {
  console.log('🧪 开始测试公司信息更新功能...\n');

  try {
    // 1. 先获取现有的公司信息
    console.log('📋 获取现有公司信息...');
    const existingCompany = await CompanyInfo.findById(1);
    console.log('现有公司信息:', JSON.stringify(existingCompany, null, 2));

    // 2. 模拟前端发送的更新数据
    const testUpdateData = {
      company_name: '上海简脉企业策划咨询有限公司',
      company_description: '这是一个测试更新的公司简介 - ' + new Date().toLocaleString(),
      company_address: '上海市嘉定区江桥万达广场',
      company_phone: '***********',
      company_email: '<EMAIL>',
      company_website: 'https://www.example.com',
      business_hours: '周一至周五 9:00-18:00',
      company_logo: 'https://example.com/logo.png',
      contact_person: '张三',
      fax: '021-12345678',
      postal_code: '201803',
      company_type: '有限公司',
      established_date: '2014-11-17',
      registration_number: '*********',
      social_credit_code: '91310000*********X',
      legal_representative: '李四',
      registered_capital: 1000000.00,
      business_scope: '企业策划咨询服务',
      company_status: 'active'
    };

    console.log('\n📝 测试更新数据:');
    console.log(JSON.stringify(testUpdateData, null, 2));

    // 3. 执行更新操作
    console.log('\n🔄 执行更新操作...');
    const updateResult = await CompanyInfo.update(1, testUpdateData);
    console.log('更新结果:', JSON.stringify(updateResult, null, 2));

    // 4. 验证更新结果
    if (updateResult.success) {
      console.log('\n✅ 更新成功，验证结果...');
      const updatedCompany = await CompanyInfo.findById(1);
      console.log('更新后的公司信息:', JSON.stringify(updatedCompany, null, 2));
    } else {
      console.log('\n❌ 更新失败:', updateResult.message);
    }

    // 5. 测试部分字段更新
    console.log('\n🔄 测试部分字段更新...');
    const partialUpdateData = {
      company_description: '部分更新测试 - ' + new Date().toLocaleString(),
      company_phone: '18621109616'
    };

    const partialResult = await CompanyInfo.update(1, partialUpdateData);
    console.log('部分更新结果:', JSON.stringify(partialResult, null, 2));

    // 6. 测试空数据更新
    console.log('\n🔄 测试空数据更新...');
    const emptyResult = await CompanyInfo.update(1, {});
    console.log('空数据更新结果:', JSON.stringify(emptyResult, null, 2));

    // 7. 测试不存在的ID
    console.log('\n🔄 测试不存在的ID...');
    const nonExistentResult = await CompanyInfo.update(999, testUpdateData);
    console.log('不存在ID更新结果:', JSON.stringify(nonExistentResult, null, 2));

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    console.error('错误堆栈:', error.stack);
  }
}

// 执行测试
testCompanyUpdate()
  .then(() => {
    console.log('\n🎉 测试完成！');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ 测试失败:', error);
    process.exit(1);
  });
