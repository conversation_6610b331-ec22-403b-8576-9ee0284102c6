/**
 * 合伙人统计控制器
 */
const UserFundAccount = require('../models/UserFundAccount');
const db = require('../config/db');

/**
 * 获取合伙人统计信息
 * GET /api/partner/stats
 */
exports.getPartnerStats = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      console.error('获取合伙人统计信息失败 - 用户ID不存在');
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    console.log('获取合伙人统计信息 - 用户ID:', userId);
    console.log('请求头信息:', JSON.stringify(req.headers, null, 2));
    console.log('用户数据:', JSON.stringify(req.userData, null, 2));

    // 获取合伙人完整统计信息
    const stats = await UserFundAccount.getPartnerStats(userId);

    console.log('合伙人统计信息获取成功:', {
      fundAccount: stats.fundAccount,
      referralStats: stats.referralStats
    });

    // 确保返回的数据格式正确
    const responseData = {
      success: true,
      data: {
        fundAccount: {
          user_id: stats.fundAccount.user_id,
          account_balance: parseFloat(stats.fundAccount.account_balance || 0),
          pending_commission: parseFloat(stats.fundAccount.pending_commission || 0),
          total_commission: parseFloat(stats.fundAccount.total_commission || 0),
          total_withdrawal: parseFloat(stats.fundAccount.total_withdrawal || 0),
          total_dividend: parseFloat(stats.fundAccount.total_dividend || 0)
        },
        referralStats: {
          user_id: stats.referralStats.user_id,
          referral_count: parseInt(stats.referralStats.referral_count || 0),
          store_count: parseInt(stats.referralStats.store_count || 0)
        }
      }
    };

    console.log('最终返回的响应数据:', JSON.stringify(responseData, null, 2));

    res.json(responseData);
  } catch (error) {
    console.error('获取合伙人统计信息失败 - 详细错误:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({ 
      success: false, 
      message: '获取统计信息失败',
      error: error.message,
      details: error.stack
    });
  }
};

/**
 * 获取合伙人资金变动记录
 * GET /api/partner/fund-records
 */
exports.getFundRecords = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const { limit = 20, offset = 0 } = req.query;
    const records = await UserFundAccount.getFundRecords(userId, parseInt(limit), parseInt(offset));

    res.json({
      success: true,
      data: records
    });
  } catch (error) {
    console.error('获取资金变动记录失败:', error);
    res.status(500).json({ success: false, message: '获取记录失败' });
  }
};

/**
 * 获取合伙人订单统计
 * GET /api/partner/order-stats
 */
exports.getOrderStats = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    // 统计作为销售人的订单
    const orderStats = await db.query(`
      SELECT 
        COUNT(CASE WHEN status = '待发货' THEN 1 END) as pending_shipment,
        COUNT(CASE WHEN status = '已发货' THEN 1 END) as shipped,
        COUNT(CASE WHEN status = '已签收' THEN 1 END) as signed,
        COUNT(CASE WHEN status IN ('退换货', '已退款') THEN 1 END) as returns
      FROM store_orders 
      WHERE salesman_id = ?
    `, [userId]);

    res.json({
      success: true,
      data: orderStats[0] || {
        pending_shipment: 0,
        shipped: 0,
        signed: 0,
        returns: 0
      }
    });
  } catch (error) {
    console.error('获取订单统计失败:', error);
    res.status(500).json({ success: false, message: '获取订单统计失败' });
  }
};

/**
 * 获取合伙人门店列表
 * GET /api/partner/stores
 */
exports.getPartnerStores = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    console.log('获取合伙人门店列表 - 用户ID:', userId);

    // 获取当前用户作为合伙人加入的门店信息
    // 使用JOIN条件而不是COLLATE以解决字符集问题
    const stores = await db.query(`
      SELECT DISTINCT 
        s.id,
        s.store_no,
        s.name,
        s.level,
        s.level_title,
        s.address,
        s.phone as contact_phone,
        s.create_time,
        p.type as partner_type,
        p.amount as investment_amount,
        p.percent as share_percent,
        p.created_at
      FROM partners p
      JOIN stores s ON p.store_no = s.store_no
      WHERE p.user_id = ?
      ORDER BY p.created_at ASC
    `, [userId]);

    console.log('查询到的门店数量:', stores.length);
    if (stores.length > 0) {
      console.log('第一条门店记录:', stores[0]);
    }

    res.json({
      success: true,
      data: stores
    });
  } catch (error) {
    console.error('获取门店列表失败 - 详细错误:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({ 
      success: false, 
      message: '获取门店列表失败',
      error: error.message,
      details: error.stack
    });
  }
};