/* 主容器和flex布局 */
.store-admin-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100vh;
  background: #fff;
}
.store-list {
  flex: 1;
  overflow-y: auto;
}

/* 搜索栏相关 */
.user-mgr-header {
  background: #fff;
  padding: 10rpx 24rpx 0 24rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: center;
}
.user-mgr-search-bar {
  display: flex;
  align-items: center;
  width: 90%;
  max-width: 700rpx;
  min-width: 240rpx;
  padding: 0;
  justify-content: center;
  background: none;
  box-shadow: none;
  margin-bottom: 32rpx;
}
.user-mgr-search-input-container {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  padding: 0 8rpx;
  position: relative;
  flex: 1;
}
.user-mgr-search-input {
  flex: 1;
  height: 100%;
  font-size: 34rpx;
  padding: 0 80rpx 0 32rpx;
  border: none;
  background: transparent;
  outline: none;
}
.user-mgr-search-btn {
  width: 58rpx;
  height: 58rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  box-shadow: none;
  padding: 0;
}
.user-mgr-search-btn image {
  width: 48rpx;
  height: 48rpx;
}

/* 顶部操作按钮栏 */
.user-mgr-actions {
  display: flex;
  gap: 16rpx;
  padding: 0 24rpx;
  background: #fff;
  margin-bottom: 28rpx;
}
.user-mgr-btn {
  flex: 1;
  background: #ff4d4f;
  color: #fff;
  border-radius: 16rpx;
  font-size: 26rpx;
  padding: 16rpx 0;
  border: none;
}
.user-mgr-btn-disabled {
  background: #e0e0e0 !important;
  color: transparent !important;
  pointer-events: none;
  border: none;
}

/* 分类标签栏 */
.user-mgr-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0;
  padding: 0 24rpx;
  background: #fff;
  height: 70rpx;
  margin-bottom: 10rpx;
  margin-top: 6rpx;
  border-top: none;
  border-bottom: none;
  box-shadow: none;
}
.user-mgr-tab {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  color: #888;
  padding-bottom: 8rpx;
  border-bottom: 4rpx solid transparent;
  background: none;
}
.user-mgr-tab.active {
  color: #ff4d4f;
  border-bottom: 4rpx solid #ff4d4f;
  background: none;
}
.user-mgr-table-header {
  display: flex;
  justify-content: space-between;
  padding: 0 40rpx 0 40rpx;
  font-size: 26rpx;
  color: #888;
  background: #fff;
  margin-bottom: 18rpx;
  height: 64rpx;
  align-items: center;
  border-radius: 16rpx;
}
.sortable-header {
  flex: 1;
  text-align: center;
}

/* 门店卡片相关样式（保留原有） */
.store-mgr-item {
  display: flex;
  align-items: center;
  background: #fff;
  margin: 16rpx 24rpx 0 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx #eee;
  padding: 18rpx 18rpx;
  position: relative;
}
/* 移除复选框相关样式 */
.store-mgr-checkbox { display: none; }

/* 门店级别右上角 */
.store-mgr-level {
  position: absolute;
  top: 14rpx;
  right: 24rpx;
  font-size: 26rpx;
  color: #ff4d4f;
  font-weight: bold;
  background: #fff;
  padding: 0 16rpx;
  border-radius: 12rpx;
  z-index: 3;
}

/* 门店图片更靠左 */
.store-mgr-img {
  width: 109rpx;
  height: 109rpx;
  border-radius: 12rpx;
  margin-left: 0;
  margin-right: 18rpx;
  border: 2rpx solid #f0f0f0;
  background: #fafafa;
  object-fit: cover;
}

/* 优化卡片内容层级和间距 */
.store-mgr-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
  justify-content: center;
}
.store-mgr-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 2rpx;
}
.store-mgr-no {
  font-size: 22rpx;
  color: #888;
  margin-bottom: 2rpx;
}
.store-mgr-status {
  position: absolute;
  top: 14rpx;
  right: 18rpx;
  font-size: 20rpx;
  padding: 2rpx 14rpx;
  border-radius: 14rpx;
  font-weight: 500;
  border: none;
  box-shadow: none;
  z-index: 2;
}
.store-mgr-status-on {
  color: #12b200;
  background: #eaffea;
  border: 1rpx solid #b2e5b2;
}
.store-mgr-status-off {
  color: #888;
  background: #f0f0f0;
  border: 1rpx solid #ddd;
}
.store-mgr-status-frozen {
  color: #e60012;
  background: #ffeaea;
  border: 1rpx solid #ffb2b2;
}
.store-mgr-meta {
  font-size: 24rpx;
  color: #888;
}
.store-mgr-contact {
  font-size: 24rpx;
  color: #888;
}
.store-mgr-edit-btn {
  position: absolute;
  right: 24rpx;
  top: auto;
  bottom: 18rpx;
  transform: none;
  font-size: 26rpx;
  color: #222;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: #fff;
  padding: 0 18rpx;
  height: 48rpx;
  line-height: 48rpx;
}
.user-mgr-footer {
  text-align: center;
  font-size: 28rpx;
  color: #888;
  background: #fff;
  padding: 24rpx 0 24rpx 0;
  margin-top: 24rpx;
}
.selected-count {
  color: #ff4d4f;
  font-size: 28rpx;
  margin-left: 8rpx;
}
.safe-area {
  height: 120rpx;
  background: transparent;
}
.province-tabs {
  width: 100%;
  white-space: nowrap;
  background: #fff;
  padding: 0 0 10rpx 0;
  margin-bottom: 10rpx;
}
.province-tab-list {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 18rpx;
  padding: 0 24rpx;
}
.province-tab {
  display: inline-block;
  padding: 10rpx 32rpx;
  font-size: 30rpx;
  color: #888;
  border-radius: 24rpx;
  background: #f7f7f7;
  margin-right: 0;
  transition: all 0.2s;
}
.province-tab.active {
  color: #ff4d4f;
  background: #fff0f0;
  font-weight: bold;
  border: 2rpx solid #ff4d4f;
} 

/* 门店订单页面样式 */
.store-orders-page {
  background: #fff;
  min-height: 100vh;
  padding-bottom: 100rpx;
}

.page-header {
  padding: 32rpx 24rpx 24rpx 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.store-info {
  font-size: 28rpx;
  color: #666;
}

.orders-content {
  padding: 48rpx 24rpx;
}

.orders-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.placeholder-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: bold;
}

.placeholder-desc {
  font-size: 28rpx;
  color: #999;
} 