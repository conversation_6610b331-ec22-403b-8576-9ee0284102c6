const axios = require('axios');
const https = require('https');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// 微信小程序配置
const appId = process.env.WX_APPID || 'wx5f3fbb40507c044a';
const appSecret = process.env.WX_SECRET || '请替换为正确的AppSecret';

// 配置axios忽略SSL证书验证（适用于云托管环境）
if (process.env.NODE_ENV === 'production' || process.env.IGNORE_SSL_CERT === 'true') {
  const httpsAgent = new https.Agent({
    rejectUnauthorized: false
  });
  axios.defaults.httpsAgent = httpsAgent;
  console.log('已配置忽略SSL证书验证（适用于云托管环境）');
}

axios.defaults.timeout = 30000;

// 验证必要的环境变量
if (!appId || !appSecret) {
  console.error('缺少微信小程序配置:');
  console.error('- WX_APPID:', appId ? '已配置' : '未配置');
  console.error('- WX_SECRET:', appSecret ? '已配置' : '未配置');
}

async function getAccessToken() {
  if (!appId || !appSecret) {
    console.error('微信小程序配置检查:');
    console.error('- WX_APPID:', appId ? '已配置' : '未配置');
    console.error('- WX_SECRET:', appSecret ? '已配置' : '未配置');
    throw new Error('微信小程序配置缺失，请检查 WX_APPID 和 WX_SECRET 环境变量');
  }

  if (appSecret === '请替换为正确的AppSecret' || appSecret.includes('请替换')) {
    throw new Error('请在环境变量中配置正确的微信小程序 AppSecret');
  }

  const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${appSecret}`;
  console.log('正在获取微信access_token...');
  console.log('使用的AppID:', appId);

  try {
    const res = await axios.get(url);
    console.log('微信API响应:', res.data);

    if (res.data && res.data.access_token) {
      console.log('access_token获取成功');
      return res.data.access_token;
    }

    if (res.data && res.data.errcode) {
      const errorMessages = {
        40013: 'AppID无效，请检查微信小程序AppID是否正确',
        40125: 'AppSecret无效，请检查微信小程序AppSecret是否正确',
        40164: 'IP白名单校验失败，请在微信公众平台添加服务器IP到白名单',
        45009: '接口调用超过限制，请稍后重试',
        40001: 'AppSecret错误或者AppSecret不属于这个公众号，请核实AppSecret的正确性'
      };

      const errorMsg = errorMessages[res.data.errcode] || `微信API错误: ${res.data.errmsg}`;
      console.error('微信API错误详情:', {
        errcode: res.data.errcode,
        errmsg: res.data.errmsg,
        解释: errorMsg
      });
      throw new Error(errorMsg);
    }

    console.error('获取access_token失败:', res.data);
    throw new Error('获取access_token失败: ' + JSON.stringify(res.data));
  } catch (error) {
    if (error.message.includes('微信API错误') || error.message.includes('AppID') || error.message.includes('AppSecret')) {
      throw error;
    }
    console.error('请求微信API失败:', error.message);
    throw new Error('请求微信API失败: ' + error.message);
  }
}

exports.generateQrcode = async (scene, page) => {
  console.log('开始生成二维码，scene参数:', scene, '页面路径:', page);

  if (!scene) {
    throw new Error('scene参数不能为空');
  }

  try {
    const accessToken = await getAccessToken();
    const qrcodeApi = `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${accessToken}`;
    
    // 简化策略：直接不使用page参数，避免41030错误
    // 微信小程序码即使不指定page，也能正常扫码进入小程序
    const postData = {
      scene: String(scene),
      width: 430,
      auto_color: false,
      line_color: {"r":"0","g":"0","b":"0"},
      is_hyaline: false
    };
    
    console.log('生成小程序码，携带推荐参数:', scene);
    console.log('请求微信小程序码API，参数:', postData);
    console.log('注意：不使用page参数，避免41030错误');

    let response;
    let retryCount = 0;
    const maxRetries = 3;
    
    while (retryCount <= maxRetries) {
      try {
        response = await axios({
          url: qrcodeApi,
          method: 'POST',
          responseType: 'arraybuffer',
          data: postData,
          timeout: 30000 // 增加超时时间
        });
        
        console.log('微信小程序码API响应状态:', response.status);
        console.log('响应头Content-Type:', response.headers['content-type']);
        
        // 如果成功，跳出重试循环
        break;
      } catch (err) {
        retryCount++;
        console.error(`第${retryCount}次请求微信小程序码API失败:`, err.response ? err.response.data : err.message);

        if (err.code === 'ECONNABORTED') {
          if (retryCount > maxRetries) {
            throw new Error('请求微信API超时，请稍后重试');
          }
          console.log('请求超时，准备重试...');
          await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒后重试
          continue;
        }

        // 处理其他错误
        if (err.response && err.response.data) {
          const errorText = err.response.data.toString('utf8');
          try {
            const errorData = JSON.parse(errorText);
            console.error('微信API错误详情:', errorData);
            
            if (retryCount > maxRetries) {
              throw new Error(`微信小程序码生成失败: ${errorData.errmsg || errorData.message || errorText}`);
            }
            
            // 等待后重试
            await new Promise(resolve => setTimeout(resolve, 2000));
            continue;
          } catch (parseError) {
            if (retryCount > maxRetries) {
              throw new Error('微信小程序码生成失败: ' + errorText);
            }
            await new Promise(resolve => setTimeout(resolve, 2000));
            continue;
          }
        } else if (retryCount > maxRetries) {
          throw new Error('请求微信小程序码API失败: ' + (err.response ? JSON.stringify(err.response.data) : err.message));
        } else {
          console.log('未知错误，准备重试...');
          await new Promise(resolve => setTimeout(resolve, 2000));
          continue;
        }
      }
    }

    // 检查返回内容是否为图片
    if (response.headers['content-type'] && response.headers['content-type'].includes('image')) {
      // 保存二维码到本地文件系统
      const filename = `${scene}_${uuidv4()}.png`;
      const qrcodeDir = path.join(__dirname, '../public/qrcode');
      
      // 确保目录存在
      if (!fs.existsSync(qrcodeDir)) {
        fs.mkdirSync(qrcodeDir, { recursive: true });
      }
      
      const filePath = path.join(qrcodeDir, filename);
      
      try {
        // 保存图片文件到本地
        fs.writeFileSync(filePath, response.data);
        console.log('二维码已保存到本地:', filePath);
        
        // 返回相对路径，由控制器构造完整URL
        const relativePath = `/public/qrcode/${filename}`;
        console.log('返回相对路径:', relativePath);
        return relativePath;
      } catch (writeError) {
        console.error('保存二维码文件失败:', writeError.message);
        throw new Error('保存二维码文件失败: ' + writeError.message);
      }
    } else {
      // 不是图片，可能是错误信息
      const text = response.data.toString('utf8');
      console.error('微信小程序码API返回非图片内容:', text);
      console.error('请求参数:', postData);

      try {
        const errorData = JSON.parse(text);
        throw new Error(`微信小程序码生成失败: ${errorData.errmsg || errorData.message || text}`);
      } catch (parseError) {
        throw new Error('微信小程序码生成失败: ' + text);
      }
    }
  } catch (error) {
    console.error('生成小程序码过程中发生错误:', error.message);
    throw error;
  }
};