Page({
  data: {
    currentRoleLabel: '',
    switchRoles: [],
    currentRoleType: ''
  },
  onLoad(options) {
    this.resolveCurrentRole(options);
  },
  onShow() {
    this.resolveCurrentRole();
  },
  resolveCurrentRole(options = {}) {
    const app = getApp();
    const roleTypeMap = {
      customer: '顾客',
      partner: '合伙人',
      admin: '管理员'
    };
    let label = '';
    let currentRoleType = '';
    let allRoles = [];
    // 1. 优先用参数
    if (options.role_type) {
      currentRoleType = options.role_type;
    }
    // 2. 其次用全局currentRole
    if (!currentRoleType && app.globalData && app.globalData.currentRole) {
      currentRoleType = app.globalData.currentRole.role_type;
      label = app.globalData.currentRole.role_name || roleTypeMap[currentRoleType] || '';
      allRoles = app.globalData.userRoles || [];
    }
    // 3. 再用userInfo
    if (!currentRoleType && app.globalData && app.globalData.userInfo) {
      if (app.globalData.userInfo.role_type) {
        currentRoleType = app.globalData.userInfo.role_type;
        label = app.globalData.userInfo.role_name || roleTypeMap[currentRoleType] || '';
      }
      allRoles = app.globalData.userInfo.roles || [];
    }
    // 4. 兜底：roles第一个为当前
    if (!currentRoleType && allRoles.length > 0) {
      currentRoleType = allRoles[0].role_type;
      label = allRoles[0].role_name || roleTypeMap[currentRoleType] || '';
    }
    // 排序：顾客、合伙人、管理员
    const order = ['customer', 'partner', 'admin'];
    const switchRoles = allRoles.filter(r => r.role_type !== currentRoleType)
      .sort((a, b) => order.indexOf(a.role_type) - order.indexOf(b.role_type))
      .map(r => ({ ...r, roleTypeName: roleTypeMap[r.role_type] || r.role_type }));
    this.setData({ currentRoleLabel: label, switchRoles, currentRoleType });
  },
  onSwitchRole(e) {
    const role = e.currentTarget.dataset.role;
    const app = getApp();
    // 切换后强制更新currentRole
    app.globalData.currentRole = role;
    if (role.role_type === 'admin') {
      wx.reLaunch({ url: '/admin/console/console' });
    } else if (role.role_type === 'partner') {
      wx.reLaunch({ url: '/partner/partner/partner' });
    } else if (role.role_type === 'customer') {
      wx.reLaunch({ url: '/pages/profile/profile' });
    }
  },
  onLogout() {
    // 退出登录逻辑
    const loginStateManager = require('../../utils/login-state-manager');
    loginStateManager.logout().then(() => {
      wx.reLaunch({ url: '/pages/login/login' });
    });
  }
}); 