.create-drawer-container {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  z-index: 9999;
  display: none;
}
.create-drawer-container.visible {
  display: block;
}
.create-drawer-mask {
  position: absolute;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 1;
}
.create-drawer-panel {
  position: absolute;
  left: 0; right: 0; bottom: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  min-height: 800rpx;
  max-height: 85vh;
  z-index: 2;
  box-shadow: 0 -8rpx 32rpx rgba(0,0,0,0.08);
  padding: 0 40rpx;
  animation: slideUp .3s;
  display: flex;
  flex-direction: column;
}
@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}
.create-drawer-header {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 32rpx;
  padding-top: 32rpx;
}
.create-drawer-form {
  flex: 1;
  padding: 0;
}

.form-group {
  margin-bottom: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f8f9fa;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #ff4d4f;
}

.form-picker {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.form-picker:focus {
  border-color: #ff4d4f;
}

.picker-text {
  color: #333;
  font-size: 28rpx;
}

/* 门店形象照上传相关样式 */
.image-upload-area {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 比例 (9/16 = 0.5625) */
  border: 2rpx dashed #e0e0e0;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  overflow: hidden;
}

.store-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #f8f9fa;
}

.upload-hint {
  position: absolute;
  bottom: 16rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 24rpx;
  color: #fff;
  background: rgba(0, 0, 0, 0.7);
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  white-space: nowrap;
  z-index: 10;
  cursor: pointer;
}

.delete-image-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  cursor: pointer;
}

.delete-icon {
  width: 24rpx;
  height: 24rpx;
}

.create-drawer-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 24rpx;
  margin-bottom: 96rpx;
}
.footer-btn {
  flex: 1;
  margin: 0 12rpx;
  height: 72rpx;
  line-height: 72rpx;
  font-size: 30rpx;
  border-radius: 12rpx;
  border: none;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255,77,79,0.08);
  transition: background 0.2s;
}
.footer-btn.cancel {
  background: #fff;
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}
.footer-btn.confirm {
  background: linear-gradient(90deg, #ff4d4f 0%, #ff7a45 100%);
  color: #fff;
  border: none;
} 