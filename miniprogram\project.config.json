{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "miniprogramRoot": "", "compileType": "miniprogram", "projectname": "微信小程序", "setting": {"useCompilerPlugins": ["sass"], "urlCheck": false, "downloadFile": {"allowDownload": true, "networkTimeout": 30000}, "domain": {"allowDomains": ["7872-prod-5geiow562624006-1258719867.tcb.qcloud.la", "6366-cloud1-8gt0q8r163e5937-1258719867.tcb.qcloud.la", "7368-shuke1-0239f8-1258719867.tcb.qcloud.la", "6c6f-lowcode-1gyeaud19ad2d0e7-1258719867.tcb.qcloud.la", "tcb-api.tencentcloudapi.com", "morebuy25-172172-8-1368182116.sh.run.tcloudbase.com", "*.sh.run.tcloudbase.com", "api.weixin.qq.com", "*.tcb.qcloud.la", "*.tcloudbaseapp.com", "tcb-api.tencentcloudapi.com", "*.cloud.tencent.com"]}, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "es6": true, "enhance": true, "minified": true, "postcss": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "", "appid": "wx5f3fbb40507c044a", "libVersion": "3.8.10", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}