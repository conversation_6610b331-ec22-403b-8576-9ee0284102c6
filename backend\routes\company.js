/**
 * 公司信息路由
 */
const express = require('express');
const CompanyInfo = require('../models/CompanyInfo');
const { checkAuth, verifyAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * 获取公司信息（无参数版本）
 * GET /api/company/info
 */
router.get('/info', async (req, res) => {
  try {
    console.log('获取公司信息请求');
    // 获取第一条记录
    const allCompanies = await CompanyInfo.findAll();
    const companyInfo = allCompanies && allCompanies.length > 0 ? allCompanies[0] : null;

    if (companyInfo) {
      console.log('返回公司信息:', companyInfo.id);
      res.json({
        success: true,
        data: companyInfo
      });
    } else {
      console.log('未找到公司信息');
      res.status(404).json({
        success: false,
        message: '暂无公司信息'
      });
    }
  } catch (error) {
    console.error('获取公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

/**
 * 获取公司信息（带ID版本）
 * GET /api/company/info/:id
 */
router.get('/info/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('获取公司信息请求，ID:', id);
    const companyInfo = await CompanyInfo.findById(id);

    if (companyInfo) {
      res.json({
        success: true,
        data: companyInfo
      });
    } else {
      res.status(404).json({
        success: false,
        message: '公司信息不存在'
      });
    }
  } catch (error) {
    console.error('获取公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

/**
 * 获取所有公司信息
 * GET /api/company/list
 */
router.get('/list', async (req, res) => {
  try {
    console.log('获取公司信息列表请求');
    const companyList = await CompanyInfo.findAll();
    res.json({
      success: true,
      data: companyList
    });
  } catch (error) {
    console.error('获取公司信息列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

/**
 * 创建公司信息
 * POST /api/company/create
 * 需要管理员权限
 */
router.post('/create', verifyAdmin, async (req, res) => {
  try {
    const companyData = req.body;
    console.log('创建公司信息请求:', companyData);

    // 基本参数验证
    if (!companyData.company_name) {
      return res.status(400).json({
        success: false,
        message: '公司名称不能为空'
      });
    }

    // 调用模型创建方法
    const result = await CompanyInfo.create(companyData);
    console.log('公司信息创建成功:', result);
    res.json(result);
  } catch (error) {
    console.error('创建公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

/**
 * 更新公司信息
 * PUT /api/company/update/:id
 * 需要管理员权限
 */
router.put('/update/:id', verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const companyData = req.body;
    console.log('=== 公司信息更新请求详情 ===');
    console.log('更新公司信息请求，ID:', id);
    console.log('请求体数据:', JSON.stringify(companyData, null, 2));
    console.log('company_logo字段:', companyData.company_logo);

    // 基本参数验证
    if (!id) {
      return res.status(400).json({
        success: false,
        message: '公司信息ID不能为空'
      });
    }

    // 调用模型更新方法
    const result = await CompanyInfo.update(id, companyData);
    console.log('公司信息更新结果:', result);
    
    res.json(result);
  } catch (error) {
    console.error('更新公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

/**
 * 删除公司信息
 * DELETE /api/company/delete/:id
 * 需要管理员权限
 */
router.delete('/delete/:id', verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    console.log('删除公司信息请求，ID:', id);

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '公司信息ID不能为空'
      });
    }

    const result = await CompanyInfo.delete(id);
    console.log('公司信息删除结果:', result);
    res.json(result);
  } catch (error) {
    console.error('删除公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

/**
 * 搜索公司信息
 * GET /api/company/search?name=关键词
 */
router.get('/search', async (req, res) => {
  try {
    const { name } = req.query;
    console.log('搜索公司信息请求，关键词:', name);

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '搜索关键词不能为空'
      });
    }

    const results = await CompanyInfo.searchByName(name);
    res.json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error('搜索公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

module.exports = router;
