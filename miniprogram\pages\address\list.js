const { userApi } = require('../../utils/api');

Page({
  data: {
    addressList: [],
    loading: true
  },

  onLoad: function(options) {
    console.log('收货地址列表页面加载');
    this.getAddressList();
  },

  onShow: function() {
    // 页面显示时刷新地址列表
    this.getAddressList();
  },

  // 获取地址列表
  getAddressList: function() {
    console.log('获取地址列表');
    
    this.setData({ loading: true });
    
    userApi.getAddressList()
      .then(res => {
        this.setData({ loading: false });
        
        if (res && res.success) {
          console.log('地址列表:', res.data);
          this.setData({
            addressList: res.data || []
          });
        } else {
          console.log('获取地址列表失败:', res.message);
          this.setData({
            addressList: []
          });
        }
      })
      .catch(err => {
        this.setData({ loading: false });
        console.error('获取地址列表失败:', err);
        this.setData({
          addressList: []
        });
      });
  },

  // 选择地址
  selectAddress: function(e) {
    const address = e.currentTarget.dataset.address;
    console.log('选择地址:', address);
    
    // 返回上一页并传递选中的地址
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];
    if (prevPage && prevPage.route === 'pages/order/create') {
      prevPage.setData({
        addressInfo: address
      });
    }
    
    wx.navigateBack();
  },

  // 编辑地址
  editAddress: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log('编辑地址:', id);
    
    wx.navigateTo({
      url: `/pages/address/edit?id=${id}`
    });
  },

  // 删除地址
  deleteAddress: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log('删除地址:', id);
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个收货地址吗？',
      success: (res) => {
        if (res.confirm) {
          userApi.deleteAddress(id)
            .then(res => {
              if (res && res.success) {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                this.getAddressList();
              } else {
                wx.showToast({
                  title: res.message || '删除失败',
                  icon: 'none'
                });
              }
            })
            .catch(err => {
              console.error('删除地址失败:', err);
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 新增地址
  addAddress: function() {
    console.log('新增地址');
    wx.navigateTo({
      url: '/pages/address/edit'
    });
  }
}); 