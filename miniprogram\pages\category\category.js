// pages/shop/shop.js
const { productApi, cartApi, favoriteApi } = require('../../utils/api');

Page({
  data: {
    categories: [],
    subCategories: [],
    products: [],
    banners: [],
    currentCategory: 0,
    currentSubCategory: 0,
    loading: true,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    sortType: 'default', // default, sales, price-asc, price-desc
    showFilter: false,
    // 收藏状态跟踪
    favoriteStatus: {},
    favoriteLoading: {},
    filterOptions: {
      minPrice: '',
      maxPrice: '',
      onlyDiscount: false,
      onlyNew: false
    },
    // 搜索相关
    searchKeyword: '',
    // 窗口宽度
    windowWidth: 0,
    // 购物车数量
    cartCount: 0,
    defaultBanners: [
      { id: 1, imageUrl: '/images/icons2/默认商品.svg', type: 'page', url: '/pages/product/list?category=1', title: '企业服务专场' },
      { id: 2, imageUrl: '/images/icons2/默认商品.svg', type: 'page', url: '/pages/product/list?category=2', title: '知识产权服务' },
      { id: 3, imageUrl: '/images/icons2/默认商品.svg', type: 'page', url: '/pages/product/list?category=3', title: '财税服务专区' },
      { id: 4, imageUrl: '/images/icons2/默认商品.svg', type: 'page', url: '/pages/product/list?category=4', title: '工商注册服务' }
    ]
  },

  onLoad: function (options) {
    // 商城页面加载

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();

    // 只用data里的defaultBanners
    this.setData({
      windowWidth: systemInfo.windowWidth,
      banners: this.data.defaultBanners // 只用data里的defaultBanners
    });

    // 尝试从API获取轮播图数据
    this.getBanners();

    // 获取分类数据（商品加载顺序优化，分类数据获取后再加载商品）
    this.getCategories(options);

    // 获取购物车数量
    this.getCartCount();

    // 启动定时器，定期检查购物车更新
    this.startCartUpdateChecker();

    // 如果有传入的分类参数，切换到对应分类
    if (options.categoryId) {
      const categoryId = parseInt(options.categoryId);
      // 查找分类索引
      const categoryIndex = this.data.categories.findIndex(cat => cat.id === categoryId);
      if (categoryIndex >= 0) {
        this.setData({ currentCategory: categoryIndex });
        this.getSubCategories(categoryId, options);
      }
    }

    // 如果有传入的子分类参数，切换到对应子分类
    if (options.subCategoryId) {
      const subCategoryId = parseInt(options.subCategoryId);
      // 等待子分类加载完成后再切换
      setTimeout(() => {
        const subCategoryIndex = this.data.subCategories.findIndex(subCat => subCat.id === subCategoryId);
        if (subCategoryIndex >= 0) {
          this.setData({ currentSubCategory: subCategoryIndex });
          this.getProducts();
        }
      }, 500);
    }
  },

  // 启动购物车更新检查器
  startCartUpdateChecker: function() {
    // 每3秒检查一次购物车更新，但只在已登录时执行
    this.cartUpdateTimer = setInterval(() => {
      // 检查登录状态，只有已登录才检查购物车更新
      const app = getApp();
      const globalData = app.globalData;
      const localUserInfo = wx.getStorageSync('userInfo');
      const isLoggedIn = (globalData && globalData.userInfo && globalData.userInfo.id) || 
                        (localUserInfo && localUserInfo.id);
      
      if (isLoggedIn) {
        this.checkCartUpdated();
      }
    }, 3000);
  },

  // 页面卸载时清除定时器
  onUnload: function() {
    if (this.cartUpdateTimer) {
      clearInterval(this.cartUpdateTimer);
    }
  },

  // 处理商品图片加载错误
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const products = this.data.products;
    
    // 确保索引有效
    if (index >= 0 && index < products.length) {
      // 设置默认图片
      products[index].images = ['/images/mo/mogoods.jpg'];
      
      this.setData({
        products: products
      });
    }
  },

  // 页面显示时更新购物车数量
  onShow: function() {
    // 每次页面显示时都刷新购物车数量
    this.getCartCount();
    // 检查购物车是否有更新，但只在已登录时执行
    const app = getApp();
    const globalData = app.globalData;
    const localUserInfo = wx.getStorageSync('userInfo');
    const isLoggedIn = (globalData && globalData.userInfo && globalData.userInfo.id) || 
                      (localUserInfo && localUserInfo.id);
    
    if (isLoggedIn && this.checkCartUpdated) {
      this.checkCartUpdated();
    }
    
    // 更新当前显示商品的收藏状态
    if (isLoggedIn && this.data.products && this.data.products.length > 0) {
      this.checkProductsFavoriteStatus(this.data.products);
    }
  },

  // 检查购物车是否有更新
  checkCartUpdated: function() {
    // 获取上次购物车更新时间
    const lastUpdated = wx.getStorageSync('cartItemsUpdated');
    const lastChecked = this.lastCheckedTime || 0;

    // 如果购物车有更新，则更新购物车数量
    if (lastUpdated && lastUpdated > lastChecked) {
      // 购物车数据已更新
      this.updateCartCount();
      this.lastCheckedTime = lastUpdated;

      // 购物车已更新，静默更新徽标
      console.log('购物车已更新，徽标已刷新');
    }
  },

  // 获取分类数据
  getCategories: function(options) {
    // 开始获取分类数据
    const localCategories = [
      { id: 1, name: '企业服务' },
      { id: 2, name: '平台使用' },
      { id: 3, name: '合伙人' },
      { id: 4, name: '周边产品' }
    ];
    // 先设置本地数据，确保界面有内容显示
    this.setData({
      categories: localCategories,
      currentCategory: 0,
      currentSubCategory: 0
    });
    // 获取第一个分类的子分类
    if (localCategories.length > 0) {
      this.getSubCategories(localCategories[0].id, options);
    }
    // 使用 API 获取分类数据
    productApi.getShopCategories()
      .then(res => {
        // 获取分类数据成功
        if (res.success) {
          const categories = res.data;
          this.setData({
            categories: categories,
            currentCategory: 0,
            currentSubCategory: 0
          });
          // 获取第一个分类的子分类
          if (categories.length > 0) {
            this.getSubCategories(categories[0].id, options);
          }
        } else {
          // 获取分类数据失败
          wx.showToast({
            title: '获取分类失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        // 获取分类数据出错
      });
  },

  // 获取子分类数据
  getSubCategories: function(categoryId, options) {
    // 开始获取子分类数据
    const subCategoriesMap = {
      1: [
        { id: 101, name: '工商注册' },
        { id: 102, name: '财税服务' },
        { id: 103, name: '知识产权' },
        { id: 104, name: '法律服务' }
      ],
      2: [
        { id: 201, name: '会员服务' },
        { id: 202, name: '平台指南' },
        { id: 203, name: '增值服务' },
        { id: 204, name: '积分套餐' }
      ],
      3: [
        { id: 301, name: '城市合伙人' },
        { id: 302, name: '服务商' },
        { id: 303, name: '推广员' }
      ],
      4: [
        { id: 401, name: '办公用品' },
        { id: 402, name: '数码产品' },
        { id: 403, name: '礼品定制' }
      ]
    };
    const localSubCategories = subCategoriesMap[categoryId] || [];
    this.setData({
      subCategories: localSubCategories,
      currentSubCategory: 0
    });
    productApi.getShopSubCategories(categoryId)
      .then(res => {
        // 获取子分类数据成功
        if (res.success) {
          const subCategories = res.data;
          if (Array.isArray(subCategories) && subCategories.length > 0) {
            this.setData({
              subCategories: subCategories,
              currentSubCategory: 0
            });
          }
          // 处理页面参数跳转
          if (options && options.subCategoryId) {
            const subCategoryId = parseInt(options.subCategoryId);
            const subCategoryIndex = this.data.subCategories.findIndex(subCat => subCat.id === subCategoryId);
            if (subCategoryIndex >= 0) {
              this.setData({ currentSubCategory: subCategoryIndex });
            }
          }
          this.getProducts();
        } else {
          // 获取子分类数据失败
          this.getProducts();
        }
      })
      .catch(err => {
        // 获取子分类数据出错
        this.getProducts();
      });
  },

  // 获取商品数据
  getProducts: function(append = false) {
    // 开始获取商品数据

    const { currentCategory, currentSubCategory, page, pageSize, sortType, filterOptions, searchKeyword } = this.data;
    const categoryId = this.data.categories[currentCategory]?.id || '';
    const subCategoryId = this.data.subCategories[currentSubCategory]?.id || '';

    // 当前分类信息准备完成

    // 设置加载状态
    if (!append) {
      this.setData({
        loading: true
      });
    }

    // 构建API请求参数
    const params = {
      page: page,
      pageSize: pageSize,
      sortType: sortType,
      keyword: searchKeyword
    };

    if (categoryId) {
      params.categoryId = categoryId;
    }

    if (subCategoryId) {
      params.subCategoryId = subCategoryId;
    }

    // 请求参数准备完成

    if (filterOptions) {
      if (filterOptions.minPrice) {
        params.minPrice = filterOptions.minPrice;
      }

      if (filterOptions.maxPrice) {
        params.maxPrice = filterOptions.maxPrice;
      }

      if (filterOptions.onlyDiscount) {
        params.onlyDiscount = filterOptions.onlyDiscount;
      }

      if (filterOptions.onlyNew) {
        params.onlyNew = filterOptions.onlyNew;
      }
    }

    // 添加调试信息
    // 准备发送API请求获取商品数据

    // 使用API获取商品数据
    productApi.getProducts(params)
      .then(res => {
        // 获取商品数据成功
        // 响应数据处理

        if (res.success) {
          // 检查返回的数据结构，兼容两种格式
          let products = [];

          if (Array.isArray(res.data)) {
            products = res.data;
            console.log('数据是数组格式');
          } else if (res.data && res.data.list && Array.isArray(res.data.list)) {
            products = res.data.list;
            console.log('数据是对象格式，包含list数组');
          } else if (res.data) {
            console.log('数据格式不符合预期，尝试解析:', typeof res.data);
            // 尝试其他可能的格式
            if (typeof res.data === 'string') {
              try {
                const parsedData = JSON.parse(res.data);
                if (Array.isArray(parsedData)) {
                  products = parsedData;
                  console.log('成功从字符串解析为数组');
                } else if (parsedData.list && Array.isArray(parsedData.list)) {
                  products = parsedData.list;
                  console.log('成功从字符串解析为对象，包含list数组');
                }
              } catch (e) {
                console.error('解析响应数据失败:', e);
              }
            }
          }

          console.log('获取到的商品数量:', products.length);
          console.log('[分类页] API响应数据结构:', res.data);

          if (products.length === 0) {
            console.log('没有获取到商品数据，检查响应:', res);
          }

          // 检查是否有会员服务产品
          const memberProducts = Array.isArray(products) ? products.filter(p => p.subCategoryId === 201) : [];
          console.log('会员服务产品数量:', memberProducts.length);
          console.log('会员服务产品:', memberProducts);

          // 处理商品数据，将 images 字符串转换为数组
          products = products.map(product => {
            console.log('处理商品数据:', product);

            // 如果 images 是字符串，尝试解析为 JSON
            if (product.images && typeof product.images === 'string') {
              try {
                product.images = JSON.parse(product.images);
                console.log('成功解析商品图片:', product.images);
              } catch (e) {
                console.error('解析商品图片失败:', e);
                // 解析失败时，尝试使用简单的字符串分割
                if (product.images.includes(',')) {
                  product.images = product.images.split(',');
                  console.log('使用逗号分割解析图片:', product.images);
                } else {
                  // 如果不包含逗号，可能是单个图片路径，直接放入数组
                  product.images = [product.images];
                  console.log('使用单个图片路径:', product.images);
                }
              }
            } else if (!product.images || !Array.isArray(product.images)) {
              // 如果没有图片或者不是数组，设置默认图片
              product.images = ['/images/mo/mogoods.jpg'];
              console.log('设置默认图片:', product.images);
            }
            
            // 检查图片路径是否包含旧路径，如果包含则使用默认图片
            // 简化图片处理逻辑 - 确保有可用的图片数组
            if (!product.images || !Array.isArray(product.images) || product.images.length === 0) {
              product.images = ['/images/mo/mogoods.jpg'];
            }

            // 确保 id 字段存在（兼容前端代码）
            if (!product.id && product._id) {
              product.id = product._id;
            } else if (!product.id) {
              // 如果既没有id也没有_id，生成一个临时id
              product.id = 'product_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
            }

            // 确保价格字段是字符串
            if (product.price && typeof product.price !== 'string') {
              product.price = product.price.toString();
            }

            if (product.originalPrice && typeof product.originalPrice !== 'string') {
              product.originalPrice = product.originalPrice.toString();
            }

            return product;
          });

          // 更新数据
          let hasMore = false;
          
          // 判断是否有更多数据的逻辑
          if (res.data && res.data.total !== undefined && res.data.total !== null) {
            // 如果后端返回了总数，用总数判断（最准确）
            const currentTotal = append ? this.data.products.length + products.length : products.length;
            hasMore = currentTotal < res.data.total;
            console.log('[分类页分页] 使用总数判断 - 当前商品数:', currentTotal, '总数:', res.data.total, '是否有更多:', hasMore);
          } else {
            // 如果没有总数，用返回的数据量判断
            if (products.length === 0) {
              // 如果本次没返回任何数据，说明没有更多了
              hasMore = false;
              console.log('[分类页分页] 本次返回0个商品，没有更多数据');
            } else if (products.length < pageSize) {
              // 如果返回的数据少于页面大小，说明是最后一页
              hasMore = false;
              console.log('[分类页分页] 返回数据少于页面大小 - 返回:', products.length, '页面大小:', pageSize, '没有更多数据');
            } else {
              // 如果返回的数据等于页面大小，可能还有更多数据
              hasMore = true;
              console.log('[分类页分页] 返回数据等于页面大小 - 返回:', products.length, '页面大小:', pageSize, '可能有更多数据');
            }
          }
          
          this.setData({
            products: append ? this.data.products.concat(products) : products,
            loading: false,
            refreshing: false,
            hasMore: hasMore
          });
          
          console.log('[分类页] 数据更新完成:');
          console.log('  - 当前页码:', page);
          console.log('  - 本次返回商品数:', products.length);
          console.log('  - 总商品数:', append ? this.data.products.length : products.length);
          console.log('  - 是否有更多:', hasMore);
          console.log('  - 后端返回的总数:', res.data ? res.data.total : '无');
          
          // 获取商品收藏状态
          this.checkProductsFavoriteStatus(products);
        } else {
          console.warn('获取商品数据失败', res);

          // 显示错误提示
          wx.showToast({
            title: res.message || '获取商品数据失败',
            icon: 'none'
          });

          this.setData({
            loading: false,
            refreshing: false
          });
        }
      })
      .catch(err => {
        console.error('获取商品数据出错', err);
        console.error('错误详情:', JSON.stringify(err));

        // 显示错误提示
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none',
          duration: 3000
        });

        this.setData({
          loading: false,
          refreshing: false
        });
      });
  },

  // 切换一级分类
  switchCategory: function(e) {
    const index = e.currentTarget.dataset.index;
    if (index === this.data.currentCategory) {
      return;
    }

    this.setData({
      currentCategory: index,
      currentSubCategory: 0,
      loading: true,
      page: 1,
      products: []
    });

    // 获取子分类
    const categoryId = this.data.categories[index].id;
    this.getSubCategories(categoryId, this.data.options);
  },

  // 切换二级分类
  switchSubCategory: function(e) {
    const index = e.currentTarget.dataset.index;
    if (index === this.data.currentSubCategory) {
      return;
    }

    this.setData({
      currentSubCategory: index,
      loading: true,
      page: 1,
      products: []
    });

    // 获取商品
    this.getProducts();
  },

  // 切换排序方式
  switchSortType: function(e) {
    const sortType = e.currentTarget.dataset.type;
    if (sortType === this.data.sortType) {
      return;
    }

    this.setData({
      sortType: sortType,
      loading: true,
      page: 1,
      products: []
    });

    // 获取商品
    this.getProducts();
  },

  // 显示筛选面板
  showFilter: function() {
    this.setData({
      showFilter: true
    });
  },

  // 隐藏筛选面板
  hideFilter: function() {
    this.setData({
      showFilter: false
    });
  },

  // 输入最低价格
  inputMinPrice: function(e) {
    const filterOptions = this.data.filterOptions;
    filterOptions.minPrice = e.detail.value;

    this.setData({
      filterOptions: filterOptions
    });
  },

  // 输入最高价格
  inputMaxPrice: function(e) {
    const filterOptions = this.data.filterOptions;
    filterOptions.maxPrice = e.detail.value;

    this.setData({
      filterOptions: filterOptions
    });
  },

  // 切换仅显示优惠商品
  toggleOnlyDiscount: function() {
    const filterOptions = this.data.filterOptions;
    filterOptions.onlyDiscount = !filterOptions.onlyDiscount;

    this.setData({
      filterOptions: filterOptions
    });
  },

  // 切换仅显示新品
  toggleOnlyNew: function() {
    const filterOptions = this.data.filterOptions;
    filterOptions.onlyNew = !filterOptions.onlyNew;

    this.setData({
      filterOptions: filterOptions
    });
  },

  // 重置筛选条件
  resetFilter: function() {
    this.setData({
      filterOptions: {
        minPrice: '',
        maxPrice: '',
        onlyDiscount: false,
        onlyNew: false
      }
    });
  },

  // 应用筛选条件
  applyFilter: function() {
    this.setData({
      showFilter: false,
      loading: true,
      page: 1,
      products: []
    });

    // 获取商品
    this.getProducts();
  },

  // 点击商品
  onProductTap: function(e) {
    const id = e.currentTarget.dataset.id;
    const product = this.data.products.find(p => p.id === id || p._id === id);
    console.log('点击商品，详细信息:', {
      clickedId: id,
      idType: typeof id,
      product: product ? {
        id: product.id,
        _id: product._id,
        name: product.name
      } : null
    });
    wx.navigateTo({
      url: `/pages/product/detail?id=${id}`
    });
  },



  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({
      refreshing: true,
      page: 1
    });

    this.getProducts();
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom: function() {
    console.log('[分类页] onReachBottom触发 - hasMore:', this.data.hasMore, 'loading:', this.data.loading);
    
    if (!this.data.hasMore || this.data.loading) {
      console.log('[分类页] 跳过加载更多 - hasMore:', this.data.hasMore, 'loading:', this.data.loading);
      return;
    }

    console.log('[分类页] 开始加载更多，当前页:', this.data.page, '-> 下一页:', this.data.page + 1);
    
    this.setData({
      page: this.data.page + 1,
      loading: true
    });

    this.getProducts(true);
  },

  // 获取轮播图数据
  getBanners: function() {
    console.log('开始获取轮播图数据');
    const _this = this;
    productApi.getBanners('customer_category')
      .then(res => {
        if (res && res.success && Array.isArray(res.data) && res.data.length > 0) {
          // 为轮播图数据添加默认字段映射
          const banners = res.data.map(banner => ({
            ...banner,
            imageUrl: banner.imageUrl || banner.image_url || '/images/icons2/默认商品.png',
            linkUrl: banner.linkUrl || banner.link_url || '',
            title: banner.title || ''
          }));
          _this.setData({ banners });
        } else {
          _this.setData({ banners: _this.data.defaultBanners });
        }
      })
      .catch(() => {
        _this.setData({ banners: _this.data.defaultBanners });
      });
  },

  // 点击轮播图
  onBannerTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const banner = this.data.banners[index];

    console.log('点击轮播图:', banner);

    if (banner.type === 'url') {
      // 打开网页
      console.log('打开网页:', banner.url || banner.linkUrl);
      wx.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(banner.url || banner.linkUrl)}`
      });
    } else if (banner.type === 'page') {
      // 打开小程序页面
      console.log('打开小程序页面:', banner.url || banner.linkUrl);
      wx.navigateTo({
        url: banner.url || banner.linkUrl
      });
    } else {
      // 默认处理
      console.log('默认处理轮播图点击:', banner.linkUrl);
      if (banner.linkUrl) {
        wx.navigateTo({
          url: banner.linkUrl
        });
      }
    }
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索确认（键盘回车）
  onSearchConfirm: function() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }
    
    console.log('搜索商品:', keyword);
    
    // 保存搜索历史
    this.saveSearchHistory(keyword);
    
    // 跳转到搜索页面
    wx.navigateTo({
      url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}&type=product`
    });
  },

  // 点击搜索按钮
  onSearchTap: function() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }
    
    console.log('搜索商品:', keyword);
    
    // 保存搜索历史
    this.saveSearchHistory(keyword);
    
    // 跳转到搜索页面
    wx.navigateTo({
      url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}&type=product`
    });
  },

  /**
   * 保存搜索历史
   */
  saveSearchHistory: function(keyword) {
    if (!keyword) return;

    try {
      let history = wx.getStorageSync('searchHistory') || [];

      // 如果已存在相同关键词，先移除
      history = history.filter(item => item !== keyword);

      // 添加到历史记录开头
      history.unshift(keyword);

      // 限制最多保存10条
      if (history.length > 10) {
        history = history.slice(0, 10);
      }

      // 保存到本地存储
      wx.setStorageSync('searchHistory', history);
    } catch (e) {
      console.error('保存搜索历史失败', e);
    }
  },

  // 获取购物车数量（不强制登录，仅用于显示）
  getCartCount: function() {
    // 检查登录状态，只有已登录才获取购物车数据
    const app = getApp();
    const globalData = app.globalData;
    const localUserInfo = wx.getStorageSync('userInfo');
    const isLoggedIn = (globalData && globalData.userInfo && globalData.userInfo.id) || 
                      (localUserInfo && localUserInfo.id);
    
    if (!isLoggedIn) {
      // 未登录时直接设置购物车数量为0，不发起API请求
      this.setData({ cartCount: 0 });
      return;
    }
    
    // 已登录时才使用API获取购物车数据
    cartApi.getCartItems()
      .then(res => {
        if (res.success) {
          const apiCartItems = res.data;
          let apiCount = 0;
          apiCartItems.forEach(item => {
            apiCount += (item.quantity || 1);
          });
          this.setData({ cartCount: apiCount });
        } else {
          this.setData({ cartCount: 0 });
        }
      })
      .catch(err => {
        // 不弹窗报错，静默失败
        this.setData({ cartCount: 0 });
      });
  },



  // 更新购物车数量（从本地存储获取实际数量）
  updateCartCount: function() {
    // 直接调用getCartCount，保证cartCount与接口数据同步
    this.getCartCount();
  },

  // 跳转到积分套餐分类
  goToPointsPackage: function() {
    // 设置平台使用分类（索引为1）
    this.setData({
      currentCategory: 1,
      loading: true,
      page: 1,
      products: []
    });

    // 获取平台使用的子分类
    this.getSubCategories(2, this.data.options);

    // 等待子分类加载完成后，切换到积分套餐子分类（索引为3）
    setTimeout(() => {
      this.setData({
        currentSubCategory: 3,
        loading: true,
        page: 1,
        products: []
      });

      // 获取商品
      this.getProducts();
    }, 500);
  },

  // 手动添加会员产品
  // addMemberProducts: function() {
  //   ...
  // },

  /**
   * 添加收藏
   */
  /**
   * 检查商品收藏状态 - 优化版本，添加防抖和批量处理
   */
  checkProductsFavoriteStatus: function(products) {
    // 清除之前的定时器，实现防抖
    if (this.favoriteCheckTimer) {
      clearTimeout(this.favoriteCheckTimer);
    }
    
    // 防抖处理，避免频繁调用
    this.favoriteCheckTimer = setTimeout(() => {
      this._doCheckProductsFavoriteStatus(products);
    }, 300); // 300ms防抖延迟
  },

  /**
   * 实际执行收藏状态检查的方法
   */
  _doCheckProductsFavoriteStatus: function(products) {
    // 直接检查登录状态，不使用app.checkNeedLogin避免弹窗
    const app = getApp();
    const globalData = app.globalData;
    const localUserInfo = wx.getStorageSync('userInfo');
    const isLoggedIn = (globalData && globalData.userInfo && globalData.userInfo.id) || 
                      (localUserInfo && localUserInfo.id);
    
    if (!isLoggedIn) {
      console.log('用户未登录，跳过收藏状态检查');
      return;
    }

    if (!products || !Array.isArray(products) || products.length === 0) {
      console.log('商品列表为空，跳过收藏状态检查');
      return;
    }

    // 获取所有商品ID，过滤掉无效的ID
    const productIds = products
      .map(product => product.id)
      .filter(id => id && typeof id !== 'undefined');
    
    if (productIds.length === 0) {
      console.log('没有有效的商品ID，跳过收藏状态检查');
      return;
    }

    console.log('开始检查收藏状态，商品数量:', productIds.length);

    // 限制并发请求数量，避免过多API调用
    const MAX_CONCURRENT = 5; // 最多同时5个请求
    const batches = [];
    for (let i = 0; i < productIds.length; i += MAX_CONCURRENT) {
      batches.push(productIds.slice(i, i + MAX_CONCURRENT));
    }

    // 批量处理，避免一次性发送太多请求
    batches.forEach((batch, batchIndex) => {
      setTimeout(() => {
        batch.forEach(productId => {
          // 检查是否已经有该商品的收藏状态，避免重复请求
          if (this.data.favoriteStatus.hasOwnProperty(productId)) {
            return;
          }

          // 检查收藏状态
          favoriteApi.checkFavoriteStatus(productId)
            .then(res => {
              if (res.success && res.data) {
                // 更新收藏状态
                this.setData({
                  [`favoriteStatus.${productId}`]: res.data.isFavorited || false
                });
              }
            })
            .catch(err => {
              console.error('获取收藏状态失败:', err, 'productId:', productId);
              // 设置默认状态，避免重复请求
              this.setData({
                [`favoriteStatus.${productId}`]: false
              });
            });
        });
      }, batchIndex * 100); // 每批间隔100ms，进一步降低并发压力
    });
    // 不再使用app.checkNeedLogin，避免弹出登录提示
  },

  onFavoriteTap: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('切换收藏状态:', productId);
    
    // 检查登录状态
    const app = getApp();
    app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        // 如果已经在加载中，则不重复操作
        if (this.data.favoriteLoading[productId]) {
          return;
        }
        
        // 设置加载状态
        this.setData({
          [`favoriteLoading.${productId}`]: true
        });
        
        // 根据当前收藏状态决定是添加还是移除收藏
        const isFavorited = this.data.favoriteStatus[productId] || false;
        
        // 调用相应的API
        const apiCall = isFavorited ? 
          favoriteApi.removeFromFavorites(productId) : 
          favoriteApi.addToFavorites(productId);
        
        apiCall.then(res => {
          // 更新收藏状态
          this.setData({
            [`favoriteStatus.${productId}`]: !isFavorited,
            [`favoriteLoading.${productId}`]: false
          });
          
          if (res.success) {
            // 显示成功提示
            wx.showToast({
              title: isFavorited ? '已取消收藏' : '已加入收藏',
              icon: 'success',
              duration: 1500
            });
          } else {
            wx.showToast({
              title: res.message || '操作失败',
              icon: 'none'
            });
          }
        }).catch(err => {
          console.error('收藏操作失败', err);
          
          // 重置加载状态
          this.setData({
            [`favoriteLoading.${productId}`]: false
          });
          
          // 显示错误提示
          wx.showToast({
            title: '网络错误，请稍后再试',
            icon: 'none'
          });
        });
      }
    });
  },

  /**
   * 加入购物车
   */
  onAddToCartTap: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('加入购物车:', productId);
    
    // 检查登录状态
    const app = getApp();
    app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.performAddToCart(productId);
      }
    });
  },

  /**
   * 执行添加到购物车操作
   */
  performAddToCart: function(productId) {
    // 显示加载中
    wx.showLoading({
      title: '正在添加到购物车',
      mask: true
    });
    
    // 使用API添加商品到购物车
    cartApi.addToCart(productId, 1)
      .then(res => {
        wx.hideLoading();
        
        if (res.success) {
          // 显示成功提示
          wx.showToast({
            title: '已加入购物车',
            icon: 'success',
            duration: 1500
          });
        } else {
          wx.showToast({
            title: res.message || '添加失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('添加到购物车失败', err);
        wx.hideLoading();
        
        // 显示错误提示
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  }
});
