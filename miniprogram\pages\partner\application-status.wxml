<!--pages/partner/application-status.wxml-->
<view class="application-status-container">
  <view class="page-header">
    <view class="page-title">申请记录</view>
  </view>
  
  <!-- 申请记录列表 -->
  <view class="application-list" wx:if="{{!loading && !empty}}">
    <view class="application-item" 
          wx:for="{{applications}}" 
          wx:key="id"
          bindtap="viewDetail"
          data-index="{{index}}">
      <view class="application-info">
        <view class="application-region">{{item.province}} {{item.city}} {{item.district}}</view>
        <view class="application-time">申请时间：{{item.created_at ? wxs.formatTime(item.created_at) : '未知'}}</view>
      </view>
      <view class="application-status" style="color: {{wxs.getStatusColor(item.status)}}">
        {{wxs.getStatusText(item.status)}}
      </view>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && empty}}">
    <view class="empty-icon">📝</view>
    <view class="empty-text">您还没有提交过合伙人申请</view>
    <button class="reapply-btn" bindtap="reapply">立即申请</button>
  </view>
</view>

<!-- 辅助函数 -->
<wxs module="wxs">
  function formatTime(timestamp) {
    if (!timestamp) return '未知时间';
    // 由于 WXS 中无法使用 Date 对象的完整功能，这里做简单处理
    var date = getDate(parseInt(timestamp));
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    var hour = date.getHours();
    var minute = date.getMinutes();
    
    month = month < 10 ? ('0' + month) : month;
    day = day < 10 ? ('0' + day) : day;
    hour = hour < 10 ? ('0' + hour) : hour;
    minute = minute < 10 ? ('0' + minute) : minute;
    
    return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
  }
  
  function getStatusText(status) {
    if (status === 'pending') return '审核中';
    if (status === 'approved') return '已通过';
    if (status === 'rejected') return '已拒绝';
    return '未知状态';
  }
  
  function getStatusColor(status) {
    if (status === 'pending') return '#FFA500';
    if (status === 'approved') return '#52c41a';
    if (status === 'rejected') return '#ff4d4f';
    return '#999';
  }
  
  module.exports = {
    formatTime: formatTime,
    getStatusText: getStatusText,
    getStatusColor: getStatusColor
  };
</wxs>