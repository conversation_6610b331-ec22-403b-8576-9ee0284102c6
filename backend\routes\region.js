const express = require('express');
const fs = require('fs');
const path = require('path');
const router = express.Router();

// 获取全部省市区数据
router.get('/all', (req, res) => {
  const filePath = path.resolve(__dirname, '../public/region/region-pcas.json');
  fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
      return res.status(500).json({ success: false, message: '读取省市区数据失败', error: err.message });
    }
    try {
      const json = JSON.parse(data);
      res.json({ success: true, data: json });
    } catch (parseErr) {
      res.status(500).json({ success: false, message: '省市区数据解析失败', error: parseErr.message });
    }
  });
});

module.exports = router; 