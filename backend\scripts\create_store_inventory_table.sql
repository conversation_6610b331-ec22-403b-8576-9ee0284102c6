-- 创建门店库存表
CREATE TABLE IF NOT EXISTS `store_inventory` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `store_no` VARCHAR(32) NOT NULL COMMENT '门店编号',
  `product_id` VARCHAR(32) NOT NULL COMMENT '商品ID',
  `cloud_quantity` INT NOT NULL DEFAULT 0 COMMENT '云仓库存数量',
  `offline_quantity` INT NOT NULL DEFAULT 0 COMMENT '线下库存数量',
  `create_time` BIGINT NOT NULL COMMENT '创建时间',
  `update_time` BIGINT NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_store_product` (`store_no`, `product_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_store_no` (`store_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店库存表';