<!--pages/settings/region/region.wxml-->
<view class="region-container">
  <view class="region-header">
    <view class="header-title">选择地区</view>
  </view>

  <view class="region-content">
    <view class="region-picker">
      <picker mode="region" bindchange="onRegionChange" value="{{region}}">
        <view class="picker-item">
          <text class="picker-label">选择地区</text>
          <view class="picker-value">
            <text>{{region[0]}} {{region[1]}} {{region[2]}}</text>
            <view class="arrow-right">》</view>
          </view>
        </view>
      </picker>
    </view>

    <view class="save-button {{loading ? 'disabled' : ''}}" bindtap="saveRegion">
      <text>{{loading ? '保存中...' : '确认选择'}}</text>
    </view>
  </view>
</view> 