/* pages/order/pay.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 加载中 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  height: 300rpx;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 26rpx;
  color: #999;
}

/* 订单信息 */
.order-section {
  background-color: #ff6b00;
  padding: 40rpx 30rpx;
  color: #fff;
}

.order-title {
  font-size: 28rpx;
  margin-bottom: 20rpx;
  opacity: 0.9;
}

.order-info {
  display: flex;
  flex-direction: column;
}

.order-no {
  font-size: 26rpx;
  margin-bottom: 10rpx;
  opacity: 0.9;
}

.order-amount {
  font-size: 28rpx;
}

.order-amount .amount {
  font-size: 40rpx;
  font-weight: 500;
  margin-left: 10rpx;
}

/* 通用区块样式 */
.payment-section,
.products-section,
.amount-section {
  background-color: #fff;
  margin: 20rpx 20rpx 0;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 24rpx;
  background-color: #ff6b00;
  border-radius: 3rpx;
}

/* 支付方式 */
.payment-methods {
  padding: 10rpx 0;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.payment-item:last-child {
  border-bottom: none;
}

.payment-left {
  display: flex;
  align-items: center;
}

.payment-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.payment-info {
  display: flex;
  flex-direction: column;
}

.payment-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.payment-desc {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.balance-info {
  font-size: 24rpx;
  color: #999;
}

.payment-right {
  display: flex;
  align-items: center;
}

.radio-circle {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

.radio-circle.checked {
  border-color: #ff6b00;
  background-color: #ff6b00;
  position: relative;
}

.radio-circle.checked::after {
  content: '';
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #fff;
}

.payment-item.selected {
  background-color: #fff9f5;
}

.payment-item.disabled {
  opacity: 0.5;
}

/* 商品列表 */
.products-list {
  padding: 10rpx 0;
}

.product-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-specs {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.product-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.product-price {
  font-size: 28rpx;
  color: #ff6b00;
  font-weight: 500;
}

.product-qty {
  font-size: 24rpx;
  color: #999;
}

/* 金额详情 */
.amount-list {
  padding: 10rpx 0;
}

.amount-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 26rpx;
}

.amount-item:last-child {
  margin-bottom: 0;
}

.amount-label {
  color: #999;
}

.amount-value {
  color: #333;
}

.amount-item.total {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx dashed #eee;
}

.amount-item.total .amount-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.amount-item.total .amount-value {
  font-size: 32rpx;
  color: #ff6b00;
  font-weight: 500;
}

/* 底部支付按钮 */
.footer-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.total-info {
  font-size: 28rpx;
  color: #333;
}

.total-amount {
  font-size: 36rpx;
  color: #ff6b00;
  font-weight: 500;
}

.pay-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #ff6b00;
  color: #fff;
  font-size: 30rpx;
  text-align: center;
  border-radius: 40rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}