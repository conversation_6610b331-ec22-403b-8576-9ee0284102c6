-- 创建合伙人表
CREATE TABLE IF NOT EXISTS `partners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `store_no` varchar(50) NOT NULL COMMENT '门店编号',
  `type` varchar(50) NOT NULL COMMENT '合伙人类型',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '股本金',
  `percent` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '股份比例',
  `created_at` bigint(20) NOT NULL COMMENT '创建时间',
  `updated_at` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_store_no` (`store_no`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合伙人表';

-- 插入一些测试数据（可选）
INSERT INTO `partners` (`user_id`, `store_id`, `store_no`, `type`, `amount`, `percent`, `created_at`) VALUES
(1, 1, 'M310104002', '创始人', 10000.00, 50.00, UNIX_TIMESTAMP() * 1000),
(2, 1, 'M310104002', '合伙人', 5000.00, 25.00, UNIX_TIMESTAMP() * 1000),
(3, 1, 'M310104002', '合伙人', 5000.00, 25.00, UNIX_TIMESTAMP() * 1000); 