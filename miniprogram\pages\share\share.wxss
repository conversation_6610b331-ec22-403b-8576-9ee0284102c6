.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 20rpx;
  background: #fff;
  min-height: 100vh;
}
.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  margin-top: 20rpx;
}
.main-image {
  width: 600rpx;
  height: 600rpx;
  background: #20647a;
  border-radius: 12rpx 12rpx 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preset-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx 12rpx 0 0;
}
.info-section {
  width: 600rpx;
  background: #fff;
  border: 2rpx solid #20647a;
  border-radius: 12rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}
.avatar-block {
  margin-left: 30rpx;
}
.avatar-circle {
  width: 100rpx;
  height: 100rpx;
  background: #1abc9c;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-right: 18rpx;
}
.nickname-block {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  margin-right: 18rpx;
}
.nickname {
  font-size: 34rpx;
  color: #333;
}
.recommend {
  font-size: 24rpx;
  color: #888;
  margin-left: 10rpx;
}
.qrcode-block {
  margin-right: 30rpx;
}
.qrcode-container {
  width: 160rpx;
  height: 160rpx;
  margin-left: 20rpx; /* 恢复原来的间距 */
  flex-shrink: 0; /* 防止二维码被压缩 */
}

.qrcode {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
}

.qrcode-loading {
  width: 160rpx;
  height: 160rpx;
  background: #f5f5f5;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 22rpx;
  text-align: center;
  border: 2rpx dashed #ddd;
  transition: all 0.3s ease;
}

.qrcode-loading:active {
  background: #eeeeee;
  transform: scale(0.95);
}

.retry-text {
  color: #ff6b6b;
  font-weight: bold;
  line-height: 1.3;
}

.loading-text {
  color: #1ec6f7;
  animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes pulse {
  from {
    opacity: 0.6;
  }
  to {
    opacity: 1;
  }
}
.desc {
  font-size: 32rpx; /* 恢复原来的字号 */
  color: #222;
  margin-top: 18rpx;
  text-align: left;
  margin-left: 30rpx;
  /* 移除之前添加的右边距和宽度限制，恢复原来的布局 */
}
.actions {
  width: 600rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 40rpx;
}
.save-btn, .share-btn {
  width: 260rpx;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  background: #f5f5f5;
  color: #222;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.save-btn:active {
  background: #e8e8e8;
  transform: scale(0.98);
}

.share-btn {
  background: #1ec6f7;
  color: #fff;
}

.share-btn:active {
  background: #1ab3e0;
  transform: scale(0.98);
}
.card-container {
  width: 600rpx;
  background: #fff;
  border: 2rpx solid #20647a;
  border-radius: 18rpx;
  box-shadow: 0 4rpx 24rpx rgba(32,100,122,0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
}
/* 横向排列头像、昵称、二维码 */
.card-row {
  width: 92%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: 18rpx;
  margin-bottom: 8rpx;
  /* 移除之前添加的gap */
}
.card-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
  /* 移除之前添加的min-width */
}
.recommend-gap {
  margin-left: 34rpx;
}
.lishu-font {
  font-family: 'STLiti', 'SimLi', 'LiSu', '隶书', serif;
}

