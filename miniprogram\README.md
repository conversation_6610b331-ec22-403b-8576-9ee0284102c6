# 小程序API环境切换说明

本项目小程序支持本地与远程API服务器灵活切换，方法如下：

## 1. 支持的环境
- prod：生产环境（云托管API）
- dev：本地开发环境（本地Node服务器）

## 2. 切换方法
- 代码已支持 setApiEnv('dev') 或 setApiEnv('prod') 切换。
- 可在开发者工具控制台执行：
  ```js
  const { setApiEnv } = require('./utils/api');
  setApiEnv('dev'); // 切换为本地API
  setApiEnv('prod'); // 切换为云API
  ```
- 切换后需重启小程序。

## 3. 机制说明
- dev 环境下所有API请求走 http://localhost:3001。
- prod 环境下所有API请求走云托管API。

## 4. 注意事项
- 本地开发需确保Node后端已启动并监听3001端口。
- 生产环境无需更改，默认即为云API。 