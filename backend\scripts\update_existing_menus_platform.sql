-- ========================================
-- 更新现有快捷菜单的平台标识
-- 确保现有菜单正确标记为顾客端菜单
-- ========================================

-- 查看当前所有菜单的平台设置
SELECT '=== 当前菜单平台分布 ===' as info;
SELECT target_platform, COUNT(*) as count, GROUP_CONCAT(name) as menu_names
FROM quick_menus 
GROUP BY target_platform;

-- 将所有没有明确设置平台的菜单（或者target_platform为customer但实际应该是customer的）
-- 确保它们正确标记为customer平台
UPDATE quick_menus 
SET target_platform = 'customer' 
WHERE target_platform IS NULL 
   OR (target_platform = 'customer' AND name NOT IN ('门店合伙人', '门店库存', '在线客服', '分享门店'));

-- 确保合伙人端菜单正确标记
UPDATE quick_menus 
SET target_platform = 'partner' 
WHERE name IN ('门店合伙人', '门店库存', '在线客服', '分享门店') 
   AND link_type = 'function' 
   AND link_url IN ('viewStorePartners', 'viewStoreInventory', 'contactService', 'shareStore');

-- 验证更新结果
SELECT '=== 更新后的菜单平台分布 ===' as info;
SELECT target_platform, COUNT(*) as count, GROUP_CONCAT(name) as menu_names
FROM quick_menus 
GROUP BY target_platform;

-- 查看顾客端菜单
SELECT '=== 顾客端菜单 ===' as info;
SELECT id, name, icon, link_type, link_url, sort_order, target_platform 
FROM quick_menus 
WHERE target_platform IN ('customer', 'all') 
AND is_active = 1
ORDER BY sort_order;

-- 查看合伙人端菜单
SELECT '=== 合伙人端菜单 ===' as info;
SELECT id, name, icon, link_type, link_url, sort_order, target_platform 
FROM quick_menus 
WHERE target_platform IN ('partner', 'all') 
AND is_active = 1
ORDER BY sort_order; 