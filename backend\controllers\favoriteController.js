/**
 * 收藏控制器
 */
const favoriteService = require('../services/favoriteService');

exports.getFavorites = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const result = await favoriteService.getFavorites(userId);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.addToFavorites = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const { productId } = req.body;
    
    // 参数验证
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }
    
    const result = await favoriteService.addToFavorites(userId, productId);
    
    if (!result.success) {
      return res.status(400).json(result);
    }
    
    res.status(201).json(result);
  } catch (error) {
    console.error('添加到收藏夹失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试'
    });
  }
};

exports.removeFromFavorites = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const { productId } = req.body;
    
    // 参数验证
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }
    
    const result = await favoriteService.removeFromFavorites(userId, productId);
    
    if (!result.success) {
      return res.status(400).json(result);
    }
    
    res.json(result);
  } catch (error) {
    console.error('从收藏夹移除失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试'
    });
  }
};

exports.checkFavoriteStatus = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const { productId } = req.query;
    
    // 参数验证
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }
    
    const result = await favoriteService.checkFavoriteStatus(userId, productId);
    res.json(result);
  } catch (error) {
    console.error('获取收藏状态失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试'
    });
  }
};

exports.getFavoriteCount = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const result = await favoriteService.getFavoriteCount(userId);
    res.json(result);
  } catch (error) {
    console.error('获取收藏数量失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试'
    });
  }
}; 