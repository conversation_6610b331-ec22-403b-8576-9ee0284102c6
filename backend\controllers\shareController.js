const db = require('../config/db');

/**
 * 记录分享行为
 */
exports.recordShare = async (req, res) => {
  try {
    const { userId, shareType, nickname } = req.body;

    if (!userId || !shareType) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 插入分享记录
    const insertSql = `
      INSERT INTO share_statistics (user_id, share_type, nickname)
      VALUES (?, ?, ?)
    `;

    // 查询用户信息，获取昵称
    let userNickname = nickname;
    if (!userNickname) {
      const userQuery = 'SELECT nickname, username FROM users WHERE user_id = ?';
      const userResult = await db.query(userQuery, [userId]);
      if (userResult && userResult.length > 0) {
        userNickname = userResult[0].nickname || userResult[0].username || '';
      }
    }
    
    await db.query(insertSql, [userId, shareType, userNickname || '']);

    res.json({
      success: true,
      message: '分享记录成功'
    });
  } catch (error) {
    console.error('记录分享行为失败:', error);
    res.status(500).json({
      success: false,
      message: '记录分享行为失败',
      error: error.message
    });
  }
};

/**
 * 获取分享统计
 */
exports.getShareStatistics = async (req, res) => {
  try {
    const { userId, startDate, endDate } = req.query;

    let sql = `
      SELECT
        share_type,
        COUNT(*) as count,
        DATE(created_at) as date
      FROM share_statistics
      WHERE 1=1
    `;
    const params = [];

    if (userId) {
      sql += ' AND user_id = ?';
      params.push(userId);
    }

    if (startDate) {
      sql += ' AND DATE(created_at) >= ?';
      params.push(startDate);
    }

    if (endDate) {
      sql += ' AND DATE(created_at) <= ?';
      params.push(endDate);
    }

    sql += ' GROUP BY share_type, DATE(created_at) ORDER BY created_at DESC';

    const statistics = await db.query(sql, params);

    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    console.error('获取分享统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取分享统计失败',
      error: error.message
    });
  }
};

/**
 * 获取用户分享总数
 */
exports.getUserShareCount = async (req, res) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '缺少用户ID'
      });
    }

    const sql = `
      SELECT
        share_type,
        COUNT(*) as count
      FROM share_statistics
      WHERE user_id = ?
      GROUP BY share_type
    `;

    const counts = await db.query(sql, [userId]);

    // 计算总分享次数
    const totalCount = counts.reduce((sum, item) => sum + item.count, 0);

    res.json({
      success: true,
      data: {
        totalCount,
        details: counts
      }
    });
  } catch (error) {
    console.error('获取用户分享总数失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户分享总数失败',
      error: error.message
    });
  }
};
