<!--pages/message/chat.wxml-->
<view class="chat-container">
  <!-- 聊天消息列表 -->
  <scroll-view
    class="message-list"
    scroll-y="true"
    scroll-into-view="{{scrollToView}}"
    scroll-with-animation="true"
    bindscrolltolower="loadMoreMessages"
  >
    <view class="message-item {{item.senderId === userInfo.id ? 'self' : 'other'}}"
          wx:for="{{messages}}"
          wx:key="id"
          id="msg-{{item.id}}"
    >
      <!-- 头像始终显示 -->
      <image class="avatar" src="{{item.senderId === userInfo.id ? userInfo.avatar || userInfo.avatarUrl || '/images/icons2/男头像.png' : targetUser.avatar || targetUser.avatarUrl || '/images/icons2/男头像.png'}}"></image>
      <view class="message-content">
        <view class="bubble {{item.type === 'image' ? 'image-bubble' : ''}}">
          <text wx:if="{{item.type === 'text'}}">{{item.content}}</text>
          <image wx:if="{{item.type === 'image'}}" src="{{item.content}}" mode="widthFix" bindtap="previewImage" data-url="{{item.content}}"></image>
        </view>
        <view class="time">{{item.displayTime}}</view>
      </view>
    </view>
  </scroll-view>

  <!-- 输入框区域 -->
  <view class="input-area">
    <view class="input-box">
      <!-- 图片选择按钮 - 左侧 -->
      <view class="action-btn image-btn" bindtap="chooseImage">
        <image src="../../images/icons2/添加.png" mode="aspectFit"></image>
      </view>

      <input class="message-input"
             value="{{inputMessage}}"
             bindinput="onInput"
             placeholder="输入消息..."
             confirm-type="send"
             bindconfirm="sendMessage"
      />

      <view class="action-buttons">
        <view class="action-btn send-btn" bindtap="sendMessage">
          <text>发送</text>
        </view>
      </view>
    </view>
  </view>
</view>