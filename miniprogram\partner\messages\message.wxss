/* pages/message/message.wxss */
.message-container {
  height: 100vh;
  background-color: #FFFFFF;
  padding-bottom: 160rpx; /* 增加底部安全区，与tabbar高度一致 */
  padding-top: 0; /* 不需要顶部间距，因为使用了fixed定位 */
  box-sizing: border-box;
  position: relative;
  overflow-x: hidden; /* 防止水平滚动 */
}

/* 设置图标样式保留，可能在其他地方使用 */
.settings-icon {
  width: 24px;
  height: 24px;
}

.settings-icon image {
  width: 100%;
  height: 100%;
}

/* 消息分类导航 */
.tab-container {
  background-color: #FFFFFF;
  position: fixed; /* 改为fixed定位，确保始终固定在搜索栏下方 */
  top: 40px; /* 在搜索栏下方固定，与搜索栏高度一致 */
  z-index: 999;
  border-top: 1px solid #EEEEEE;
  width: 100%;
  box-sizing: border-box;
  height: 52px; /* 增大一级标签高度30%（原40px * 1.3 = 52px） */
}

.tab-bar {
  display: flex;
  height: 52px; /* 增大一级标签高度30%（原40px * 1.3 = 52px） */
  border-bottom: 1px solid #F5F5F5;
  box-sizing: border-box;
  background: linear-gradient(to bottom, #FFFFFF, #FAFAFA);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.tab-item {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 17px;
  color: #666666;
  transition: all 0.2s ease;
  overflow: hidden;
}

.tab-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.05);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tab-item:active::after {
  opacity: 1;
}

.tab-item.active {
  color: #29b7cb;
  font-weight: bold;
  text-shadow: 0 0.5px 0 rgba(0, 0, 0, 0.05);
}

.tab-line {
  position: absolute;
  bottom: 2px;
  width: 20px;
  height: 3px;
  background-color: #29b7cb;
  border-radius: 1.5px;
  box-shadow: 0 1px 2px rgba(41, 183, 203, 0.2);
  transition: all 0.2s ease;
}

.tab-item:active .tab-line {
  width: 24px;
}

/* 登录提示 */
.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding-top: 120px;
}

.login-icon {
  width: 100px;
  height: 100px;
  margin-bottom: 24px;
  object-fit: contain;
  opacity: 0.9;
  transition: all 0.2s ease;
}

.login-text {
  font-size: 16px;
  color: #666666;
  margin-bottom: 20px;
}

.login-btn {
  background-color: #29b7cb;
  color: #FFFFFF;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
}

/* 消息列表 */
.message-list {
  padding-bottom: 100px; /* 进一步增加底部安全区高度，确保与底部导航栏有足够间距 */
  padding-top: 80px; /* 增加顶部间距，确保不被搜索栏和分类标签遮挡，40px(搜索栏) + 40px(分类标签) */
  box-sizing: border-box;
}

/* 左滑删除容器 */
.message-item-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  background-color: #FF3B30;
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.message-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #EEEEEE;
  position: relative;
  transition: transform 0.2s ease;
  width: 100%;
  box-sizing: border-box;
  z-index: 1;
}

.message-item.unread {
  background-color: #F0F9FF;
}

.delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 80px;
  background-color: #FF3B30;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  z-index: 0;
}

.avatar {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  margin-right: 10px;
  flex-shrink: 0;
}

.system-icon {
  width: 46px;
  height: 46px;
  margin-right: 10px;
}

.message-content {
  flex: 1;
  margin-left: 12px;
  overflow: hidden;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.sender-name {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60%;
}

.message-time {
  font-size: 12px;
  color: #999999;
  white-space: nowrap;
}

.message-text {
  font-size: 14px;
  color: #666666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}



.action-btn {
  align-self: flex-start;
  padding: 4px 12px;
  background-color: #F5F5F5;
  color: #FF4D4F;
  border-radius: 15px;
  font-size: 12px;
  margin-top: 5px;
}

.unread-badge {
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 12px;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5px;
  box-sizing: border-box;
  margin-left: 8px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  opacity: 0.9;
}

/* 群聊加载状态 */
.group-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  opacity: 0.95;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  margin: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.loading-icon {
  width: 20px;
  height: 20px;
  border: 2px solid #EEEEEE;
  border-top: 2px solid #29b7cb;
  border-radius: 50%;
  margin-right: 8px;
  animation: spin 1.2s ease infinite;
  box-shadow: 0 0 10px rgba(41, 183, 203, 0.3);
}

.group-loading-icon {
  width: 32px;
  height: 32px;
  border: 3px solid #F0F0F0;
  border-top: 3px solid #29b7cb;
  border-right: 3px solid #1e9ba8;
  border-radius: 50%;
  margin-bottom: 16px;
  animation: spin 1.5s ease infinite;
  box-shadow: 0 0 15px rgba(41, 183, 203, 0.4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #666666;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
}

.group-loading-text {
  font-size: 16px;
  color: #333333;
  font-weight: 600;
  letter-spacing: 0.8px;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.9);
  background: linear-gradient(to right, #29b7cb, #1e9ba8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  padding: 4px 12px;
  border-radius: 16px;
  position: relative;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0 120px 0; /* 进一步增加底部间距，确保与底部导航栏有足够间距 */
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999999;
}

/* 搜索栏样式 */
.search-container {
  padding: 0 16px;
  background-color: #FFFFFF;
  position: fixed; /* 改为fixed定位，确保始终固定在顶部 */
  top: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  left: 0;
  right: 0;
  height: 40px; /* 固定搜索栏高度 */
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 20px;
  padding: 6px 12px;
  flex: 1;
  margin-right: 10px;
  height: 28px;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.search-box:active,
.search-box:focus-within {
  background-color: #FFFFFF;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.search-box:focus-within .search-icon {
  opacity: 1;
  transform: scale(1.05);
}

.search-input {
  flex: 1;
  height: 20px;
  font-size: 14px;
  color: #333333;
  transition: all 0.2s ease;
  caret-color: #29b7cb;
}

.search-input::placeholder {
  color: #999999;
  transition: all 0.2s ease;
}

.search-box:focus-within .search-input::placeholder {
  color: #CCCCCC;
  font-size: 13px;
}

/* 刷新按钮样式 */
.refresh-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F5F5F5;
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.refresh-btn:active {
  transform: rotate(180deg) scale(0.9);
  background-color: #E8E8E8;
}

.refresh-icon {
  width: 20px;
  height: 20px;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.refresh-btn:active .refresh-icon {
  opacity: 1;
}

/* 群消息样式 */
.group-container {
  padding: 20rpx;
}

.group-section {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.group-list {
  display: flex;
  flex-direction: column;
  padding: 0 12rpx;
  background: #f7f7f7;
}

.group-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  margin: 0 12rpx;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.group-item:active {
  background-color: #f9f9f9;
  transform: scale(0.98);
}

.group-item:last-child {
  border-bottom: none;
}

.group-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 16rpx;
  margin-right: 20rpx;
  object-fit: cover;
  background: #f5f5f5;
  border: 1px solid #f0f0f0;
  flex-shrink: 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  position: relative;
  z-index: 2;
}

.group-item:active .group-avatar {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.03);
}

.group-info {
  flex: 1;
  overflow: hidden;
  transition: all 0.2s ease;
  transform-origin: left center;
}

.group-item:active .group-info {
  transform: translateX(4rpx);
}

.group-name {
  font-size: 30rpx;
  color: #222;
  margin-bottom: 8rpx;
  font-weight: 600;
  transition: color 0.2s ease;
  text-shadow: 0 0.5px 0 rgba(0,0,0,0.02);
}

.group-item:active .group-name {
  color: #29b7cb;
}

.group-desc {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.group-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 20rpx;
  transition: all 0.2s ease;
}

.group-item:active .group-meta {
  transform: scale(0.98);
}

.member-count {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  background: #f0f8ff;
  padding: 2rpx 10rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.group-item:active .member-count {
  background: #e6f7ff;
  color: #29b7cb;
}

.group-role {
  font-size: 24rpx;
  color: #fff;
  background: #29b7cb;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(41, 183, 203, 0.2);
  transition: all 0.2s ease;
}

.group-item:active .group-role {
  transform: scale(0.95);
  box-shadow: 0 1rpx 3rpx rgba(41, 183, 203, 0.1);
}

.join-btn {
  font-size: 24rpx;
  color: #fff;
  background: #29b7cb;
  padding: 0 20rpx;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 24rpx;
  margin: 0;
  box-shadow: 0 2rpx 8rpx rgba(41, 183, 203, 0.2);
  transition: all 0.2s ease;
}

.join-btn:active {
  transform: scale(0.95);
  background: #1e9ba8;
  box-shadow: 0 1rpx 4rpx rgba(41, 183, 203, 0.1);
}

.join-btn::after {
  border: none;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0 180rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
  opacity: 0.8;
  transition: all 0.2s ease;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

.empty-text {
  font-size: 30rpx;
  color: #666;
  font-weight: 500;
  letter-spacing: 1rpx;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
  background: linear-gradient(to right, #666, #999);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.group-tab-switch {
  display: flex;
  justify-content: center;
  margin: 0;
  position: fixed;
  top: 92px;
  z-index: 998;
  width: 100%;
  background-color: #FFFFFF;
  left: 0;
  right: 0;
  box-sizing: border-box;
  height: 44px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  padding: 0 20px;
  border-bottom: 1px solid #F0F0F0;
  transition: all 0.2s ease;
}

.group-tab {
  flex: 1;
  text-align: center;
  padding: 0;
  font-size: 14px;
  color: #666666;
  background: transparent;
  margin: 0 10px;
  cursor: pointer;
  position: relative;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;
  transition: all 0.2s ease;
  overflow: visible;
  border-radius: 0;
}

.group-tab.active {
  color: #29b7cb;
  background: transparent;
  font-weight: bold;
}

/* 为选中标签的文字添加边框 */
.group-tab.active::after {
  content: attr(data-text);
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 5px 14px;
  color: #29b7cb;
  font-size: 14px;
  font-weight: bold;
  white-space: nowrap;
  background: transparent;
  border: 1px solid #29b7cb;
  border-radius: 8px;
  z-index: 1;
  transition: all 0.2s ease;
  line-height: 1.2;
  height: auto;
  min-height: auto;
}

.group-tab-underline {
  display: none;
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 40%;
  height: 3px;
  background: #29b7cb;
  border-radius: 2px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(41, 183, 203, 0.2);
}

.group-tab-content {
  background: #FFFFFF;
  border-radius: 8px 8px 0 0;
  padding: 20rpx 0 40rpx 0;
  min-height: 200px;
  margin-top: 44px;
  padding-top: 16px;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
  transition: opacity 0.2s ease, transform 0.2s ease;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.02);
}

.create-group-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0 12px 0;
  padding: 8px 0;
  background: #1e9ba8;
  color: #fff;
  border-radius: 18px;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
}

/* 创建群聊按钮容器 */
.create-group-btn-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 180px; /* 进一步增加底部位置，确保不被底部导航栏遮挡 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  z-index: 99;
  pointer-events: none; /* 允许点击穿透到下层元素 */
}

/* 悬浮创建群聊按钮 */
.float-create-group-btn {
  width: 20vw;
  height: 20vw;
  max-width: 80px;
  max-height: 80px;
  min-width: 56px;
  min-height: 56px;
  background: #29b7cb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(41, 183, 203, 0.25);
  transition: all 0.2s ease;
  pointer-events: auto;
}

.float-create-group-btn:active {
  transform: scale(0.9);
  box-shadow: 0 2px 6px rgba(41, 183, 203, 0.2);
  background: #1e9ba8;
}

.float-create-group-icon {
  width: 50%;
  height: 50%;
  object-fit: contain;
  filter: brightness(1.1);
  transition: transform 0.2s ease;
}

.float-create-group-btn:active .float-create-group-icon {
  transform: rotate(90deg) scale(0.9);
}

.member-btn {
  margin-left: 10px;
  background: #f5f7fa;
  color: #262626;
  border-radius: 12px;
  font-size: 12px;
  padding: 0 12px;
  height: 28px;
  line-height: 28px;
  border: 1px solid #262626;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(38, 38, 38, 0.1);
  position: relative;
  overflow: hidden;
}

.member-btn:active {
  transform: scale(0.95);
  background: #f0f0f0;
  color: #000000;
  box-shadow: 0 1px 2px rgba(38, 38, 38, 0.05);
}

.group-header-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 6px;
  justify-content: space-between;
}

.group-title-col {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.group-row-1 {
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: 6rpx;
  padding-right: 80rpx;
}

.group-title-text {
  font-size: 34rpx;
  color: #222;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  line-height: 1.4;
  letter-spacing: 0.5rpx;
  transition: color 0.3s ease;
  text-shadow: 0 0.5px 0 rgba(0,0,0,0.05);
}

.group-item:active .group-title-text {
  color: #29b7cb;
}

.owner-tag, .joined-tag {
  margin-left: 8px;
  margin-right: 100px; /* 原72px，改为100px，增加与按钮的间隔 */
}

.group-row-1 .member-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 0;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  padding: 0 10px;
  border-radius: 12px;
}

.group-row-2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.group-desc {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 16rpx;
  line-height: 1.4;
  max-width: 70%;
  transition: color 0.3s ease;
}

.group-item:active .group-desc {
  color: #666;
}

.group-row-2 .member-count {
  color: #666;
  font-size: 24rpx;
  min-width: 64rpx;
  margin-left: 0;
  display: flex;
  align-items: center;
  height: 40rpx;
  flex-shrink: 0;
  background: transparent;
  border-radius: 0;
  padding: 0;
  justify-content: center;
  font-weight: 500;
  box-shadow: none;
}

.group-header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 8px;
  min-width: 56px;
  max-width: 64px;
}

.owner-tag {
  margin-bottom: 4px;
  font-size: 12px;
  color: #fff;
  background: linear-gradient(to right, #fa8c16, #d46b08);
  border-radius: 10px;
  padding: 2px 10px;
  font-weight: 500;
  display: inline-block;
  vertical-align: middle;
  box-shadow: 0 2px 4px rgba(250, 140, 22, 0.2);
  transition: all 0.2s ease;
  transform-origin: center;
}

.group-item:active .owner-tag {
  transform: scale(0.95);
  box-shadow: 0 1px 2px rgba(250, 140, 22, 0.15);
}

.group-header-right .member-count {
  font-size: 14px;
  color: #888;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 4px;
}

.group-header-right .member-btn {
  margin: 0;
  background: transparent;
  color: #262626;
  border-radius: 16px;
  font-size: 13px;
  padding: 0 14px;
  height: 28px;
  line-height: 28px;
  border: 1px solid #262626;
  box-shadow: none;
}

.joined-tag {
  margin-bottom: 4px;
  font-size: 12px;
  color: #fff;
  background: linear-gradient(to right, #52c41a, #389e0d);
  border-radius: 10px;
  padding: 2px 10px;
  font-weight: 500;
  display: inline-block;
  vertical-align: middle;
  margin-left: 8px;
  box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
  transition: all 0.2s ease;
  transform-origin: center;
}

.group-item:active .joined-tag {
  transform: scale(0.95);
  box-shadow: 0 1px 2px rgba(82, 196, 26, 0.15);
}

/* 添加tabbar容器样式 */
.tabbar-container {
  width: 100%;
  height: 160rpx;
  position: relative;
  z-index: 1000;
}
