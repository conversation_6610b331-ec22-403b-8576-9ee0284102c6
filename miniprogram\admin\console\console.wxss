.admin-console-container { padding: 30rpx; }
.admin-navbar { display: flex; gap: 20rpx; margin-bottom: 30rpx; }
.nav-btn { flex: 1; padding: 18rpx 0; background: #1E6A9E; color: #fff; border-radius: 8rpx; font-size: 30rpx; }
.nav-btn.active { background: #17637a; }
.admin-content { padding: 40rpx 0; font-size: 32rpx; color: #333; } 

.console-container {
  background: #f7f7f7;
  min-height: 100vh;
}

.console-header {
  position: relative;
  width: 100%;
  padding: 10px 10px 4px 10px;
  background-color: #FFFFFF;
  color: #333333;
  border-bottom: 1px solid #EEEEEE;
  border-radius: 0 0 32rpx 32rpx;
  margin-bottom: 16rpx;
}
.header-left {
  display: flex;
  align-items: center;
}
.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 2px solid #fff;
  background-color: #1E6A9E;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
}
.user-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 15px;
}
.user-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.user-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333333;
}
.user-info-right {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.role {
  font-size: 14px;
  background: #fff3e0;
  color: #d32f2f;
  border-radius: 16rpx;
  padding: 2px 10px;
  margin-right: 4px;
  height: 20px;
  line-height: 20px;
  display: flex;
  align-items: center;
}
.role-icon {
  display: none;
}
.switch-role-icon {
  width: 16px;
  height: 16px;
  margin-left: 0;
  cursor: pointer;
  opacity: 0.85;
  transition: opacity 0.2s;
  vertical-align: middle;
  display: flex;
  align-items: center;
}
.switch-role-icon:active {
  opacity: 0.6;
}
.user-id {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
  line-height: 18px;
}
.header-right {
  display: flex;
  align-items: center;
  margin-left: 0;
  height: 100%;
}

.stats-row {
  display: flex;
  background: #fff;
  border-radius: 20rpx;
  /* 调整margin-top，避免负值导致遮挡 */
  margin: 0 24rpx 24rpx 24rpx;
  box-shadow: 0 4rpx 16rpx #e0e0e0;
  overflow: hidden;
  min-height: 120rpx; /* 增大统计区高度 */
}
.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0; /* 增大上下内边距 */
  border-right: 1rpx solid #f0f0f0;
}
.stat-item:last-child {
  border-right: none;
}
.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #d32f2f;
}
.stat-label {
  font-size: 22rpx;
  color: #888;
  margin-top: 8rpx;
}

.section {
  background: #fff;
  border-radius: 20rpx;
  margin: 0 24rpx 24rpx 24rpx;
  padding: 40rpx 0 0 0; /* 增大上内边距 */
  min-height: 120rpx; /* 增大卡片最小高度 */
}
.section-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24rpx 16rpx 24rpx;
}
.section-title {
  font-size: 28rpx;
  font-weight: bold;
}
.section-more {
  display: flex;
  align-items: center;
  color: #d32f2f;
  font-size: 24rpx;
}
.arrow {
  margin-left: 4rpx;
}

.order-row {
  display: flex;
  justify-content: space-between;
  padding: 0 24rpx 40rpx 24rpx; /* 增大下内边距 */
}
.order-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.order-icon {
  width: 66rpx;
  height: 66rpx;
  margin-bottom: 8rpx;
}
.order-label {
  font-size: 22rpx;
  color: #333;
}
.badge {
  position: absolute;
  top: 0;
  right: 32rpx;
  background: #ff4d4f;
  color: #fff;
  font-size: 18rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 32rpx;
  box-sizing: border-box;
  padding: 0;
}

.tools-row {
  display: flex;
  justify-content: space-between;
  padding: 0 24rpx 40rpx 24rpx; /* 增大下内边距 */
}
.tool-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.tool-icon {
  width: 72rpx;
  height: 72rpx;
  margin-bottom: 8rpx;
}
.tool-label {
  font-size: 22rpx;
  color: #333;
}

.platform-row {
  display: flex;
  justify-content: flex-start;
  padding: 0 24rpx 24rpx 24rpx;
  gap: 40rpx;
}
.platform-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.platform-icon {
  width: 72rpx;
  height: 72rpx;
  margin-bottom: 8rpx;
}
.platform-label {
  font-size: 22rpx;
  color: #333;
} 

.switch-role-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 4rpx;
  vertical-align: middle;
} 

.admin-level {
  font-size: 14px;
  color: #d32f2f;
  margin-top: 2px;
  margin-left: 4px;
  font-weight: bold;
} 

.avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 2px solid #FFFFFF;
  background-color: #1E6A9E;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 16px;
}
.user-info-content {
  flex: 1;
  margin-left: 15px;
  min-width: 0;
}
.user-info-flex {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.user-info-main-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-right: 24px;
}
.user-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333333;
}
.switch-badge-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  min-width: 0;
  justify-content: flex-end;
}
.vip-badge {
  display: flex;
  align-items: center;
  background: #fff3e0;
  color: #d32f2f;
  border-radius: 16rpx;
  padding: 2px 10px;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  margin-right: 4px;
  font-weight: bold;
}
.user-id.user-id-bottom {
  font-size: 14px;
  color: #666666;
  margin-top: 4px;
  line-height: 18px;
} 