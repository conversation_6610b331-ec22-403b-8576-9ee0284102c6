// pages/order/rate.js
const { orderApi } = require('../../utils/api');

Page({
  data: {
    orderId: null,
    orderDetail: null,
    loading: true,
    submitting: false,
    rateItems: [],
    overallRating: 5,
    deliveryRating: 5,
    serviceRating: 5,
    comment: '',
    maxCommentLength: 200,
    anonymousRate: false,
    uploadedImages: [],
    maxImageCount: 6
  },

  onLoad: function(options) {
    console.log('订单评价页面加载，参数:', options);
    if (options && options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail(options.id);
    } else {
      wx.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载订单详情
  loadOrderDetail: function(orderId) {
    this.setData({ loading: true });
    
    orderApi.getOrderById(orderId).then(res => {
      if (res.success && res.data) {
        const orderDetail = res.data;
        
        // 构建评价项
        const rateItems = orderDetail.items.map(item => ({
          id: item.id,
          product_id: item.product_id,
          name: item.name,
          image: item.image,
          specs: item.specs,
          rating: 5,
          comment: '',
          images: []
        }));
        
        this.setData({
          orderDetail,
          rateItems,
          loading: false
        });
      } else {
        this.setData({ loading: false });
        wx.showToast({
          title: '获取订单详情失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取订单详情失败:', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '获取订单详情失败',
        icon: 'none'
      });
    });
  },

  // 设置整体评分
  setOverallRating: function(e) {
    const rating = parseInt(e.currentTarget.dataset.rating);
    this.setData({
      overallRating: rating
    });
  },

  // 设置配送评分
  setDeliveryRating: function(e) {
    const rating = parseInt(e.currentTarget.dataset.rating);
    this.setData({
      deliveryRating: rating
    });
  },

  // 设置服务评分
  setServiceRating: function(e) {
    const rating = parseInt(e.currentTarget.dataset.rating);
    this.setData({
      serviceRating: rating
    });
  },

  // 设置商品评分
  setProductRating: function(e) {
    const index = e.currentTarget.dataset.index;
    const rating = parseInt(e.currentTarget.dataset.rating);
    const rateItems = this.data.rateItems;
    
    rateItems[index].rating = rating;
    
    this.setData({
      rateItems
    });
  },

  // 输入评价内容
  inputComment: function(e) {
    this.setData({
      comment: e.detail.value
    });
  },

  // 输入商品评价内容
  inputProductComment: function(e) {
    const index = e.currentTarget.dataset.index;
    const comment = e.detail.value;
    const rateItems = this.data.rateItems;
    
    rateItems[index].comment = comment;
    
    this.setData({
      rateItems
    });
  },

  // 切换匿名评价
  toggleAnonymous: function() {
    this.setData({
      anonymousRate: !this.data.anonymousRate
    });
  },

  // 选择图片
  chooseImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const rateItems = this.data.rateItems;
    const currentImages = rateItems[index].images || [];
    
    if (currentImages.length >= this.data.maxImageCount) {
      wx.showToast({
        title: `最多上传${this.data.maxImageCount}张图片`,
        icon: 'none'
      });
      return;
    }
    
    console.log('开始选择评价图片');
    const count = this.data.maxImageCount - currentImages.length;
    // 先尝试使用chooseMedia API
    try {
      wx.chooseMedia({
        count: count,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: res => {
          console.log('选择评价图片成功:', res);
          const validFiles = [];
          const validPaths = [];
          
          for (let i = 0; i < res.tempFiles.length; i++) {
            const file = res.tempFiles[i];
            if (file.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
              wx.showToast({ title: `第${i+1}张图片不能大于1.2MB`, icon: 'none' });
              continue;
            }
            validFiles.push(file);
            validPaths.push(file.tempFilePath);
          }
          
          if (validPaths.length > 0) {
            this.uploadImages(validPaths, index);
          }
        },
        fail: err => {
          console.error('chooseMedia 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消选择图片');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才使用备选方案
          this.chooseRateImageFallback(count, index);
        }
      });
    } catch (error) {
      console.error('chooseMedia API 不支持，使用备选方案:', error);
      // 如果chooseMedia不支持，直接使用chooseImage
      this.chooseRateImageFallback(count, index);
    }
  },

  // 使用chooseImage作为备选方案
  chooseRateImageFallback: function(count, index) {
    console.log('使用chooseImage作为备选方案');
    wx.chooseImage({
      count: count,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        console.log('chooseImage成功:', res);
        const validPaths = [];
        
        for (let i = 0; i < res.tempFilePaths.length; i++) {
          const tempFilePath = res.tempFilePaths[i];
          const tempFile = res.tempFiles[i];
          if (tempFile && tempFile.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
            wx.showToast({ title: `第${i+1}张图片不能大于1.2MB`, icon: 'none' });
            continue;
          }
          validPaths.push(tempFilePath);
        }
        
        if (validPaths.length > 0) {
          this.uploadImages(validPaths, index);
        }
      },
      fail: err => {
        console.error('chooseImage 失败:', err);
        // 检查是否是用户取消操作
        if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
          console.log('用户取消选择图片');
          return; // 用户取消，直接返回
        }
        // 其他错误情况才显示提示
        wx.showToast({
          title: '选择图片失败，请检查相关权限',
          icon: 'none'
        });
      }
    });
  },

  // 上传图片
  uploadImages: function(tempFilePaths, index) {
    const rateItems = this.data.rateItems;
    
    wx.showLoading({
      title: '上传中...',
      mask: true
    });
    
    // 由于小程序环境限制，这里直接使用本地临时路径，实际项目中应该上传到服务器
    // 这里模拟上传成功，直接使用临时路径
    const uploadPromises = tempFilePaths.map(filePath => {
      return new Promise((resolve) => {
        // 模拟网络请求延迟
        setTimeout(() => {
          resolve(filePath);
        }, 500);
      });
    });
    
    
    Promise.all(uploadPromises).then(urls => {
      rateItems[index].images = [...rateItems[index].images, ...urls];
      
      this.setData({
        rateItems
      });
      
      wx.hideLoading();
    }).catch(err => {
      console.error('上传图片失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '上传图片失败',
        icon: 'none'
      });
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const { index, imageIndex } = e.currentTarget.dataset;
    const rateItems = this.data.rateItems;
    
    rateItems[index].images.splice(imageIndex, 1);
    
    this.setData({
      rateItems
    });
  },

  // 预览图片
  previewImage: function(e) {
    const { index, imageIndex } = e.currentTarget.dataset;
    const rateItems = this.data.rateItems;
    const images = rateItems[index].images;
    
    wx.previewImage({
      current: images[imageIndex],
      urls: images
    });
  },

  // 提交评价
  submitRate: function() {
    if (this.data.submitting) return;
    
    // 验证评价内容
    const { rateItems, overallRating, deliveryRating, serviceRating, comment, orderId } = this.data;
    
    // 检查商品评价
    for (let i = 0; i < rateItems.length; i++) {
      if (!rateItems[i].comment.trim()) {
        wx.showToast({
          title: `请填写第${i + 1}个商品的评价内容`,
          icon: 'none'
        });
        return;
      }
    }
    
    this.setData({ submitting: true });
    
    // 构建评价数据
    const rateData = {
      order_id: orderId,
      overall_rating: overallRating,
      delivery_rating: deliveryRating,
      service_rating: serviceRating,
      comment: comment,
      is_anonymous: this.data.anonymousRate ? 1 : 0,
      items: rateItems.map(item => ({
        product_id: item.product_id,
        rating: item.rating,
        comment: item.comment,
        images: item.images
      }))
    };
    
    orderApi.rateOrder(orderId, rateData).then(res => {
      if (res.success) {
        wx.showToast({
          title: '评价成功',
          icon: 'success'
        });
        
        // 返回订单详情页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        this.setData({ submitting: false });
        wx.showToast({
          title: res.message || '评价失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('提交评价失败:', err);
      this.setData({ submitting: false });
      wx.showToast({
        title: '评价失败',
        icon: 'none'
      });
    });
  }
});