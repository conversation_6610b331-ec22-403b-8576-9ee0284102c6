/* pages/splash/splash.wxss */
.splash-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, #FF4D4F, #FFE8E8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
}

.app-name {
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 40px;
}

.loading-container {
  width: 80%;
  margin-bottom: 40px;
}

.loading-bar {
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.loading-progress {
  height: 100%;
  background-color: #FFFFFF;
  transition: width 0.2s ease-in-out;
}

.version-info {
  position: absolute;
  bottom: 20px;
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.version-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 5px;
}
