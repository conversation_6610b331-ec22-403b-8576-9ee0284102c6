<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>

  <block wx:if="{{!loading && orderDetail}}">
    <!-- 订单信息 -->
    <view class="order-section">
      <view class="order-title">订单信息</view>
      <view class="order-info">
        <view class="order-no">订单号：{{orderDetail.order_no}}</view>
        <view class="order-amount">支付金额：<text class="amount">¥{{orderDetail.total_amount}}</text></view>
      </view>
    </view>

    <!-- 支付方式 -->
    <view class="payment-section">
      <view class="section-title">支付方式</view>
      <view class="payment-methods">
        <block wx:for="{{paymentMethods}}" wx:key="id">
          <view class="payment-item {{item.selected ? 'selected' : ''}} {{item.id === 'balance' && !balanceEnough ? 'disabled' : ''}}" 
                bindtap="selectPayment" 
                data-id="{{item.id}}">
            <view class="payment-left">
              <image class="payment-icon" src="{{item.icon}}"></image>
              <view class="payment-info">
                <text class="payment-name">{{item.name}}</text>
                <text class="payment-desc" wx:if="{{item.description}}">{{item.description}}</text>
                <text class="balance-info" wx:if="{{item.id === 'balance' || item.id === 'balance_priority'}}">可用余额: ¥{{userBalance}}</text>
              </view>
            </view>
            <view class="payment-right">
              <view class="radio-circle {{item.selected ? 'checked' : ''}}"></view>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 订单商品 -->
    <view class="products-section">
      <view class="section-title">订单商品</view>
      <view class="products-list">
        <block wx:for="{{orderDetail.items}}" wx:key="id">
          <view class="product-item">
            <image class="product-image" src="{{item.image || '/images/icons2/默认商品.png'}}"></image>
            <view class="product-info">
              <view class="product-name">{{item.name}}</view>
              <view class="product-specs" wx:if="{{item.specs}}">{{item.specs}}</view>
              <view class="product-price-qty">
                <text class="product-price">¥{{item.price}}</text>
                <text class="product-qty">x{{item.quantity}}</text>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 金额详情 -->
    <view class="amount-section">
      <view class="section-title">金额详情</view>
      <view class="amount-list">
        <view class="amount-item">
          <text class="amount-label">商品金额</text>
          <text class="amount-value">¥{{orderDetail.goods_amount}}</text>
        </view>
        <view class="amount-item">
          <text class="amount-label">运费</text>
          <text class="amount-value">¥{{orderDetail.shipping_fee}}</text>
        </view>
        <view class="amount-item" wx:if="{{orderDetail.discount_amount > 0}}">
          <text class="amount-label">优惠金额</text>
          <text class="amount-value">-¥{{orderDetail.discount_amount}}</text>
        </view>
        <view class="amount-item total">
          <text class="amount-label">实付金额</text>
          <text class="amount-value">¥{{orderDetail.total_amount}}</text>
        </view>
      </view>
    </view>

    <!-- 底部支付按钮 -->
    <view class="footer-actions">
      <view class="total-info">
        <text>实付金额：</text>
        <text class="total-amount">¥{{orderDetail.total_amount}}</text>
      </view>
      <view class="pay-btn" bindtap="confirmPayment">确认支付</view>
    </view>
  </block>

  <!-- 订单不存在 -->
  <view class="empty-container" wx:if="{{!loading && !orderDetail}}">
    <image class="empty-icon" src="/images/icons/empty-order.svg"></image>
    <text class="empty-text">订单不存在或已被删除</text>
  </view>
</view>