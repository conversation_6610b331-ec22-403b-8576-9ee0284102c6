/**
 * Users表结构迁移脚本
 * 将现有的id字段改为user_id，并添加新的自增id字段
 * 
 * 使用方法：
 * node backend/scripts/migrate_users_table.js
 */

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'morebuy',
  charset: 'utf8mb4'
};

async function migrateUsersTable() {
  let connection;
  
  try {
    console.log('🔄 开始Users表结构迁移...');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 第一步：备份当前users表
    console.log('📋 备份当前users表...');
    await connection.execute('CREATE TABLE IF NOT EXISTS users_backup AS SELECT * FROM users');
    console.log('✅ users表备份完成');
    
    // 第二步：添加新的自增id字段
    console.log('➕ 添加新的自增id字段...');
    await connection.execute('ALTER TABLE users ADD COLUMN new_id INT NOT NULL AUTO_INCREMENT FIRST');
    console.log('✅ 新id字段添加成功');
    
    // 第三步：将现有的id字段重命名为user_id
    console.log('🔄 将现有id字段重命名为user_id...');
    await connection.execute('ALTER TABLE users CHANGE COLUMN id user_id VARCHAR(32) NOT NULL COMMENT "用户ID（业务ID）"');
    console.log('✅ id字段重命名为user_id成功');
    
    // 第四步：将新添加的id字段重命名为id并设置为主键
    console.log('🔄 设置新的id字段为主键...');
    await connection.execute('ALTER TABLE users CHANGE COLUMN new_id id INT NOT NULL AUTO_INCREMENT COMMENT "自增主键ID"');
    console.log('✅ 新id字段设置成功');
    
    // 第五步：设置id为主键
    console.log('🔑 设置id为主键...');
    await connection.execute('ALTER TABLE users DROP PRIMARY KEY');
    await connection.execute('ALTER TABLE users ADD PRIMARY KEY (id)');
    console.log('✅ 主键设置成功');
    
    // 第六步：为user_id添加唯一索引
    console.log('📊 为user_id添加唯一索引...');
    await connection.execute('ALTER TABLE users ADD UNIQUE INDEX uk_user_id (user_id)');
    console.log('✅ user_id唯一索引添加成功');
    
    // 第七步：验证迁移结果
    console.log('🔍 验证迁移结果...');
    const [columns] = await connection.execute(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_KEY,
        COLUMN_DEFAULT,
        COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'users' 
      AND TABLE_SCHEMA = DATABASE()
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('📋 表结构验证结果:');
    columns.forEach(col => {
      console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.COLUMN_KEY ? `(${col.COLUMN_KEY})` : ''} - ${col.COLUMN_COMMENT || ''}`);
    });
    
    // 第八步：检查索引
    console.log('📊 检查索引...');
    const [indexes] = await connection.execute('SHOW INDEX FROM users');
    console.log('索引列表:');
    indexes.forEach(idx => {
      console.log(`  ${idx.Key_name}: ${idx.Column_name} (${idx.Non_unique === 0 ? 'UNIQUE' : 'NON-UNIQUE'})`);
    });
    
    // 第九步：验证数据完整性
    console.log('🔍 验证数据完整性...');
    const [userCount] = await connection.execute('SELECT COUNT(*) as total_users FROM users');
    const [userWithUserIdCount] = await connection.execute('SELECT COUNT(*) as users_with_user_id FROM users WHERE user_id IS NOT NULL');
    const [userWithIdCount] = await connection.execute('SELECT COUNT(*) as users_with_id FROM users WHERE id IS NOT NULL');
    
    console.log(`总用户数: ${userCount[0].total_users}`);
    console.log(`有user_id的用户数: ${userWithUserIdCount[0].users_with_user_id}`);
    console.log(`有id的用户数: ${userWithIdCount[0].users_with_id}`);
    
    // 第十步：检查user_id的唯一性
    console.log('🔍 检查user_id唯一性...');
    const [duplicates] = await connection.execute(`
      SELECT user_id, COUNT(*) as count 
      FROM users 
      GROUP BY user_id 
      HAVING count > 1
    `);
    
    if (duplicates.length > 0) {
      console.log('⚠️  发现重复的user_id:');
      duplicates.forEach(dup => {
        console.log(`  ${dup.user_id}: ${dup.count}条记录`);
      });
      console.log('请手动处理重复的user_id');
    } else {
      console.log('✅ user_id唯一性验证通过');
    }
    
    console.log('🎉 Users表结构迁移完成！');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error);
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      sqlState: error.sqlState,
      sql: error.sql
    });
    
    // 如果迁移失败，提供回滚建议
    console.log('\n🔄 如果迁移失败，可以执行以下SQL回滚:');
    console.log('-- 删除新添加的id字段');
    console.log('ALTER TABLE users DROP COLUMN id;');
    console.log('-- 将user_id重命名回id');
    console.log('ALTER TABLE users CHANGE COLUMN user_id id VARCHAR(32) NOT NULL;');
    console.log('-- 恢复主键');
    console.log('ALTER TABLE users ADD PRIMARY KEY (id);');
    
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
    process.exit(0);
  }
}

// 执行迁移
migrateUsersTable(); 