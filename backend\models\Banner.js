/**
 * 轮播图模型
 */
const db = require('../config/db');

class Banner {
  /**
   * 获取所有轮播图
   * @param {Object} options - 查询选项
   * @returns {Array} 轮播图列表
   */
  static async findAll(options = {}) {
    try {
      let sql = 'SELECT * FROM banners';
      let params = [];
      
      // 添加筛选条件
      const conditions = [];
      
      if (options.is_active !== undefined) {
        conditions.push('is_active = ?');
        params.push(options.is_active);
      }
      
      if (options.page_type !== undefined) {
        conditions.push('page_type = ?');
        params.push(options.page_type);
      }
      
      if (conditions.length > 0) {
        sql += ' WHERE ' + conditions.join(' AND ');
      }
      
      // 添加排序
      sql += ' ORDER BY sort_order ASC, id DESC';
      
      const result = await db.query(sql, params);
      return result;
    } catch (error) {
      console.error('获取轮播图列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取轮播图
   * @param {number} id - 轮播图ID
   * @returns {Object|null} 轮播图对象
   */
  static async findById(id) {
    try {
      const sql = 'SELECT * FROM banners WHERE id = ?';
      const result = await db.query(sql, [id]);
      return result.length > 0 ? result[0] : null;
    } catch (error) {
      console.error('获取轮播图失败:', error);
      throw error;
    }
  }

  /**
   * 创建轮播图
   * @param {Object} bannerData - 轮播图数据
   * @returns {Object} 创建结果
   */
  static async create(bannerData) {
    try {
      const {
        title = '',
        image_url,
        link_url = '',
        page_type = 'customer_home',
        sort_order = 0,
        is_active = true
      } = bannerData;

      if (!image_url) {
        throw new Error('轮播图片不能为空');
      }

      const currentTime = Date.now();
      
      const sql = `
        INSERT INTO banners (
          title, image_url, link_url, page_type, sort_order, is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const params = [
        title, image_url, link_url, page_type, sort_order, 
        is_active ? 1 : 0, currentTime, currentTime
      ];

      const result = await db.query(sql, params);
      return {
        success: true,
        id: result.insertId,
        message: '轮播图创建成功'
      };
    } catch (error) {
      console.error('创建轮播图失败:', error);
      throw error;
    }
  }

  /**
   * 更新轮播图
   * @param {number} id - 轮播图ID
   * @param {Object} bannerData - 更新的轮播图数据
   * @returns {Object} 更新结果
   */
  static async update(id, bannerData) {
    try {
      const updateFields = [];
      const params = [];

      // 动态构建更新字段
      const allowedFields = [
        'title', 'image_url', 'link_url', 'page_type', 'sort_order', 'is_active'
      ];

      allowedFields.forEach(field => {
        if (bannerData[field] !== undefined) {
          updateFields.push(`${field} = ?`);
          if (field === 'is_active') {
            params.push(bannerData[field] ? 1 : 0);
          } else {
            params.push(bannerData[field]);
          }
        }
      });

      if (updateFields.length === 0) {
        return { success: false, message: '没有需要更新的字段' };
      }

      // 添加更新时间
      updateFields.push('updated_at = ?');
      params.push(Date.now());
      params.push(id);

      const sql = `UPDATE banners SET ${updateFields.join(', ')} WHERE id = ?`;
      const result = await db.query(sql, params);

      if (result.affectedRows > 0) {
        return { success: true, message: '轮播图更新成功' };
      } else {
        return { success: false, message: '轮播图不存在' };
      }
    } catch (error) {
      console.error('更新轮播图失败:', error);
      throw error;
    }
  }

  /**
   * 删除轮播图
   * @param {number} id - 轮播图ID
   * @returns {Object} 删除结果
   */
  static async delete(id) {
    try {
      const sql = 'DELETE FROM banners WHERE id = ?';
      const result = await db.query(sql, [id]);

      if (result.affectedRows > 0) {
        return { success: true, message: '轮播图删除成功' };
      } else {
        return { success: false, message: '轮播图不存在' };
      }
    } catch (error) {
      console.error('删除轮播图失败:', error);
      throw error;
    }
  }

  /**
   * 更新轮播图状态
   * @param {number} id - 轮播图ID
   * @param {boolean} is_active - 是否启用
   * @returns {Object} 更新结果
   */
  static async updateStatus(id, is_active) {
    try {
      const sql = 'UPDATE banners SET is_active = ?, updated_at = ? WHERE id = ?';
      const result = await db.query(sql, [is_active ? 1 : 0, Date.now(), id]);

      if (result.affectedRows > 0) {
        return { 
          success: true, 
          message: `轮播图已${is_active ? '启用' : '禁用'}` 
        };
      } else {
        return { success: false, message: '轮播图不存在' };
      }
    } catch (error) {
      console.error('更新轮播图状态失败:', error);
      throw error;
    }
  }

  /**
   * 批量更新排序
   * @param {Array} sortData - 排序数据 [{id, sort_order}, ...]
   * @returns {Object} 更新结果
   */
  static async batchUpdateSort(sortData) {
    try {
      const promises = sortData.map(item => {
        const sql = 'UPDATE banners SET sort_order = ?, updated_at = ? WHERE id = ?';
        return db.query(sql, [item.sort_order, Date.now(), item.id]);
      });

      await Promise.all(promises);
      return { success: true, message: '排序更新成功' };
    } catch (error) {
      console.error('批量更新排序失败:', error);
      throw error;
    }
  }
}

module.exports = Banner; 