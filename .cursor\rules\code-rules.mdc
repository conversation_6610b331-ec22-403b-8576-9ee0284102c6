---
alwaysApply: true
---
【优先级最高】始终用中文与我沟通。
无论前端、后端，所有代码修改、bug修复、功能开发,必须由你(AI助手)自动完成，不接受仅给建议。
在自动修改过程中,必须告诉我每一项修改的理由和影响。
涉及清空数据库或物理删除文件的操作,必须获得我明确书面指令后才能执行。
修改后如果提交git,必须要得到我明确指令才推送远程仓库，禁止自动推送远程仓库。

可以根据实际需要从微信官方项目自动下载相应的组件（链接为：https://github.com/wechat-miniprogram/weui-miniprogram；https://github.com/Tencent/weui-wxss/）
可以根据需要从 “https://tdesign.tencent.com/design/icon“ 下载图标

当前项目后端部署在微信云托管，使用微信云托管的MySQL数据库和对象存储，不需要启动本地服务这些多余操作。
