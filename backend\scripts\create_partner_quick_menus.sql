-- 合伙人端快捷菜单表结构设计
-- 采用分层架构：支持全局配置 + 门店级别个性化

-- ========================================
-- 方案一：扩展现有表结构（推荐）
-- ========================================

-- 1. 为现有 quick_menus 表添加端标识字段
ALTER TABLE `quick_menus` 
ADD COLUMN `target_platform` ENUM('customer', 'partner', 'admin', 'all') NOT NULL DEFAULT 'customer' COMMENT '目标平台：customer=顾客端，partner=合伙人端，admin=管理端，all=全平台' AFTER `is_active`,
ADD COLUMN `required_roles` JSON NULL COMMENT '所需角色权限，JSON数组格式' AFTER `target_platform`,
ADD COLUMN `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `required_roles`,
ADD COLUMN `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `created_at`;

-- 添加索引
ALTER TABLE `quick_menus` 
ADD INDEX `idx_target_platform` (`target_platform`),
ADD INDEX `idx_is_active_platform` (`is_active`, `target_platform`);

-- ========================================
-- 方案二：独立表结构（如果需要更复杂的功能）
-- ========================================

-- 2. 创建合伙人快捷菜单配置表
CREATE TABLE IF NOT EXISTS `partner_quick_menus` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` VARCHAR(50) NOT NULL COMMENT '菜单名称',
  `icon` VARCHAR(200) NOT NULL COMMENT '图标路径',
  `link_type` ENUM('page', 'function', 'external', 'modal') NOT NULL DEFAULT 'page' COMMENT '链接类型：page=页面跳转，function=执行功能，external=外部链接，modal=弹窗',
  `link_url` VARCHAR(500) NOT NULL COMMENT '链接地址或功能标识',
  `function_params` JSON NULL COMMENT '功能参数，JSON格式',
  `required_permissions` JSON NULL COMMENT '所需权限，JSON数组格式，如["partner:view", "store:manage"]',
  `display_condition` JSON NULL COMMENT '显示条件，JSON格式，如{"store_level": ">=2", "has_products": true}',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序序号，数字越小越靠前',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用：1=启用，0=禁用',
  `is_system` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否系统菜单：1=系统级不可删除，0=可配置',
  `description` VARCHAR(200) NULL COMMENT '菜单描述',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_system` (`is_system`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合伙人端快捷菜单配置表';

-- 3. 创建门店级别菜单个性化配置表
CREATE TABLE IF NOT EXISTS `store_quick_menu_config` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_id` VARCHAR(50) NOT NULL COMMENT '门店ID',
  `menu_id` INT NOT NULL COMMENT '菜单ID（partner_quick_menus表主键）',
  `is_enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '该门店是否启用此菜单：1=启用，0=禁用',
  `custom_name` VARCHAR(50) NULL COMMENT '自定义菜单名称（为空则使用默认名称）',
  `custom_icon` VARCHAR(200) NULL COMMENT '自定义图标路径（为空则使用默认图标）',
  `custom_sort_order` INT NULL COMMENT '自定义排序（为空则使用默认排序）',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_store_menu` (`store_id`, `menu_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_menu_id` (`menu_id`),
  CONSTRAINT `fk_store_menu_config_menu` FOREIGN KEY (`menu_id`) REFERENCES `partner_quick_menus` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店快捷菜单个性化配置表';

-- ========================================
-- 插入默认的合伙人端快捷菜单数据
-- ========================================

-- 方案一：为现有表插入合伙人端菜单
INSERT INTO `quick_menus` (`name`, `icon`, `link_type`, `link_url`, `sort_order`, `is_active`, `target_platform`, `required_roles`) VALUES
('门店合伙人', '/images/icons2/门店合伙人.png', 'page', '/partner/partner/store-partners', 1, 1, 'partner', '["partner"]'),
('门店库存', '/images/icons2/门店库存.png', 'page', '/partner/partner/store-inventory', 2, 1, 'partner', '["partner"]'),
('在线客服', '/images/icons2/在线客服.png', 'function', 'contactService', 3, 1, 'partner', '["partner"]'),
('分享门店', '/images/icons2/分享门店.png', 'function', 'shareStore', 4, 1, 'partner', '["partner"]');

-- 方案二：为独立表插入默认菜单
INSERT INTO `partner_quick_menus` (`name`, `icon`, `link_type`, `link_url`, `function_params`, `required_permissions`, `display_condition`, `sort_order`, `is_active`, `is_system`, `description`) VALUES
('门店合伙人', '/images/icons2/门店合伙人.png', 'page', '/partner/partner/store-partners', NULL, '["partner:view"]', '{"store_partners": ">0"}', 1, 1, 1, '查看和管理门店合伙人信息'),
('门店库存', '/images/icons2/门店库存.png', 'page', '/partner/partner/store-inventory', NULL, '["partner:inventory"]', '{"has_store": true}', 2, 1, 1, '查看门店商品库存情况'),
('在线客服', '/images/icons2/在线客服.png', 'function', 'contactService', '{"type": "partner"}', '["partner:basic"]', NULL, 3, 1, 1, '联系在线客服获取帮助'),
('分享门店', '/images/icons2/分享门店.png', 'function', 'shareStore', '{"share_type": "partner"}', '["partner:share"]', '{"has_store": true}', 4, 1, 1, '分享门店给其他用户'),
('销售统计', '/images/icons2/销售统计.png', 'page', '/partner/partner/sales-stats', NULL, '["partner:stats"]', '{"store_level": ">=2"}', 5, 1, 0, '查看门店销售数据统计'),
('佣金明细', '/images/icons2/佣金明细.png', 'page', '/partner/partner/commission-detail', NULL, '["partner:finance"]', NULL, 6, 1, 0, '查看佣金收入明细');

-- ========================================
-- 创建相关视图和索引优化
-- ========================================

-- 创建合伙人菜单视图（如果使用方案二）
CREATE OR REPLACE VIEW `v_partner_menus` AS
SELECT 
    pm.id,
    COALESCE(smc.custom_name, pm.name) AS name,
    COALESCE(smc.custom_icon, pm.icon) AS icon,
    pm.link_type,
    pm.link_url,
    pm.function_params,
    pm.required_permissions,
    pm.display_condition,
    COALESCE(smc.custom_sort_order, pm.sort_order) AS sort_order,
    pm.is_active AND COALESCE(smc.is_enabled, 1) AS is_enabled,
    smc.store_id
FROM `partner_quick_menus` pm
LEFT JOIN `store_quick_menu_config` smc ON pm.id = smc.menu_id
WHERE pm.is_active = 1
ORDER BY sort_order ASC; 