// pages/order/list.js
const { orderApi } = require('../../utils/api');

Page({
  data: {
    orderList: [],
    loading: true,
    currentTab: 'all',
    tabs: [
      { key: 'all', name: '全部' },
      { key: 'pending_payment', name: '待付款' },
      { key: 'pending_shipment', name: '待发货' },
      { key: 'shipped', name: '待收货' },
      { key: 'completed', name: '已完成' },
      { key: 'cancelled', name: '已取消' },
      { key: 'refund', name: '退款/售后' }
    ],
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    isEmpty: false,
    statusMap: {
      'all': 'all',
      'pending_payment': 'pending_payment',
      'pending_shipment': 'pending_shipment',
      'shipped': 'shipped',
      'completed': 'completed',
      'cancelled': 'cancelled',
      'refund': 'refund'
    }
  },

  onLoad: function(options) {
    console.log('订单列表页面加载，参数:', options);
    // 设置当前标签页
    if (options && options.type && this.data.statusMap[options.type]) {
      this.setData({
        currentTab: options.type
      });
    }
    
    // 加载订单数据
    this.loadOrders();
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({
      orderList: [],
      pageNum: 1,
      hasMore: true,
      isEmpty: false
    });
    this.loadOrders().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreOrders();
    }
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab === this.data.currentTab) return;
    
    this.setData({
      currentTab: tab,
      orderList: [],
      pageNum: 1,
      hasMore: true,
      isEmpty: false,
      loading: true
    });
    
    this.loadOrders();
  },

  // 加载订单数据
  loadOrders: function() {
    const { currentTab, statusMap, pageNum, pageSize } = this.data;
    const status = statusMap[currentTab];
    
    this.setData({ loading: true });
    
    const params = {
      page: pageNum,
      limit: pageSize
    };
    
    if (status) {
      params.status = status;
    }
    
    console.log('订单列表页面 - 加载订单数据, 参数:', params);
    
    return orderApi.getOrders(params).then(res => {
      console.log('订单列表页面 - 获取订单响应:', res);
      
      if (res.success && res.data) {
        let list = [];
        let total = 0;
        
        // 处理不同的返回数据格式
        if (Array.isArray(res.data)) {
          // 如果直接返回数组
          list = res.data;
          total = res.data.length;
        } else if (res.data.list) {
          // 如果返回包含list和total的对象
          list = res.data.list;
          total = res.data.total || list.length;
        } else {
          // 其他情况，尝试处理
          list = res.data.orders || res.data.orderList || [];
          total = res.data.total || list.length;
        }
        
        console.log('处理后的订单数据:', { list, total });
        
        const hasMore = list.length >= pageSize && pageNum * pageSize < total;
        const isEmpty = list.length === 0 && pageNum === 1;
        
        this.setData({
          orderList: list,
          hasMore,
          isEmpty,
          loading: false
        });
      } else {
        console.warn('订单列表为空或请求失败');
        this.setData({
          isEmpty: true,
          loading: false
        });
      }
    }).catch(err => {
      console.error('获取订单列表失败:', err);
      this.setData({
        loading: false,
        isEmpty: true
      });
      wx.showToast({
        title: '获取订单失败',
        icon: 'none'
      });
    });
  },

  // 加载更多订单
  loadMoreOrders: function() {
    if (!this.data.hasMore || this.data.loading) return;
    
    const { currentTab, statusMap, pageNum, pageSize, orderList } = this.data;
    const status = statusMap[currentTab];
    
    this.setData({
      loading: true,
      pageNum: pageNum + 1
    });
    
    const params = {
      page: pageNum + 1,
      limit: pageSize
    };
    
    if (status) {
      params.status = status;
    }
    
    console.log('订单列表页面 - 加载更多订单, 参数:', params);
    
    return orderApi.getOrders(params).then(res => {
      console.log('订单列表页面 - 加载更多订单响应:', res);
      
      if (res.success && res.data) {
        let list = [];
        let total = 0;
        
        // 处理不同的返回数据格式
        if (Array.isArray(res.data)) {
          // 如果直接返回数组
          list = res.data;
          total = res.data.length;
        } else if (res.data.list) {
          // 如果返回包含list和total的对象
          list = res.data.list;
          total = res.data.total || list.length;
        } else {
          // 其他情况，尝试处理
          list = res.data.orders || res.data.orderList || [];
          total = res.data.total || list.length;
        }
        
        console.log('处理后的更多订单数据:', { list, total });
        
        const newList = [...orderList, ...list];
        const hasMore = list.length >= pageSize && newList.length < total;
        
        this.setData({
          orderList: newList,
          hasMore,
          loading: false
        });
      } else {
        console.warn('加载更多订单为空或请求失败');
        this.setData({
          loading: false,
          hasMore: false
        });
      }
    }).catch(err => {
      console.error('加载更多订单失败:', err);
      this.setData({
        loading: false
      });
      wx.showToast({
        title: '加载更多失败',
        icon: 'none'
      });
    });
  },

  // 查看订单详情
  viewOrderDetail: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order/detail?id=${orderId}`
    });
  },

  // 取消订单
  cancelOrder: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '提示',
      content: '确定要取消该订单吗？',
      success: (res) => {
        if (res.confirm) {
          orderApi.cancelOrder(orderId).then(res => {
            if (res.success) {
              wx.showToast({
                title: '订单已取消',
                icon: 'success'
              });
              // 刷新订单列表
              this.setData({
                pageNum: 1,
                orderList: []
              });
              this.loadOrders();
            } else {
              wx.showToast({
                title: res.message || '取消失败',
                icon: 'none'
              });
            }
          }).catch(() => {
            wx.showToast({
              title: '取消失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 去支付
  goToPay: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order/pay?id=${orderId}`
    });
  },

  // 确认收货
  confirmReceipt: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '提示',
      content: '确认已收到商品吗？',
      success: (res) => {
        if (res.confirm) {
          orderApi.confirmReceipt(orderId).then(res => {
            if (res.success) {
              wx.showToast({
                title: '确认收货成功',
                icon: 'success'
              });
              // 刷新订单列表
              this.setData({
                pageNum: 1,
                orderList: []
              });
              this.loadOrders();
            } else {
              wx.showToast({
                title: res.message || '操作失败',
                icon: 'none'
              });
            }
          }).catch(() => {
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 申请退款
  applyRefund: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order/refund?id=${orderId}`
    });
  },

  // 查看物流
  viewLogistics: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order/logistics?id=${orderId}`
    });
  },

  // 去评价
  goToRate: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order/rate?id=${orderId}`
    });
  },

  // 删除订单
  deleteOrder: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '提示',
      content: '确定要删除该订单吗？删除后将无法恢复',
      success: (res) => {
        if (res.confirm) {
          orderApi.deleteOrder(orderId).then(res => {
            if (res.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              // 刷新订单列表
              this.setData({
                pageNum: 1,
                orderList: []
              });
              this.loadOrders();
            } else {
              wx.showToast({
                title: res.message || '删除失败',
                icon: 'none'
              });
            }
          }).catch(() => {
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 查看退款进度
  viewRefundDetail: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order/refund-detail?id=${orderId}`
    });
  }
});