const { userApi, storeApi, partner<PERSON>pi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager'); // 新增

Page({
  data: {
    userList: [],
    storeList: [],
    joinTypeList: ['A', 'B', 'C', 'M'], // M为总部派出代表
    selectedUserIndex: null,
    selectedUserName: '',
    selectedStoreIndex: null,
    selectedStoreName: '',
    selectedTypeIndex: null,
    selectedType: '',
    amount: '',
    percent: '',
    userSearchValue: '',
    storeSearchValue: '',
    filteredUserList: [],
    filteredStoreList: [],
    showUserDropdown: false,
    showStoreDropdown: false,
    showTypeSelector: false,
  },
  onLoad() {
    // 热重载或页面加载时恢复全局登录状态
    loginStateManager.restoreLoginStateToGlobal();
  },
  // 用户搜索输入
  onUserSearchInput(e) {
    const val = e.detail.value.trim();
    this.setData({ userSearchValue: val });
    if (val.length < 1) {
      this.setData({ filteredUserList: [], showUserDropdown: false });
      return;
    }
    userApi.getUserList({ search: val }).then(res => {
      if (res.success && Array.isArray(res.data)) {
        this.setData({ filteredUserList: res.data, showUserDropdown: true });
      } else {
        this.setData({ filteredUserList: [], showUserDropdown: true });
      }
    }).catch(() => {
      this.setData({ filteredUserList: [], showUserDropdown: true });
    });
  },
  selectUser(e) {
    const idx = e.currentTarget.dataset.index;
    const user = this.data.filteredUserList[idx];
    this.setData({
      selectedUserIndex: idx,
      selectedUserName: user.nickname,
      userSearchValue: user.nickname,
      showUserDropdown: false
    });
  },
  // 门店搜索输入
  onStoreSearchInput(e) {
    const val = e.detail.value.trim();
    this.setData({ storeSearchValue: val });
    if (val.length < 1) {
      this.setData({ filteredStoreList: [], showStoreDropdown: false });
      return;
    }
    storeApi.searchStores(val).then(res => {
      if (res.success && Array.isArray(res.data)) {
        // 保证每项有code字段
        const list = res.data.map(item => ({ ...item, code: item.code || item.store_no || item.storeNo || '' }));
        this.setData({ filteredStoreList: list, showStoreDropdown: true });
      } else {
        this.setData({ filteredStoreList: [], showStoreDropdown: true });
      }
    }).catch(() => {
      this.setData({ filteredStoreList: [], showStoreDropdown: true });
    });
  },
  selectStore(e) {
    const idx = e.currentTarget.dataset.index;
    const store = this.data.filteredStoreList[idx];
    this.setData({
      selectedStoreIndex: idx,
      selectedStoreName: store.name,
      storeSearchValue: store.name,
      showStoreDropdown: false
    });
  },
  // 加入类型自定义弹窗逻辑
  openTypeSelector() {
    this.setData({ showTypeSelector: true });
  },
  closeTypeSelector() {
    this.setData({ showTypeSelector: false });
  },
  selectType(e) {
    const idx = e.currentTarget.dataset.index;
    const type = this.data.joinTypeList[idx];
    this.setData({
      selectedTypeIndex: idx,
      selectedType: type,
      showTypeSelector: false
    });
  },
  // 其它表单逻辑保持不变
  onTypeChange(e) {
    const idx = e.detail.value;
    this.setData({
      selectedTypeIndex: idx,
      selectedType: this.data.joinTypeList[idx]
    });
  },
  onAmountInput(e) {
    this.setData({ amount: e.detail.value });
  },
  onPercentInput(e) {
    this.setData({ percent: e.detail.value });
  },
  onSubmit() {
    if (!this.data.userSearchValue) {
      wx.showToast({ title: '请选择会员', icon: 'none' });
      return;
    }
    if (!this.data.storeSearchValue) {
      wx.showToast({ title: '请选择门店', icon: 'none' });
      return;
    }
    if (!this.data.selectedType) {
      wx.showToast({ title: '请选择类型', icon: 'none' });
      return;
    }
    if (!this.data.amount) {
      wx.showToast({ title: '请输入金额', icon: 'none' });
      return;
    }
    if (!this.data.percent) {
      wx.showToast({ title: '请输入股份占比', icon: 'none' });
      return;
    }
    // 获取选中用户和门店的ID
    const user = this.data.filteredUserList.find(u => u.nickname === this.data.userSearchValue);
    const store = this.data.filteredStoreList.find(s => s.name === this.data.storeSearchValue);
    if (!user || !store) {
      wx.showToast({ title: '请从下拉列表选择会员和门店', icon: 'none' });
      return;
    }
    const data = {
      user_id: user.id,
      store_id: store.id,
      store_no: store.code || store.store_no || store.storeNo || '',
      type: this.data.selectedType,
      amount: this.data.amount,
      percent: this.data.percent
    };
    wx.showLoading({ title: '提交中...' });
    partnerApi.joinPartner(data).then(res => {
      wx.hideLoading();
      if (res.success) {
        wx.showToast({ title: '提交成功', icon: 'success' });
      } else {
        wx.showToast({ title: res.message || '提交失败', icon: 'none' });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({ title: err.message || '提交失败', icon: 'none' });
    });
  }
}); 