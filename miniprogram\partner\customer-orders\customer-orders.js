const { orderApi } = require('../../utils/api');
const app = getApp();

Page({
  data: {
    // 搜索相关
    searchKeyword: '',
    
    // 筛选相关
    storeList: [],
    selectedStore: 'all',
    selectedStoreLabel: '所有门店',
    deliveryMethod: 'all',
    deliveryMethodLabel: '所有订单',
    deliveryMethodIndex: 0,
    dateRange: 'all',
    dateRangeLabel: '所有日期',
    dateRangeIndex: 0,
    
    // 订单状态标签
    currentStatus: 'all',
    statusTabs: [
      { key: 'all', name: '全部' },
      { key: 'pending_shipment', name: '待发货' },
      { key: 'shipped', name: '已发货' },
      { key: 'signed', name: '已签收' },
      { key: 'returns', name: '退换货' }
    ],
    
    // 订单列表
    orderList: [],
    loading: true,
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    isEmpty: false,
    
    // 用户信息
    userInfo: null
  },

  onLoad: function(options) {
    console.log('合伙人端顾客订单页面加载，参数:', options);
    
    // 获取用户信息
    const userInfo = app.globalData.userInfo || {};
    console.log('当前用户信息:', userInfo);
    
    this.setData({
      userInfo: userInfo
    });
    
    // 设置初始状态标签
    if (options && options.status) {
      this.setData({
        currentStatus: options.status
      });
    }
    
    // 加载门店列表
    this.loadStoreList();
    
    // 加载订单数据
    this.loadOrders();
  },

  onShow: function() {
    console.log('合伙人端顾客订单页面显示');
    
    // 检查是否从其他页面切换过来，需要刷新数据
    const lastPage = wx.getStorageSync('lastPage');
    const isFromOtherPage = lastPage && lastPage.includes('partner/') && !lastPage.includes('customer-orders');
    
    if (isFromOtherPage) {
      console.log('从其他页面切换过来，刷新订单数据');
      this.refreshOrders();
    }
    
    // 记录当前页面路径
    wx.setStorageSync('lastPage', 'partner/customer-orders');
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.refreshOrders().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreOrders();
    }
  },

  /**
   * 加载门店列表
   */
  loadStoreList: function() {
    console.log('加载门店列表');
    
    // 直接从API获取最新的门店列表
    this.fetchStoreList();
  },

  /**
   * 从API获取门店列表
   */
  fetchStoreList: function() {
    console.log('从API获取门店列表');
    
    const { partnerApi } = require('../../utils/api');
    
    // 同时调用两个获取门店的API
    Promise.all([
      partnerApi.getPartnerStores(),
      partnerApi.getPartnerJoinedStores()
    ]).then(([storesRes, joinedStoresRes]) => {
      // 合并两个API的门店数据，并去重
      let allStores = [];
      
      if (storesRes && storesRes.success && storesRes.data) {
        allStores = [...storesRes.data];
      }
      
      if (joinedStoresRes && joinedStoresRes.success && joinedStoresRes.data) {
        // 将joinedStores中的门店添加到allStores中，避免重复
        joinedStoresRes.data.forEach(store => {
          // 检查是否已存在相同store_no的门店
          const existingIndex = allStores.findIndex(s => s.store_no === store.store_no);
          if (existingIndex === -1) {
            // 不存在则添加
            allStores.push(store);
          }
        });
      }
      
      console.log('合并后的门店列表数据:', allStores);
      
      // 保存到本地存储
      wx.setStorageSync('partnerStoreList', allStores);
      
      // 从本地存储获取之前选择的门店
      const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
      let selectedStoreNo = 'all';
      let selectedStoreLabel = '所有门店';
      
      // 转换为下拉选择器格式
      const storeOptions = [
        { value: 'all', label: '所有门店' },
        ...allStores.map(store => ({
          value: store.store_no,
          label: store.name || store.store_name
        }))
      ];
      
      // 如果之前选择了特定门店，保持选择
      if (storedSelectedStore && storedSelectedStore.store_no !== 'all') {
        const matchedStore = allStores.find(store => 
          store.id === storedSelectedStore.id || store.store_no === storedSelectedStore.store_no
        );
        
        if (matchedStore) {
          selectedStoreNo = matchedStore.store_no;
          selectedStoreLabel = matchedStore.name || matchedStore.store_name;
        }
      }
      
      this.setData({
        storeList: storeOptions,
        selectedStore: selectedStoreNo,
        selectedStoreLabel: selectedStoreLabel
      });
      
      console.log('门店列表已更新:', storeOptions);
    }).catch(err => {
      console.error('获取门店列表失败:', err);
      
      // 使用默认数据
      const defaultStoreList = [
        { value: 'all', label: '所有门店' }
      ];
      
      this.setData({
        storeList: defaultStoreList,
        selectedStore: 'all',
        selectedStoreLabel: '所有门店'
      });
    });
  },

  /**
   * 加载订单数据
   */
  loadOrders: function() {
    console.log('加载顾客订单数据');
    
    this.setData({
      loading: true,
      orderList: [],
      pageNum: 1,
      hasMore: true,
      isEmpty: false
    });

    this.fetchOrders();
  },

  /**
   * 刷新订单数据
   */
  refreshOrders: function() {
    console.log('刷新订单数据');
    return this.loadOrders();
  },

  /**
   * 加载更多订单
   */
  loadMoreOrders: function() {
    if (!this.data.hasMore || this.data.loading) return;
    
    console.log('加载更多订单');
    this.setData({
      pageNum: this.data.pageNum + 1
    });
    
    this.fetchOrders(true);
  },

  /**
   * 获取订单数据
   */
  fetchOrders: function(isLoadMore = false) {
    // 显示加载状态
    if (!isLoadMore) {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });
    }
    
    const params = {
      page: this.data.pageNum,
      limit: this.data.pageSize,
      status: this.data.currentStatus === 'all' ? '' : this.data.currentStatus,
      store_no: this.data.selectedStore === 'all' ? '' : this.data.selectedStore,
      delivery_method: this.data.deliveryMethod === 'all' ? '' : this.data.deliveryMethod,
      date_range: this.data.dateRange === 'all' ? '' : this.data.dateRange,
      keyword: this.data.searchKeyword
    };

    console.log('获取顾客订单参数:', params);

    // 调用合伙人端顾客订单API
    orderApi.getPartnerCustomerOrders(params).then(res => {
      // 隐藏加载状态
      wx.hideLoading();
      
      console.log('顾客订单数据:', res);
      
      if (res && res.success && res.data) {
        const newOrders = res.data.orders || [];
        const total = res.data.total || 0;
        
        // 处理订单数据，添加显示所需的额外信息
        const processedOrders = newOrders.map(order => {
          // 处理订单状态显示
          let statusText = '未知状态';
          switch(order.status) {
            case 'pending_shipment':
              statusText = '待发货';
              break;
            case 'shipped':
              statusText = '已发货';
              break;
            case 'signed':
              statusText = '已签收';
              break;
            case 'returns':
              statusText = '退换货';
              break;
            default:
              statusText = '未知状态';
          }
          order.statusText = statusText;
          
          // 处理配送方式显示
          let deliveryText = '未知配送';
          switch(order.delivery_method) {
            case 'express':
              deliveryText = '快递配送';
              break;
            case 'pickup':
              deliveryText = '门店自提';
              break;
            default:
              deliveryText = '未知配送';
          }
          order.deliveryText = deliveryText;
          
          // 处理是否为当前用户销售的订单
          const currentUserId = this.data.userInfo.id || this.data.userInfo.userId;
          console.log('当前用户ID:', currentUserId, '订单销售人ID:', order.salesman_id);
          order.isSalesmanOrder = order.salesman_id == currentUserId;
          
          return order;
        });
        
        if (isLoadMore) {
          this.setData({
            orderList: [...this.data.orderList, ...processedOrders],
            hasMore: this.data.orderList.length + processedOrders.length < total,
            loading: false
          });
        } else {
          this.setData({
            orderList: processedOrders,
            hasMore: processedOrders.length === this.data.pageSize,
            isEmpty: processedOrders.length === 0,
            loading: false
          });
        }
      } else {
        console.error('获取顾客订单失败:', res ? res.message : '未知错误');
        this.setData({
          loading: false,
          isEmpty: true
        });
        wx.showToast({
          title: res && res.message ? res.message : '获取订单失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      // 隐藏加载状态
      wx.hideLoading();
      
      console.error('获取顾客订单异常:', err);
      this.setData({
        loading: false,
        isEmpty: true
      });
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 搜索订单
   */
  onSearch: function() {
    console.log('搜索订单:', this.data.searchKeyword);
    this.loadOrders();
  },

  /**
   * 搜索输入
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 门店选择
   */
  onStoreChange: function(e) {
    const storeNo = e.detail.value;
    console.log('选择门店:', storeNo);
    
    // 计算门店标签
    let selectedStoreLabel = '所有门店';
    if (storeNo !== 'all') {
      const selectedStore = this.data.storeList.find(s => s.value === storeNo);
      if (selectedStore) {
        selectedStoreLabel = selectedStore.label;
      }
    }
    
    this.setData({
      selectedStore: storeNo,
      selectedStoreLabel: selectedStoreLabel
    });
    
    this.loadOrders();
  },

  /**
   * 配送方式选择
   */
  onDeliveryMethodChange: function(e) {
    const methodIndex = e.detail.value;
    let deliveryMethod = 'all';
    let deliveryMethodLabel = '所有订单';
    
    if (methodIndex == 1) {
      deliveryMethod = 'express';
      deliveryMethodLabel = '快递订单';
    } else if (methodIndex == 2) {
      deliveryMethod = 'pickup';
      deliveryMethodLabel = '自提订单';
    }
    
    console.log('选择配送方式:', deliveryMethod);
    
    this.setData({
      deliveryMethod: deliveryMethod,
      deliveryMethodLabel: deliveryMethodLabel,
      deliveryMethodIndex: methodIndex
    });
    
    this.loadOrders();
  },

  /**
   * 日期范围选择
   */
  onDateRangeChange: function(e) {
    const rangeIndex = e.detail.value;
    let dateRange = 'all';
    let dateRangeLabel = '所有日期';
    
    if (rangeIndex == 1) {
      dateRange = 'today';
      dateRangeLabel = '今天';
    } else if (rangeIndex == 2) {
      dateRange = 'yesterday';
      dateRangeLabel = '昨天';
    } else if (rangeIndex == 3) {
      dateRange = 'week';
      dateRangeLabel = '本周';
    } else if (rangeIndex == 4) {
      dateRange = 'month';
      dateRangeLabel = '本月';
    }
    
    console.log('选择日期范围:', dateRange);
    
    this.setData({
      dateRange: dateRange,
      dateRangeLabel: dateRangeLabel,
      dateRangeIndex: rangeIndex
    });
    
    this.loadOrders();
  },

  /**
   * 切换订单状态标签
   */
  switchStatusTab: function(e) {
    const status = e.currentTarget.dataset.status;
    console.log('切换订单状态:', status);
    
    if (status === this.data.currentStatus) return;
    
    this.setData({
      currentStatus: status
    });
    
    this.loadOrders();
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail: function(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('查看订单详情:', orderId);
    
    wx.navigateTo({
      url: `/partner/customer-orders/detail?id=${orderId}`
    });
  },

  /**
   * 返回上一页
   */
  goBack: function() {
    wx.navigateBack();
  }
}); 