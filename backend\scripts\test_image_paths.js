/**
 * 测试图片路径处理和错误修复
 */
const request = require('supertest');
const app = require('../app');

async function testImagePaths() {
  console.log('开始测试图片路径处理...');
  
  // 测试正常的图片路径
  console.log('\n1. 测试正常图片路径:');
  try {
    const response1 = await request(app)
      .get('/images/xinxi/企业服务.jpg')
      .expect(200);
    console.log('✅ 正常中文路径测试通过');
  } catch (err) {
    console.log('❌ 正常中文路径测试失败:', err.message);
  }
  
  // 测试错误编码的路径
  console.log('\n2. 测试错误编码路径:');
  try {
    const response2 = await request(app)
      .get('/images/xinxi/XE7XOF%A5%E8%AF%85%E4%A8%BA%E6%9D%83.jpg')
      .expect(404);
    console.log('✅ 错误编码路径正确返回404');
    console.log('响应内容:', response2.body);
  } catch (err) {
    console.log('❌ 错误编码路径测试失败:', err.message);
  }
  
  // 测试URL编码的正确中文路径
  console.log('\n3. 测试URL编码的正确中文路径:');
  try {
    const encodedPath = encodeURIComponent('企业服务.jpg');
    console.log('编码后的路径:', encodedPath);
    const response3 = await request(app)
      .get(`/images/xinxi/${encodedPath}`)
      .expect(200);
    console.log('✅ URL编码的中文路径测试通过');
  } catch (err) {
    console.log('❌ URL编码的中文路径测试失败:', err.message);
  }
  
  // 测试不存在的文件
  console.log('\n4. 测试不存在的文件:');
  try {
    const response4 = await request(app)
      .get('/images/xinxi/不存在的文件.jpg')
      .expect(404);
    console.log('✅ 不存在文件正确返回404');
    console.log('响应内容:', response4.body);
  } catch (err) {
    console.log('❌ 不存在文件测试失败:', err.message);
  }
  
  console.log('\n图片路径测试完成');
}

// 如果直接运行此脚本
if (require.main === module) {
  testImagePaths().then(() => {
    console.log('\n测试脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('\n测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testImagePaths };