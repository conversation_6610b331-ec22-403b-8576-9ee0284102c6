<!--partner/inventory/inventory.wxml-->
<!--合伙人端门店库存页面-->
<view class="inventory-container">
  <!-- 门店选择区域 -->
  <view class="store-select-section">
    <view class="store-select-header">
      <view class="section-title">当前门店</view>
      <view class="store-select-container">
        <picker class="store-picker" 
                mode="selector" 
                range="{{storeList}}" 
                range-key="name" 
                bindchange="onStoreChange" 
                wx:if="{{storeList.length > 0}}">
          <view class="store-selector">
            <text class="store-name">{{selectedStore.name}}（{{selectedStore.store_no}}）</text>
            <view class="dropdown-arrow">▼</view>
          </view>
        </picker>
        <view class="store-selector" wx:else>
          <text class="store-name">暂无门店数据</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-input-wrapper">
      <image class="search-icon" src="/images/icons2/搜索.png"></image>
      <input class="search-input" 
             placeholder="搜索商品名称" 
             placeholder-class="search-placeholder"
             value="{{searchKeyword}}"
             bindinput="onSearchInput"
             bindconfirm="onSearchConfirm" />
    </view>
  </view>

  <!-- 排序筛选栏 -->
  <view class="sort-filter-bar">
    <view class="sort-options">
      <view class="sort-item {{currentSort === 'default' ? 'active' : ''}}" 
            bindtap="onSortChange" 
            data-sort="default">
        <text>综合</text>
      </view>
      <view class="sort-item {{currentSort === 'inventory_asc' || currentSort === 'inventory_desc' ? 'active' : ''}}" 
            bindtap="onToggleInventorySort">
        <text>库存</text>
        <image class="sort-icon" src="/images/icons2/{{currentSort === 'inventory_desc' ? '降序' : '升序'}}.svg"></image>
      </view>
    </view>
    <view class="action-btns">
      <view class="filter-btn" bindtap="onShowFilter">
        <text>筛选</text>
        <image class="filter-icon" src="/images/icons2/筛选.svg"></image>
      </view>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="products-section">
    <block wx:if="{{products.length > 0}}">
      <view class="product-card" 
            wx:for="{{products}}" 
            wx:key="id"
            data-id="{{item.id}}">
        <image class="product-image" src="{{item.imageUrl || '/images/mo/mogoods.jpg'}}" mode="aspectFill" binderror="onImageError" data-id="{{item.id}}" catchtap="onProductTap" data-id="{{item.id}}"></image>
        <view class="product-info" catchtap="onProductTap" data-id="{{item.id}}">
          <view class="product-name">{{item.name}}</view>
          <view class="product-spec" wx:if="{{item.spec}}">{{item.spec}}</view>
          
          <view class="product-price-row">
            <view class="retail-price">零售价: ¥{{item.price}}</view>
            <view class="purchase-price">采购价: ¥{{item.purchasePrice || item.price}}</view>
          </view>
          
          <view class="inventory-row">
            <view class="cloud-inventory {{item.editInventory !== undefined && item.editInventory !== item.cloudInventory ? 'modified' : ''}}">
              <text>云端库存: {{item.cloudInventory || 0}}</text>
              <text wx:if="{{item.editInventory !== undefined && item.editInventory !== item.cloudInventory}}" class="new-value">→ {{item.editInventory}}</text>
            </view>
            <view class="local-inventory">本地库存: {{item.localInventory || 0}}</view>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && products.length === 0}}">
      <image class="empty-icon" src="/images/icons2/空状态.svg"></image>
      <text class="empty-text">暂无库存商品</text>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{!loading && hasMore}}">
      <text class="load-more-text">上拉加载更多</text>
    </view>
    
    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!loading && !hasMore && products.length > 0}}">
      <text class="no-more-text">没有更多了</text>
    </view>
  </view>

  <!-- 筛选抽屉 -->
  <view class="filter-drawer {{showFilterDrawer ? 'show' : ''}}">
    <view class="filter-drawer-mask" bindtap="onHideFilter"></view>
    <view class="filter-drawer-content">
      <view class="filter-drawer-header">
        <text class="filter-drawer-title">筛选</text>
        <view class="filter-drawer-close" bindtap="onHideFilter">
          <image class="close-icon" src="/images/icons2/关闭.svg"></image>
        </view>
      </view>
      
      <view class="filter-drawer-body">
        <!-- 价格区间 -->
        <view class="filter-section">
          <view class="filter-section-title">价格区间</view>
          <view class="price-range-inputs">
            <input class="price-input" type="digit" placeholder="最低价" value="{{filterOptions.priceRange[0]}}" />
            <text class="price-range-separator">-</text>
            <input class="price-input" type="digit" placeholder="最高价" value="{{filterOptions.priceRange[1]}}" />
          </view>
        </view>
        
        <!-- 商品分类 -->
        <view class="filter-section">
          <view class="filter-section-title">商品分类</view>
          <view class="category-list">
            <view class="category-item {{filterOptions.categoryId === null ? 'active' : ''}}" 
                  bindtap="onCategorySelect" 
                  data-id="{{null}}">
              <text>全部</text>
            </view>
            <view class="category-item {{filterOptions.categoryId === item.id ? 'active' : ''}}" 
                  wx:for="{{categories}}" 
                  wx:key="id"
                  bindtap="onCategorySelect"
                  data-id="{{item.id}}">
              <text>{{item.name}}</text>
            </view>
          </view>
        </view>
        
        <!-- 库存量 -->
        <view class="filter-section">
          <view class="filter-section-title">库存量</view>
          <view class="inventory-range-inputs">
            <input class="inventory-input" type="number" placeholder="最低库存" value="{{filterOptions.inventoryRange[0]}}" />
            <text class="inventory-range-separator">-</text>
            <input class="inventory-input" type="number" placeholder="最高库存" value="{{filterOptions.inventoryRange[1]}}" />
          </view>
        </view>
      </view>
      
      <view class="filter-drawer-footer">
        <button class="reset-btn" bindtap="onResetFilter">重置</button>
        <button class="apply-btn" bindtap="onApplyFilter">确定</button>
      </view>
    </view>
  </view>
</view>