/* pages/order/refund.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 加载中 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  height: 300rpx;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 26rpx;
  color: #999;
}

/* 订单信息 */
.order-section {
  background-color: #ff6b00;
  padding: 40rpx 30rpx;
  color: #fff;
}

.order-title {
  font-size: 28rpx;
  margin-bottom: 20rpx;
  opacity: 0.9;
}

.order-info {
  display: flex;
  flex-direction: column;
}

.order-no {
  font-size: 26rpx;
  margin-bottom: 10rpx;
  opacity: 0.9;
}

.order-amount {
  font-size: 28rpx;
}

.order-amount .amount {
  font-size: 36rpx;
  font-weight: 500;
  margin-left: 10rpx;
}

/* 退款表单 */
.refund-form {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

/* 退款类型 */
.refund-type-options {
  display: flex;
  align-items: center;
}

.type-option {
  display: flex;
  align-items: center;
  margin-right: 60rpx;
  padding: 10rpx 0;
}

.radio-circle {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  margin-right: 10rpx;
}

.radio-circle.checked {
  border-color: #ff6b00;
  background-color: #ff6b00;
  position: relative;
}

.radio-circle.checked::after {
  content: '';
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #fff;
}

.type-option text {
  font-size: 28rpx;
  color: #333;
}

/* 退款金额输入 */
.form-input-container {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 10rpx;
}

.input-prefix {
  font-size: 32rpx;
  color: #333;
  margin-right: 10rpx;
}

.form-input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  height: 80rpx;
}

.form-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 退款原因 */
.reason-options {
  display: flex;
  flex-direction: column;
}

.reason-option {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.reason-option:last-child {
  border-bottom: none;
}

.reason-option text {
  font-size: 28rpx;
  color: #333;
}

/* 自定义原因 */
.form-textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
}

.textarea-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 上传凭证 */
.upload-container {
  margin-top: 10rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}

.delete-btn {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  background-color: #f9f9f9;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.upload-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 10rpx;
  line-height: 1;
}

.upload-btn text {
  font-size: 24rpx;
  color: #999;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 提交按钮 */
.submit-btn {
  width: 90%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #ff6b00;
  color: #fff;
  font-size: 32rpx;
  text-align: center;
  border-radius: 44rpx;
  margin: 40rpx auto;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}