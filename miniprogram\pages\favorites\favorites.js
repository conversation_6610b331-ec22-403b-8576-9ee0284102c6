const { favoriteApi, cartApi, productApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');

Page({
  data: {
    favorites: [],
    filteredFavorites: [],
    categories: [],
    currentCategory: 'all',
    selectedItems: [],
    allSelected: false,
    selectedCount: 0,
    loading: true
  },

  onLoad: function() {
    console.log('收藏夹页面加载');
    this.checkLoginStatus();
  },

  onShow: function() {
    this.checkLoginStatus();
  },

  checkLoginStatus: function() {
    const app = getApp();
    this.setData({ loading: true });

    if (app.globalData.isLogin && app.globalData.userInfo) {
      this.setData({ loading: false });
      this.getFavorites();
      this.getCategories();
      return;
    }

    const userInfo = wx.getStorageSync('userInfo');
    if (loginStateManager.getLoginState() && loginStateManager.getLoginState().isLogin && userInfo) {
      app.globalData.userInfo = userInfo;
      app.globalData.isLogin = true;
      this.setData({ loading: false });
      this.getFavorites();
      this.getCategories();
      return;
    }

    loginStateManager.validateLoginState()
      .then(result => {
        if (result.isValid) {
          app.globalData.userInfo = result.userInfo;
          app.globalData.isLogin = true;
          this.setData({ loading: false });
          this.getFavorites();
          this.getCategories();
        } else {
          wx.navigateTo({
            url: '/pages/auth/auth'
          });
        }
      })
      .catch(err => {
        console.error('验证登录状态出错:', err);
        wx.navigateTo({
          url: '/pages/auth/auth'
        });
      });
  },

  // 获取分类数据
  getCategories: function() {
    productApi.getCategories()
      .then(res => {
        if (res.success && Array.isArray(res.data)) {
          this.setData({ categories: res.data });
        }
      })
      .catch(err => {
        console.error('获取分类失败:', err);
      });
  },

  getFavorites: function() {
    this.setData({ loading: true });
    
    favoriteApi.getFavorites()
      .then(res => {
        console.log('收藏API响应:', res);
        if (res && res.success && res.data) {
          // 为每个商品添加选中状态
          const favoritesWithSelection = res.data.map(item => ({
            ...item,
            selected: false
          }));
          
          this.setData({
            favorites: favoritesWithSelection,
            filteredFavorites: favoritesWithSelection,
            loading: false
          });
        } else {
          this.setData({
            favorites: [],
            filteredFavorites: [],
            loading: false
          });
        }
      })
      .catch(err => {
        console.error('获取收藏列表失败:', err);
        this.setData({
          favorites: [],
          filteredFavorites: [],
          loading: false
        });
        
        wx.showToast({
          title: '获取收藏列表失败',
          icon: 'none',
          duration: 2000
        });
      });
  },

  // 切换分类
  switchCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({ currentCategory: category });
    this.filterFavorites();
  },

  // 筛选收藏商品
  filterFavorites: function() {
    const { favorites, currentCategory } = this.data;
    let filtered = favorites;

    if (currentCategory !== 'all') {
      filtered = favorites.filter(item => item.categoryId == currentCategory);
    }

    this.setData({ filteredFavorites: filtered });
  },

  // 切换全选状态
  toggleSelectAll: function() {
    const { allSelected, filteredFavorites } = this.data;
    const newSelected = !allSelected;
    
    const updatedFavorites = filteredFavorites.map(item => ({
      ...item,
      selected: newSelected
    }));

    const selectedCount = newSelected ? updatedFavorites.length : 0;

    this.setData({
      filteredFavorites: updatedFavorites,
      allSelected: newSelected,
      selectedCount: selectedCount
    });

    // 同步更新原始favorites数组
    this.syncFavoritesSelection();
  },

  // 切换单个商品选中状态
  toggleSelectItem: function(e) {
    const index = e.currentTarget.dataset.index;
    const { filteredFavorites } = this.data;
    
    const updatedFavorites = [...filteredFavorites];
    updatedFavorites[index].selected = !updatedFavorites[index].selected;

    const selectedCount = updatedFavorites.filter(item => item.selected).length;
    const allSelected = selectedCount === updatedFavorites.length;

    this.setData({
      filteredFavorites: updatedFavorites,
      selectedCount: selectedCount,
      allSelected: allSelected
    });

    // 同步更新原始favorites数组
    this.syncFavoritesSelection();
  },

  // 同步选中状态到原始数组
  syncFavoritesSelection: function() {
    const { filteredFavorites, favorites } = this.data;
    const updatedFavorites = favorites.map(fav => {
      const filteredItem = filteredFavorites.find(f => f.id === fav.id);
      return filteredItem ? { ...fav, selected: filteredItem.selected } : fav;
    });

    this.setData({ favorites: updatedFavorites });
  },

  // 移出选中的商品
  removeSelected: function() {
    const { selectedCount } = this.data;
    if (selectedCount === 0) {
      wx.showToast({
        title: '请先选择要移出的商品',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认移出',
      content: `确定要移出选中的${selectedCount}个商品吗？`,
      success: (res) => {
        if (res.confirm) {
          this.removeSelectedItems();
        }
      }
    });
  },

  // 执行移出操作
  removeSelectedItems: function() {
    const { filteredFavorites } = this.data;
    const selectedItems = filteredFavorites.filter(item => item.selected);
    const selectedIds = selectedItems.map(item => item.id);

    wx.showLoading({ title: '正在移出...' });

    // 这里需要调用API批量移出收藏
    Promise.all(selectedItems.map(item => favoriteApi.removeFromFavorites(item.productId)))
      .then(() => {
        wx.hideLoading();
        wx.showToast({
          title: '移出成功',
          icon: 'success'
        });
        // 重新获取收藏列表
        this.getFavorites();
      })
      .catch(err => {
        console.error('移出收藏失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '移出失败，请重试',
          icon: 'none'
        });
      });
  },

  // 移出单个商品
  removeItem: function(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.filteredFavorites[index];

    wx.showModal({
      title: '确认移出',
      content: '确定要移出这个商品吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '正在移出...' });
          
          favoriteApi.removeFromFavorites(item.productId)
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '移出成功',
                icon: 'success'
              });
              // 重新获取收藏列表
              this.getFavorites();
            })
            .catch(err => {
              console.error('移出收藏失败:', err);
              wx.hideLoading();
              wx.showToast({
                title: '移出失败，请重试',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  addToCart: function(e) {
    const product = e.currentTarget.dataset.product;
    
    wx.showLoading({
      title: '正在加入购物车',
      mask: true
    });
    
    cartApi.addToCart(product.productId, 1)
      .then(res => {
        wx.hideLoading();
        
        if (res.success) {
          wx.showToast({
            title: '已加入购物车',
            icon: 'success',
            duration: 1500
          });
        } else {
          wx.showToast({
            title: res.message || '加入购物车失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('加入购物车失败', err);
        wx.hideLoading();
        
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  },

  goToShop: function() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  }
}); 