Page({
  data: {
    groupName: '',
    groupDesc: '',
    visible: true,
    needApprove: false,
    coverUrl: '/images/icons2/qun.png',
    coverLocalPath: ''
  },
  onGroupNameInput(e) {
    this.setData({ groupName: e.detail.value });
  },
  onGroupDescInput(e) {
    this.setData({ groupDesc: e.detail.value });
  },
  onVisibleChange(e) {
    this.setData({ visible: e.detail.value });
  },
  onApproveChange(e) {
    this.setData({ needApprove: e.detail.value });
  },
  
  onShow: function() {
    // 检查是否有新的裁剪后的封面图片
    if (this.data.coverLocalPath && this.data.coverUrl === this.data.coverLocalPath) {
      // 有新的裁剪图片，先显示缩略图
      console.log('检测到裁剪后的图片，显示缩略图:', this.data.coverLocalPath);
      // coverUrl已经在cropper页面中设置为本地路径，这里不需要额外操作
    }
  },
  onChooseCover() {
    console.log('开始选择群组封面');
    // 先尝试使用chooseMedia API
    try {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: res => {
          console.log('选择群组封面成功:', res);
          const file = res.tempFiles[0];
          if (file.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
            wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
            return;
          }
          // 跳转到裁剪页面
          wx.navigateTo({
            url: `/partner/messages/cropper/cropper?src=${encodeURIComponent(file.tempFilePath)}`
          });
        },
        fail: err => {
          console.error('chooseMedia 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消选择群组封面');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才使用备选方案
          this.onChooseCoverFallback();
        }
      });
    } catch (error) {
      console.error('chooseMedia 异常:', error);
      // 如果chooseMedia出现异常，尝试使用chooseImage作为备选方案
      this.onChooseCoverFallback();
    }
  },

  // 选择群组封面备选方案
  onChooseCoverFallback() {
    console.log('使用chooseImage作为备选方案选择群组封面');
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        console.log('chooseImage选择群组封面成功:', res);
        const tempFilePath = res.tempFilePaths[0];
        const tempFile = res.tempFiles[0];
        if (tempFile && tempFile.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
          wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
          return;
        }
        // 跳转到裁剪页面
        wx.navigateTo({
          url: `/partner/messages/cropper/cropper?src=${encodeURIComponent(tempFilePath)}`
        });
      },
      fail: err => {
        console.error('chooseImage 失败:', err);
        // 检查是否是用户取消操作
        if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
          console.log('用户取消选择群组封面');
          return; // 用户取消，直接返回
        }
        // 其他错误情况才显示提示
        wx.showToast({
          title: '选择图片失败，请检查相关权限',
          icon: 'none'
        });
      }
    });
  },
  async onSubmit() {
    const { groupName, groupDesc, visible, needApprove, coverUrl, coverLocalPath } = this.data;
    if (!groupName.trim()) {
      wx.showToast({ title: '请输入群名称', icon: 'none' });
      return;
    }
    wx.showLoading({ title: '创建中...', mask: true });
    let avatarUrl = coverUrl;
    if (coverLocalPath && coverUrl === coverLocalPath) {
      try {
                                     const { uploadFile } = require('../../utils/api');
        avatarUrl = await uploadFile(coverLocalPath);
      } catch (e) {
        wx.hideLoading();
        wx.showToast({ title: '封面上传失败', icon: 'none' });
        return;
      }
    }
                     const { groupApi } = require('../../utils/api');
    groupApi.createGroup({
      name: groupName,
      description: groupDesc,
      visible,
      needApprove,
      avatar: avatarUrl
    }).then(res => {
      wx.hideLoading();
      if (res.success) {
        wx.showToast({ title: '创建成功', icon: 'success' });
        setTimeout(() => {
          wx.navigateBack();
        }, 800);
      } else {
        wx.showToast({ title: res.message || '创建失败', icon: 'none' });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({ title: '网络错误', icon: 'none' });
    });
  }
});