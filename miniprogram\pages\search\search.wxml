<!--pages/search/search.wxml-->
<view class="search-container">
  <!-- 搜索栏 -->
  <view class="search-header">
    <view class="search-input-container">
      <input class="search-input" placeholder="{{placeholder || '搜索感兴趣的内容'}}" placeholder-class="search-placeholder"
        bindinput="onSearchInput" value="{{keyword}}" focus="{{true}}" confirm-type="search" bindconfirm="onSearch" />
      <view class="search-btn" bindtap="onSearch">
        <image src="/images/icons2/搜索.png"></image>
        <text>搜索</text>
      </view>
    </view>
    <view class="clear-btn" bindtap="clearSearch">清除</view>
  </view>

  <!-- 搜索历史 -->
  <view class="search-history" wx:if="{{!keyword && searchHistory.length > 0 && !showResults}}">
    <view class="history-header">
      <text class="history-title">搜索历史</text>
      <view class="clear-history" bindtap="clearHistory">
        <text class="delete-icon">🗑️</text>
      </view>
    </view>
    <view class="history-list">
      <view class="history-item" wx:for="{{searchHistory}}" wx:key="*this" bindtap="onHistoryTap" data-keyword="{{item}}">
        <text class="history-icon">⏱️</text>
        <text class="history-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{showResults}}">
    <!-- 商品搜索结果 -->
    <view class="result-count" wx:if="{{productResults.length > 0}}">找到 {{productResults.length}} 个相关商品</view>

    <!-- 商品列表 -->
    <view class="product-list">
      <view class="product-item" wx:for="{{productResults}}" wx:key="_id" bindtap="onProductTap" data-id="{{item._id || item.id}}">
        <image class="product-image" src="{{imgErrorMap[item._id || item.id] ? '/images/mo/mogoods.jpg' : (item.images && item.images.length > 0 ? item.images[0] : '/images/mo/mogoods.jpg')}}" mode="aspectFill" binderror="onImageError" data-id="{{item._id || item.id}}"></image>
        <view class="product-info">
          <view class="product-name">{{item.name}}</view>
          <view class="product-price-row">
            <view class="product-price">¥{{item.price}}</view>
            <view class="product-original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</view>
          </view>
          <view class="product-sales">已售 {{item.salesCount || 0}}</view>
        </view>
      </view>
    </view>

    <!-- 空结果 -->
    <view class="empty-container" wx:if="{{productResults.length === 0 && !loading}}">
      <text class="empty-icon">🔍</text>
      <text class="empty-text">没有找到相关商品</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-icon"></view>
      <text class="loading-text">搜索中...</text>
    </view>
  </view>
</view>
