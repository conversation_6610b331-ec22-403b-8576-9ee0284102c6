/**
 * 合伙人申请控制器
 */
const PartnerApplication = require('../models/PartnerApplication');
const User = require('../models/User');
const db = require('../config/db');

/**
 * 验证管理员权限
 */
async function verifyAdminPermission(userId) {
  try {
    const result = await db.query(
      'SELECT COUNT(*) as count FROM user_roles WHERE user_id = ? AND role_type = ?',
      [userId, 'admin']
    );
    return result[0].count > 0;
  } catch (error) {
    console.error('验证管理员权限失败:', error);
    return false;
  }
}

/**
 * 提交合伙人申请
 * POST /api/partner/apply
 */
exports.applyPartner = async (req, res) => {
  try {
    // 从请求中获取用户ID和申请信息
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const { name, phone, province, city, district } = req.body;

    // 验证必填字段
    if (!name || !phone || !province || !city || !district) {
      return res.status(400).json({ success: false, message: '请填写完整的申请信息' });
    }

    // 创建申请记录
    const result = await PartnerApplication.create({
      user_id: userId,
      name,
      phone,
      province,
      city,
      district
    });

    res.json({ success: true, id: result.id, message: '申请提交成功，请等待审核' });
  } catch (error) {
    console.error('提交合伙人申请失败:', error);
    res.status(500).json({ success: false, message: error.message });
  }
};

/**
 * 获取用户申请记录
 * GET /api/partner/applications/my
 */
exports.getMyApplications = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const applications = await PartnerApplication.getByUserId(userId);
    res.json({ success: true, data: applications });
  } catch (error) {
    console.error('获取用户申请记录失败:', error);
    res.status(500).json({ success: false, message: error.message });
  }
};

/**
 * 获取所有申请记录（管理员接口）
 * GET /api/partner/applications
 */
exports.getAllApplications = async (req, res) => {
  try {
    // 验证管理员权限
    if (!req.userData || !req.userData.userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const isAdmin = await verifyAdminPermission(req.userData.userId);
    if (!isAdmin) {
      return res.status(403).json({ success: false, message: '无权访问' });
    }

    const { status, limit = 20, offset = 0 } = req.query;
    console.log('查询参数:', { status, limit, offset });
    
    const result = await PartnerApplication.getAll({
      status: status && status !== '' ? status : null,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    console.log('查询结果:', { applications: result.applications.length, total: result.total });

    res.json({
      success: true,
      data: result.applications,
      total: result.total
    });
  } catch (error) {
    console.error('获取申请记录列表失败:', error);
    res.status(500).json({ success: false, message: error.message });
  }
};

/**
 * 获取申请详情（管理员接口）
 * GET /api/partner/applications/:id
 */
exports.getApplicationDetail = async (req, res) => {
  try {
    // 验证管理员权限
    if (!req.userData || !req.userData.userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const isAdmin = await verifyAdminPermission(req.userData.userId);
    if (!isAdmin) {
      return res.status(403).json({ success: false, message: '无权访问' });
    }

    const { id } = req.params;
    const application = await PartnerApplication.getById(parseInt(id));

    if (!application) {
      return res.status(404).json({ success: false, message: '申请记录不存在' });
    }

    res.json({ success: true, data: application });
  } catch (error) {
    console.error('获取申请详情失败:', error);
    res.status(500).json({ success: false, message: error.message });
  }
};

/**
 * 审核申请（管理员接口）
 * PUT /api/partner/applications/:id/review
 */
exports.reviewApplication = async (req, res) => {
  try {
    // 验证管理员权限
    if (!req.userData || !req.userData.userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const isAdmin = await verifyAdminPermission(req.userData.userId);
    if (!isAdmin) {
      return res.status(403).json({ success: false, message: '无权访问' });
    }

    const { id } = req.params;
    const { status, admin_remark } = req.body;

    // 验证状态值
    if (!status || !['approved', 'rejected'].includes(status)) {
      return res.status(400).json({ success: false, message: '无效的状态值' });
    }

    // 更新申请状态
    await PartnerApplication.updateStatus(parseInt(id), {
      status,
      admin_id: req.userData.userId,
      admin_remark
    });

    res.json({ success: true, message: '审核完成' });
  } catch (error) {
    console.error('审核申请失败:', error);
    res.status(500).json({ success: false, message: error.message });
  }
};