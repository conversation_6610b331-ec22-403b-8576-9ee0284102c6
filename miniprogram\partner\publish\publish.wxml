<!--partner/publish/publish.wxml-->
<view class="shelf-container">
  <!-- 标签页 -->
  <view class="tab-header">
    <view 
      wx:for="{{tabs}}" 
      wx:key="*this"
      class="tab-item {{activeTab === index ? 'active' : ''}}"
      bindtap="onTabChange"
      data-index="{{index}}"
    >
      {{item}}
    </view>
  </view>

  <!-- 采购卡片 -->
  <view class="purchase-card" wx:if="{{activeTab === 0}}">
    <!-- 门店选择框 -->
    <view class="store-selector">
      <view class="store-selector-row">
        <view class="selector-label">当前门店</view>
        <picker 
          class="store-picker" 
          mode="selector" 
          range="{{storeList}}" 
          range-key="name" 
          bindchange="onStoreChange" 
          wx:if="{{storeList.length > 0}}"
        >
          <view class="picker-content">
            <text class="store-name">{{selectedStore.name}}（{{selectedStore.store_no}}）</text>
            <view class="dropdown-arrow">▼</view>
          </view>
        </picker>
        <view class="picker-content" wx:else>
          <text class="store-name">暂无门店数据</text>
        </view>
      </view>
    </view>

    <!-- 采购车商品列表 -->
    <view class="purchase-list" wx:if="{{purchaseItems.length > 0}}">
      <view class="purchase-item" wx:for="{{purchaseItems}}" wx:key="id">
        <view class="select-box {{item.selected ? 'selected' : ''}}" bindtap="toggleSelectItem" data-index="{{index}}">
          <image src="{{item.selected ? '/images/icons2/勾选.png' : '/images/icons/checkbox.png'}}"></image>
        </view>

        <image class="product-image" src="{{item.image}}" mode="aspectFill" binderror="onImageError" data-index="{{index}}"></image>
        
        <view class="product-info">
          <view class="product-name">{{item.name}}</view>
          <view class="product-price">采购价: ¥{{item.purchasePrice}}</view>
        </view>

        <view class="right-controls">
          <!-- 数量控件 -->
          <view class="quantity-control">
            <view class="quantity-btn" bindtap="decreaseQuantity" data-index="{{index}}">-</view>
            <input 
              class="quantity-input" 
              type="number" 
              value="{{item.quantity}}" 
              bindblur="inputQuantity" 
              data-index="{{index}}" 
            />
            <view class="quantity-btn" bindtap="increaseQuantity" data-index="{{index}}">+</view>
          </view>
          
          <!-- 小计金额 -->
          <view class="subtotal-price">¥{{item.subtotal}}</view>
        </view>
      </view>
    </view>

    <!-- 空采购车 -->
    <view class="empty-purchase" wx:else>
      <image class="empty-icon" src="/images/icons2/店铺.png"></image>
      <view class="empty-text">采购车还是空的</view>
      <view class="add-product-btn" bindtap="goToProductSelect">添加商品</view>
    </view>
  </view>

  <!-- 上架卡片 -->
  <view class="shelf-card" wx:if="{{activeTab === 1}}">
    <view class="card-placeholder">
      <text>上架功能开发中...</text>
    </view>
  </view>

  <!-- 移库卡片 -->
  <view class="transfer-card" wx:if="{{activeTab === 2}}">
    <view class="card-placeholder">
      <text>移库功能开发中...</text>
    </view>
  </view>

  <!-- 采购结算栏 -->
  <view class="checkout-bar" wx:if="{{activeTab === 0 && purchaseItems.length > 0}}">
    <view class="select-all" bindtap="toggleSelectAll">
      <view class="select-box {{allSelected ? 'selected' : ''}}">
        <image src="{{allSelected ? '/images/icons2/勾选.png' : '/images/icons/checkbox.png'}}"></image>
      </view>
      <text>全选</text>
    </view>

    <view class="total-info">
      <view class="total-price">
        合计: <text class="price">¥{{totalPurchasePrice}}</text>
      </view>
      <view class="total-desc">共{{totalPurchaseCount}}件商品</view>
    </view>

    <view class="checkout-btn {{selectedCount > 0 ? 'active' : 'disabled'}}" bindtap="{{selectedCount > 0 ? 'purchaseCheckout' : ''}}">
      采购结算
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 合伙人底部导航栏 -->
<partner-tabbar selected="1" /> 