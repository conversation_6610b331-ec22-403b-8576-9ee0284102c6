-- 添加用户表的新字段
-- 推荐人ID
ALTER TABLE users ADD COLUMN IF NOT EXISTS referrerId VARCHAR(20) DEFAULT NULL COMMENT '推荐人ID';

-- 销售人ID
ALTER TABLE users ADD COLUMN IF NOT EXISTS salesmanId VARCHAR(20) DEFAULT NULL COMMENT '销售人ID';

-- 订阅门店
ALTER TABLE users ADD COLUMN IF NOT EXISTS subscribedStore VARCHAR(50) DEFAULT NULL COMMENT '订阅门店编号';

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_users_referrerId ON users(referrerId);
CREATE INDEX IF NOT EXISTS idx_users_salesmanId ON users(salesmanId);
CREATE INDEX IF NOT EXISTS idx_users_subscribedStore ON users(subscribedStore); 