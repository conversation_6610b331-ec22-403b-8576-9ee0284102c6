/* pages/settings/cropper/cropper.wxss */
.cropper-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 40rpx;
  box-sizing: border-box;
}

.cropper-box {
  width: 95vw;
  height: 95vw;
  background: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: relative;
}

.cropper-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.cropper-btn {
  margin-top: 48rpx;
  width: 60vw;
  background: #1E6A9E;
  color: #fff;
  border-radius: 24rpx;
  font-size: 16px;
  font-weight: bold;
  padding: 16rpx 0;
}