-- 智能订单号生成脚本
-- 根据不同订单类型生成不同格式的订单号
-- 请在数据库管理工具中执行此脚本

-- 1. 检查orders表结构
DESCRIBE orders;

-- 2. 检查order_no字段是否存在
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'orders'
    AND COLUMN_NAME = 'order_no';

-- 3. 如果order_no字段不存在，则添加（如果已存在会报错，可忽略）
-- ALTER TABLE orders ADD COLUMN order_no VARCHAR(50) NULL COMMENT '订单号' AFTER id;

-- 4. 检查索引是否存在
SELECT
    INDEX_NAME,
    COLUMN_NAME
FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'orders'
    AND INDEX_NAME = 'idx_order_no';

-- 5. 如果索引不存在，则添加（如果已存在会报错，可忽略）
-- CREATE INDEX idx_order_no ON orders (order_no);

-- 6. 查看现有订单类型分布
SELECT
    type,
    COUNT(*) as count,
    COUNT(order_no) as has_order_no,
    COUNT(*) - COUNT(order_no) as missing_order_no
FROM orders
GROUP BY type;

-- 7. 为不同类型的订单生成对应的订单号
-- 顾客订单（购物车订单和普通订单）：XS + 年月日时分(12位) + 序号(4位)
UPDATE orders
SET order_no = CONCAT(
    'XS',
    DATE_FORMAT(FROM_UNIXTIME(created_at/1000), '%Y%m%d%H%i'),
    LPAD(id % 10000, 4, '0')
)
WHERE (type = 'cart' OR type = 'normal' OR type IS NULL)
    AND (order_no IS NULL OR order_no = '');

-- 门店采购订单：CG + 年月日时分(12位) + 序号(4位)
UPDATE orders
SET order_no = CONCAT(
    'CG',
    DATE_FORMAT(FROM_UNIXTIME(created_at/1000), '%Y%m%d%H%i'),
    LPAD(id % 10000, 4, '0')
)
WHERE type = 'purchase'
    AND (order_no IS NULL OR order_no = '');

-- 门店移库订单：YK + 年月日时分(12位) + 序号(4位)
UPDATE orders
SET order_no = CONCAT(
    'YK',
    DATE_FORMAT(FROM_UNIXTIME(created_at/1000), '%Y%m%d%H%i'),
    LPAD(id % 10000, 4, '0')
)
WHERE type = 'transfer'
    AND (order_no IS NULL OR order_no = '');

-- 8. 验证结果
SELECT
    type,
    COUNT(*) as total_orders,
    COUNT(order_no) as orders_with_no,
    COUNT(*) - COUNT(order_no) as orders_without_no
FROM orders
GROUP BY type;

-- 9. 显示各类型订单号示例
SELECT
    type,
    order_no,
    status,
    created_at
FROM orders
WHERE order_no IS NOT NULL
ORDER BY type, created_at DESC;

-- 10. 总体统计
SELECT
    COUNT(*) as total_orders,
    COUNT(order_no) as orders_with_no,
    COUNT(*) - COUNT(order_no) as orders_without_no
FROM orders;
