<view class="container">
  <view class="title">向好友分享陌派商城</view>
  <view class="card-container">
    <view class="main-image">
      <image src="/images/share/share.jpg" mode="aspectFill" class="preset-image">预定图片</image>
    </view>
    <view class="card-row">
      <view class="card-left">
        <view style="display:flex;align-items:center;">
          <image class="avatar-circle" src="{{avatarUrl}}"></image>
          <view class="nickname-block">
            <text class="nickname">{{nickname}}</text>
            <text class="recommend recommend-gap">向您推荐</text>
          </view>
        </view>
        <view class="desc lishu-font">陌派超零售，财富新宇宙</view>
      </view>
      <view class="qrcode-container">
        <image wx:if="{{qrcodeUrl && qrcodeUrl !== '' && qrcodeUrl !== null}}" class="qrcode" src="{{qrcodeUrl}}" mode="aspectFit" bindload="onQrcodeImageLoad" binderror="onQrcodeImageError"></image>
        <view wx:else class="qrcode-loading" bindtap="retryGenerateQrcode">
          <text wx:if="{{qrcodeUrl === null}}" class="retry-text">生成失败\n点击重试</text>
          <text wx:else class="loading-text">二维码生成中...</text>
        </view>
      </view>
    </view>
  </view>
  <view class="actions">
    <button class="save-btn" bindtap="onSaveImage">保存图片</button>
    <button class="share-btn" bindtap="onShareImage">直接分享</button>
  </view>
  <canvas type="2d" id="shareCanvas" style="width:600px;height:800px;position:fixed;left:-2000px;top:0;"></canvas>
</view>