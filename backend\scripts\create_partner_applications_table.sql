-- 创建合伙人申请表
CREATE TABLE IF NOT EXISTS `partner_applications` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `user_id` VARCHAR(20) NOT NULL COMMENT '申请用户ID',
  `name` VARCHAR(50) NOT NULL COMMENT '申请人姓名',
  `phone` VARCHAR(20) NOT NULL COMMENT '联系电话',
  `province` VARCHAR(50) NOT NULL COMMENT '意向省份',
  `city` VARCHAR(50) NOT NULL COMMENT '意向城市',
  `district` VARCHAR(50) NOT NULL COMMENT '意向区县',
  `status` ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending' COMMENT '申请状态：pending=待审核，approved=已通过，rejected=已拒绝',
  `admin_id` VARCHAR(20) NULL COMMENT '处理管理员ID',
  `admin_remark` TEXT NULL COMMENT '管理员备注',
  `created_at` BIGINT NOT NULL COMMENT '创建时间',
  `updated_at` BIGINT NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合伙人申请表';