/* partner/store-partners/store-partners.wxss */
.store-partners-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

/* 门店选择区域 */
.store-select-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.store-select-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
}

.store-select-container {
  position: relative;
  flex: 1;
}

/* 门店选择器样式 */
.store-picker {
  width: 100%;
}

.store-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  background: #fff;
  box-sizing: border-box;
}

.store-name {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.dropdown-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

/* 用户状况区域 */
.user-status-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20rpx;
}

.status-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.status-label {
  font-size: 24rpx;
  color: #20B2AA;
  margin-bottom: 10rpx;
}

.status-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 合伙人列表区域 */
.partners-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.partner-count {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 合伙人列表 */
.partner-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.partner-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  border: 1rpx solid #e9ecef;
}

/* 合伙人头部信息 */
.partner-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.partner-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 2rpx solid #e0e0e0;
}

.partner-info {
  flex: 1;
}

.partner-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.partner-id {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.partner-join-date {
  font-size: 24rpx;
  color: #999;
}

.partner-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.message-btn {
  background: #FF4D4F;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  line-height: 1.2;
  min-height: auto;
}

.message-btn::after {
  border: none;
}

/* 合伙人详细信息 */
.partner-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  white-space: nowrap;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部间距 */
.bottom-spacing {
  height: 40rpx;
}