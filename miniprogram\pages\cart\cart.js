// pages/cart/cart.js
const { cartApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');

Page({
  data: {
    cartItems: [],
    isLogin: false,
    loading: true,
    isEdit: false,
    allSelected: false,
    totalPrice: 0,
    totalCount: 0,
    selectedCount: 0,
    // 添加请求状态管理
    isRequesting: false,
    lastRequestTime: 0
  },

  onLoad: function (options) {
    console.log('购物车页面加载');
    
    // 直接检查登录状态，不强制跳转登录页面
    this.checkLoginStatus();
  },

  onShow: function () {
    // 每次显示页面时都重新检查登录状态
    this.checkLoginStatus();
  },

  // 检查登录状态（不弹窗版本）
  checkLoginStatus: function() {
    console.log('购物车页面检查登录状态');
    const app = getApp();
    
    // 设置加载状态
    this.setData({ loading: true });

    // 直接检查全局登录状态，不使用checkNeedLogin避免弹窗
    if (app.globalData.isLogin && app.globalData.userInfo) {
      console.log('购物车页面检测到已登录状态');
      
      // 检查token是否过期（简单检查：如果token存在超过24小时，则可能过期）
      const now = Date.now();
      const tokenTimestamp = wx.getStorageSync('tokenTimestamp') || 0;
      const tokenAge = now - tokenTimestamp;
      const ONE_DAY = 24 * 60 * 60 * 1000; // 24小时，单位毫秒
      
      if (tokenAge > ONE_DAY) {
        console.warn('登录状态可能已过期，将进行服务器验证');
        // 使用登录状态管理器验证登录状态
        loginStateManager.validateLoginState().then(result => {
          if (result.isValid) {
            this.setData({
              isLogin: true,
              loading: false
            });
            this.getCartItems();
          } else {
            // 登录状态无效，清除登录状态
            loginStateManager.clearLoginState();
            this.setData({
              isLogin: false,
              loading: false,
              cartItems: [] // 清空购物车数据
            });
          }
        });
        return;
      }
      
      this.setData({
        isLogin: true,
        loading: false
      });
      this.getCartItems();
      return;
    }

    // 检查本地存储的登录状态
    const userInfo = wx.getStorageSync('userInfo');

    if (loginStateManager.getLoginState() && loginStateManager.getLoginState().isLogin && userInfo) {
      console.log('购物车页面从本地存储检测到登录状态');
      // 更新全局状态
      app.globalData.userInfo = userInfo;
      app.globalData.isLogin = true;
      
      this.setData({
        isLogin: true,
        loading: false
      });
      this.getCartItems();
      return;
    }

    // 异步验证登录状态，但不弹窗
    loginStateManager.validateLoginState()
      .then(result => {
        console.log('购物车页面登录状态验证结果:', result);
        
        if (result.isValid) {
          // 登录状态有效
          app.globalData.userInfo = result.userInfo;
          app.globalData.isLogin = true;
          
          this.setData({
            isLogin: true,
            loading: false
          });
          this.getCartItems();
        } else {
          // 登录状态无效，显示未登录状态（不弹窗）
          console.log('购物车页面检测到未登录状态，显示未登录购物车');
          this.setData({
            isLogin: false,
            loading: false,
            cartItems: [] // 清空购物车数据
          });
        }
      })
      .catch(err => {
        console.error('购物车页面验证登录状态出错:', err);
        // 网络错误时，显示未登录状态
        this.setData({
          isLogin: false,
          loading: false,
          cartItems: []
        });
      });
  },

  // 获取购物车数据
  getCartItems: function() {
    console.log('开始获取购物车数据');
    
    // 防抖机制：如果正在请求中，则跳过
    if (this.data.isRequesting) {
      console.log('购物车数据请求中，跳过重复请求');
      return;
    }
    
    // 防抖机制：如果距离上次请求时间小于2秒，则跳过
    const now = Date.now();
    if (now - this.data.lastRequestTime < 2000) {
      console.log('购物车数据请求过于频繁，跳过');
      return;
    }
    
    this.setData({ 
      loading: true,
      isRequesting: true,
      lastRequestTime: now
    });
    
    // 直接获取真实数据
    cartApi.getCartItems()
      .then(res => {
        console.log('购物车API响应:', res);
        
        if (res && res.success && res.data) {
          const cartItems = res.data;
          console.log('购物车原始数据:', cartItems);
          
          // 设置每个商品的选中状态和小计金额，并处理图片路径
          cartItems.forEach(item => {
            item.selected = false;
            // 计算小计金额并保留两位小数
            const price = parseFloat(item.price) || 0;
            const quantity = parseInt(item.quantity) || 1;
            item.subtotal = (price * quantity).toFixed(2);
            
            // 简化图片处理逻辑 - 如果没有图片或图片路径有问题，直接使用默认图片
            if (!item.image) {
              item.image = '/images/mo/mogoods.jpg';
            }
          });
          
          this.setData({
            cartItems: cartItems,
            loading: false,
            isRequesting: false,
            allSelected: false
          });
          this.calculateTotal();
          console.log('购物车数据已加载，商品数量:', cartItems.length);
        } else {
          // 如果没有数据，设置为空数组
          console.log('购物车为空或API返回失败');
          this.setData({
            cartItems: [],
            loading: false,
            isRequesting: false,
            allSelected: false
          });
          this.calculateTotal();
        }
      })
      .catch(err => {
        console.error('获取购物车数据失败', err);
        // 设置为空数组，显示错误提示
        this.setData({
          cartItems: [],
          loading: false,
          isRequesting: false,
          allSelected: false
        });
        this.calculateTotal();
        
        // 显示错误提示
        wx.showToast({
          title: '获取购物车数据失败',
          icon: 'none',
          duration: 2000
        });
      });
  },

  // 切换编辑模式
  toggleEditMode: function() {
    this.setData({
      isEdit: !this.data.isEdit
    });
  },

  // 选择/取消选择单个商品
  toggleSelectItem: function(e) {
    const index = e.currentTarget.dataset.index;
    const cartItems = this.data.cartItems;

    cartItems[index].selected = !cartItems[index].selected;

    // 检查是否全选
    const allSelected = cartItems.every(item => item.selected);

    this.setData({
      cartItems: cartItems,
      allSelected: allSelected
    });

    this.calculateTotal();
  },

  // 全选/取消全选
  toggleSelectAll: function() {
    const allSelected = !this.data.allSelected;
    const cartItems = this.data.cartItems;

    cartItems.forEach(item => {
      item.selected = allSelected;
    });

    this.setData({
      cartItems: cartItems,
      allSelected: allSelected
    });

    this.calculateTotal();
  },

  // 计算总价和数量
  calculateTotal: function() {
    const cartItems = this.data.cartItems;
    let totalPrice = 0;
    let totalCount = 0;
    let selectedCount = 0;

    cartItems.forEach(item => {
      totalCount += item.quantity;

      if (item.selected) {
        totalPrice += item.price * item.quantity;
        selectedCount += item.quantity;
      }
    });

    this.setData({
      totalPrice: totalPrice.toFixed(2),
      totalCount: totalCount,
      selectedCount: selectedCount
    });

    // 注意：购物车数量变化事件已在各个修改方法中触发
  },

  // 减少商品数量
  decreaseQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const cartItems = this.data.cartItems;
    const item = cartItems[index];
    
    console.log('减少商品数量 - 购物车项:', item);
    
    // 如果数量为1，则删除商品
    if (item.quantity <= 1) {
      wx.showModal({
        title: '提示',
        content: '确定要从购物车中移除该商品吗？',
        success: (res) => {
          if (res.confirm) {
            // 使用购物车项ID删除商品
            cartApi.removeCartItem(item.id)
              .then(res => {
                if (res.success) {
                  wx.showToast({ title: '商品已移除', icon: 'success' });
                  // 重新获取购物车数据
                  this.getCartItems();
                } else {
                  wx.showToast({ title: res.message || '删除失败', icon: 'none' });
                }
              })
              .catch(err => {
                console.error('删除商品失败', err);
                wx.showToast({ title: '删除失败', icon: 'none' });
              });
          }
        }
      });
      return;
    }
    
    // 使用购物车项ID更新商品数量
    cartApi.updateCartItem(item.id, item.quantity - 1)
      .then(res => {
        if (res.success) {
          cartItems[index].quantity -= 1;
          // 更新小计金额
          cartItems[index].subtotal = (cartItems[index].price * cartItems[index].quantity).toFixed(2);
          this.setData({ cartItems });
          this.calculateTotal();
        } else {
          // 检查是否为库存不足错误，显示友好提示
          let errorMessage = res.message || '操作失败';
          if (errorMessage.includes('库存不足') || errorMessage.includes('商品库存不足')) {
            errorMessage = '商品库存不足，请联系客服，或购买其他商品';
          }
          
          wx.showToast({ title: errorMessage, icon: 'none' });
        }
      })
      .catch(err => {
        console.error('减少商品数量失败', err);
        wx.showToast({ title: '操作失败', icon: 'none' });
      });
  },

  // 增加商品数量
  increaseQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const cartItems = this.data.cartItems;
    const item = cartItems[index];
    
    console.log('增加商品数量 - 购物车项:', item);
    
    // 使用购物车项ID更新商品数量
    cartApi.updateCartItem(item.id, item.quantity + 1)
      .then(res => {
        if (res.success) {
          cartItems[index].quantity += 1;
          // 更新小计金额
          cartItems[index].subtotal = (cartItems[index].price * cartItems[index].quantity).toFixed(2);
          this.setData({ cartItems });
          this.calculateTotal();
        } else {
          // 检查是否为库存不足错误，显示友好提示
          let errorMessage = res.message || '操作失败';
          if (errorMessage.includes('库存不足') || errorMessage.includes('商品库存不足')) {
            errorMessage = '商品库存不足，请联系客服，或购买其他商品';
          }
          
          wx.showToast({ title: errorMessage, icon: 'none' });
        }
      })
      .catch(err => {
        console.error('增加商品数量失败', err);
        wx.showToast({ title: '操作失败', icon: 'none' });
      });
  },

  // 输入商品数量
  inputQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const value = e.detail.value;
    const quantity = parseInt(value);
    if (isNaN(quantity) || quantity < 1) {
      return 1;
    }
    const cartItems = this.data.cartItems;
    const item = cartItems[index];
    
    console.log('输入商品数量 - 购物车项:', item, '新数量:', quantity);
    
    // 使用购物车项ID更新商品数量
    cartApi.updateCartItem(item.id, quantity)
      .then(res => {
        if (res.success) {
          cartItems[index].quantity = quantity;
          // 更新小计金额
          cartItems[index].subtotal = (cartItems[index].price * cartItems[index].quantity).toFixed(2);
          this.setData({ cartItems });
          this.calculateTotal();
        } else {
          // 检查是否为库存不足错误，显示友好提示
          let errorMessage = res.message || '操作失败';
          if (errorMessage.includes('库存不足') || errorMessage.includes('商品库存不足')) {
            errorMessage = '商品库存不足，请联系客服，或购买其他商品';
          }
          
          wx.showToast({ title: errorMessage, icon: 'none' });
        }
      })
      .catch(err => {
        console.error('更新商品数量失败', err);
        wx.showToast({ title: '操作失败', icon: 'none' });
      });
    return quantity;
  },



  // 批量删除商品
  deleteSelected: function() {
    const cartItems = this.data.cartItems;
    const selectedItems = cartItems.filter(item => item.selected);
    
    console.log('批量删除 - 所有购物车项:', cartItems);
    console.log('批量删除 - 选中的购物车项:', selectedItems);
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要删除的商品',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '提示',
      content: `确定要删除选中的${selectedItems.length}件商品吗？`,
      success: (res) => {
        if (res.confirm) {
          // 获取选中商品的购物车项ID（不是商品ID）
          const cartItemIds = selectedItems.map(item => item.id);
          console.log('批量删除 - 购物车项ID列表:', cartItemIds);
          
          cartApi.batchRemoveCartItems(cartItemIds)
            .then(res => {
              console.log('批量删除API响应:', res);
              if (res.success) {
                wx.showToast({ title: '删除成功', icon: 'success' });
                this.getCartItems();
              } else {
                wx.showToast({ title: res.message || '删除失败', icon: 'none' });
              }
            })
            .catch(err => {
              console.error('批量删除商品失败', err);
              wx.showToast({ title: '删除失败', icon: 'none' });
            });
        }
      }
    });
  },

  // 点击商品
  onItemTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product/detail?id=${id}`
    });
  },

  // 结算
  checkout: function() {
    if (this.data.selectedCount === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      });
      return;
    }

    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.continueCheckout();
      }
    })) {
      return;
    }

    this.continueCheckout();
  },

  // 继续结算流程
  continueCheckout: function() {
    const cartItems = this.data.cartItems;
    console.log('购物车所有商品:', cartItems);
    
    const selectedItems = cartItems.filter(item => item.selected);
    console.log('选中的购物车项:', selectedItems);
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请先选择要结算的商品',
        icon: 'none'
      });
      return;
    }
    
    // 直接传递选中的商品数据，避免重复请求
    const selectedItemsData = selectedItems.map(item => ({
      id: item.id,
      productId: item.productId,
      name: item.name || '商品名称', // 确保有默认值
      price: parseFloat(item.price) || 0, // 确保是数字类型
      image: item.image || '/images/mo/mogoods.jpg', // 确保有默认图片
      quantity: parseInt(item.quantity) || 1, // 确保是整数类型
      selected: true
    })).filter(item => {
      // 过滤掉无效的商品数据
      if (!item.id || !item.productId || item.price <= 0 || item.quantity <= 0) {
        console.warn('过滤无效商品数据:', item);
        return false;
      }
      return true;
    });

    console.log('准备存储到本地存储的数据:', selectedItemsData);
    
    // 验证处理后的数据
    if (!selectedItemsData || selectedItemsData.length === 0) {
      wx.showToast({
        title: '没有有效的商品数据',
        icon: 'none'
      });
      return;
    }

    // 将数据存储到本地存储，供确认订单页面使用
    try {
      wx.setStorageSync('selectedCartItems', selectedItemsData);
      
      // 验证数据是否成功存储
      const storedData = wx.getStorageSync('selectedCartItems');
      console.log('本地存储已设置:', storedData);
      
      if (!storedData || storedData.length === 0) {
        wx.showToast({
          title: '数据存储失败',
          icon: 'none'
        });
        return;
      }
    } catch (error) {
      console.error('存储数据到本地失败:', error);
      wx.showToast({
        title: '数据存储异常',
        icon: 'none'
      });
      return;
    }

    // 延迟跳转，确保数据已完全写入
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/order/create'
      });
    }, 100);
  },

  // 跳转到登录页
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/auth/auth'
    });
  },

  // 跳转到收藏夹页面
  goToFavorites: function() {
    wx.navigateTo({
      url: '/pages/favorites/favorites'
    });
  },

  // 跳转到商城
  goToShop: function() {
    // 如果是从商城页面跳转过来的，直接返回
    const pages = getCurrentPages();
    if (pages.length > 1 && pages[pages.length - 2].route === 'pages/category/category') {
      wx.navigateBack();
    } else {
      // 否则跳转到商城页面
      wx.switchTab({
        url: '/pages/category/category'
      });
    }
  },

  // 显示选择提示
  showSelectTip: function() {
    wx.showToast({
      title: '请先选择商品',
      icon: 'none',
      duration: 1500
    });
  },

  // 保存购物车数据并触发更新事件
  saveCartAndNotify: function(cartItems) {
    // 保存到本地存储
    wx.setStorageSync('cartItems', cartItems);

    // 触发购物车数量变化事件
    wx.setStorageSync('cartItemsUpdated', new Date().getTime());
  },

  // 图片加载失败处理
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const cartItems = [...this.data.cartItems];
    
    if (cartItems[index]) {
      // 创建新对象，避免直接修改原对象
      cartItems[index] = {...cartItems[index], image: '/images/mo/mogoods.jpg'};
      this.setData({
        cartItems: cartItems
      });
    }
  }
});
