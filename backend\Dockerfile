FROM node:18-alpine

# 设置Alpine镜像源为国内源，加速包安装
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装必要的编译工具（只安装必需的）
RUN apk add --no-cache python3 make g++

# 创建工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 设置npm镜像源为淘宝镜像，加速安装
RUN npm config set registry https://registry.npmmirror.com

# 安装依赖（使用--production减少安装时间）
RUN npm install --production --no-audit --no-fund

# 复制所有文件
COPY . .

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3001
ENV USE_MYSQL=true

# 暴露端口
EXPOSE 3001

# 启动命令
CMD ["node", "server.js"]
