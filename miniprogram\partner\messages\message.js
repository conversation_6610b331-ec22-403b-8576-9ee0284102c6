// pages/message/message.js
const app = getApp();
const request = require('../../utils/request');
const api = require('../../utils/api');

Page({
  data: {
    currentTab: 0,
    tabs: ['个人私信', '群消息', '系统通知'], // 移除了'贴子互动'选项
    messages: [],
    filteredMessages: [],
    searchKeyword: '',
    loading: true,
    isLogin: false,
    myGroups: [], // 我的群聊列表
    publicGroups: [], // 公开群聊列表
    groupLoading: false, // 群聊加载状态
    groupsLoaded: false, // 群聊是否已加载过的标志
    groupTab: 0, // 新增：群聊卡片切换 0-我的 1-全部
    startX: 0 // 触摸开始位置
  },

  onLoad: function (options) {
    console.log('消息页面加载');
    this.checkLoginStatus();
    this.getMessages();
    
    // 如果用户已登录，预加载群聊数据
    if (this.data.isLogin) {
      console.log('用户已登录，预加载群聊数据');
      // 延迟加载群聊数据，避免与消息列表加载冲突
      setTimeout(() => {
        this.loadGroups();
        this.setData({ groupsLoaded: true });
      }, 1000);
    }
  },

  onShow: function () {
    console.log('消息页面显示');
    // 获取最新的登录状态
    const app = getApp();
    const globalData = app.globalData || {};
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    console.log('当前登录状态:', globalData.isLogin);
    console.log('当前token:', token ? '存在' : '不存在');
    console.log('当前用户信息:', userInfo ? '存在' : '不存在');
    // 只刷新登录状态和数据，不强制登录
    const wasLogin = this.data.isLogin;
    const isLogin = this.checkLoginStatus();
    // 如果已登录，获取消息
    if (this.data.isLogin) {
      this.getMessages();
    } else {
      console.log('用户未登录，不获取消息');
      this.setData({
        messages: [],
        filteredMessages: [],
        loading: false
      });
    }
    
    // 如果当前在群聊标签，且满足以下条件之一：
    // 1. 用户已登录但群聊数据尚未加载
    // 2. 登录状态发生变化（从未登录到已登录）
    if (this.data.currentTab === 1 && isLogin && (!this.data.groupsLoaded || !wasLogin)) {
      console.log('页面显示，需要加载群聊数据');
      this.setData({ groupLoading: true });
      
      setTimeout(() => {
        this.loadGroups();
        this.setData({ groupsLoaded: true });
      }, 300);
    }
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const app = getApp();
    const globalData = app.globalData || {};
    const loginStateManager = require('../../utils/login-state-manager');
    // 从全局状态获取登录状态
    let isLogin = globalData.isLogin;
    const oldLoginStatus = this.data.isLogin;
    
    // 如果全局状态不是登录状态，尝试从本地存储获取
    if (!isLogin) {
      const loginState = loginStateManager.getLoginState();
      const token = wx.getStorageSync('token');
      const userInfo = wx.getStorageSync('userInfo');
      console.log('本地登录状态:', loginState ? '存在' : '不存在');
      console.log('本地token:', token ? '存在' : '不存在');
      console.log('本地用户信息:', userInfo ? '存在' : '不存在');
      // 如果本地有登录状态，更新全局状态
      if (loginState && loginState.isLogin && token && userInfo) {
        console.log('从本地存储恢复登录状态');
        isLogin = true;
        globalData.isLogin = true;
        globalData.userInfo = userInfo;
      }
    }
    console.log('最终登录状态:', isLogin);
    this.setData({ isLogin });
    
    // 如果登录状态发生变化（从未登录到已登录），且当前在群聊标签，则加载群聊数据
    if (isLogin && !oldLoginStatus && this.data.currentTab === 1) {
      console.log('登录状态变化，加载群聊数据');
      this.setData({ groupLoading: true });
      
      setTimeout(() => {
        this.loadGroups();
        this.setData({ groupsLoaded: true });
      }, 300);
    }
    
    return isLogin;
  },

  // 判断是否是图片URL
  isImageUrl: function(content) {
    console.log('检查内容是否是图片URL:', content);
    if (!content || typeof content !== 'string') {
      console.log('不是有效的字符串内容');
      return false;
    }

    // 检查是否是图片扩展名
    if (content.match(/\.(jpg|jpeg|png|gif|bmp|webp)(\?.*)?$/i)) {
      console.log('匹配到图片扩展名');
      return true;
    }

    // 检查是否包含云托管存储路径
    if (content.includes('/storage/')) {
      console.log('匹配到云托管存储路径');
      return true;
    }

    console.log('不是图片URL');
    return false;
  },

  // 获取消息列表
  getMessages: function() {
    this.setData({ loading: true });
    if (!this.data.isLogin) {
      this.setData({ messages: [], filteredMessages: [], loading: false });
      return;
    }
    // 已登录才请求用户信息和消息
    wx.showLoading({ title: '加载消息中...', mask: true });
    request({
      url: '/api/message/recent',
      method: 'GET'
    }).then(res => {
      wx.hideLoading();
      let messages = [];
      // 兼容data为数组或对象（含list）
      if (res.success && Array.isArray(res.data)) {
        messages = res.data;
      } else if (res.success && res.data && Array.isArray(res.data.list)) {
        messages = res.data.list;
      } else {
        messages = [];
      }
      // 合并同一聊天对象的消息，只保留最新一条，并统计未读数
      const chatMap = {};
      messages.forEach(msg => {
        const targetUserId = msg.targetUserId || '';
        if (!targetUserId) return;
        // 统计未读数
        const isUnread = msg.isRead !== 1 && msg.senderId !== (app.globalData.userInfo ? app.globalData.userInfo.id : '');
        if (!chatMap[targetUserId]) {
          chatMap[targetUserId] = {
            ...msg,
            unreadCount: isUnread ? 1 : 0
          };
        } else {
          // 保留最新一条
          if (msg.createTime > chatMap[targetUserId].createTime) {
            chatMap[targetUserId] = {
              ...msg,
              unreadCount: chatMap[targetUserId].unreadCount + (isUnread ? 1 : 0)
            };
          } else {
            // 只累计未读数
            chatMap[targetUserId].unreadCount += isUnread ? 1 : 0;
          }
        }
      });

      // 处理消息内容
      messages = Object.values(chatMap).map(msg => {
        console.log('处理消息:', msg);
        const targetNickname = msg.targetNickname || '未知用户';
        const targetAvatar = msg.targetAvatar || '/images/icons2/男头像.png';
        const targetUserId = msg.targetUserId || '';
        const currentUserId = app.globalData.userInfo ? app.globalData.userInfo.id : '';
        const isFromMe = msg.senderId === currentUserId;
        const isImage = this.isImageUrl(msg.content);

        console.log('消息内容:', msg.content);
        console.log('是否是图片:', isImage);

        const processedMsg = {
          _id: msg.id,
          type: 'chat',
          targetInfo: {
            userId: targetUserId,
            nickName: targetNickname,
            avatarUrl: targetAvatar
          },
          isFromMe: isFromMe,
          content: msg.content, // 保持原始内容
          contentType: isImage ? 'image' : 'text', // 添加内容类型
          isRead: msg.isRead === 1,
          createTime: new Date(msg.createTime).toLocaleString(),
          unreadCount: msg.unreadCount || 0
        };

        console.log('处理后的消息:', processedMsg);
        return processedMsg;
      });

      // 按时间倒序排列
      messages.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

      // 初始化所有消息的滑动状态为0
      messages = messages.map(msg => ({
        ...msg,
        slideOffset: 0
      }));

      this.setData({
        messages,
        filteredMessages: messages,
        loading: false
      });
    }).catch(err => {
      wx.hideLoading();
      this.setData({ loading: false });
      wx.showToast({
        title: '获取消息失败: ' + (err.errMsg || '网络错误'),
        icon: 'none',
        duration: 3000
      });
    });
  },

  // 切换标签
  switchTab: function(e) {
    const index = e.currentTarget.dataset.index;
    const currentIndex = this.data.currentTab;
    
    // 如果点击的是当前标签，不做任何操作
    if (index === currentIndex) return;
    
    this.setData({
      currentTab: index
    });
    // 重置所有消息的滑动状态
    this.resetAllSlideOffsets();
    this.filterMessages();
    
    // 只有切换到群消息卡片时才加载群聊数据
    if (index === 1) { // 更新索引从2到1
      // 显示加载状态
      this.setData({ groupLoading: true });
      
      // 如果群聊数据尚未加载过
      if (!this.data.groupsLoaded) {
        // 添加小延迟，让加载动画效果更明显
        setTimeout(() => {
          this.loadGroups();
          this.setData({ groupsLoaded: true });
        }, 300);
      } else if ((this.data.groupTab === 0 && this.data.myGroups.length > 0) || 
                (this.data.groupTab === 1 && this.data.publicGroups.length > 0)) {
        // 如果数据已经加载过，直接隐藏加载状态
        setTimeout(() => {
          this.setData({ groupLoading: false });
        }, 300);
      } else {
        // 数据为空，重新加载
        setTimeout(() => {
          this.loadGroups();
        }, 300);
      }
    }
  },
  
  // 重置所有消息的滑动状态
  resetAllSlideOffsets: function() {
    const { filteredMessages } = this.data;
    const updatedMessages = filteredMessages.map(msg => ({
      ...msg,
      slideOffset: 0
    }));
    
    this.setData({
      filteredMessages: updatedMessages
    });
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    // 重置所有消息的滑动状态
    this.resetAllSlideOffsets();
    this.filterMessages();
  },

  // 搜索确认
  onSearch: function() {
    // 重置所有消息的滑动状态
    this.resetAllSlideOffsets();
    this.filterMessages();
  },

  // 处理消息内容，将图片URL替换为[图片]
  processMessageContent: function(message) {
    if (!message || !message.content) return message;
    // 如果是图片URL，则替换为[图片]
    if ((message.content.startsWith('http') && (message.content.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i) || message.content.includes('cloud.weixin.qq.com')))) {
      return {
        ...message,
        content: '[图片]',
        originalContent: message.content // 保留原始内容，以便搜索时使用
      };
    }
    return message;
  },

  // 过滤消息
  filterMessages: function() {
    const { messages, currentTab, searchKeyword, filteredMessages } = this.data;
    // 处理所有消息的内容
    const processedMessages = messages.map(msg => this.processMessageContent(msg));
    let filtered = processedMessages;

    // 根据当前标签过滤
    if (currentTab === 0) {
      filtered = processedMessages.filter(msg => msg.type === 'chat');
    }

    // 根据搜索关键词过滤
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(msg => {
        // 使用原始内容进行搜索
        const searchContent = msg.originalContent || msg.content || '';
        if (msg.type === 'chat') {
          const senderNick = (msg.senderInfo && (msg.senderInfo.nickName || msg.senderInfo.nickname)) || '';
          return senderNick.toLowerCase().includes(keyword) ||
                 searchContent.toLowerCase().includes(keyword);
        }
        return false;
      });
    }

    // 保留现有消息的滑动状态
    const slideOffsets = {};
    filteredMessages.forEach(msg => {
      if (msg._id && msg.slideOffset) {
        slideOffsets[msg._id] = msg.slideOffset;
      }
    });

    // 将滑动状态应用到新过滤的消息
    filtered = filtered.map(msg => {
      if (msg._id && slideOffsets[msg._id]) {
        return { ...msg, slideOffset: slideOffsets[msg._id] };
      }
      return msg;
    });

    this.setData({
      filteredMessages: filtered
    });
  },

  // 点击消息项
  onMessageTap: function(e) {
    const app = getApp();
    const index = e.currentTarget.dataset.index;
    const message = this.data.filteredMessages[index];
    const { currentTab } = this.data;

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.navigateToMessageDetail(message, currentTab);
      }
    })) {
      return;
    }

    // 标记消息为已读
    this.markAsRead(message._id);

    // 导航到消息详情
    this.navigateToMessageDetail(message, currentTab);
  },

  // 导航到消息详情
  navigateToMessageDetail: function(message, currentTab) {
    console.log('导航到消息详情:', message, '当前标签:', currentTab);

    if (currentTab === 0) {
      // 个人私信，跳转到聊天页面
      if (!message.targetInfo || !message.targetInfo.userId) {
        console.error('消息缺少目标用户ID:', message);
        wx.showToast({
          title: '无法打开聊天，缺少用户信息',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      console.log('跳转到聊天页面，目标用户ID:', message.targetInfo.userId);
      wx.navigateTo({
        url: `/partner/messages/chat?targetId=${message.targetInfo.userId}`
      });
    }
  },

  // 标记消息为已读
  markAsRead: function(messageId) {
    const messages = this.data.messages.map(msg => {
      if (msg._id === messageId) {
        msg.isRead = true;
        msg.unreadCount = 0;
      }
      return msg;
    });

    this.setData({
      messages,
      filteredMessages: this.data.filteredMessages.map(msg => {
        if (msg._id === messageId) {
          msg.isRead = true;
          msg.unreadCount = 0;
        }
        return msg;
      })
    });
  },

  // 长按消息
  onMessageLongPress: function(e) {
    const index = e.currentTarget.dataset.index;
    const message = this.data.filteredMessages[index];

    wx.showActionSheet({
      itemList: ['标记为已读', '删除消息'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.markAsRead(message._id);
        } else if (res.tapIndex === 1) {
          // 创建一个模拟事件对象，以便复用deleteMessage函数
          const event = {
            currentTarget: {
              dataset: {
                id: message._id
              }
            }
          };
          this.deleteMessage(event);
        }
      }
    });
  },

  // 触摸开始事件
  touchStart: function(e) {
    if (e.touches.length === 1) {
      this.setData({
        startX: e.touches[0].clientX
      });
    }
  },

  // 触摸移动事件
  touchMove: function(e) {
    if (e.touches.length === 1) {
      const moveX = e.touches[0].clientX;
      const index = e.currentTarget.dataset.index;
      const messages = this.data.filteredMessages;
      
      // 计算滑动距离
      let offset = moveX - this.data.startX;
      
      // 限制只能向左滑动，且最大滑动距离为删除按钮宽度
      if (offset < 0) {
        if (offset < -80) offset = -80;
        
        // 更新当前消息的滑动偏移量
        const key = `filteredMessages[${index}].slideOffset`;
        this.setData({
          [key]: offset
        });
      }
    }
  },

  // 触摸结束事件
  touchEnd: function(e) {
    const index = e.currentTarget.dataset.index;
    const messages = this.data.filteredMessages;
    const message = messages[index];
    const offset = message.slideOffset || 0;
    
    // 根据滑动距离决定是否显示删除按钮
    let newOffset = 0;
    if (offset < -40) {
      // 如果滑动距离超过阈值，则完全显示删除按钮
      newOffset = -80;
    }
    
    const key = `filteredMessages[${index}].slideOffset`;
    this.setData({
      [key]: newOffset
    });
  },

  // 删除消息
  deleteMessage: function(e) {
    const messageId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条消息吗？',
      success: (res) => {
        if (res.confirm) {
          // 从本地数据中删除消息
          const messages = this.data.messages.filter(msg => msg._id !== messageId);
          const filteredMessages = this.data.filteredMessages.filter(msg => msg._id !== messageId);

          this.setData({
            messages,
            filteredMessages
          });
          
          // 可以在这里添加调用API删除消息的逻辑
          // request({
          //   url: `/api/message/${messageId}`,
          //   method: 'DELETE'
          // }).then(res => {
          //   if (res.success) {
          //     wx.showToast({
          //       title: '删除成功',
          //       icon: 'success'
          //     });
          //   }
          // }).catch(err => {
          //   wx.showToast({
          //     title: '删除失败',
          //     icon: 'none'
          //   });
          // });
        }
      }
    });
  },

  // 跳转到登录页
  goToLogin: function() {
    wx.navigateTo({ url: '/pages/auth/auth?redirect=/partner/messages/message' });
  },

  // 刷新消息
  refreshMessages: function() {
    console.log('手动刷新消息');

    // 显示加载提示
    wx.showLoading({
      title: '刷新中...',
      mask: true
    });

    // 重新检查登录状态
    const isLogin = this.checkLoginStatus();

    if (isLogin) {
      // 清空现有消息并重置滑动状态
      this.setData({
        messages: [],
        filteredMessages: [],
        loading: true
      });

      // 获取最新消息
      this.getMessages();
    } else {
      wx.hideLoading();
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 在渲染群聊列表时，保证 avatar 字段有值，否则用默认封面，并处理云存储fileID
  ensureGroupAvatar: async function(list) {
    const defaultAvatar = '/images/icons2/qun.png';
    const groups = (list || []).map(item => {
      // 标准化 id 字段，兼容 id/_id
      const id = item.id || item._id;
      return {
        ...item,
        id, // 强制有 id 字段
        avatar: item.avatar && item.avatar.trim() ? item.avatar : defaultAvatar
      };
    });
    // 收集所有cloud://开头的avatar
    const fileIDs = groups.filter(g => g.avatar.startsWith('cloud://')).map(g => g.avatar);
    if (fileIDs.length > 0 && typeof wx.cloud !== 'undefined' && wx.cloud.getTempFileURL) {
      try {
        const res = await wx.cloud.getTempFileURL({ fileList: fileIDs });
        const urlMap = {};
        (res.fileList || []).forEach(item => {
          urlMap[item.fileID] = item.tempFileURL;
        });
        groups.forEach(g => {
          if (g.avatar.startsWith('cloud://') && urlMap[g.avatar]) {
            g.avatar = urlMap[g.avatar];
          }
        });
      } catch (e) { console.warn('群聊封面fileID转URL失败', e); }
    }
    return groups;
  },

  // 加载群聊数据
  loadGroups: async function() {
    if (!this.data.isLogin) return;
    
    // 显示加载状态并添加延迟，使动画效果更明显
    this.setData({ groupLoading: true, myGroups: [], publicGroups: [] });
    
    // 添加小延迟，让加载动画效果更明显
    await new Promise(resolve => setTimeout(resolve, 300));
    
    try {
      // 显示加载提示
      console.log('开始加载群聊数据...');
      
      const [myRes, pubRes] = await Promise.all([
        api.groupApi.getMyGroups(),
        api.groupApi.getPublicGroups()
      ]);
      
      if (myRes.success) {
        console.log('成功获取我的群聊数据');
        const myGroups = await this.ensureGroupAvatar(myRes.data);
        this.setData({ myGroups });
      }
      
      if (pubRes.success) {
        console.log('成功获取公开群聊数据');
        let publicGroups = await this.ensureGroupAvatar(pubRes.data);
        
        // ====== 强制兼容 id/_id 字段和类型，保证"已加入"标签显示 ======
        const myGroupIdSet = new Set((this.data.myGroups || []).map(g => String(g.id || g._id)));
        publicGroups = publicGroups.map(g => {
          const gid = String(g.id || g._id);
          if (myGroupIdSet.has(gid) && (!g.role || g.role === null)) {
            return { ...g, role: 'member' };
          }
          return g;
        });
        
        // 自动检查 role 字段
        let hasMember = false, hasOwner = false, hasNull = false;
        publicGroups.forEach(g => {
          if (g.role === 'member') hasMember = true;
          if (g.role === 'owner') hasOwner = true;
          if (!g.role) hasNull = true;
        });

        this.setData({ publicGroups });
      }
      
      console.log('群聊数据加载完成');
    } catch (err) {
      console.error('获取群聊失败:', err);
      wx.showToast({ 
        title: '获取群聊失败', 
        icon: 'none',
        duration: 2000
      });
    } finally {
      // 添加小延迟，让加载动画效果更明显
      setTimeout(() => {
        this.setData({ groupLoading: false });
      }, 300);
    }
  },

  // 点击我的群聊
  onGroupTap: function(e) {
    const group = e.currentTarget.dataset.group;
    wx.navigateTo({
      url: `/partner/messages/group-chat?groupId=${group.id}`
    });
  },

  // 点击公开群聊
  onPublicGroupTap: function(e) {
    const group = e.currentTarget.dataset.group;
    wx.navigateTo({
      url: `/partner/messages/group-detail?groupId=${group.id}`
    });
  },

  // 加入群聊
  onJoinGroup: function(e) {
    const group = e.currentTarget.dataset.group;
    const { groupApi } = require('../../utils/api');

    wx.showLoading({
      title: '加入中...',
      mask: true
    });

    groupApi.joinGroup(group.id).then(res => {
      if (res.success) {
        wx.showToast({
          title: '加入成功',
          icon: 'success'
        });
        // 刷新群聊列表
        this.loadGroups();
      } else {
        wx.showToast({
          title: res.message || '加入失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('加入群聊失败:', err);
      wx.showToast({
        title: '加入失败',
        icon: 'none'
      });
    }).finally(() => {
      wx.hideLoading();
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    if (this.data.currentTab === 1) { // 更新索引从2到1
      // 显示加载提示
      wx.showLoading({
        title: '刷新群聊中...',
        mask: true
      });
      
      // 调用加载群聊函数
      this.loadGroups().then(() => {
        wx.hideLoading();
        wx.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1500
        });
      }).catch(err => {
        wx.hideLoading();
        wx.showToast({
          title: '刷新失败',
          icon: 'none',
          duration: 1500
        });
      });
    } else {
      this.getMessages(); // 修正函数名称，从loadMessages改为getMessages
    }
    wx.stopPullDownRefresh();
  },

  // 切换群聊卡片
  switchGroupTab: function(e) {
    const index = Number(e.currentTarget.dataset.index);
    const currentIndex = this.data.groupTab;
    
    // 如果点击的是当前标签，不做任何操作
    if (index === currentIndex) return;
    
    // 设置新的标签索引并显示加载状态
    this.setData({ 
      groupTab: index,
      groupLoading: true 
    });
    
    // 添加小延迟，让加载动画效果更明显
    setTimeout(() => {
      // 如果数据已经加载过，直接隐藏加载状态
      if ((index === 0 && this.data.myGroups.length > 0) || 
          (index === 1 && this.data.publicGroups.length > 0)) {
        this.setData({ groupLoading: false });
      } else {
        // 否则重新加载群聊数据
        this.loadGroups();
      }
    }, 300);
  },

  // 跳转到创建群聊页面
  goToCreateGroup: function() {
    wx.navigateTo({ url: '/partner/messages/create-group' });
  },

  onViewMembers(e) {
    const groupId = e.currentTarget.dataset.groupid;
    wx.navigateTo({
      url: `/partner/messages/group-detail?groupId=${groupId}&showMembers=1`
    });
  },

  // 在全部群聊卡片渲染时，判断item.role，显示对应标签
  // ... existing code ...
});
