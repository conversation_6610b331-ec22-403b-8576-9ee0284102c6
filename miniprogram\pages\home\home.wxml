<!--pages/home/<USER>
<!--新版首页 - 电商风格-->
<view class="home-container">
  <!-- 搜索栏占位，防止内容被固定搜索栏遮挡 -->
  <view class="search-placeholder"></view>
  
  <!-- 搜索栏区域 -->
  <view class="search-section">
    <view class="search-input-wrapper">
      <input class="search-input" 
             placeholder="请输入搜索的内容" 
             placeholder-class="search-input-placeholder"
             value="{{searchKeyword}}"
             bindinput="onSearchInput"
             bindconfirm="onSearchConfirm" />
      <image class="search-icon" src="/images/icons2/搜索.svg" bindtap="onSearchConfirm"></image>
    </view>
  </view>

  <!-- 轮播广告 -->
  <view class="banner-section">
    <swiper class="banner-swiper" 
            indicator-dots="{{true}}" 
            autoplay="{{true}}" 
            interval="{{3000}}" 
            duration="{{500}}" 
            circular="{{true}}"
            indicator-color="rgba(255, 255, 255, 0.6)"
            indicator-active-color="#FF6B35">
      <swiper-item wx:for="{{banners}}" wx:key="id" bindtap="onBannerTap" data-index="{{index}}">
        <image src="{{item.imageUrl}}" mode="aspectFill" class="banner-image"></image>
        <view class="banner-overlay">
          <view class="banner-title">{{item.title}}</view>
          <view class="banner-subtitle" wx:if="{{item.subtitle}}">{{item.subtitle}}</view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 快捷菜单 -->
  <view class="quick-menu-section">
    <view class="quick-menu-grid">
      <view class="quick-menu-item" 
            wx:for="{{quickMenus}}" 
            wx:key="id" 
            bindtap="onQuickMenuTap" 
            data-menu="{{item}}">
        <view class="quick-menu-icon-wrapper">
          <image class="quick-menu-icon" src="{{item.icon}}"></image>
        </view>
        <text class="quick-menu-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 新品上市横滑区域 - 只有当新品数量>=5个时才显示 -->
  <view class="new-products-section" wx:if="{{showNewProducts}}">
    <!-- 新品上市标题栏 -->
    <view class="new-products-header">
      <view class="new-products-title">新品上市</view>
      <view class="new-products-more" bindtap="onNewProductsMoreTap">
        <text class="more-text">更多</text>
        <text class="more-icon">›</text>
      </view>
    </view>
    <scroll-view class="new-products-scroll" scroll-x="true" show-scrollbar="false" bindscroll="onNewProductsScroll">
      <view class="new-products-list">
        <view class="new-product-card" wx:for="{{newProducts}}" wx:key="index" style="width: 110px; margin-right: 6px; display: inline-block;" bindtap="onNewProductTap" data-id="{{item.id}}">
          <image class="new-product-image" src="{{imgErrorMap[item.id] ? '/images/mo/mogoods.jpg' : (item.imageUrl || '/images/mo/mogoods.jpg')}}" mode="aspectFill" binderror="onGoodsImgError" data-id="{{item.id}}"></image>
          <view class="new-product-name">{{item.name}}</view>
          <view class="new-product-original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</view>
          <view class="new-product-price">¥{{item.price}}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 推荐商品区域 -->
  <view class="recommend-section">
    <!-- 筛选标签横滑 -->
    <scroll-view class="recommend-tabs-scroll" scroll-x="true" show-scrollbar="false">
      <view class="recommend-tabs">
        <view class="recommend-tab {{currentProductTab === index ? 'active' : ''}}"
              wx:for="{{productTabs}}"
              wx:key="*this"
              bindtap="onProductTabTap"
              data-index="{{index}}">
          {{item}}
        </view>
      </view>
    </scroll-view>
    <!-- 商品网格 -->
    <view class="products-grid">
      <view class="product-item"
            wx:for="{{products}}"
            wx:key="index"
            bindtap="onProductTap"
            data-id="{{item.id}}">
        <view class="product-image-wrapper">
          <image class="product-image" src="{{imgErrorMap[item.id] ? '/images/mo/mogoods.jpg' : (item.imageUrl || '/images/mo/mogoods.jpg')}}" mode="aspectFill" binderror="onGoodsImgError" data-id="{{item.id}}"></image>
          <!-- 商品标签 -->
          <view class="product-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <text class="product-tag" wx:for="{{item.tags}}" wx:for-item="tag" wx:key="tag-{{index}}-{{tag}}">{{tag}}</text>
          </view>
        </view>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-spec" wx:if="{{item.spec}}">{{item.spec}}</text>
          <view class="product-original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</view>
          <view class="product-price-row">
            <view class="product-price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{item.price}}</text>
            </view>
            <view class="product-actions">
              <view class="action-btn favorite-btn" catchtap="onFavoriteTap" data-id="{{item.id}}">
                <image class="action-icon" 
                       src="{{favoriteStatus[item.id] ? '/images/icons2/已收藏.svg' : '/images/icons2/未收藏_red.svg'}}"
                       wx:if="{{!favoriteLoading[item.id]}}"></image>
                <view class="loading-spinner" wx:if="{{favoriteLoading[item.id]}}"></view>
              </view>
              <view class="action-btn cart-btn" catchtap="onAddToCartTap" data-id="{{item.id}}">
                <image class="action-icon" src="/images/icons2/添加_red.svg"></image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading && !refreshing}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more-container" wx:if="{{!loading && !hasMore && products.length > 0}}">
    <view class="no-more-line"></view>
    <text class="no-more-text">没有更多商品了</text>
    <view class="no-more-line"></view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && products.length === 0}}">
    <image class="empty-icon" src="/images/icons2/暂无数据.svg"></image>
    <text class="empty-text">暂无商品数据</text>
    <view class="retry-btn" bindtap="initData">
      <text>重新加载</text>
    </view>
  </view>

  <!-- 回到顶部悬浮按钮 -->
  <view class="back-to-top" wx:if="{{showBackToTop}}" bindtap="onBackToTop">
    <image class="back-to-top-icon" src="/images/icons2/返回顶部.svg"></image>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>