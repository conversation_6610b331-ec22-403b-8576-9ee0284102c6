const { cartApi, userApi } = require('../../utils/api');

Page({
  data: {
    deliveryMethod: 'express', // 配送方式：express-快递，self-自提
    addressInfo: {}, // 收货地址信息
    storeInfo: {}, // 自提门店信息
    cartItems: [], // 购物车商品列表
    totalAmount: '0.00', // 总金额
    loading: true, // 加载状态
    cartItemIds: [], // 购物车商品ID列表
    wechatPaySelected: true, // 微信支付是否选中
    balancePayEnabled: false // 余额支付是否启用
  },

  onLoad: function (options) {
    console.log('确认订单页面加载成功');
    console.log('页面参数:', options);
    
    // 简化登录状态验证：只检查基本的token和用户信息存在性
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');

    console.log('订单页面登录状态检查:',
      '有token:', !!token,
      '有用户信息:', !!userInfo);

    if (!token || !userInfo) {
      console.log('订单页面检测到缺少token或用户信息');

      wx.showModal({
        title: '登录失效',
        content: '请先登录后再进行订单操作',
        showCancel: false,
        success: () => {
          wx.redirectTo({ url: '/pages/auth/auth' });
        }
      });
      return;
    }
    
    // 处理从商品详情页面直接购买的情况
    if (options.from === 'buy_now' && options.id) {
      this.handleBuyNow(options.id);
    } else {
      this.initPage();
    }
  },

  onShow: function () {
    console.log('确认订单页面显示');
    // 页面显示时不重复获取数据，避免干扰初始化
  },

  // 处理立即购买
  handleBuyNow: function(productId) {
    console.log('处理立即购买，商品ID:', productId);
    
    // 从购物车中获取对应的商品
    const cartItems = wx.getStorageSync('cartItems') || [];
    const targetItem = cartItems.find(item => item.productId === productId);
    
    if (targetItem) {
      // 将商品数据存储到本地存储，供页面使用
      wx.setStorageSync('selectedCartItems', [targetItem]);
      console.log('立即购买商品数据已存储:', targetItem);
    } else {
      console.log('未找到对应的购物车商品');
      wx.showToast({
        title: '商品数据获取失败',
        icon: 'none'
      });
    }
    
    this.initPage();
  },

  // 初始化页面
  initPage: function() {
    console.log('开始初始化确认订单页面');
    // 先获取商品数据，再获取地址或门店
    this.getCartItems();
    // 延迟获取地址或门店，避免同时请求
    setTimeout(() => {
      if (this.data.deliveryMethod === 'express') {
        this.getDefaultAddress();
      } else {
        this.getDefaultStore();
      }
    }, 100);
  },

  // 获取购物车商品信息
  getCartItems: function() {
    console.log('开始获取购物车商品数据');
    
    this.setData({ loading: true });
    
    // 尝试多种方式获取数据
    let selectedItems = null;
    
    // 1. 尝试从本地存储获取
    try {
      selectedItems = wx.getStorageSync('selectedCartItems');
      console.log('从本地存储获取数据:', selectedItems);
    } catch (error) {
      console.error('读取本地存储失败:', error);
    }
    
    // 2. 如果本地存储没有，尝试从全局数据获取
    if (!selectedItems || selectedItems.length === 0) {
      try {
        const app = getApp();
        selectedItems = app.globalData.selectedCartItems;
        console.log('从全局数据获取数据:', selectedItems);
      } catch (error) {
        console.error('读取全局数据失败:', error);
      }
    }
    
    // 3. 验证数据完整性
    if (selectedItems && Array.isArray(selectedItems)) {
      // 检查每个商品是否有必要的字段
      selectedItems = selectedItems.filter(item => {
        if (!item.id || !item.productId || !item.price || !item.quantity) {
          console.warn('发现不完整的商品数据，已过滤:', item);
          return false;
        }
        // 验证数据类型和范围
        const price = parseFloat(item.price);
        const quantity = parseInt(item.quantity);
        if (isNaN(price) || price <= 0 || isNaN(quantity) || quantity <= 0) {
          console.warn('发现无效的价格或数量数据，已过滤:', item);
          return false;
        }
        return true;
      });
    }
    
    if (selectedItems && selectedItems.length > 0) {
      console.log('成功获取购物车数据:', selectedItems);
      
      // 处理商品数据
      const processedItems = selectedItems.map(item => {
        // 计算小计金额
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 1;
        const subtotal = (price * quantity).toFixed(2);
        
        return {
          ...item,
          subtotal: subtotal
        };
      });
      
      console.log('设置到页面的商品数据:', processedItems);
      
      this.setData({
        cartItems: processedItems,
        loading: false
      });
      
      console.log('页面数据设置完成，当前cartItems:', this.data.cartItems);
      
      this.calculateTotal();
      
      // 延迟清除购物车数据，确保数据已正确设置到页面
      setTimeout(() => {
        try {
          wx.removeStorageSync('selectedCartItems');
          console.log('购物车本地存储数据已清除');
        } catch (error) {
          console.error('清除购物车本地存储失败:', error);
        }

        try {
          const app = getApp();
          app.globalData.selectedCartItems = null;
          console.log('购物车全局数据已清除');
        } catch (error) {
          console.error('清除购物车全局数据失败:', error);
        }
      }, 500); // 增加延迟时间
      
      return;
    }
    
    // 如果没有全局数据，显示错误提示
    console.log('没有找到购物车数据');
    wx.showToast({
      title: '请从购物车页面选择商品进入',
      icon: 'none',
      duration: 2000
    });
    this.setData({
      cartItems: [],
      loading: false
    });
    this.calculateTotal();
  },

  // 获取默认收货地址
  getDefaultAddress: function() {
    console.log('开始获取默认收货地址');
    
    userApi.getDefaultAddress()
      .then(res => {
        console.log('默认地址API响应:', res);
        
        if (res && res.success) {
          if (res.data) {
            // 格式化地址信息
            const addressInfo = {
              id: res.data.id,
              name: res.data.name,
              phone: this.maskPhone(res.data.phone),
              address: `${res.data.province}${res.data.city}${res.data.district}${res.data.detail}`
            };
            
            console.log('格式化后的地址信息:', addressInfo);
            
            this.setData({
              addressInfo: addressInfo
            });
          } else {
            console.log('用户暂无默认地址');
            this.setData({
              addressInfo: {}
            });
          }
        } else {
          console.log('获取默认地址失败:', res.message);
          this.setData({
            addressInfo: {}
          });
        }
      })
      .catch(err => {
        console.error('获取默认地址失败:', err);
        this.setData({
          addressInfo: {}
        });
      });
  },

  // 手机号脱敏处理
  maskPhone: function(phone) {
    if (!phone) return '';
    if (phone.length < 7) return phone;
    return phone.substring(0, 3) + '****' + phone.substring(7);
  },

  // 获取默认自提门店
  getDefaultStore: function() {
    console.log('开始获取默认自提门店');
    
    userApi.getSubscribedStore()
      .then(res => {
        console.log('订阅门店API响应:', res);
        
        if (res && res.success) {
          if (res.data) {
            console.log('获取到订阅门店:', res.data);
            this.setData({
              storeInfo: res.data
            });
          } else {
            console.log('用户暂无订阅门店');
            this.setData({
              storeInfo: {}
            });
          }
        } else {
          console.log('获取订阅门店失败:', res.message);
          this.setData({
            storeInfo: {}
          });
        }
      })
      .catch(err => {
        console.error('获取订阅门店失败:', err);
        this.setData({
          storeInfo: {}
        });
      });
  },

  // 切换配送方式
  switchDelivery: function(e) {
    const type = e.currentTarget.dataset.type;
    console.log('切换配送方式:', type);
    
    this.setData({
      deliveryMethod: type
    });
    
    // 根据配送方式获取相应的数据
    if (type === 'express') {
      this.getDefaultAddress();
    } else {
      this.getDefaultStore();
    }
  },

  // 切换微信支付选择状态
  toggleWechatPay: function() {
    this.setData({
      wechatPaySelected: !this.data.wechatPaySelected
    });
    console.log('微信支付选择状态:', this.data.wechatPaySelected);
  },

  // 切换余额支付开关状态
  toggleBalancePay: function(e) {
    const enabled = e.detail.value;
    this.setData({
      balancePayEnabled: enabled
    });
    console.log('余额支付开关状态:', enabled);
  },

  // 选择收货地址
  selectAddress: function() {
    wx.navigateTo({
      url: '/pages/address/list'
    });
  },

  // 选择自提门店
  selectStore: function() {
    wx.navigateTo({
      url: '/pages/store/select'
    });
  },

  // 计算总金额
  calculateTotal: function() {
    let total = 0;
    
    this.data.cartItems.forEach(item => {
      total += parseFloat(item.subtotal || 0);
    });
    
    this.setData({
      totalAmount: total.toFixed(2)
    });
    
    console.log('计算总金额:', this.data.totalAmount);
  },

  // 提交订单
  submitOrder: function() {
    console.log('点击提交订单按钮');
    
    // 显示加载提示
    wx.showLoading({
      title: '验证登录状态...',
      mask: true
    });
    
    // 增强登录状态验证，主动验证token有效性
    const loginStateManager = require('../../utils/login-state-manager');
    
    // 先检查本地token是否存在
    let token = wx.getStorageSync('token');
    let userInfo = wx.getStorageSync('userInfo');
    let tokenTime = wx.getStorageSync('tokenTimestamp');
    const now = Date.now();
    
    // 如果本地存储没有token，尝试从全局数据获取
    const app = getApp();
    if (!token && app && app.globalData && app.globalData.token) {
      token = app.globalData.token;
      tokenTime = app.globalData.tokenTimestamp || now;
      userInfo = app.globalData.userInfo;
      console.log('从全局数据恢复token成功');
      
      // 同步回本地存储
      wx.setStorageSync('token', token);
      wx.setStorageSync('tokenTimestamp', tokenTime);
      if (userInfo) {
        wx.setStorageSync('userInfo', userInfo);
      }
    }
    
    // 如果仍然没有token，尝试从登录状态管理器获取
    if (!token) {
      try {
        const loginState = loginStateManager.getLoginState();
        if (loginState && loginState.token) {
          token = loginState.token;
          tokenTime = loginState.timestamp || now;
          userInfo = loginState.userInfo;
          console.log('从登录状态管理器获取token成功');
          
          // 更新本地存储和全局数据
          wx.setStorageSync('token', token);
          wx.setStorageSync('tokenTimestamp', tokenTime);
          if (userInfo) {
            wx.setStorageSync('userInfo', userInfo);
          }
          if (app && app.globalData) {
            app.globalData.token = token;
            app.globalData.tokenTimestamp = tokenTime;
            if (userInfo) {
              app.globalData.userInfo = userInfo;
            }
          }
        }
      } catch (e) {
        console.error('尝试从登录状态管理器获取token失败:', e);
      }
    }
    
    console.log('提交订单前登录状态检查:',
      '有token:', !!token,
      '有用户信息:', !!userInfo,
      'token时间戳:', tokenTime,
      '当前时间:', now);

    // 简化验证逻辑：只检查基本的token和用户信息存在性
    // 让后端的认证中间件来处理token的有效性验证
    if (!token || !userInfo) {
      wx.hideLoading();
      console.log('提交订单时检测到缺少token或用户信息');

      wx.showModal({
        title: '登录已失效',
        content: '您的登录状态已失效，请重新登录后再提交订单',
        showCancel: true,
        cancelText: '返回购物车',
        confirmText: '去登录',
        success: (res) => {
          if (res.cancel) {
            // 返回购物车页面
            wx.navigateBack();
          } else {
            // 跳转到登录页面，并指定返回到购物车页面
            wx.redirectTo({ url: '/pages/auth/auth?redirect=/pages/cart/cart' });
          }
        }
      });
      return;
    }
    
    // 直接提交订单，让后端的认证中间件来验证token有效性
    console.log('基本验证通过，开始提交订单');
    wx.hideLoading();
    this.processOrderSubmission();
  },
  
  // 处理订单提交
  processOrderSubmission: function() {
    // 验证地址或门店
    if (this.data.deliveryMethod === 'express' && !this.data.addressInfo.id) {
      wx.showToast({
        title: '请先设置收货地址',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.deliveryMethod === 'self' && !this.data.storeInfo.id) {
      wx.showToast({
        title: '请先选择自提门店',
        icon: 'none'
      });
      return;
    }

    // 验证商品
    if (this.data.cartItems.length === 0) {
      wx.showToast({
        title: '购物车为空',
        icon: 'none'
      });
      return;
    }

    // 验证购物车商品数据
    const validCartItems = this.data.cartItems.filter(item => {
      if (!item.id || !item.productId || !item.price || !item.quantity) {
        console.warn('过滤无效商品数据:', item);
        return false;
      }
      const price = parseFloat(item.price);
      const quantity = parseInt(item.quantity);
      if (isNaN(price) || price <= 0 || isNaN(quantity) || quantity <= 0) {
        console.warn('过滤价格或数量无效的商品:', item);
        return false;
      }
      return true;
    });
    
    const cartItemIds = validCartItems.map(item => item.id);
    if (cartItemIds.length === 0) {
      wx.showToast({
        title: '没有有效的商品数据',
        icon: 'none'
      });
      return;
    }
    
    // 重新计算总金额，确保准确性
    const recalculatedTotal = validCartItems.reduce((sum, item) => {
      return sum + (parseFloat(item.price) * parseInt(item.quantity));
    }, 0);
    
    if (recalculatedTotal <= 0) {
      wx.showToast({
        title: '订单金额计算错误',
        icon: 'none'
      });
      return;
    }
    
    console.log('验证通过的商品数量:', validCartItems.length, '总金额:', recalculatedTotal);

    // 验证支付方式
    if (!this.data.wechatPaySelected && !this.data.balancePayEnabled) {
      wx.showToast({
        title: '请选择支付方式',
        icon: 'none'
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '提交订单中...'
    });
    
    // 构建订单数据
    const orderData = {
      cartItemIds: cartItemIds,
      deliveryMethod: this.data.deliveryMethod,
      addressId: this.data.deliveryMethod === 'express' ? this.data.addressInfo.id : null,
      storeId: this.data.deliveryMethod === 'self' ? this.data.storeInfo.id : null,
      paymentMethods: this.getSelectedPaymentMethods(),
      totalAmount: recalculatedTotal // 使用重新计算的总金额
    };

    console.log('提交订单数据:', orderData);

    // 调用订单创建API
    const { orderApi } = require('../../utils/api');
    
    // 真实提交订单，不再使用模拟提交
    console.log('开始提交订单');
    orderApi.createOrder(orderData)
      .then(res => {
        wx.hideLoading();
        
        console.log('订单创建结果:', res);
        
        // 检查API返回是否成功
        if (res.success) {
          // 检查是否有订单数据
          if (res.data && res.data.orderId) {
            const orderId = res.data.orderId;
            const isPaid = res.data.paid;
            console.log('订单创建成功:', orderId, '是否已支付:', isPaid);

            if (isPaid) {
              // 订单已支付，显示成功信息并返回购物车
              wx.showToast({
                title: '订单提交并支付成功',
                icon: 'success'
              });

              setTimeout(() => {
                // 返回购物车页面
                wx.switchTab({
                  url: '/pages/cart/cart'
                });
              }, 1500);
            } else {
              // 订单未支付，跳转到支付页面
              wx.showToast({
                title: '订单提交成功',
                icon: 'success'
              });

              setTimeout(() => {
                wx.navigateTo({
                  url: `/pages/order/pay?id=${orderId}`
                });
              }, 1000);
            }
          } else {
            // API返回成功但数据异常
            console.error('订单创建API返回异常:', res);
            wx.showModal({
              title: '订单提交失败',
              content: res.message || '服务器返回数据异常，请稍后重试',
              showCancel: false
            });
          }
        } else {
          // API返回失败
          console.error('订单创建失败:', res);
          
          // 检查是否为库存不足错误，显示友好提示
          let errorMessage = res.message || '订单提交失败，请稍后重试';
          if (errorMessage.includes('库存不足') || errorMessage.includes('商品库存不足')) {
            errorMessage = '商品库存不足，请联系客服，或购买其他商品';
          }
          
          wx.showModal({
            title: '订单提交失败',
            content: errorMessage,
            showCancel: false
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('订单创建失败:', err);
        
        // 显示错误信息
        let errorMessage = '网络异常，请稍后重试';
        let isLoginExpired = false;
        
        // 处理401错误（登录失效）
        if (err.statusCode === 401 || err.code === 401 || (err.errMsg && err.errMsg.includes('401'))) {
          isLoginExpired = true;
          errorMessage = '登录已失效，请返回购物车重新结算';
          
          // 不清除登录状态，让用户返回购物车页面
          console.log('订单提交时遇到401错误，不清除登录状态');
        } else if (err.message) {
          // 检查是否为库存不足错误
          if (err.message.includes('库存不足') || err.message.includes('商品库存不足')) {
            errorMessage = '商品库存不足，请联系客服，或购买其他商品';
          } else {
            errorMessage = err.message;
          }
        }
        
        if (isLoginExpired) {
          // 登录失效时，提供更明确的选项
          wx.showModal({
            title: '登录已失效',
            content: '您的登录状态已失效，请返回购物车重新结算',
            showCancel: true,
            cancelText: '返回购物车',
            confirmText: '去登录',
            success: (res) => {
              if (res.cancel) {
                // 返回购物车页面
                wx.navigateBack();
              } else {
                // 跳转到登录页面，并指定返回到购物车页面
                wx.redirectTo({ url: '/pages/auth/auth?redirect=/pages/cart/cart' });
              }
            }
          });
        } else {
          // 其他错误
          wx.showModal({
            title: '订单提交失败',
            content: errorMessage,
            showCancel: false
          });
        }
      });
  },

  // 获取选中的支付方式
  getSelectedPaymentMethods: function() {
    const methods = [];
    
    if (this.data.wechatPaySelected) {
      methods.push('wechat');
    }
    
    if (this.data.balancePayEnabled) {
      methods.push('balance');
    }
    
    return methods;
  },
  
  // 图片加载失败处理
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const cartItems = this.data.cartItems;
    
    if (cartItems[index]) {
      // 直接使用默认图片
      cartItems[index].image = '/images/mo/mogoods.jpg';
      this.setData({
        cartItems: cartItems
      });
      console.log('订单商品图片加载失败，使用默认图片:', index);
    }
  }
});