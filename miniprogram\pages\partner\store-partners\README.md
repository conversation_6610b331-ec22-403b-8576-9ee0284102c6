# 门店合伙人页面

## 功能描述

门店合伙人页面用于显示指定门店的合伙人信息，包括：

1. **门店选择**：用户可以选择要查看的门店
2. **用户状况**：显示当前用户在该门店的股本金、股份、角色和股份比例
3. **合伙人列表**：显示该门店的所有合伙人信息
4. **合伙人统计**：显示该门店的合伙人总数

## 页面结构

### 门店选择区域
- 门店搜索输入框
- 门店下拉列表
- 支持按门店名称或编号搜索

### 用户状况区域
- 我的股本金
- 我的股份
- 我的角色
- 股份比例

### 合伙人列表区域
- 合伙人头像和基本信息
- 合伙人详细信息（股本金、类型、股份比例、加入方式、状态）
- 空状态提示

## 跳转入口

1. **选品页面**：点击"门店合伙人"快捷菜单按钮
2. **合伙人页面**：点击"门店合伙人"按钮

## API接口

- `partnerApi.getPartnersByStoreId(storeNo)`：获取门店合伙人列表
- `storeApi.getPartnerStores()`：获取合伙人门店列表

## 数据格式

### 合伙人信息
```javascript
{
  id: 1,
  user_id: 123,
  store_no: "ST001",
  type: "投资合伙人",
  amount: 10000,
  percent: 25,
  created_at: 1640995200000,
  nickname: "张三",
  avatar: "/images/avatar.png",
  phone: "13800138000",
  user_status: 1,
  joinDate: "2022-01-01",
  statusText: "正常",
  statusClass: "status-normal"
}
```

## 注意事项

1. 页面需要用户登录才能访问
2. 如果用户不是该门店的合伙人，会显示"非合伙人"状态
3. 支持下拉刷新功能
4. 页面支持分享功能 