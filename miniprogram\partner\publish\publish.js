// partner/publish/publish.js
const { partnerApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');

Page({
  data: {
    activeTab: 0, // 默认选中采购标签
    tabs: ['采购', '上架', '移库'],
    
    // 门店相关
    storeList: [],
    selectedStore: null,
    
    // 采购车商品列表
    purchaseItems: [],
    totalPurchasePrice: 0,
    totalPurchaseCount: 0,
    allSelected: false, // 全选状态
    selectedCount: 0, // 选中商品数量
    
    // 加载状态
    loading: true,
    isLogin: false
  },

  onLoad: function (options) {
    this.checkLoginStatus();
  },

  onShow: function () {
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus: function() {
    console.log('上架页面检查登录状态');
    const app = getApp();
    
    this.setData({ loading: true });

    if (app.globalData.isLogin && app.globalData.userInfo) {
      console.log('上架页面检测到已登录状态');
      this.setData({
        isLogin: true,
        loading: false
      });
      this.loadPartnerData();
      return;
    }

    const userInfo = wx.getStorageSync('userInfo');

    if (loginStateManager.getLoginState() && loginStateManager.getLoginState().isLogin && userInfo) {
      console.log('上架页面从本地存储检测到登录状态');
      app.globalData.userInfo = userInfo;
      app.globalData.isLogin = true;
      
      this.setData({
        isLogin: true,
        loading: false
      });
      this.loadPartnerData();
      return;
    }

    loginStateManager.validateLoginState()
      .then(result => {
        console.log('上架页面登录状态验证结果:', result);
        
        if (result.isValid) {
          app.globalData.userInfo = result.userInfo;
          app.globalData.isLogin = true;
          
          this.setData({
            isLogin: true,
            loading: false
          });
          this.loadPartnerData();
        } else {
          console.log('上架页面检测到未登录状态');
          this.setData({
            isLogin: false,
            loading: false
          });
        }
      })
      .catch(err => {
        console.error('上架页面验证登录状态出错:', err);
        this.setData({
          isLogin: false,
          loading: false
        });
      });
  },

  // 加载合伙人数据
  loadPartnerData: function() {
    console.log('开始加载合伙人数据');
    
    // 获取门店列表
    this.getPartnerStores();
  },

  // 获取合伙人门店列表
  getPartnerStores: function() {
    console.log('获取合伙人门店列表');
    
    // 同时调用两个获取门店的API
    Promise.all([
      partnerApi.getPartnerStores(),
      partnerApi.getPartnerJoinedStores()
    ])
      .then(([storesRes, joinedStoresRes]) => {
        // 合并两个API的门店数据，并去重
        let allStores = [];
        
        if (storesRes && storesRes.success && storesRes.data) {
          allStores = [...storesRes.data];
        }
        
        if (joinedStoresRes && joinedStoresRes.success && joinedStoresRes.data) {
          // 将joinedStores中的门店添加到allStores中，避免重复
          joinedStoresRes.data.forEach(store => {
            // 检查是否已存在相同store_no的门店
            const existingIndex = allStores.findIndex(s => s.store_no === store.store_no);
            if (existingIndex === -1) {
              // 不存在则添加
              allStores.push(store);
            }
          });
        }
        
        console.log('合并后的门店列表数据:', allStores);
        
        if (allStores.length > 0) {
          console.log('获取门店列表成功:', allStores);
          
          // 从本地存储获取之前选择的门店
          const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
          let selectedStore = null;
          
          if (storedSelectedStore) {
            // 在新获取的门店列表中查找之前选择的门店
            const previousSelectedStore = allStores.find(store => 
              store.id === storedSelectedStore.id || store.store_no === storedSelectedStore.store_no
            );
            if (previousSelectedStore) {
              selectedStore = previousSelectedStore;
              console.log('保持之前选择的门店:', selectedStore);
            } else {
              selectedStore = allStores[0];
              console.log('之前选择的门店不在新列表中，选择第一个:', selectedStore);
            }
          } else {
            selectedStore = allStores[0];
          }
          
          this.setData({
            storeList: allStores,
            selectedStore: selectedStore
          });
          
          // 保存到本地存储
          wx.setStorageSync('partnerStoreList', allStores);
          wx.setStorageSync('partnerSelectedStore', selectedStore);
          
          // 加载采购车数据
          this.loadPurchaseItems();
        } else {
          console.log('获取门店列表失败或为空');
          this.setData({
            storeList: [],
            selectedStore: null
          });
        }
      })
      .catch(err => {
        console.error('获取门店列表失败:', err);
        this.setData({
          storeList: [],
          selectedStore: null
        });
        wx.showToast({
          title: '获取门店列表失败',
          icon: 'none'
        });
      });
  },

  // 加载采购车数据
  loadPurchaseItems: function() {
    console.log('加载采购车数据');
    
    // 从本地存储获取采购车数据
    const purchaseItems = wx.getStorageSync('purchaseCart') || [];
    
    // 为每个商品添加选中状态
    purchaseItems.forEach(item => {
      item.selected = false;
    });
    
    this.setData({
      purchaseItems: purchaseItems
    });
    
    this.calculatePurchaseTotal();
    console.log('采购车数据已加载，商品数量:', purchaseItems.length);
  },

  // 保存采购车数据到本地存储
  savePurchaseItems: function() {
    const purchaseItems = this.data.purchaseItems;
    wx.setStorageSync('purchaseCart', purchaseItems);
    console.log('采购车数据已保存到本地存储');
  },

  // 计算采购车总价和数量
  calculatePurchaseTotal: function() {
    const items = this.data.purchaseItems;
    let totalPrice = 0;
    let totalCount = 0;
    let selectedCount = 0;
    
    items.forEach(item => {
      if (item.selected) {
        // 确保subtotal是数字类型
        const subtotal = parseFloat(item.subtotal);
        totalPrice += subtotal;
        selectedCount += item.quantity;
      }
      totalCount += item.quantity;
    });
    
    // 检查是否全选
    const allSelected = items.length > 0 && items.every(item => item.selected);
    
    this.setData({
      totalPurchasePrice: totalPrice.toFixed(2),
      totalPurchaseCount: totalCount,
      selectedCount: selectedCount,
      allSelected: allSelected
    });
  },

  // 标签页切换
  onTabChange: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      activeTab: index
    });
  },

  // 门店选择
  onStoreChange: function(e) {
    const index = e.detail.value;
    const selectedStore = this.data.storeList[index];
    
    console.log('门店选择:', selectedStore);
    
    this.setData({ selectedStore });
    
    // 保存到本地存储
    wx.setStorageSync('partnerSelectedStore', selectedStore);
    wx.setStorageSync('partnerStoreList', this.data.storeList);
    
    // 重新加载采购车数据（根据门店级别）
    this.loadPurchaseItems();
  },

  // 选择/取消选择单个商品
  toggleSelectItem: function(e) {
    const index = e.currentTarget.dataset.index;
    const purchaseItems = this.data.purchaseItems;

    purchaseItems[index].selected = !purchaseItems[index].selected;

    this.setData({
      purchaseItems: purchaseItems
    });

    this.calculatePurchaseTotal();
    this.savePurchaseItems();
  },

  // 全选/取消全选
  toggleSelectAll: function() {
    const allSelected = !this.data.allSelected;
    const purchaseItems = this.data.purchaseItems;

    purchaseItems.forEach(item => {
      item.selected = allSelected;
    });

    this.setData({
      purchaseItems: purchaseItems
    });

    this.calculatePurchaseTotal();
    this.savePurchaseItems();
  },

  // 增加商品数量
  increaseQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const items = this.data.purchaseItems;
    
    items[index].quantity += 1;
    // 确保purchasePrice是数字类型
    const purchasePrice = parseFloat(items[index].purchasePrice);
    items[index].subtotal = (purchasePrice * items[index].quantity).toFixed(2);
    
    this.setData({
      purchaseItems: items
    });
    
    this.calculatePurchaseTotal();
    this.savePurchaseItems();
  },

  // 减少商品数量
  decreaseQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const items = this.data.purchaseItems;
    
    if (items[index].quantity > 1) {
      items[index].quantity -= 1;
      // 确保purchasePrice是数字类型
      const purchasePrice = parseFloat(items[index].purchasePrice);
      items[index].subtotal = (purchasePrice * items[index].quantity).toFixed(2);
      
      this.setData({
        purchaseItems: items
      });
      
      this.calculatePurchaseTotal();
      this.savePurchaseItems();
    } else {
      // 数量为1时，再次减少则删除商品
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这个商品吗？',
        success: (res) => {
          if (res.confirm) {
            items.splice(index, 1);
            this.setData({
              purchaseItems: items
            });
            this.calculatePurchaseTotal();
            this.savePurchaseItems();
          }
        }
      });
    }
  },

  // 输入商品数量
  inputQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const quantity = parseInt(e.detail.value);
    const items = this.data.purchaseItems;
    
    if (quantity > 0) {
      items[index].quantity = quantity;
      // 确保purchasePrice是数字类型
      const purchasePrice = parseFloat(items[index].purchasePrice);
      items[index].subtotal = (purchasePrice * quantity).toFixed(2);
      
      this.setData({
        purchaseItems: items
      });
      
      this.calculatePurchaseTotal();
      this.savePurchaseItems();
    } else if (quantity === 0) {
      // 数量为0时删除商品
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这个商品吗？',
        success: (res) => {
          if (res.confirm) {
            items.splice(index, 1);
            this.setData({
              purchaseItems: items
            });
            this.calculatePurchaseTotal();
            this.savePurchaseItems();
          }
        }
      });
    }
  },

  // 采购结算
  purchaseCheckout: function() {
    if (this.data.selectedCount === 0) {
      wx.showToast({
        title: '请选择要采购的商品',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.selectedStore) {
      wx.showToast({
        title: '请选择门店',
        icon: 'none'
      });
      return;
    }
    
    // 跳转到采购结算页面
    wx.navigateTo({
      url: '/partner/publish/checkout/checkout'
    });
  },

  // 跳转到商品选择页面
  goToProductSelect: function() {
    if (!this.data.selectedStore) {
      wx.showToast({
        title: '请先选择门店',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: '/partner/select-products/select-products'
    });
  },

  // 图片加载错误处理
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const purchaseItems = this.data.purchaseItems;
    
    if (purchaseItems[index]) {
      // 设置默认图片
      purchaseItems[index].image = '/images/mo/mogoods.jpg';
      
      this.setData({
        purchaseItems: purchaseItems
      });
      
      console.log('商品图片加载失败，使用默认图片:', index);
    }
  }
}); 