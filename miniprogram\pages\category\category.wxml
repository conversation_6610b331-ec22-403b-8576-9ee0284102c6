<!--pages/shop/shop.wxml-->
<view class="shop-container">
  <!-- 搜索栏区域 -->
  <view class="search-section">
    <view class="search-input-wrapper" bindtap="onSearchTap">
      <image class="search-icon" src="/images/icons2/搜索.svg"></image>
      <input class="search-input" 
             placeholder="请输入搜索的内容" 
             placeholder-class="search-placeholder"
             value="{{searchKeyword}}"
             bindinput="onSearchInput"
             bindconfirm="onSearchConfirm" />
    </view>
  </view>
  
  <!-- 搜索栏占位元素 -->
  <view class="search-placeholder-space"></view>

  <!-- 分类页整体布局：左侧一级分类，右侧为轮播图+二级分类+商品列表 -->
  <view class="category-main-container">
    <!-- 左侧一级分类 -->
    <scroll-view class="category-left" scroll-y="true">
      <view class="category-left-item {{currentCategory === index ? 'active' : ''}}"
            wx:for="{{categories}}"
            wx:key="id"
            bindtap="switchCategory"
            data-index="{{index}}">
        <text>{{item.name}}</text>
      </view>
    </scroll-view>

    <!-- 右侧内容区 -->
    <view class="category-right">
      <!-- 轮播图，必须在二级分类标签上方 -->
      <swiper class="banner" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}" indicator-color="rgba(255, 255, 255, 0.6)" indicator-active-color="#FFFFFF">
        <swiper-item wx:for="{{banners}}" wx:key="id" bindtap="onBannerTap" data-index="{{index}}">
          <image src="{{item.imageUrl}}" mode="aspectFill" class="banner-image"></image>
          <view class="banner-title">{{item.title}}</view>
        </swiper-item>
      </swiper>
    <!-- 二级分类 -->
      <scroll-view class="subcategory-scroll" scroll-x="true" enhanced="true" show-scrollbar="false" wx:if="{{subCategories.length > 0}}">
      <view class="subcategory-item {{currentSubCategory === index ? 'active' : ''}}"
            wx:for="{{subCategories}}"
            wx:key="id"
            bindtap="switchSubCategory"
            data-index="{{index}}">
        {{item.name}}
      </view>
    </scroll-view>

      <!-- 排序及筛选栏 -->
      <view class="filter-bar">
    <view class="filter-item {{sortType === 'default' ? 'active' : ''}}" bindtap="switchSortType" data-type="default">
      <text>销量</text>
    </view>
    <view class="filter-item {{sortType === 'price-asc' || sortType === 'price-desc' ? 'active' : ''}}" bindtap="switchSortType" data-type="{{sortType === 'price-asc' ? 'price-desc' : 'price-asc'}}">
      <text>价格</text>
      <view class="sort-icon">
        <image src="../../images/icons2/向上.svg" class="{{sortType === 'price-asc' ? 'active' : ''}}"></image>
        <image src="../../images/icons2/向下.svg" class="{{sortType === 'price-desc' ? 'active' : ''}}"></image>
      </view>
    </view>
    <view class="filter-item" bindtap="showFilter">
      <text>筛选</text>
      <image class="filter-icon" src="../../images/icons2/筛选.svg"></image>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="product-list" wx:if="{{!loading && products.length > 0}}">
        <view class="product-card" wx:for="{{products}}" wx:key="id" bindtap="onProductTap" data-id="{{item.id}}">
          <image class="product-card-image" src="{{item.images && item.images.length > 0 ? item.images[0] : '/images/mo/mogoods.jpg'}}" mode="aspectFill" binderror="onImageError" data-index="{{index}}"></image>
          <view class="product-card-info">
            <view class="product-card-title">{{item.name || '未命名商品'}}</view>
            <!-- 划线价单独一行 -->
            <text class="product-card-original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
            <!-- 零售价与按钮在同一行 -->
            <view class="product-card-bottom-row">
              <view class="product-card-price">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{item.price || '0.00'}}</text>
              </view>
              <view class="product-card-actions">
                <view class="action-btn favorite-btn" catchtap="onFavoriteTap" data-id="{{item.id}}">
                  <image class="action-icon" src="{{favoriteStatus[item.id] ? '/images/icons2/已收藏.svg' : '/images/icons2/未收藏_red.svg'}}"></image>
                </view>
                <view class="action-btn cart-btn" catchtap="onAddToCartTap" data-id="{{item.id}}">
                  <image class="action-icon" src="/images/icons2/添加_red.svg"></image>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 加载、空、无更多等状态可按原有逻辑放在右侧下方 -->
  <view class="loading-container" wx:if="{{loading && !refreshing}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>
  <view class="no-more" wx:if="{{!loading && !hasMore && products.length > 0}}">
    <text>没有更多商品了</text>
  </view>
  <view class="empty-container" wx:if="{{!loading && products.length === 0}}">
    <image class="empty-icon" src="/images/icons2/空.svg"></image>
    <text class="empty-text">暂无商品</text>
  </view>
    </view>
  </view>





  <!-- 筛选面板 -->
  <view class="filter-panel-mask" wx:if="{{showFilter}}" bindtap="hideFilter"></view>
  <view class="filter-panel {{showFilter ? 'show' : ''}}">
    <view class="filter-panel-header">
      <text class="filter-panel-title">筛选</text>
      <view class="filter-panel-close" bindtap="hideFilter">
        <image src="/images/icons2/关闭.svg"></image>
      </view>
    </view>

    <view class="filter-section">
      <view class="filter-section-title">价格区间</view>
      <view class="price-range">
        <input class="price-input" type="digit" placeholder="最低价" value="{{filterOptions.minPrice}}" bindinput="inputMinPrice" />
        <view class="price-separator">-</view>
        <input class="price-input" type="digit" placeholder="最高价" value="{{filterOptions.maxPrice}}" bindinput="inputMaxPrice" />
      </view>
    </view>

    <view class="filter-section">
      <view class="filter-section-title">商品类型</view>
      <view class="filter-options">
        <view class="filter-option {{filterOptions.onlyDiscount ? 'active' : ''}}" bindtap="toggleOnlyDiscount">
          <view class="filter-checkbox">
            <image src="{{filterOptions.onlyDiscount ? '/images/icons2/选中.svg' : '/images/icons2/未选中.svg'}}"></image>
          </view>
          <text>优惠商品</text>
        </view>
        <view class="filter-option {{filterOptions.onlyNew ? 'active' : ''}}" bindtap="toggleOnlyNew">
          <view class="filter-checkbox">
            <image src="{{filterOptions.onlyNew ? '/images/icons2/选中.svg' : '/images/icons2/未选中.svg'}}"></image>
          </view>
          <text>新品</text>
        </view>
      </view>
    </view>

    <view class="filter-panel-footer">
      <view class="filter-reset-btn" bindtap="resetFilter">重置</view>
      <view class="filter-apply-btn" bindtap="applyFilter">确定</view>
    </view>
  </view>
</view>
