/**
 * 购物车服务
 */
const Cart = require('../models/Cart');
const Product = require('../models/Product');

class CartService {
  async getCart(userId) {
    try {
      console.log('【购物车服务】开始获取购物车，userId:', userId);
      
      // 验证userId格式
      if (!userId) {
        console.error('【购物车服务】userId为空');
        return {
          success: false,
          message: '用户ID不能为空',
          data: []
        };
      }
      
      // 确保userId是字符串格式
      const normalizedUserId = String(userId);
      console.log('【购物车服务】userId类型:', typeof normalizedUserId, '值:', normalizedUserId);
      
      const cartItems = await Cart.findAll(normalizedUserId);
      console.log('【购物车服务】数据库查询结果数量:', cartItems.length);
      console.log('【购物车服务】数据库查询结果详情:', cartItems);
      
      // 处理购物车数据
      const processedItems = cartItems.map(item => {
        console.log('【购物车服务】处理购物车项:', item);
        
        // 处理图片字段
        let imageUrl = '';
        
        // 优先使用cart表中的image字段
        if (item.image) {
          imageUrl = item.image;
        }
        // 如果cart表中没有图片，尝试从products表的images字段获取
        else if (item.images) {
          if (typeof item.images === 'string') {
            try {
              const images = JSON.parse(item.images);
              imageUrl = images.length > 0 ? images[0] : '';
            } catch (e) {
              console.log('【购物车服务】解析images JSON失败:', e);
              imageUrl = '';
            }
          } else if (Array.isArray(item.images)) {
            imageUrl = item.images.length > 0 ? item.images[0] : '';
          }
        }
        
        // 如果还是没有图片，使用默认图片
        if (!imageUrl) {
          imageUrl = '/images/mo/mogoods.jpg';
        }
        
        const processedItem = {
          ...item,
          image: imageUrl,
          checked: true // 默认选中
        };
        
        console.log('【购物车服务】处理后的购物车项:', processedItem);
        return processedItem;
      });
      
      console.log('【购物车服务】处理后的数据数量:', processedItems.length);
      console.log('【购物车服务】处理后的数据详情:', processedItems);
      
      return {
        success: true,
        data: processedItems,
        message: '获取购物车成功'
      };
    } catch (error) {
      console.error('【购物车服务】获取购物车失败:', error);
      return {
        success: false,
        message: '获取购物车失败',
        error: error.message,
        data: []
      };
    }
  }
  
  async addToCart(userId, productId, quantity = 1) {
    try {
      // 确保userId是字符串格式
      const normalizedUserId = String(userId);
      console.log('【购物车服务】添加到购物车 - userId:', normalizedUserId, 'productId:', productId, 'quantity:', quantity);
      
      // 检查商品是否存在
      const product = await Product.findById(productId);
      if (!product) {
        return {
          success: false,
          message: '商品不存在'
        };
      }
      
      // 检查商品库存
      const db = require('../config/db');
      const totalStockQuery = 'SELECT SUM(quantity) as total FROM store_cloud_stock WHERE product_id = ?';
      const totalStockResult = await db.query(totalStockQuery, [productId]);
      const totalStock = totalStockResult[0]?.total || 0;
      
      console.log(`商品 ${productId} 平台总库存: ${totalStock}，请求数量: ${quantity}`);
      
      // 检查购物车中是否已存在该商品，计算总需求量
       const existingItem = await Cart.findByUserAndProduct(normalizedUserId, productId);
       const totalRequestQuantity = existingItem ? (existingItem.quantity + quantity) : quantity;
       
       if (totalStock < totalRequestQuantity) {
         return {
           success: false,
           message: `商品库存不足，商品ID: ${productId}，需求: ${totalRequestQuantity}件，库存: ${totalStock}件`
         };
       }
      
      if (existingItem) {
        // 更新数量
        const updatedItem = await Cart.update(existingItem.id, {
          quantity: existingItem.quantity + quantity
        });
        return {
          success: true,
          data: updatedItem,
          message: '更新购物车成功'
        };
      } else {
        // 添加新商品
        const newItem = await Cart.create({
          id: 'cart_' + normalizedUserId + '_' + productId,
          userId: normalizedUserId,
          productId,
          name: product.name || '',
          price: product.price != null ? product.price : 0,
          image: (product.images && Array.isArray(product.images) && product.images[0]) ? product.images[0] : (product.image || ''),
          quantity: quantity || 1,
          selected: true
        });
        return {
          success: true,
          data: newItem,
          message: '添加到购物车成功'
        };
      }
    } catch (error) {
      console.error('添加到购物车失败:', error);
      throw new Error('添加到购物车失败，请稍后再试');
    }
  }
  
  async updateCart(id, quantity) {
    try {
      const cartItem = await Cart.findById(id);
      if (!cartItem) {
        return {
          success: false,
          message: '购物车商品不存在'
        };
      }
      
      // 检查商品库存
      const db = require('../config/db');
      const totalStockQuery = 'SELECT SUM(quantity) as total FROM store_cloud_stock WHERE product_id = ?';
      const totalStockResult = await db.query(totalStockQuery, [cartItem.productId]);
      const totalStock = totalStockResult[0]?.total || 0;
      
      console.log(`更新购物车商品 ${cartItem.productId} 库存检查: 总库存${totalStock}，请求数量${quantity}`);
      
      if (totalStock < quantity) {
        return {
          success: false,
          message: `商品库存不足，商品ID: ${cartItem.productId}，需求: ${quantity}件，库存: ${totalStock}件`
        };
      }
      
      // 更新数量
      const updatedItem = await Cart.update(id, { quantity });
      
      return {
        success: true,
        data: updatedItem,
        message: '更新购物车成功'
      };
    } catch (error) {
      console.error('更新购物车失败:', error);
      throw error;
    }
  }
  
  async removeFromCart(id) {
    try {
      const cartItem = await Cart.findById(id);
      if (!cartItem) {
        return {
          success: false,
          message: '购物车商品不存在'
        };
      }
      
      // 删除商品
      await Cart.remove(id);
      
      return {
        success: true,
        data: { id },
        message: '删除购物车商品成功'
      };
    } catch (error) {
      console.error('删除购物车商品失败:', error);
      throw error;
    }
  }
  
  async clearCart(userId) {
    try {
      // 确保userId是字符串格式
      const normalizedUserId = String(userId);
      console.log('【购物车服务】清空购物车 - userId:', normalizedUserId);
      
      // 清空购物车
      await Cart.removeByUser(normalizedUserId);
      
      return {
        success: true,
        message: '清空购物车成功'
      };
    } catch (error) {
      console.error('清空购物车失败:', error);
      throw error;
    }
  }
  
  async getCartCount(userId) {
    try {
      // 确保userId是字符串格式
      const normalizedUserId = String(userId);
      console.log('【购物车服务】获取购物车数量 - userId:', normalizedUserId);
      
      const count = await Cart.count(normalizedUserId);
      
      return {
        success: true,
        data: { count },
        message: '获取购物车数量成功'
      };
    } catch (error) {
      console.error('获取购物车数量失败:', error);
      throw error;
    }
  }
  
  async batchRemoveFromCart(ids) {
    try {
      // 验证所有ID是否存在
      const existingItems = await Promise.all(
        ids.map(id => Cart.findById(id))
      );
      
      const notFoundIds = [];
      existingItems.forEach((item, index) => {
        if (!item) {
          notFoundIds.push(ids[index]);
        }
      });
      
      if (notFoundIds.length > 0) {
        return {
          success: false,
          message: `购物车商品不存在: ${notFoundIds.join(', ')}`
        };
      }
      
      // 批量删除
      await Promise.all(ids.map(id => Cart.remove(id)));
      
      return {
        success: true,
        data: { deletedIds: ids },
        message: `成功删除${ids.length}件商品`
      };
    } catch (error) {
      console.error('批量删除购物车商品失败:', error);
      throw error;
    }
  }

  async getCartItemsByIds(userId, ids) {
    try {
      console.log('【购物车服务】根据ID获取购物车商品 - userId:', userId, 'ids:', ids);
      
      // 确保userId是字符串格式
      const normalizedUserId = String(userId);
      
      // 获取指定ID的购物车商品
      const cartItems = await Cart.findByIds(normalizedUserId, ids);
      console.log('【购物车服务】数据库查询结果数量:', cartItems.length);
      
      // 处理购物车数据
      const processedItems = cartItems.map(item => {
        // 处理图片字段
        let imageUrl = '';
        
        if (item.image) {
          imageUrl = item.image;
        } else if (item.images) {
          if (typeof item.images === 'string') {
            try {
              const images = JSON.parse(item.images);
              imageUrl = images.length > 0 ? images[0] : '';
            } catch (e) {
              console.log('【购物车服务】解析images JSON失败:', e);
              imageUrl = '';
            }
          } else if (Array.isArray(item.images)) {
            imageUrl = item.images.length > 0 ? item.images[0] : '';
          }
        }
        
        if (!imageUrl) {
          imageUrl = '/images/mo/mogoods.jpg';
        }
        
        return {
          ...item,
          image: imageUrl,
          selected: true // 默认选中
        };
      });
      
      return {
        success: true,
        data: processedItems,
        message: '获取购物车商品成功'
      };
    } catch (error) {
      console.error('【购物车服务】根据ID获取购物车商品失败:', error);
      return {
        success: false,
        message: '获取购物车商品失败',
        error: error.message,
        data: []
      };
    }
  }
}

module.exports = new CartService();
