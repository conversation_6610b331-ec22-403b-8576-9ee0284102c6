/* pages/partner/contract.wxss */
.contract-container {
  min-height: 100vh;
  background: #fff;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* 合约标题区域 */
.contract-header {
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.contract-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  letter-spacing: 4rpx;
}

/* 合约内容区域 */
.contract-content {
  flex: 1;
  padding: 40rpx 30rpx;
}

.contract-item {
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #ff4d4f;
}

.item-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 底部操作区域 */
.contract-footer {
  padding: 40rpx 30rpx 60rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.footer-notice {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

.contact-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  background: #ff4d4f;
  color: #fff;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
}

.contact-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
} 