.partner-join-container {
  padding: 32rpx 24rpx 0 24rpx;
  background: #fff;
  min-height: 100vh;
}
.form-group {
  margin-bottom: 32rpx;
  position: relative;
}
.form-label {
  font-size: 30rpx;
  color: #222;
  margin-bottom: 12rpx;
}
.form-picker {
  height: 80rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #888;
}
.form-row {
  display: flex;
  gap: 48rpx;
  padding-left: 8rpx;
  padding-right: 8rpx;
}
.form-group.half {
  flex: 1;
  min-width: 0;
}
.form-input {
  width: 100%;
  height: 80rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
  color: #222;
  padding: 0 24rpx;
  margin-top: 0;
  margin-right: 0;
}
.form-btn-row {
  display: flex;
  justify-content: center;
  margin-top: 48rpx;
}
.form-btn {
  width: 240rpx;
  height: 64rpx;
  background: #e54322;
  color: #fff;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: none;
}
.partner-join-bg {
  width: 100vw;
  height: 100vh;
  background: #f7f7f7;
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
}
.selector-modal {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.selector-mask {
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 1;
}
.selector-popup {
  position: relative;
  z-index: 2;
  width: 90vw;
  max-width: 600rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx #ccc;
  padding: 32rpx 24rpx 24rpx 24rpx;
  display: flex;
  flex-direction: column;
}
.selector-input {
  width: 100%;
  height: 64rpx;
  border-radius: 8rpx;
  border: 1rpx solid #eee;
  font-size: 28rpx;
  padding: 0 18rpx;
  margin-bottom: 18rpx;
  background: #f7f7f7;
}
.selector-list {
  max-height: 400rpx;
  min-height: 80rpx;
  overflow-y: auto;
}
.selector-item {
  padding: 18rpx 0;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}
.selector-item:last-child {
  border-bottom: none;
}
.selector-item:active {
  background: #f5f5f5;
}
.selector-empty {
  text-align: center;
  color: #aaa;
  font-size: 26rpx;
  padding: 24rpx 0;
}
.dropdown-list {
  position: absolute;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 0 0 12rpx 12rpx;
  box-shadow: 0 8rpx 32rpx #ccc;
  z-index: 10;
  max-height: 320rpx;
  overflow-y: auto;
  margin-top: -8rpx;
}
.dropdown-item {
  padding: 18rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fff;
}
.dropdown-item:last-child {
  border-bottom: none;
}
.dropdown-item:active {
  background: #f5f5f5;
}
.dropdown-empty {
  text-align: center;
  color: #aaa;
  font-size: 26rpx;
  padding: 24rpx 0;
  background: #fff;
}
/* 加入类型picker弹窗选项字号增大 */
picker .weui-picker__item,
.picker-view .picker-item,
.picker-view .weui-picker__item {
  font-size: 56rpx !important;
}
.type-popup {
  padding: 48rpx 24rpx;
}
.type-item {
  font-size: 56rpx;
  color: #222;
  text-align: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #eee;
}
.type-item:last-child {
  border-bottom: none;
}

/* 投资金额与股份占比上下分隔 */ 
.form-input, .form-picker, .form-input[readonly] {
  padding-left: 24rpx;
  padding-right: 24rpx;
  box-sizing: border-box;
}

/* 加入类型纵向居中显示 */
.form-group.join-type-group {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 120rpx;
}
.form-group.join-type-group .form-input {
  line-height: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  padding-top: 0;
  padding-bottom: 0;
}
.form-input.percent-input {
  position: relative;
  padding-right: 60rpx;
}
.percent-suffix {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
  font-size: 28rpx;
  pointer-events: none;
} 