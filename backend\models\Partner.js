const db = require('../config/db');

class Partner {
  static async create({ user_id, store_id, store_no, type, amount, percent }) {
    const now = Date.now();
    const sql = `INSERT INTO partners (user_id, store_id, store_no, type, amount, percent, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)`;
    const params = [user_id, store_id, store_no, type, amount, percent, now];
    const result = await db.query(sql, params);
    return { success: true, id: result.insertId };
  }

  static async getPartnersByStore(storeNo) {
    try {
      const db = require('../config/db');
      
      const sql = `
        SELECT 
          p.*,
          u.nickname,
          u.avatar,
          u.phone
        FROM partners p
        LEFT JOIN users u ON p.user_id = u.user_id
        WHERE p.store_no = ?
        ORDER BY p.created_at DESC
      `;
      
      const partners = await db.query(sql, [storeNo]);
      
      return {
        success: true,
        data: partners
      };
    } catch (error) {
      console.error('获取门店合伙人列表失败:', error);
      return {
        success: false,
        message: '获取门店合伙人列表失败'
      };
    }
  }
}

module.exports = Partner; 