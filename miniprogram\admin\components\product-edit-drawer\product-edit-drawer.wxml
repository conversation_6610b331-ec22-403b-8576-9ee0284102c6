<view class="edit-drawer-container {{visible ? 'visible' : ''}}" catchtouchmove="preventTouchMove">
  <view class="edit-drawer-mask" bindtap="onCancel" catch:touchmove="preventTouchMove"></view>
  <view class="edit-drawer-panel" catch:touchmove="catchTouchMove">
    <view class="edit-drawer-header">编辑商品</view>
    <view class="edit-drawer-form">
      <view class="form-row">
        <text class="form-label">SKU号</text>
        <input class="form-input" placeholder="请输入SKU号" value="{{sku}}" bindinput="onSkuInput" />
      </view>
      <view class="form-row">
        <text class="form-label">品名</text>
        <input class="form-input" placeholder="请输入品名" value="{{name}}" bindinput="onNameInput" />
      </view>
      <view class="form-row">
        <text class="form-label">主图</text>
        <view class="product-image-upload">
          <image wx:if="{{imageTemp || image}}" class="product-image-preview" src="{{imageTemp || image}}" mode="aspectFill" bindtap="previewImage"></image>
          <view wx:else class="product-image-add-btn" bindtap="chooseImage">
            <image src="/images/icons2/添加图片.png" class="add-image-icon"></image>
            <text class="add-image-text">上传图片</text>
          </view>
          <view wx:if="{{imageTemp || image}}" class="product-image-delete-btn" bindtap="deleteImage">
            <image src="/images/icons2/关闭.png" class="delete-image-icon"></image>
          </view>
        </view>
      </view>
      <view class="form-row">
        <text class="form-label">规格</text>
        <input class="form-input" placeholder="请输入规格" value="{{spec}}" bindinput="onSpecInput" />
      </view>
      <view class="form-row">
        <text class="form-label">平台成本价</text>
        <input class="form-input" type="digit" placeholder="请输入平台成本价" value="{{platform_price}}" bindinput="onPlatformPriceInput" />
      </view>
      <view class="form-row">
        <text class="form-label">门店基准价</text>
        <input class="form-input" type="digit" placeholder="请输入门店基准价" value="{{store_price}}" bindinput="onStorePriceInput" />
      </view>
      <view class="form-row">
        <text class="form-label">零售基准价</text>
        <input class="form-input" type="digit" placeholder="请输入零售基准价" value="{{retail_price}}" bindinput="onRetailPriceInput" />
      </view>
    </view>
    <view class="edit-drawer-footer">
      <button class="footer-btn cancel" bindtap="onCancel">取消</button>
      <button class="footer-btn confirm" bindtap="onConfirm">确定</button>
    </view>
  </view>
</view> 