// pages/splash/splash.js
// 预加载登录状态管理器，确保它被正确初始化
const loginStateManager = require('../../utils/login-state-manager');

Page({
  data: {
    loading: true,
    progress: 0
  },

  onLoad: function (options) {
    // 模拟加载进度
    this.simulateLoading();
  },

  // 模拟加载进度
  simulateLoading: function() {
    let progress = 0;
    const that = this;
    const interval = setInterval(() => {
      progress += 10;
      that.setData({
        progress: progress
      });

      if (progress >= 100) {
        clearInterval(interval);
        that.setData({
          loading: false
        });

        // 检查登录状态
        that.checkLoginStatus();
      }
    }, 200);
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const app = getApp();
    // 检查登录状态

    // 使用登录状态管理器验证登录状态
    loginStateManager.validateLoginState()
      .then(result => {
        // 登录状态验证结果

        if (result.isValid) {
          // 登录状态有效
          app.globalData.userInfo = result.userInfo;
          app.globalData.isLogin = true;
        } else {
          // 登录状态无效或未登录
          // 清除可能存在的无效登录状态
          loginStateManager.clearLoginState();
          app.globalData.userInfo = null;
          app.globalData.isLogin = false;
        }
      })
      .catch(err => {
        // 验证登录状态出错
        // 出错时不影响用户体验，仍然进入首页
      })
      .finally(() => {
        // 无论登录状态如何，都直接进入首页
        this.navigateToHome();
      });
  },

  // 跳转到首页
  navigateToHome: function() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  }
});
