// partner/store-orders/detail.js
// 门店订单详情页面
const { orderApi } = require('../../utils/api');

Page({
  data: {
    // 页面状态
    loading: true,
    error: false,
    errorMsg: '',
    
    // 订单数据
    orderId: null,
    orderType: null,
    orderDetail: null
  },

  onLoad: function(options) {
    console.log('门店订单详情页面加载，参数:', options);
    
    // 获取订单ID和类型
    const orderId = options.id;
    const orderType = options.type || 'purchase';
    
    if (!orderId) {
      this.setData({
        loading: false,
        error: true,
        errorMsg: '订单ID不能为空'
      });
      return;
    }
    
    this.setData({
      orderId,
      orderType
    });
    
    // 加载订单详情
    this.loadOrderDetail();
  },

  /**
   * 加载订单详情
   */
  loadOrderDetail: function() {
    this.setData({
      loading: true,
      error: false
    });
    
    // 这里应该调用API获取订单详情
    // 由于API可能还未实现，这里先模拟一个订单详情数据
    setTimeout(() => {
      // 模拟订单数据
      const orderDetail = this.getMockOrderDetail();
      
      this.setData({
        orderDetail,
        loading: false
      });
    }, 1000);
    
    // 实际API调用应该类似下面的代码
    /*
    orderApi.getStoreOrderDetail({
      orderId: this.data.orderId,
      orderType: this.data.orderType
    }).then(res => {
      console.log('获取订单详情成功:', res);
      
      if (res.success && res.data) {
        this.setData({
          orderDetail: this.processOrderDetail(res.data),
          loading: false
        });
      } else {
        this.setData({
          loading: false,
          error: true,
          errorMsg: res.message || '获取订单详情失败'
        });
      }
    }).catch(err => {
      console.error('获取订单详情异常:', err);
      this.setData({
        loading: false,
        error: true,
        errorMsg: '网络异常，请重试'
      });
    });
    */
  },

  /**
   * 处理订单详情数据
   */
  processOrderDetail: function(data) {
    if (!data) return null;
    
    // 处理订单状态文本
    let statusText = '';
    if (data.order_type === 'purchase') {
      // 采购订单状态
      switch(data.status) {
        case 'pending_review': statusText = '未审核'; break;
        case 'reviewed': statusText = '已审核'; break;
        default: statusText = data.status || '未知状态';
      }
    } else {
      // 移库订单状态
      switch(data.status) {
        case 'pending_shipment': statusText = '未发货'; break;
        case 'shipped': statusText = '已发货'; break;
        case 'received': statusText = '已到店'; break;
        default: statusText = data.status || '未知状态';
      }
    }
    
    // 处理订单项目，计算金额
    const items = data.items || [];
    let totalQuantity = 0;
    
    items.forEach(item => {
      item.amount = (parseFloat(item.price) * parseInt(item.quantity)).toFixed(2);
      totalQuantity += parseInt(item.quantity);
    });
    
    // 格式化日期时间
    const createdAt = data.created_at ? this.formatDateTime(new Date(data.created_at)) : '未知时间';
    const reviewTime = data.review_time ? this.formatDateTime(new Date(data.review_time)) : '';
    const shippedTime = data.shipped_time ? this.formatDateTime(new Date(data.shipped_time)) : '';
    const receivedTime = data.received_time ? this.formatDateTime(new Date(data.received_time)) : '';
    
    return {
      ...data,
      status_text: statusText,
      items: items,
      total_quantity: totalQuantity,
      created_at: createdAt,
      review_time: reviewTime,
      shipped_time: shippedTime,
      received_time: receivedTime
    };
  },

  /**
   * 格式化日期时间
   */
  formatDateTime: function(date) {
    if (!date) return '';
    
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  /**
   * 获取模拟订单详情数据（仅用于开发测试）
   */
  getMockOrderDetail: function() {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    // 根据订单类型返回不同的模拟数据
    if (this.data.orderType === 'purchase') {
      return {
        id: this.data.orderId,
        order_no: 'PO' + this.data.orderId.padStart(8, '0'),
        order_type: 'purchase',
        status: 'pending_review',
        status_text: '未审核',
        created_at: this.formatDateTime(yesterday),
        store_no: 'S001',
        store_name: '上海徐汇店',
        operator_name: '张店长',
        total_amount: '1280.00',
        items: [
          {
            id: '1',
            product_id: '101',
            product_name: '有机红富士苹果',
            product_image: '/images/icons2/默认商品.png',
            specs: '500g/袋',
            price: '15.80',
            quantity: 50,
            amount: '790.00'
          },
          {
            id: '2',
            product_id: '102',
            product_name: '进口香蕉',
            product_image: '/images/icons2/默认商品.png',
            specs: '2.5kg/箱',
            price: '35.00',
            quantity: 14,
            amount: '490.00'
          }
        ],
        total_quantity: 64
      };
    } else {
      return {
        id: this.data.orderId,
        order_no: 'TO' + this.data.orderId.padStart(8, '0'),
        order_type: 'transfer',
        status: 'pending_shipment',
        status_text: '未发货',
        created_at: this.formatDateTime(yesterday),
        store_no: 'S002',
        store_name: '北京朝阳店',
        operator_name: '李经理',
        logistics_status: '待发货',
        total_amount: '960.00',
        items: [
          {
            id: '1',
            product_id: '201',
            product_name: '有机西红柿',
            product_image: '/images/icons2/默认商品.png',
            specs: '1kg/箱',
            price: '12.00',
            quantity: 30,
            amount: '360.00'
          },
          {
            id: '2',
            product_id: '202',
            product_name: '新鲜黄瓜',
            product_image: '/images/icons2/默认商品.png',
            specs: '500g/袋',
            price: '6.00',
            quantity: 100,
            amount: '600.00'
          }
        ],
        total_quantity: 130
      };
    }
  },

  /**
   * 审核订单
   */
  reviewOrder: function() {
    wx.showModal({
      title: '确认审核',
      content: '是否确认审核通过该订单？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          // 模拟API调用
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '审核成功',
              icon: 'success'
            });
            
            // 更新订单状态
            const orderDetail = this.data.orderDetail;
            orderDetail.status = 'reviewed';
            orderDetail.status_text = '已审核';
            orderDetail.review_time = this.formatDateTime(new Date());
            
            this.setData({
              orderDetail: orderDetail
            });
          }, 1500);
        }
      }
    });
  },

  /**
   * 发货
   */
  shipOrder: function() {
    wx.showModal({
      title: '确认发货',
      content: '是否确认该订单已发货？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          // 模拟API调用
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '发货成功',
              icon: 'success'
            });
            
            // 更新订单状态
            const orderDetail = this.data.orderDetail;
            orderDetail.status = 'shipped';
            orderDetail.status_text = '已发货';
            orderDetail.shipped_time = this.formatDateTime(new Date());
            orderDetail.logistics_status = '运输中';
            
            this.setData({
              orderDetail: orderDetail
            });
          }, 1500);
        }
      }
    });
  },

  /**
   * 确认到店
   */
  receiveOrder: function() {
    wx.showModal({
      title: '确认到店',
      content: '是否确认商品已到店？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          // 模拟API调用
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '确认成功',
              icon: 'success'
            });
            
            // 更新订单状态
            const orderDetail = this.data.orderDetail;
            orderDetail.status = 'received';
            orderDetail.status_text = '已到店';
            orderDetail.received_time = this.formatDateTime(new Date());
            orderDetail.logistics_status = '已送达';
            
            this.setData({
              orderDetail: orderDetail
            });
          }, 1500);
        }
      }
    });
  }
}); 