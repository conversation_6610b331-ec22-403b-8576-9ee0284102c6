<view class="store-admin-page">
  <!-- 门店订单页面 -->
  <view wx:if="{{pageType === 'orders'}}" class="store-orders-page">
    <view class="page-header">
      <view class="page-title">门店订单</view>
      <view class="store-info" wx:if="{{storeNo}}">门店编号：{{storeNo}}</view>
    </view>
    <view class="orders-content">
      <view class="orders-placeholder">
        <image class="placeholder-icon" src="/images/icons2/待发货.png" />
        <text class="placeholder-text">门店订单功能开发中</text>
        <text class="placeholder-desc">敬请期待...</text>
      </view>
    </view>
  </view>
  
  <!-- 其他门店管理页面 -->
  <view wx:else>
    <!-- 搜索栏 -->
    <view class="user-mgr-header">
      <view class="user-mgr-search-bar">
        <view class="user-mgr-search-input-container">
          <input class="user-mgr-search-input" placeholder="请输入搜索内容" value="{{searchValue}}" bindinput="onSearchInput" confirm-type="search" bindconfirm="onSearch" />
          <view class="user-mgr-search-btn" bindtap="onSearch">
            <image src="/images/icons2/搜索.png"></image>
          </view>
        </view>
      </view>
    </view>
    <!-- 操作按钮栏 -->
    <view class="user-mgr-actions">
      <button class="user-mgr-btn" bindtap="onSelectAll">创建门店</button>
      <button class="user-mgr-btn user-mgr-btn-disabled"></button>
      <button class="user-mgr-btn" bindtap="onBatchUpgrade">合伙人变更</button>
      <button class="user-mgr-btn" bindtap="onFundManage">资金管理</button>
    </view>
    <!-- 分类标签栏 -->
    <scroll-view class="province-tabs" scroll-x="true" show-scrollbar="false">
      <view class="province-tab-list">
        <block wx:for="{{levelList}}" wx:key="level">
          <view class="province-tab {{selectedLevel === item ? 'active' : ''}}" data-level="{{item}}" bindtap="onLevelTabChange">{{item}}</view>
        </block>
      </view>
    </scroll-view>
    <!-- 排序栏（表头） -->
    <view class="user-mgr-table-header">
      <text class="sortable-header">创建时间</text>
      <text class="sortable-header">门店名称</text>
      <text class="sortable-header">级别</text>
    </view>
    <!-- 门店列表 -->
    <view class="store-list">
      <block wx:for="{{stores}}" wx:key="id">
        <view class="store-mgr-item">
          <!-- 门店级别右上角 -->
          <view class="store-mgr-level">{{item.level_title}}</view>
          <!-- 门店图片更靠左，无复选框 -->
          <image class="store-mgr-img" src="{{item.image}}" />
          <view class="store-mgr-info">
            <view class="store-mgr-title">{{item.name}}</view>
            <view class="store-mgr-no">编号: {{item.store_no}}</view>
            <view class="store-mgr-meta">{{item.address}}</view>
          </view>
          <view class="store-mgr-edit-btn" bindtap="onEditStore" data-index="{{index}}">编辑</view>
        </view>
      </block>
      <view class="user-mgr-footer">
        共计：{{stores.length}}家门店
        <text wx:if="{{selectedCount > 0}}" class="selected-count">（已选中{{selectedCount}}家）</text>
      </view>
      <view class="safe-area"></view>
    </view>
  </view>
  
  <store-create-drawer id="store-create-drawer" visible="{{showCreateDrawer}}" bind:cancel="onCreateDrawerCancel" bind:confirm="onCreateDrawerConfirm" />
  <store-edit-drawer id="store-edit-drawer" visible="{{showEditDrawer}}" storeData="{{currentStore}}" bind:cancel="onEditDrawerCancel" bind:confirm="onEditDrawerConfirm" />
  <admin-tabbar current="store" />
</view>