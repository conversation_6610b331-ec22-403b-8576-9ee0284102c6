// API 环境配置
const API_CONFIG = {
  // 生产环境（云托管公网地址）
  prod: {
    baseUrl: 'https://morebuy25-172172-8-1368182116.sh.run.tcloudbase.com'
  },
  // 新增本地开发环境
  dev: {
    baseUrl: 'http://localhost:3001'
  }
};

// 默认环境
const DEFAULT_ENV = 'prod'; // 使用云托管生产环境

// 获取当前API环境
const getApiEnv = () => {
  // 检测是否在微信开发者工具环境
  if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
    const sysInfo = wx.getSystemInfoSync();
    if (sysInfo.platform === 'devtools') {
      // 开发者工具环境，检查是否有手动设置的环境
      const manualEnv = wx.getStorageSync('apiEnv');
      if (manualEnv) {
        console.log('开发者工具使用手动设置的API环境:', manualEnv);
        return manualEnv;
      }
      return 'prod'; // 默认走云托管API
    }
  }
  return wx.getStorageSync('apiEnv') || DEFAULT_ENV;
};

// 当前环境
let CURRENT_ENV = getApiEnv();

// API 基础 URL - 根据环境返回
function getBaseUrl() {
  return API_CONFIG[CURRENT_ENV]?.baseUrl || API_CONFIG.prod.baseUrl;
}

// 设置API环境
const setApiEnv = (env) => {
  if (!API_CONFIG[env]) {
    return false;
  }

  // 更新当前环境
  wx.setStorageSync('apiEnv', env);

  // 需要刷新页面以应用新的BASE_URL
  wx.showModal({
    title: '环境已切换',
    content: `API环境已切换为: ${env}，需要重启小程序以应用更改`,
    showCancel: false
  });

  return true;
};

// 云托管相关配置
// const envId = 'prod-4g3qet1k59f2d66f'; // 微信云托管环境ID
const serviceName = 'morebuy25'; // 云托管服务名

// 引入统一的请求方法
const request = require('./request');

// 请求方法包装器，适配原有的API调用方式
const apiRequest = (url, method = 'GET', data = {}, requireAuth = true) => {
  return request({
    url,
    method,
    data,
    requireAuth
  });
};

// 商品相关 API
const productApi = {
  // 获取商品列表
  getProducts: (params = {}) => {
    return apiRequest('/api/products', 'GET', params, false);
  },

  // 获取商品详情
  getProductById: (id) => {
    console.log('API调用: getProductById, id:', id, 'id类型:', typeof id);
    return apiRequest(`/api/products/${id}`, 'GET', {}, false);
  },

  // 获取商品分类
  getCategories: () => {
    return apiRequest('/api/products/categories', 'GET', {}, false);
  },

  // 获取商城分类
  getShopCategories: () => {
    return apiRequest('/api/products/shop/categories', 'GET', {}, false);
  },

  // 获取商城子分类
  getShopSubCategories: (parentId) => {
    return apiRequest('/api/products/shop/subcategories', 'GET', { parentId }, false);
  },

  // 获取轮播图
  getBanners: (page_type = 'customer_home') => {
    return apiRequest('/api/banners', 'GET', { page_type }, false);
  },

  // 获取热门商品
  getHotProducts: () => {
    return apiRequest('/api/products/hot', 'GET', {}, false);
  },

  // 获取新品推荐
  getNewProducts: () => {
    return apiRequest('/api/products/new', 'GET', {}, false);
  },

  // 批量更新商品状态
  batchUpdateStatus: (ids, status) => {
    return apiRequest('/api/products/batch-status', 'POST', { ids, status });
  },

  // 更新商品信息
  updateProduct: (id, data) => {
    return apiRequest(`/api/products/${id}`, 'PUT', data);
  }
};

// 购物车相关 API
const cartApi = {
  // 获取购物车商品
  getCartItems: () => {
    return apiRequest('/api/cart', 'GET', {}, true);
  },

  // 根据ID获取购物车商品
  getCartItemsByIds: (ids) => {
    return apiRequest('/api/cart/byIds', 'GET', { ids }, true);
  },

  // 添加商品到购物车
  addToCart: (productId, quantity = 1) => {
    return apiRequest('/api/cart', 'POST', { productId, quantity }, true);
  },

  // 更新购物车商品
  updateCartItem: (id, quantity) => {
    return apiRequest(`/api/cart/${id}`, 'PUT', { quantity }, true);
  },

  // 删除购物车商品
  removeCartItem: (id) => {
    return apiRequest(`/api/cart/${id}`, 'DELETE', {}, true);
  },

  // 批量删除购物车商品
  batchRemoveCartItems: (ids) => {
    return apiRequest('/api/cart/batch', 'DELETE', { ids }, true);
  },

  // 删除多个购物车商品
  removeCartItems: (ids) => {
    return apiRequest('/api/cart/remove', 'DELETE', { ids }, true);
  }
};

// 收藏相关 API
const favoriteApi = {
  // 获取收藏列表
  getFavorites: () => {
    return apiRequest('/api/favorites', 'GET', {}, true);
  },

  // 添加到收藏夹
  addToFavorites: (productId) => {
    return apiRequest('/api/favorites', 'POST', { productId }, true);
  },

  // 从收藏夹移除
  removeFromFavorites: (productId) => {
    return apiRequest('/api/favorites', 'DELETE', { productId }, true);
  },

  // 检查收藏状态
  checkFavoriteStatus: (productId) => {
    return apiRequest('/api/favorites/status', 'GET', { productId }, true);
  },

  // 获取收藏数量
  getFavoriteCount: () => {
    return apiRequest('/api/favorites/count', 'GET', {}, true);
  }
};

// 用户相关 API
const userApi = {
  // 用户登录
  login: (code) => {
    return apiRequest('/api/user/login', 'POST', { code });
  },
  // 手机号登录
  loginByPhone: (phone, code) => {
    // 获取推荐人ID
    const referrerId = wx.getStorageSync('referrerId');
    console.log('API调用 - loginByPhone 参数:', { phone, code, referrerId });
    // 将推荐人ID添加到请求参数中，如果存在
    return apiRequest('/api/users/login/phone', 'POST', { 
      phone, 
      code, 
      referrerId: referrerId || undefined 
    }, false).then(res => { // 设置requireAuth为false，允许未登录用户进行登录
      if (res.success && res.data && res.data.token) {
        wx.setStorageSync('token', res.data.token);
        if (res.data.user || res.data.userInfo) {
          wx.setStorageSync('userInfo', res.data.user || res.data.userInfo);
        }
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.isLogin = true;
          app.globalData.userInfo = res.data.user || res.data.userInfo;
          app.globalData.token = res.data.token; // 新增：同步全局token
        }
      }
      return res;
    });
  },

  // 账号密码登录
  loginByAccount: (username, password) => {
    console.log('API调用 - loginByAccount 参数:', { username, password: '***' });
    // 确保使用正确的路径
    return apiRequest('/api/users/login', 'POST', { username, password }, false).then(res => { // 设置requireAuth为false，允许未登录用户进行登录
      if (res.success && res.data && res.data.token) {
        wx.setStorageSync('token', res.data.token);
        if (res.data.user || res.data.userInfo) {
          wx.setStorageSync('userInfo', res.data.user || res.data.userInfo);
        }
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.isLogin = true;
          app.globalData.userInfo = res.data.user || res.data.userInfo;
          app.globalData.token = res.data.token; // 新增：同步全局token
        }
      }
      return res;
    });
  },

  // 微信登录
  loginByWechat: (code) => {
    return apiRequest('/api/users/login/wechat', 'POST', { code }, false).then(res => { // 设置requireAuth为false，允许未登录用户进行微信登录
      if (res.success && res.data && res.data.token) {
        wx.setStorageSync('token', res.data.token);
        if (res.data.user || res.data.userInfo) {
          wx.setStorageSync('userInfo', res.data.user || res.data.userInfo);
        }
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.isLogin = true;
          app.globalData.userInfo = res.data.user || res.data.userInfo;
          app.globalData.token = res.data.token; // 新增：同步全局token
        }
      }
      return res;
    });
  },

  // 获取用户信息
  getUserInfo: (userId) => {
    // 兼容后端有的用id，有的用userId
    let url = '/api/users/info';
    if (userId) {
      url += userId.length > 10 ? `?userId=${userId}` : `?id=${userId}`;
    }
    return apiRequest(url);
  },

  // 获取用户钱包信息
  getUserWallet: () => {
    return apiRequest('/api/users/wallet');
  },

  // 获取用户资金变动记录
  getUserFundRecords: (limit = 20, offset = 0) => {
    return apiRequest('/api/users/fund-records', 'GET', { limit, offset });
  },

  // 获取用户统计信息
  getUserStats: () => {
    return apiRequest('/api/users/stats');
  },

  // 发送验证码
  sendVerifyCode: (phone) => {
    console.log('API调用 - sendVerifyCode 参数:', { phone });
    return apiRequest('/api/users/send-code', 'POST', { phone }, false); // 设置requireAuth为false，允许未登录用户获取验证码
  },

  // 更新用户信息
  updateUserInfo: (data) => {
    console.log('API调用 - updateUserInfo 参数:', data);
    // 添加调试信息
    const token = wx.getStorageSync('token');
    console.log('更新用户信息时的令牌:', token ? token.substring(0, 10) + '...' : '未提供');
    // 确保使用正确的路径
    return apiRequest('/api/users/info', 'PUT', data)
      .then(res => {
        console.log('更新用户信息响应:', res);
        return res;
      })
      .catch(err => {
        console.error('更新用户信息失败:', err);
        throw err;
      });
  },

  // 管理员更新用户信息（包括余额调整）
  adminUpdateUser: (userId, data) => {
    console.log('API调用 - adminUpdateUser 参数:', { userId, data });
    return apiRequest(`/api/users/admin/${userId}`, 'PUT', data)
      .then(res => {
        console.log('管理员更新用户信息响应:', res);
        return res;
      })
      .catch(err => {
        console.error('管理员更新用户信息失败:', err);
        throw err;
      });
  },

  // 更新密码
  updatePassword: (oldPassword, newPassword) => {
    console.log('API调用 - updatePassword', { oldPassword: '***', newPassword: '***' });
    // 添加调试信息
    const token = wx.getStorageSync('token');
    console.log('修改密码时的令牌:', token ? token.substring(0, 10) + '...' : '未提供');
    return apiRequest('/api/users/password', 'PUT', { oldPassword, newPassword })
      .then(res => {
        console.log('修改密码响应:', res);
        return res;
      })
      .catch(err => {
        console.error('修改密码请求失败:', err);
        throw err;
      });
  },

  // 绑定微信
  bindWechat: (code, userInfo) => {
    console.log('API调用 - bindWechat');
    return apiRequest('/api/users/bind/wechat', 'POST', { code, userInfo });
  },

  // 解绑微信
  unbindWechat: () => {
    console.log('API调用 - unbindWechat');
    return apiRequest('/api/users/unbind/wechat', 'POST');
  },

  // 通过手机号重置密码
  resetPasswordByPhone: (phone, code, newPassword) => {
    console.log('API调用 - resetPasswordByPhone 参数:', { phone, code: '***', newPassword: '***' });
    return apiRequest('/api/users/reset-password', 'POST', { phone, code, newPassword }, false) // 设置requireAuth为false，允许未登录用户重置密码
      .then(res => {
        console.log('重置密码响应:', res);
        return res;
      })
      .catch(err => {
        console.error('重置密码请求失败:', err);
        throw err;
      });
  },

  // 获取我的推广用户列表
  getMyPromotionList: () => {
    return apiRequest('/api/users/my-promotion');
  },

  // 获取用户多重身份
  getUserRoles: () => {
    return apiRequest('/api/users/roles', 'GET', {}, true);
  },

  // 获取用户列表（管理端）
  getUserList: (params = {}) => {
    // 兼容旧的调用方式（传入字符串）
    if (typeof params === 'string') {
      params = params ? { search: params } : {};
    }
    return apiRequest('/api/users/list', 'GET', params, false);
  },

  // 批量修改用户状态（管理端）
  batchUpdateStatus: (ids, status) => {
    return apiRequest('/api/users/batch-status', 'POST', { ids, status }, false);
  },

  // 批量冻结用户（管理端）
  batchFreezeUsers: (ids) => {
    return apiRequest('/api/users/batch-status', 'POST', { ids, status: '冻结' }, false);
  },

  // 批量解冻用户（管理端）
  batchUnfreezeUsers: (ids) => {
    return apiRequest('/api/users/batch-status', 'POST', { ids, status: '正常' }, false);
  },

  // 获取默认收货地址
  getDefaultAddress: () => {
    return apiRequest('/api/users/address/default', 'GET', {}, true);
  },

  // 获取收货地址列表
  getAddressList: () => {
    return apiRequest('/api/users/address/list', 'GET', {}, true);
  },

  // 添加收货地址
  addAddress: (addressData) => {
    return apiRequest('/api/users/address', 'POST', addressData, true);
  },

  // 更新收货地址
  updateAddress: (id, addressData) => {
    return apiRequest(`/api/users/address/${id}`, 'PUT', addressData, true);
  },

  // 删除收货地址
  deleteAddress: (id) => {
    return apiRequest(`/api/users/address/${id}`, 'DELETE', {}, true);
  },

  // 设置默认收货地址
  setDefaultAddress: (id) => {
    return apiRequest(`/api/users/address/${id}/default`, 'PUT', {}, true);
  },

  // 获取用户订阅门店
  getSubscribedStore: () => {
    return apiRequest('/api/users/store/subscribed', 'GET', {}, true);
  },

  // 获取销售人名下的门店列表
  getSalesmanStores: () => {
    return apiRequest('/api/users/store/salesman', 'GET', {}, true);
  }
};



// 消息相关 API
const messageApi = {
  // 获取消息
  getMessages: (params = {}) => {
    console.log('API调用 - getMessages 参数:', params);

    // 如果有targetUserId参数，说明是获取与特定用户的聊天记录
    if (params.targetUserId) {
      // 修改为使用正确的路由
      return apiRequest('/api/message/list', 'GET', params);
    } else {
      // 否则获取所有消息
      return apiRequest('/api/message/list', 'GET', params);
    }
  },

  // 删除消息
  deleteMessage: (id) => {
    return apiRequest(`/api/messages/${id}`, 'DELETE');
  },

  // 标记消息为已读
  markMessageRead: (id) => {
    return apiRequest(`/api/messages/${id}/read`, 'PUT');
  },

  // 发送消息
  sendMessage: (receiverId, content, type = 'text') => {
    console.log('API调用 - sendMessage 参数:', { receiverId, content, type });
    // 修改为使用正确的路由
    return apiRequest('/api/message/send', 'POST', { receiverId, content, type });
  },

  // 获取最近聊天列表
  getRecentChats: () => {
    // 修改为使用正确的路由
    return apiRequest('/api/message/recent', 'GET');
  }
};

// 群聊相关API
const groupApi = {
  // 获取我的群聊列表
  getMyGroups: function() {
    return apiRequest('/api/group/my-groups', 'GET');
  },

  // 获取公开群聊列表
  getPublicGroups: function(page = 1, pageSize = 20) {
    return apiRequest('/api/group/public-groups', 'GET', { page, pageSize });
  },

  // 创建群聊
  createGroup: function(data) {
    return apiRequest('/api/group', 'POST', data);
  },

  // 获取群组详情
  getGroupDetail: function(groupId) {
    return apiRequest(`/api/group/${groupId}`, 'GET');
  },

  // 获取群组成员列表
  getGroupMembers: function(groupId) {
    return apiRequest(`/api/group/${groupId}/members`, 'GET');
  },

  // 加入群组
  joinGroup: function(groupId) {
    return apiRequest(`/api/group/${groupId}/join`, 'POST');
  },

  // 退出群组
  leaveGroup: function(groupId) {
    return apiRequest(`/api/group/${groupId}/leave`, 'POST');
  },

  // 获取群消息历史
  getGroupMessages: function(groupId, page = 1, pageSize = 20) {
    return apiRequest(`/api/group/${groupId}/messages`, 'GET', { page, pageSize });
  },

  // 发送群消息
  sendGroupMessage: function(groupId, content, type = 'text') {
    return apiRequest(`/api/group/${groupId}/messages`, 'POST', { content, type });
  },

  // 更新群公告
  updateAnnouncement: function(groupId, announcement) {
    return apiRequest(`/api/group/${groupId}/announcement`, 'PUT', { announcement });
  },

  // 更新群组信息
  updateGroupInfo: function(groupId, updateData) {
    return apiRequest(`/api/group/${groupId}/info`, 'PUT', updateData);
  }
};

// 积分相关 API
const pointsApi = {
  // 获取积分规则和获取方式
  getPointsConfig: () => {
    return apiRequest('/api/points/config');
  },
  // 获取用户积分
  getUserPoints: (userId) => {
    const url = userId ? `/api/points/user?userId=${userId}` : '/api/points/user';
    console.log('积分API请求URL:', url);
    return apiRequest(url);
  },
  // 获取用户积分记录
  getUserPointsRecords: (params = {}) => {
    const { userId, limit = 10, offset = 0 } = params;
    let url = `/api/points/user/records?limit=${limit}&offset=${offset}`;
    if (userId) {
      url += `&userId=${userId}`;
    }
    console.log('积分记录API请求URL:', url);
    return apiRequest(url);
  },
  // 更新用户积分
  updateUserPoints: (changeAmount, event) => {
    console.log('更新积分API请求:', { changeAmount, event });
    return apiRequest('/api/points/user/update', 'POST', { changeAmount, event });
  }
};

// VIP会员相关 API
const vipApi = {
  // 获取会员等级列表
  getVipLevels: () => {
    return apiRequest('/api/vip/levels');
  },

  // 获取会员权益
  getVipBenefits: (levelCode) => {
    return apiRequest(`/api/vip/benefits${levelCode ? `?levelCode=${levelCode}` : ''}`);
  },

  // 获取用户会员信息
  getUserVipInfo: (userId) => {
    return apiRequest(`/api/vip/user${userId ? `?userId=${userId}` : ''}`);
  },

  // 获取会员产品列表
  getVipProducts: () => {
    return apiRequest('/api/vip/products');
  },

  // 更新用户会员信息
  updateUserVip: (data) => {
    return apiRequest('/api/vip/user/update', 'POST', data);
  }
};

// 门店相关 API
const storeApi = {
  // 创建门店
  createStore: (data) => {
    return apiRequest('/api/admin/store/create', 'POST', data, true);
  },
  // 获取门店列表
  getStores: () => {
    return apiRequest('/api/admin/store/list', 'GET');
  },
  // 门店模糊搜索
  searchStores: (keyword) => {
    return apiRequest('/api/admin/store/list', 'GET', { search: keyword });
  },
  // 更新门店信息
  updateStore: (id, data) => {
    return apiRequest(`/api/admin/store/update/${id}`, 'PUT', data, true);
  },
  // 获取门店库存商品列表
  getInventoryProducts: (params) => {
    return apiRequest('/api/store/inventory', 'GET', params, true);
  },
  // 更新门店商品库存
  updateInventory: (storeNo, productId, quantity) => {
    return apiRequest('/api/store/inventory/update', 'POST', {
      storeNo,
      productId,
      quantity
    }, true);
  },
  // 批量更新门店商品库存
  batchUpdateInventory: (storeNo, items) => {
    return apiRequest('/api/store/inventory/batch-update', 'POST', {
      storeNo,
      items
    }, true);
  },
  // 根据门店编号获取门店信息
  getStoreByNo: (storeNo) => {
    return apiRequest('/api/store/by-no', 'GET', { store_no: storeNo }, true);
  },
  // 获取我的门店列表
  getMyStores: () => {
    return apiRequest('/api/partner/stores', 'GET', {}, true);
  }
};

// 资金相关 API
const fundApi = {
  // 获取门店资金变动记录
  getFundRecords: (store_id, limit = 50, offset = 0) => {
    return apiRequest('/api/admin/fund/records', 'GET', { store_id, limit, offset });
  },
  // 新增资金变动记录
  createFundRecord: (data) => {
    return apiRequest('/api/admin/fund/create', 'POST', data);
  }
};

// 门店资金相关 API
const storeFundApi = {
  // 获取门店资金信息
  getStoreFunds: (storeNo) => {
    return apiRequest(`/api/store/funds/${storeNo}`, 'GET', {}, true);
  },

  // 记录门店资金变动
  createFundRecord: (data) => {
    return apiRequest('/api/store/funds/record', 'POST', data, true);
  },

  // 获取门店资金变动记录
  getFundRecords: (storeNo, limit = 20, offset = 0) => {
    return apiRequest(`/api/store/funds/records/${storeNo}`, 'GET', { limit, offset }, true);
  }
};

// 订单相关 API
const orderApi = {
  // 创建订单
  createOrder: (orderData) => {
    console.log('调用创建订单API, 参数:', orderData);
    return apiRequest('/api/orders/create', 'POST', orderData, true)
      .then(res => {
        console.log('创建订单API响应:', res);
        return res;
      })
      .catch(err => {
        console.error('创建订单API错误:', err);
        throw err;
      });
  },

  // 获取订单列表
  getOrders: (params = {}) => {
    console.log('调用获取订单列表API, 参数:', params);
    return apiRequest('/api/orders', 'GET', params, true)
      .then(res => {
        console.log('获取订单列表API响应:', res);
        // 处理返回数据格式，确保前端能正确显示
        if (res.success && res.data) {
          // 如果返回的是数组，包装成带total的对象
          if (Array.isArray(res.data)) {
            return {
              success: true,
              data: {
                list: res.data,
                total: res.data.length
              }
            };
          }
          // 如果已经是正确格式，直接返回
          return res;
        }
        return res;
      })
      .catch(err => {
        console.error('获取订单列表API错误:', err);
        throw err;
      });
  },

  // 获取订单详情
  getOrderById: (orderId) => {
    console.log('调用获取订单详情API, orderId:', orderId);

    // 检查认证状态
    const token = wx.getStorageSync('token');
    console.log('API调用前检查 - token存在:', !!token);

    return apiRequest(`/api/orders/${orderId}`, 'GET', {}, true);
  },

  // 更新订单状态
  updateOrderStatus: (orderId, status) => {
    console.log('调用更新订单状态API, orderId:', orderId, 'status:', status);
    return apiRequest(`/api/orders/${orderId}/status`, 'PUT', { status }, true);
  },
  
  // 取消订单
  cancelOrder: (orderId) => {
    console.log('调用取消订单API, orderId:', orderId);
    return apiRequest(`/api/orders/${orderId}/cancel`, 'POST', {}, true);
  },
  
  // 确认收货
  confirmReceipt: (orderId) => {
    console.log('调用确认收货API, orderId:', orderId);
    return apiRequest(`/api/orders/${orderId}/confirm`, 'POST', {}, true);
  },

  // 删除订单
  deleteOrder: (orderId) => {
    console.log('调用删除订单API, orderId:', orderId);
    return apiRequest(`/api/orders/${orderId}`, 'DELETE', {}, true);
  },
  
  // 申请退款
  applyRefund: (orderId, data) => {
    console.log('调用申请退款API, orderId:', orderId, 'data:', data);
    return apiRequest(`/api/orders/${orderId}/refund`, 'POST', data, true);
  },
  
  // 查看退款进度
  getRefundDetail: (orderId) => {
    console.log('调用查看退款进度API, orderId:', orderId);
    return apiRequest(`/api/orders/${orderId}/refund`, 'GET', {}, true);
  },
  
  // 获取物流信息
  getLogistics: (orderId) => {
    console.log('调用获取物流信息API, orderId:', orderId);
    return apiRequest(`/api/orders/${orderId}/logistics`, 'GET', {}, true);
  },
  
  // 评价订单
  rateOrder: (orderId, data) => {
    console.log('调用评价订单API, orderId:', orderId, 'data:', data);
    return apiRequest(`/api/orders/${orderId}/rate`, 'POST', data, true);
  },

  // 支付订单
  payOrder: (data) => {
    console.log('调用支付订单API, data:', data);
    const { order_id, payment_method } = data;
    return apiRequest(`/api/orders/${order_id}/pay`, 'POST', { payment_method }, true);
  },

  // 合伙人端获取顾客订单列表
  getPartnerCustomerOrders: (params = {}) => {
    console.log('调用合伙人端获取顾客订单API, 参数:', params);
    return apiRequest('/api/partner/customer-orders', 'GET', params, true)
      .then(res => {
        console.log('合伙人端获取顾客订单API响应:', res);
        
        // 确保返回正确的数据格式
        if (res && res.success && res.data) {
          return {
            success: true,
            data: {
              orders: res.data.orders || [],
              total: res.data.total || 0,
              page: res.data.page || 1,
              limit: res.data.limit || 10,
              totalPages: res.data.totalPages || 1
            }
          };
        }
        
        // 处理错误响应
        return {
          success: false,
          message: res && res.message ? res.message : '获取订单数据失败',
          data: {
            orders: [],
            total: 0,
            page: 1,
            limit: 10,
            totalPages: 1
          }
        };
      })
      .catch(err => {
        console.error('合伙人端获取顾客订单API错误:', err);
        return {
          success: false,
          message: '网络异常，请重试',
          data: {
            orders: [],
            total: 0,
            page: 1,
            limit: 10,
            totalPages: 1
          }
        };
      });
  },
  
  // 获取门店订单列表
  getStoreOrders: (params = {}) => {
    console.log('调用获取门店订单API, 参数:', params);
    return apiRequest('/api/partner/store-orders', 'GET', params, true)
      .then(res => {
        console.log('获取门店订单API响应:', res);
        
        // 确保返回正确的数据格式
        if (res && res.success && res.data) {
          return {
            success: true,
            data: {
              orders: res.data.orders || [],
              total: res.data.total || 0,
              page: res.data.page || 1,
              limit: res.data.limit || 10,
              totalPages: res.data.totalPages || 1
            }
          };
        }
        
        // 处理错误响应
        return {
          success: false,
          message: res && res.message ? res.message : '获取订单数据失败',
          data: {
            orders: [],
            total: 0,
            page: 1,
            limit: 10,
            totalPages: 1
          }
        };
      })
      .catch(err => {
        console.error('获取门店订单API错误:', err);
        return {
          success: false,
          message: '网络异常，请重试',
          data: {
            orders: [],
            total: 0,
            page: 1,
            limit: 10,
            totalPages: 1
          }
        };
      });
  }
};

// 管理端API
const adminApi = {
  // 轮播图管理
  getBanners: (params = {}) => {
    return apiRequest('/api/admin/banners', 'GET', params, true);
  },
  
  createBanner: (data) => {
    return apiRequest('/api/admin/banners', 'POST', data, true);
  },
  
  updateBanner: (id, data) => {
    return apiRequest(`/api/admin/banners/${id}`, 'PUT', data, true);
  },
  
  deleteBanner: (id) => {
    return apiRequest(`/api/admin/banners/${id}`, 'DELETE', {}, true);
  },
  
  updateBannerStatus: (id, is_active) => {
    return apiRequest(`/api/admin/banners/${id}/status`, 'PATCH', { is_active }, true);
  },
  
  // 公司信息管理
  getCompanyInfo: () => {
    return apiRequest('/api/company/info', 'GET', {}, true);
  },
  
  createCompanyInfo: (data) => {
    return apiRequest('/api/company/create', 'POST', data, true);
  },
  
  updateCompanyInfo: (id, data) => {
    return apiRequest(`/api/company/update/${id}`, 'PUT', data, true);
  },

  // 消息管理
  getMessages: (params = {}) => {
    return apiRequest('/api/admin/messages', 'GET', params, true);
  },
  
  toggleMessageBlock: (id, isBlocked) => {
    return apiRequest(`/api/admin/messages/${id}/block`, 'PATCH', { is_blocked: isBlocked }, true);
  },
  
  deleteMessage: (id) => {
    return apiRequest(`/api/admin/messages/${id}`, 'DELETE', {}, true);
  },

  // 常见问题管理
  getFaqList: () => {
    return apiRequest('/api/admin/faq', 'GET', {}, true);
  },
  
  createFaq: (data) => {
    return apiRequest('/api/admin/faq', 'POST', data, true);
  },
  
  updateFaq: (id, data) => {
    return apiRequest(`/api/admin/faq/${id}`, 'PUT', data, true);
  },
  
  deleteFaq: (id) => {
    return apiRequest(`/api/admin/faq/${id}`, 'DELETE', {}, true);
  },
  
  updateFaqStatus: (id, is_active) => {
    return apiRequest(`/api/admin/faq/${id}/status`, 'PATCH', { is_active }, true);
  }
};

// 设置相关 API
const settingsApi = {
  // 获取公司联系方式
  getCompanyContact: () => {
    // 先尝试获取第一条记录，如果失败则尝试获取列表
    // 明确设置requireAuth为false，因为公司信息是公开的，不需要登录验证
    return apiRequest('/api/company/info', 'GET', {}, false)
      .catch(err => {
        console.log('尝试获取公司信息失败，尝试获取列表:', err);
        return apiRequest('/api/company/list', 'GET', {}, false).then(res => {
          if (res.success && res.data && res.data.length > 0) {
            return {
              success: true,
              data: res.data[0] // 返回第一条记录
            };
          }
          throw new Error('没有找到公司信息');
        });
      });
  },

  // 提交客服留言
  submitMessage: (data) => {
    return apiRequest('/api/system/feedback', 'POST', data, true); // 明确设置requireAuth为true，确保需要登录验证
  },

  // 获取常见问题列表
  getFaqList: () => {
    return apiRequest('/api/faq/list', 'GET', {}, false); // 公开接口，不需要登录验证
  }
};

// 重置API配置到默认值
const resetApiConfig = () => {
  // 重置API环境为生产环境
  setApiEnv('prod');

  console.log('API配置已重置: 环境=prod');

  return {
    env: 'prod'
  };
};

/**
 * 上传单个文件到微信云存储，返回Promise<fileID>
 * @param {string} filePath 本地临时文件路径
 * @param {string} [dir] 可选，上传到云存储的目录，默认group_covers
 * @returns {Promise<string>} 云存储fileID
 */
const uploadFile = (filePath, dir = 'group_covers') => {
  return new Promise((resolve, reject) => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    const fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
    const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    const cloudPath = `${dir}/${timestamp}_${random}.${fileExt}`;
    // 用getApp()获取全局实例
    const appInstance = getApp ? getApp() : null;
    const cloudEnvId = appInstance && appInstance.globalData && appInstance.globalData.cloudEnvId
      ? appInstance.globalData.cloudEnvId
      : 'prod-4g3qet1k59f2d66f';
    wx.cloud.uploadFile({
      cloudPath,
      filePath,
      config: { env: cloudEnvId },
      success: res => {
        if (res.fileID) {
          resolve(res.fileID);
        } else {
          reject(new Error('上传失败'));
        }
      },
      fail: err => {
        reject(err);
      }
    });
  });
};

const partnerApi = {
  joinPartner: (data) => apiRequest('/api/partner/join', 'POST', data, true),
  getPartnersByStoreId: (storeNo) => {
    console.log('[API] 调用getPartnersByStoreId，门店编号:', storeNo);
    return apiRequest('/api/partner/list', 'GET', { store_no: storeNo }, true).then(res => {
      console.log('[API] getPartnersByStoreId响应:', res);
      return res;
    }).catch(err => {
      console.error('[API] getPartnersByStoreId错误:', err);
      throw err;
    });
  },
  getStorePartners: (storeId) => {
    console.log('[API] 调用getStorePartners，门店ID:', storeId);
    return apiRequest('/api/partner/store-partners', 'GET', { store_id: storeId }, true).then(res => {
      console.log('[API] getStorePartners响应:', res);
      return res;
    }).catch(err => {
      console.error('[API] getStorePartners错误:', err);
      throw err;
    });
  },
  // 合伙人统计相关API
  getPartnerStats: () => {
    console.log('调用合伙人统计API...');
    return apiRequest('/api/partner/stats', 'GET', {}, true).then(res => {
      console.log('合伙人统计API响应:', res);
      return res;
    }).catch(err => {
      console.error('合伙人统计API错误:', err);
      throw err;
    });
  },
  getFundRecords: (limit = 20, offset = 0) => {
    console.log('调用资金记录API...');
    return apiRequest('/api/partner/fund-records', 'GET', { limit, offset }, true).then(res => {
      console.log('资金记录API响应:', res);
      return res;
    }).catch(err => {
      console.error('资金记录API错误:', err);
      throw err;
    });
  },
  getOrderStats: () => {
    console.log('调用订单统计API...');
    return apiRequest('/api/partner/order-stats', 'GET', {}, true).then(res => {
      console.log('订单统计API响应:', res);
      return res;
    }).catch(err => {
      console.error('订单统计API错误:', err);
      throw err;
    });
  },
  getPartnerStores: () => {
    console.log('调用门店列表API...');
    return apiRequest('/api/partner/stores', 'GET', {}, true).then(res => {
      console.log('门店列表API响应:', res);
      return res;
    }).catch(err => {
      console.error('门店列表API错误:', err);
      throw err;
    });
  },
  getPartnerJoinedStores: () => {
    console.log('调用合伙人加入的门店列表API...');
    return apiRequest('/api/partner/joined-stores', 'GET', {}, true).then(res => {
      console.log('合伙人加入的门店列表API响应:', res);
      return res;
    }).catch(err => {
      console.error('合伙人加入的门店列表API错误:', err);
      throw err;
    });
  },
  // 合伙人申请相关API
  applyPartner: (data) => {
    console.log('提交合伙人申请:', data);
    return apiRequest('/api/partner/apply', 'POST', data, true).then(res => {
      console.log('申请提交响应:', res);
      return res;
    }).catch(err => {
      console.error('申请提交错误:', err);
      throw err;
    });
  },
  getMyApplications: () => {
    return apiRequest('/api/partner/applications/my', 'GET', {}, true);
  },
  // 管理员接口
  getAllApplications: (params = {}) => {
    return apiRequest('/api/partner/applications', 'GET', params, true);
  },
  getApplicationDetail: (id) => {
    return apiRequest(`/api/partner/applications/${id}`, 'GET', {}, true);
  },
  reviewApplication: (id, data) => {
    return apiRequest(`/api/partner/applications/${id}/review`, 'PUT', data, true);
  }
};

const dashboardApi = {
  getDashboardSummary: () => apiRequest('/api/admin/dashboard/summary', 'GET', {}, true),
  getDashboardBadges: () => apiRequest('/api/admin/dashboard/badges', 'GET', {}, true)
};

module.exports = {
  productApi,
  cartApi,
  favoriteApi,
  userApi,
  messageApi,
  groupApi,
  pointsApi,
  vipApi,
  settingsApi,
  storeApi,
  partnerApi, // 新增导出
  fundApi, // 新增导出
  dashboardApi, // 新增导出
  orderApi, // 新增导出
  storeFundApi, // 新增导出
  adminApi, // 新增导出
  // 导出API环境控制函数
  setApiEnv,
  getApiEnv,
  // 导出环境配置
  API_CONFIG,
  // 导出重置函数
  resetApiConfig,
  uploadFile, // 新增导出
  getBaseUrl, // 新增导出
  apiRequest   // 新增导出，解决组件 require 取不到问题
};
