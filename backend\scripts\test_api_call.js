/**
 * 测试公司信息API调用
 * 模拟前端完整的API调用流程
 */

const axios = require('axios');

// 配置API基础URL（根据实际情况调整）
const API_BASE_URL = 'http://localhost:3001';

async function testCompanyInfoAPI() {
  console.log('🧪 开始测试公司信息API调用...\n');

  try {
    // 1. 首先测试获取公司信息
    console.log('📋 测试获取公司信息...');
    const getResponse = await axios.get(`${API_BASE_URL}/api/company/info`);
    console.log('获取公司信息响应状态:', getResponse.status);
    console.log('获取公司信息响应数据:', JSON.stringify(getResponse.data, null, 2));

    const companyInfo = getResponse.data.data;
    if (!companyInfo || !companyInfo.id) {
      console.log('❌ 没有找到公司信息，无法进行更新测试');
      return;
    }

    // 2. 模拟前端的更新数据
    const updateData = {
      ...companyInfo,
      company_description: '测试API更新 - ' + new Date().toLocaleString(),
      company_email: '<EMAIL>',
      company_website: 'https://api-test.example.com'
    };

    console.log('\n📝 准备更新的数据:');
    console.log(JSON.stringify(updateData, null, 2));

    // 3. 测试不带认证的更新请求（应该失败）
    console.log('\n🔄 测试不带认证的更新请求...');
    try {
      const updateResponse = await axios.put(
        `${API_BASE_URL}/api/company/update/${companyInfo.id}`,
        updateData
      );
      console.log('❌ 不带认证的请求竟然成功了，这不应该发生');
    } catch (error) {
      console.log('✅ 不带认证的请求正确地被拒绝了');
      console.log('错误状态码:', error.response?.status);
      console.log('错误信息:', error.response?.data?.message);
    }

    // 4. 测试带有无效token的更新请求
    console.log('\n🔄 测试带有无效token的更新请求...');
    try {
      const updateResponse = await axios.put(
        `${API_BASE_URL}/api/company/update/${companyInfo.id}`,
        updateData,
        {
          headers: {
            'Authorization': 'Bearer invalid-token-12345'
          }
        }
      );
      console.log('❌ 无效token的请求竟然成功了，这不应该发生');
    } catch (error) {
      console.log('✅ 无效token的请求正确地被拒绝了');
      console.log('错误状态码:', error.response?.status);
      console.log('错误信息:', error.response?.data?.message);
    }

    // 5. 测试健康检查接口
    console.log('\n🔄 测试健康检查接口...');
    try {
      const healthResponse = await axios.get(`${API_BASE_URL}/health`);
      console.log('✅ 健康检查成功');
      console.log('健康检查响应:', JSON.stringify(healthResponse.data, null, 2));
    } catch (error) {
      console.log('❌ 健康检查失败');
      console.log('错误:', error.message);
    }

    // 6. 测试系统状态接口
    console.log('\n🔄 测试系统状态接口...');
    try {
      const statusResponse = await axios.get(`${API_BASE_URL}/api/system/status`);
      console.log('✅ 系统状态检查成功');
      console.log('系统状态响应:', JSON.stringify(statusResponse.data, null, 2));
    } catch (error) {
      console.log('❌ 系统状态检查失败');
      console.log('错误状态码:', error.response?.status);
      console.log('错误信息:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('响应状态码:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    if (error.code === 'ECONNREFUSED') {
      console.error('⚠️ 连接被拒绝，请确保后端服务正在运行在', API_BASE_URL);
    }
  }
}

// 执行测试
testCompanyInfoAPI()
  .then(() => {
    console.log('\n🎉 API测试完成！');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ API测试失败:', error);
    process.exit(1);
  });
