// pages/partner/application-status.js
const { partnerApi } = require('../../utils/api');

Page({
  data: {
    applications: [],
    loading: true,
    empty: false
  },

  onLoad: function (options) {
    this.loadApplications();
  },

  onPullDownRefresh: function () {
    this.loadApplications();
  },

  // 加载申请记录
  loadApplications: function() {
    const app = getApp();
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再查看申请状态',
        confirmText: '去登录',
        success(res) {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          } else {
            wx.navigateBack();
          }
        }
      });
      return;
    }
    
    this.setData({ loading: true });
    
    partnerApi.getMyApplications().then(res => {
      wx.stopPullDownRefresh();
      if (res.success && res.data) {
        this.setData({
          applications: res.data,
          loading: false,
          empty: res.data.length === 0
        });
      } else {
        this.setData({
          applications: [],
          loading: false,
          empty: true
        });
      }
    }).catch(err => {
      wx.stopPullDownRefresh();
      this.setData({
        loading: false,
        empty: true
      });
      wx.showToast({
        title: '获取申请记录失败',
        icon: 'none'
      });
      console.error('获取申请记录失败:', err);
    });
  },

  // 格式化时间戳
  formatTime: function(timestamp) {
    if (!timestamp) return '未知时间';
    const date = new Date(parseInt(timestamp));
    return date.toLocaleString();
  },

  // 获取状态文本
  getStatusText: function(status) {
    switch(status) {
      case 'pending': return '审核中';
      case 'approved': return '已通过';
      case 'rejected': return '已拒绝';
      default: return '未知状态';
    }
  },

  // 获取状态颜色
  getStatusColor: function(status) {
    switch(status) {
      case 'pending': return '#FFA500';
      case 'approved': return '#52c41a';
      case 'rejected': return '#ff4d4f';
      default: return '#999';
    }
  },

  // 查看申请详情
  viewDetail: function(e) {
    const index = e.currentTarget.dataset.index;
    const application = this.data.applications[index];
    
    let content = `申请时间：${this.formatTime(application.created_at)}\n`;
    content += `申请状态：${this.getStatusText(application.status)}\n`;
    content += `申请区域：${application.province} ${application.city} ${application.district}\n`;
    
    if (application.status === 'approved') {
      content += `审核时间：${this.formatTime(application.updated_at)}\n`;
      content += '恭喜您已成为我们的合伙人！';
    } else if (application.status === 'rejected') {
      content += `审核时间：${this.formatTime(application.updated_at)}\n`;
      content += `拒绝理由：${application.reject_reason || '无'}\n`;
      content += '您可以重新提交申请或联系客服了解更多信息。';
    } else {
      content += '您的申请正在审核中，请耐心等待。';
    }
    
    wx.showModal({
      title: '申请详情',
      content: content,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 重新申请
  reapply: function() {
    wx.navigateTo({
      url: '/pages/partner/join-partner'
    });
  }
});