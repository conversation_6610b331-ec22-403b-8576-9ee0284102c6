<!--pages/settings/avatar.wxml-->
<view class="page">
  <view class="avatar-section">
    <view class="current-avatar">
      <image class="avatar-preview"
             src="{{selectedAvatar || avatarUrl || '/images/icons2/男头像.png'}}{{imageUpdateTime ? '?t=' + imageUpdateTime : ''}}"
             mode="aspectFill"
             binderror="onImageError"
             bindload="onImageLoad"></image>
      <view class="avatar-tips">当前头像</view>
    </view>
  </view>
  
  <view class="section-title">选择默认头像</view>
  <scroll-view class="avatar-scroll" scroll-x="true" show-scrollbar="false">
    <view class="avatar-list">
      <view class="avatar-item {{selectedAvatar === item ? 'selected' : ''}}" 
            wx:for="{{defaultAvatars}}" 
            wx:key="*this" 
            bindtap="selectAvatar" 
            data-avatar="{{item}}">
        <image class="avatar-image" src="{{item}}"></image>
        <view class="selected-icon" wx:if="{{selectedAvatar === item}}"></view>
      </view>
    </view>
  </scroll-view>
  
  <view class="section-title">自定义头像</view>
  <view class="action-list">
    <button class="action-button" bindtap="chooseFromAlbum">
      <text>从相册选择</text>
    </button>
    <button class="action-button" bindtap="takePhoto">
      <text>拍照</text>
    </button>
  </view>
  
  <view class="footer">
    <button class="primary-button {{loading ? 'disabled' : ''}}" bindtap="saveAvatar" disabled="{{loading}}">
      {{loading ? '保存中...' : '保存'}}
    </button>
  </view>
</view>
