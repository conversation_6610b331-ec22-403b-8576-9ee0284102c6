Page({
  data: {
    // 一级分类
    firstLevelTabs: [
      { key: 'kuaidi', label: '顾客快递' },
      { key: 'ziti', label: '顾客自提' },
      { key: 'caigou', label: '门店采购' },
      { key: 'yiku', label: '门店移库' }
    ],
    // 二级分类映射
    secondLevelTabsMap: {
      kuaidi: [
        { key: 'daifukuan', label: '待付款' },
        { key: 'yifukuan', label: '已付款' },
        { key: 'yifahuo', label: '已发货' },
        { key: 'yiwancheng', label: '已完成' },
        { key: 'tuihuo', label: '退货/售后' }
      ],
      ziti: [
        { key: 'daifukuan', label: '待付款' },
        { key: 'yifukuan', label: '已付款' },
        { key: 'yiziti', label: '已自提' },
        { key: 'tuihuo', label: '退货/售后' }
      ],
      caigou: [
        { key: 'yixiadan', label: '已下单' },
        { key: 'yishenhe', label: '已审核' }
      ],
      yiku: [
        { key: 'yixiadan', label: '已下单' },
        { key: 'yishenhe', label: '已审核' },
        { key: 'yifahuo', label: '已发货' },
        { key: 'yiluodi', label: '已落地' }
      ]
    },
    currentFirstTab: 'kuaidi',
    currentSecondTab: 'daifukuan',
    currentFirstTabLabel: '顾客快递',
    currentSecondTabLabel: '待付款',
    // 订单列表（后续对接接口）
    orderList: []
  },
  onLoad(options) {
    let tab = options && options.tab;
    if (!tab || !this.data.firstLevelTabs.some(t => t.key === tab)) {
      tab = this.data.currentFirstTab;
    }
    const firstTabObj = this.data.firstLevelTabs.find(t => t.key === tab);
    const secondTabObj = this.data.secondLevelTabsMap[tab][0];
    this.setData({
      currentFirstTab: tab,
      currentSecondTab: secondTabObj.key,
      currentFirstTabLabel: firstTabObj.label,
      currentSecondTabLabel: secondTabObj.label
    });
  },
  // 一级分类切换
  onFirstTabChange(e) {
    const key = e.currentTarget.dataset.key;
    const secondTabs = this.data.secondLevelTabsMap[key];
    const firstTabObj = this.data.firstLevelTabs.find(t => t.key === key);
    const secondTabObj = secondTabs[0];
    this.setData({
      currentFirstTab: key,
      currentSecondTab: secondTabObj.key,
      currentFirstTabLabel: firstTabObj.label,
      currentSecondTabLabel: secondTabObj.label
    });
    // 可在此处请求订单数据
  },
  // 二级分类切换
  onSecondTabChange(e) {
    const key = e.currentTarget.dataset.key;
    const secondTabObj = this.data.secondLevelTabsMap[this.data.currentFirstTab].find(t => t.key === key);
    this.setData({
      currentSecondTab: key,
      currentSecondTabLabel: secondTabObj.label
    });
    // 可在此处请求订单数据
  }
}); 