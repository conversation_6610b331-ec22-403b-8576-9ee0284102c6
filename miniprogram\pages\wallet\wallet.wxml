<!--pages/wallet/wallet.wxml-->
<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>

  <block wx:if="{{!loading}}">
    <!-- 余额卡片 -->
    <view class="balance-card">
      <view class="balance-header">
        <text class="balance-title">当前可用余额</text>
      </view>
      <view class="balance-amount">
        <text class="amount-text">¥{{walletInfo.balance || 0}}</text>
      </view>
      <view class="balance-actions">
        <view class="bind-card" bindtap="bindBankCard">
          <text>绑定银行卡 ></text>
        </view>
      </view>
      <view class="action-buttons">
        <button class="action-btn recharge-btn" bindtap="recharge">充值</button>
        <button class="action-btn withdraw-btn" bindtap="withdraw">提现</button>
      </view>
    </view>

    <!-- 钱包明细 -->
    <view class="wallet-details">
      <view class="details-title">钱包明细</view>
      
      <!-- 标签栏 -->
      <view class="tabs-container">
        <view class="tabs-bar">
          <block wx:for="{{tabs}}" wx:key="key">
            <view class="tab-item {{activeTab === item.key ? 'active' : ''}}" 
                  bindtap="switchTab" 
                  data-key="{{item.key}}">
              <view class="tab-value">{{item.value}}</view>
              <view class="tab-name">{{item.name}}</view>
            </view>
          </block>
        </view>
      </view>

      <!-- 资金记录列表 -->
      <view class="records-container">
        <block wx:for="{{fundRecords}}" wx:key="id">
          <view class="record-item">
            <view class="record-left">
              <view class="record-desc">{{item.description}}</view>
              <view class="record-time">{{item.created_at}}</view>
              <view class="record-balance">余额: {{item.balance || 0}}</view>
            </view>
            <view class="record-right">
              <view class="record-amount {{item.amount > 0 ? 'positive' : 'negative'}}">
                {{item.amount > 0 ? '+' : ''}}{{item.amount || 0}}
              </view>
            </view>
          </view>
        </block>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{loadingRecords}}">
          <view class="loading-spinner"></view>
          <text>加载中...</text>
        </view>

        <!-- 没有更多数据 -->
        <view class="no-more" wx:if="{{!hasMoreRecords && fundRecords.length > 0}}">
          <text>没有更多记录了</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{!loadingRecords && fundRecords.length === 0}}">
          <image class="empty-icon" src="/images/icons/empty.png"></image>
          <text class="empty-text">暂无资金记录</text>
        </view>
      </view>
    </view>
  </block>
</view> 