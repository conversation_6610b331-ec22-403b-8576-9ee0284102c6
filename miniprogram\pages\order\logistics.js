// pages/order/logistics.js
const { orderApi } = require('../../utils/api');

Page({
  data: {
    orderId: null,
    orderDetail: null,
    logisticsInfo: null,
    loading: true
  },

  onLoad: function(options) {
    console.log('订单物流页面加载，参数:', options);
    if (options && options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail(options.id);
      this.loadLogisticsInfo(options.id);
    } else {
      wx.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载订单详情
  loadOrderDetail: function(orderId) {
    orderApi.getOrderById(orderId).then(res => {
      if (res.success && res.data) {
        const orderDetail = res.data;
        
        this.setData({
          orderDetail
        });
      } else {
        wx.showToast({
          title: '获取订单详情失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取订单详情失败:', err);
      wx.showToast({
        title: '获取订单详情失败',
        icon: 'none'
      });
    });
  },

  // 加载物流信息
  loadLogisticsInfo: function(orderId) {
    this.setData({ loading: true });
    
    orderApi.getLogisticsInfo(orderId).then(res => {
      if (res.success && res.data) {
        const logisticsInfo = res.data;
        
        this.setData({
          logisticsInfo,
          loading: false
        });
      } else {
        this.setData({ loading: false });
        wx.showToast({
          title: '获取物流信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取物流信息失败:', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '获取物流信息失败',
        icon: 'none'
      });
    });
  },

  // 复制运单号
  copyTrackingNumber: function() {
    const { logisticsInfo } = this.data;
    if (!logisticsInfo || !logisticsInfo.tracking_number) return;
    
    wx.setClipboardData({
      data: logisticsInfo.tracking_number,
      success: () => {
        wx.showToast({
          title: '运单号已复制',
          icon: 'success'
        });
      }
    });
  },

  // 刷新物流信息
  refreshLogistics: function() {
    const { orderId } = this.data;
    if (!orderId) return;
    
    this.loadLogisticsInfo(orderId);
  },

  // 联系快递公司
  contactExpress: function() {
    const { logisticsInfo } = this.data;
    if (!logisticsInfo || !logisticsInfo.express_phone) {
      wx.showToast({
        title: '暂无快递公司联系方式',
        icon: 'none'
      });
      return;
    }
    
    wx.makePhoneCall({
      phoneNumber: logisticsInfo.express_phone,
      fail: () => {
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  // 联系客服
  contactService: function() {
    wx.navigateTo({
      url: '/pages/service/index'
    });
  }
});