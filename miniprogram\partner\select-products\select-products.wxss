/* pages/partner/select-products/select-products.wxss */
/* 合伙人端选品页面样式 */

.select-products-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 100rpx;
}

/* 导航栏占位 */
.nav-placeholder {
  width: 100%;
}

/* ==================== 自定义导航栏 ==================== */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.navbar-center {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-right: 16px;
}

.store-picker {
  flex: 0 0 auto;
}

.store-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f8f8;
  padding: 10.8rpx 14.4rpx;
  border-radius: 8rpx;
  min-width: 162rpx;
  border: 1rpx solid #e0e0e0;
}

.store-name {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  text-align: left;
  margin-right: 8rpx;
  font-weight: normal;
}

.dropdown-arrow {
  font-size: 20rpx;
  color: #999;
  font-weight: bold;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.more-icon, .eye-icon {
  width: 20px;
  height: 20px;
  opacity: 0.7;
}

/* ==================== 固定搜索栏区域 ==================== */
.fixed-search-section {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #f5f5f5;
  padding: 8px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-input-wrapper {
  background-color: #FFFFFF;
  border-radius: 18px;
  display: flex;
  align-items: center;
  padding: 5.4px 12px;
  height: 28.8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.search-icon {
  width: 14.4px;
  height: 14.4px;
  margin-right: 8px;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 14px;
  color: #333;
}

.search-placeholder {
  color: #999;
  font-size: 14px;
}

/* 搜索栏占位 */
.search-placeholder {
  width: 100%;
}

/* ==================== 轮播图区域 ==================== */
.banner-section {
  margin: 0 12px 6px;
}

.banner-swiper {
  height: 160px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 20px 16px 16px;
  color: #FFFFFF;
}

.banner-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.banner-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

/* ==================== 快捷菜单 ==================== */
.quick-menu-section {
  background-color: #FFFFFF;
  margin: 0 12px 6px;
  border-radius: 12px;
  padding: 12px 12px 4px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.quick-menu-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.quick-menu-item {
  width: 22%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
}

.quick-menu-icon-wrapper {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #22a2c3 0%, #1a8ba8 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  box-shadow: 0 4px 12px rgba(34, 162, 195, 0.3);
}

.quick-menu-icon {
  width: 28px;
  height: 28px;
}

.quick-menu-name {
  font-size: 12px;
  color: #333;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

/* ==================== 新品上市区域 ==================== */
.new-products-section {
  background-color: #FFFFFF;
  margin: 0 12px 6px;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.new-products-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.new-products-scroll {
  width: 100%;
  overflow-x: auto;
}

.new-products-list {
  display: flex;
}

.new-product-card {
  flex-shrink: 0;
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 6px;
  margin-right: 6px;
  transition: transform 0.2s ease;
}

.new-product-card:active {
  transform: scale(0.95);
}

.new-product-image {
  width: 100%;
  height: 80px;
  border-radius: 6px;
  margin-bottom: 6px;
}

.new-product-name {
  font-size: 12px;
  color: #333;
  line-height: 1.2;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.new-product-price {
  font-size: 14px;
  color: #FF6B35;
  font-weight: bold;
}

.new-product-purchase-price {
  margin-top: 2px;
  font-size: 11px;
  line-height: 1.2;
}

.new-product-purchase-price .purchase-price-label {
  color: #666;
  font-size: 10px;
}

.new-product-purchase-price .purchase-price-value {
  color: #22a2c3;
  font-weight: bold;
  font-size: 11px;
}

.new-product-sales {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
}



/* ==================== 热门商品区域 ==================== */
.hot-products-section {
  background-color: #FFFFFF;
  margin: 0 12px 6px;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.hot-products-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.hot-products-scroll {
  width: 100%;
  overflow-x: auto;
}

.hot-products-list {
  display: flex;
}

.hot-product-card {
  flex-shrink: 0;
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 6px;
  margin-right: 6px;
  transition: transform 0.2s ease;
}

.hot-product-card:active {
  transform: scale(0.95);
}

.hot-product-image {
  width: 100%;
  height: 80px;
  border-radius: 6px;
  margin-bottom: 6px;
}

.hot-product-name {
  font-size: 12px;
  color: #333;
  line-height: 1.2;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hot-product-price {
  font-size: 14px;
  color: #FF6B35;
  font-weight: bold;
}

.hot-product-purchase-price {
  margin-top: 2px;
  font-size: 11px;
  line-height: 1.2;
}

.hot-product-purchase-price .purchase-price-label {
  color: #666;
  font-size: 10px;
}

.hot-product-purchase-price .purchase-price-value {
  color: #22a2c3;
  font-weight: bold;
  font-size: 11px;
}

.hot-product-sales {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
}

/* ==================== 商品列表区域 ==================== */
.products-section {
  background-color: #FFFFFF;
  margin: 0 12px 8px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.product-tabs-scroll {
  width: 100%;
  overflow-x: auto;
  border-bottom: 1px solid #f0f0f0;
}

.product-tabs {
  display: flex;
  padding: 0 12px;
}

.product-tab {
  flex-shrink: 0;
  padding: 10px 12px;
  margin-right: 6px;
  font-size: 14px;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.product-tab.active {
  color: #FF6B35;
  font-weight: bold;
}

.product-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: #FF6B35;
  border-radius: 1px;
}

.products-grid {
  padding: 12px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.product-item {
  background-color: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}

.product-item:active {
  transform: scale(0.98);
}

.product-image-wrapper {
  position: relative;
  width: 100%;
  height: 120px;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-tags {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-tag {
  background-color: rgba(255, 107, 53, 0.9);
  color: #FFFFFF;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
}

.product-new-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #FF4444;
  color: #FFFFFF;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
}

.product-hot-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #FF8800;
  color: #FFFFFF;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
}

.product-info {
  padding: 8px;
}

.product-name {
  font-size: 14px;
  color: #333;
  line-height: 1.3;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.2;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.product-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 12px;
  color: #FF6B35;
  font-weight: bold;
}

.price-value {
  font-size: 16px;
  color: #FF6B35;
  font-weight: bold;
}

.price-unit {
  font-size: 10px;
  color: #999;
  margin-left: 2px;
}

.product-sales {
  font-size: 10px;
  color: #999;
}

.product-shop {
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.shop-icon {
  width: 12px;
  height: 12px;
  margin-right: 4px;
}

.shop-name {
  font-size: 10px;
  color: #666;
}

.product-stock {
  margin-top: 4px;
}

.stock-text {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
}

.stock-text.in-stock {
  background-color: #E8F5E8;
  color: #52C41A;
}

.stock-text.out-of-stock {
  background-color: #FFF2F0;
  color: #FF4444;
}

/* 采购价样式 */
.product-purchase-price {
  display: flex;
  align-items: center;
  margin-top: 4px;
  font-size: 12px;
}

.purchase-price-label {
  color: #666;
  margin-right: 4px;
}

.purchase-price-value {
  color: #22a2c3;
  font-weight: 500;
}

/* 商品信息样式 */

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #FF6B35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999;
}

/* ==================== 没有更多数据 ==================== */
.no-more-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.no-more-line {
  flex: 1;
  height: 1px;
  background-color: #f0f0f0;
  margin: 0 16px;
}

.no-more-text {
  font-size: 12px;
  color: #999;
  padding: 0 16px;
}

/* ==================== 空状态 ==================== */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

.retry-btn {
  background-color: #FF6B35;
  color: #FFFFFF;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
}

/* ==================== 回到顶部按钮 ==================== */
.back-to-top {
  position: fixed;
  right: 20px;
  bottom: 120px;
  width: 44px;
  height: 44px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 999;
}

.back-to-top-icon {
  width: 24px;
  height: 24px;
}

/* ==================== 底部间距 ==================== */
.bottom-spacing {
  height: 20px;
}

/* ==================== 加入采购车按钮 ==================== */
.add-to-cart-btn {
  margin-top: 8px;
  background: linear-gradient(135deg, #22a2c3 0%, #1a8ba8 100%); /* 改为海青色调 */
  border-radius: 6px;
  padding: 5px 10px; /* 减小15%：原6px 12px -> 5px 10px */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.add-to-cart-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.add-to-cart-text {
  color: #FFFFFF;
  font-size: 10px; /* 减小15%：原12px -> 10px */
  font-weight: 500;
}

/* 小卡片采购车按钮 */
.add-to-cart-btn-small {
  margin-top: 4px;
  background: linear-gradient(135deg, #22a2c3 0%, #1a8ba8 100%); /* 改为海青色调 */
  border-radius: 4px;
  padding: 3px 5px; /* 减小15%：原3px 6px -> 3px 5px */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.add-to-cart-btn-small:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.add-to-cart-text-small {
  color: #FFFFFF;
  font-size: 9px; /* 减小15%：原10px -> 9px */
  font-weight: 500;
} 