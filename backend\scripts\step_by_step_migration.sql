-- 分步执行的数据库迁移脚本
-- 请按步骤逐个执行，避免一次性执行导致的错误

-- ========================================
-- 第1步：创建顾客主订单表
-- ========================================
CREATE TABLE IF NOT EXISTS `customer_orders` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
  `user_id` VARCHAR(32) NOT NULL COMMENT '顾客用户ID',
  `salesman_id` VARCHAR(32) NULL COMMENT '销售人ID',
  `delivery_method` ENUM('express', 'self') NOT NULL DEFAULT 'express' COMMENT '配送方式：express=快递，self=自提',
  `address_id` VARCHAR(50) NULL COMMENT '收货地址ID（快递时使用）',
  `store_id` VARCHAR(50) NULL COMMENT '自提门店ID（自提时使用）',
  `payment_methods` JSON NULL COMMENT '支付方式JSON数组',
  `total_amount` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
  `total_quantity` INT NOT NULL DEFAULT 0 COMMENT '商品总数量',
  `sub_order_count` INT NOT NULL DEFAULT 0 COMMENT '子订单数量',
  `status` VARCHAR(32) NOT NULL DEFAULT '待支付' COMMENT '订单状态',
  `paid_at` BIGINT NULL COMMENT '支付时间',
  `shipped_at` BIGINT NULL COMMENT '发货时间',
  `completed_at` BIGINT NULL COMMENT '完成时间',
  `created_at` BIGINT NOT NULL COMMENT '创建时间',
  `updated_at` BIGINT NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_salesman_id` (`salesman_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='顾客主订单表';

-- ========================================
-- 第2步：创建顾客子订单表
-- ========================================
CREATE TABLE IF NOT EXISTS `customer_sub_orders` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '子订单ID',
  `main_order_id` INT NOT NULL COMMENT '主订单ID',
  `sub_order_no` VARCHAR(50) NOT NULL COMMENT '子订单号',
  `store_no` VARCHAR(32) NOT NULL COMMENT '门店编号',
  `store_type` ENUM('subscribe', 'salesman', 'platform') NOT NULL COMMENT '门店类型：subscribe=订阅门店，salesman=销售人门店，platform=平台总部',
  `sub_total_amount` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '子订单金额',
  `sub_total_quantity` INT NOT NULL DEFAULT 0 COMMENT '子订单商品数量',
  `status` VARCHAR(32) NOT NULL DEFAULT '待支付' COMMENT '子订单状态',
  `shipped_at` BIGINT NULL COMMENT '发货时间',
  `completed_at` BIGINT NULL COMMENT '完成时间',
  `created_at` BIGINT NOT NULL COMMENT '创建时间',
  `updated_at` BIGINT NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sub_order_no` (`sub_order_no`),
  KEY `idx_main_order_id` (`main_order_id`),
  KEY `idx_store_no` (`store_no`),
  KEY `idx_store_type` (`store_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='顾客子订单表';

-- ========================================
-- 第3步：创建顾客订单商品明细表
-- ========================================
CREATE TABLE IF NOT EXISTS `customer_order_items` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `main_order_id` INT NOT NULL COMMENT '主订单ID',
  `sub_order_id` INT NOT NULL COMMENT '子订单ID',
  `product_id` VARCHAR(50) NOT NULL COMMENT '商品ID',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `product_image` VARCHAR(500) NULL COMMENT '商品图片',
  `product_price` DECIMAL(10,2) NOT NULL COMMENT '商品单价',
  `quantity` INT NOT NULL COMMENT '购买数量',
  `subtotal` DECIMAL(10,2) NOT NULL COMMENT '小计金额',
  `created_at` BIGINT NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_main_order_id` (`main_order_id`),
  KEY `idx_sub_order_id` (`sub_order_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='顾客订单商品明细表';

-- ========================================
-- 第4步：检查orders表结构（可选）
-- ========================================
-- 检查order_source字段是否存在
SELECT 
  COLUMN_NAME,
  DATA_TYPE,
  IS_NULLABLE,
  COLUMN_DEFAULT,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'orders' 
  AND COLUMN_NAME = 'order_source';

-- ========================================
-- 第5步：创建查询视图
-- ========================================
CREATE OR REPLACE VIEW `v_customer_order_summary` AS
SELECT 
  co.id,
  co.order_no,
  co.user_id,
  co.salesman_id,
  co.delivery_method,
  co.total_amount,
  co.total_quantity,
  co.sub_order_count,
  co.status,
  co.created_at,
  co.updated_at,
  GROUP_CONCAT(DISTINCT cso.store_no) as involved_stores,
  GROUP_CONCAT(DISTINCT cso.store_type) as store_types
FROM customer_orders co
LEFT JOIN customer_sub_orders cso ON co.id = cso.main_order_id
GROUP BY co.id;

-- ========================================
-- 第6步：验证创建结果
-- ========================================
-- 查看新创建的表
SELECT 
  TABLE_NAME,
  TABLE_COMMENT,
  TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('customer_orders', 'customer_sub_orders', 'customer_order_items');

-- 查看表结构
DESCRIBE customer_orders;

-- ========================================
-- 说明：
-- 1. 原orders表保持不变，继续用于门店采购、移库订单
-- 2. 新表专门处理顾客订单拆分逻辑
-- 3. 如果需要为orders表添加order_source字段，请单独执行：
--    ALTER TABLE orders ADD COLUMN order_source ENUM('legacy', 'customer', 'store') DEFAULT 'legacy' COMMENT '订单来源' AFTER type;
-- 4. 如果需要创建索引，请单独执行：
--    CREATE INDEX idx_order_source ON orders (order_source);
-- ========================================
