/**
 * 管理员常见问题控制器
 */
const db = require('../utils/db');

// 获取常见问题列表
exports.getFaqList = async (req, res) => {
  try {
    console.log('[管理员常见问题控制器] 获取常见问题列表');

    const query = `
      SELECT 
        id,
        question,
        answer,
        sort_order,
        is_active,
        createTime,
        updateTime
      FROM faq
      ORDER BY sort_order ASC, createTime DESC
    `;

    const faqList = await db.query(query);

    res.json({
      success: true,
      data: faqList,
      message: '获取常见问题列表成功'
    });

  } catch (error) {
    console.error('[管理员常见问题控制器] 获取常见问题列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取常见问题列表失败',
      error: error.message
    });
  }
};

// 创建常见问题
exports.createFaq = async (req, res) => {
  try {
    const { question, answer, sort_order = 0, is_active = true } = req.body;

    console.log('[管理员常见问题控制器] 创建常见问题:', { question, answer, sort_order, is_active });

    // 验证必填字段
    if (!question || !question.trim()) {
      return res.status(400).json({
        success: false,
        message: '问题标题不能为空'
      });
    }

    if (!answer || !answer.trim()) {
      return res.status(400).json({
        success: false,
        message: '问题答案不能为空'
      });
    }

    const currentTime = Date.now();

    const query = `
      INSERT INTO faq (question, answer, sort_order, is_active, createTime, updateTime)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    const result = await db.query(query, [
      question.trim(),
      answer.trim(),
      parseInt(sort_order) || 0,
      is_active ? 1 : 0,
      currentTime,
      currentTime
    ]);

    console.log('[管理员常见问题控制器] 常见问题创建成功, ID:', result.insertId);

    res.status(201).json({
      success: true,
      data: {
        id: result.insertId,
        question: question.trim(),
        answer: answer.trim(),
        sort_order: parseInt(sort_order) || 0,
        is_active: is_active ? 1 : 0,
        createTime: currentTime,
        updateTime: currentTime
      },
      message: '常见问题创建成功'
    });

  } catch (error) {
    console.error('[管理员常见问题控制器] 创建常见问题失败:', error);
    res.status(500).json({
      success: false,
      message: '创建常见问题失败',
      error: error.message
    });
  }
};

// 更新常见问题
exports.updateFaq = async (req, res) => {
  try {
    const { id } = req.params;
    const { question, answer, sort_order, is_active } = req.body;

    console.log('[管理员常见问题控制器] 更新常见问题:', id, { question, answer, sort_order, is_active });

    // 验证必填字段
    if (!question || !question.trim()) {
      return res.status(400).json({
        success: false,
        message: '问题标题不能为空'
      });
    }

    if (!answer || !answer.trim()) {
      return res.status(400).json({
        success: false,
        message: '问题答案不能为空'
      });
    }

    // 检查常见问题是否存在
    const checkQuery = 'SELECT id FROM faq WHERE id = ?';
    const existingFaq = await db.query(checkQuery, [id]);

    if (existingFaq.length === 0) {
      return res.status(404).json({
        success: false,
        message: '常见问题不存在'
      });
    }

    const currentTime = Date.now();

    const query = `
      UPDATE faq 
      SET question = ?, answer = ?, sort_order = ?, is_active = ?, updateTime = ?
      WHERE id = ?
    `;

    await db.query(query, [
      question.trim(),
      answer.trim(),
      parseInt(sort_order) || 0,
      is_active ? 1 : 0,
      currentTime,
      id
    ]);

    console.log('[管理员常见问题控制器] 常见问题更新成功');

    res.json({
      success: true,
      data: {
        id: parseInt(id),
        question: question.trim(),
        answer: answer.trim(),
        sort_order: parseInt(sort_order) || 0,
        is_active: is_active ? 1 : 0,
        updateTime: currentTime
      },
      message: '常见问题更新成功'
    });

  } catch (error) {
    console.error('[管理员常见问题控制器] 更新常见问题失败:', error);
    res.status(500).json({
      success: false,
      message: '更新常见问题失败',
      error: error.message
    });
  }
};

// 删除常见问题
exports.deleteFaq = async (req, res) => {
  try {
    const { id } = req.params;

    console.log('[管理员常见问题控制器] 删除常见问题:', id);

    // 检查常见问题是否存在
    const checkQuery = 'SELECT id FROM faq WHERE id = ?';
    const existingFaq = await db.query(checkQuery, [id]);

    if (existingFaq.length === 0) {
      return res.status(404).json({
        success: false,
        message: '常见问题不存在'
      });
    }

    const query = 'DELETE FROM faq WHERE id = ?';
    await db.query(query, [id]);

    console.log('[管理员常见问题控制器] 常见问题删除成功');

    res.json({
      success: true,
      message: '常见问题删除成功'
    });

  } catch (error) {
    console.error('[管理员常见问题控制器] 删除常见问题失败:', error);
    res.status(500).json({
      success: false,
      message: '删除常见问题失败',
      error: error.message
    });
  }
};

// 更新常见问题状态
exports.updateFaqStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;

    console.log('[管理员常见问题控制器] 更新常见问题状态:', id, is_active);

    // 检查常见问题是否存在
    const checkQuery = 'SELECT id FROM faq WHERE id = ?';
    const existingFaq = await db.query(checkQuery, [id]);

    if (existingFaq.length === 0) {
      return res.status(404).json({
        success: false,
        message: '常见问题不存在'
      });
    }

    const currentTime = Date.now();

    const query = 'UPDATE faq SET is_active = ?, updateTime = ? WHERE id = ?';
    await db.query(query, [is_active ? 1 : 0, currentTime, id]);

    console.log('[管理员常见问题控制器] 常见问题状态更新成功');

    res.json({
      success: true,
      message: is_active ? '常见问题已启用' : '常见问题已禁用'
    });

  } catch (error) {
    console.error('[管理员常见问题控制器] 更新常见问题状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新常见问题状态失败',
      error: error.message
    });
  }
};

// 获取常见问题统计信息
exports.getFaqStats = async (req, res) => {
  try {
    console.log('[管理员常见问题控制器] 获取常见问题统计信息');

    const statsQuery = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive
      FROM faq
    `;

    const stats = await db.query(statsQuery);

    res.json({
      success: true,
      data: stats[0] || { total: 0, active: 0, inactive: 0 },
      message: '获取统计信息成功'
    });

  } catch (error) {
    console.error('[管理员常见问题控制器] 获取常见问题统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计信息失败',
      error: error.message
    });
  }
}; 