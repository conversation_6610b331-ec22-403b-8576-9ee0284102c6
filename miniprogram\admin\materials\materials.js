const { adminApi, uploadFile } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');

Page({
  data: {
    activeTab: 'company', // company | banners | messages | faq
    
    // 公司信息相关
    companyInfo: {},
    showCompanyEditModal: false,
    editCompanyData: {},
    companyLogoTemp: '', // 新增：临时存储选择的Logo图片路径
    companyCustomImageMode: false, // 标记是否使用自定义公司logo
    
    // 轮播图相关
    banners: [],
    showBannerEditModal: false,
    editBannerData: {},
    editBannerIndex: -1,
    editBannerPageTypeIndex: -1,
    pageTypeInputMode: 'select', // 'select' 或 'input'
    customPageType: '', // 自定义页面类型
    bannerCustomImageMode: false, // 标记是否使用自定义轮播图图片
    
    // 页面类型配置（从数据库动态获取）
    pageTypes: [],
    selectedPageType: '',
    
    // 页面类型标签映射
    pageTypeLabels: {},
    
    // 消息管理相关
    messages: [],
    messageFilter: 'all', // all | private | group
    messagePage: 1,
    messagePageSize: 20,
    messageLoading: false,
    
    // 常见问题相关
    faqList: [],
    showFaqEditModal: false,
    editFaqData: {},
    editFaqIndex: -1,
    
    loading: false
  },

  async onLoad() {
    try {
      // 直接加载页面数据，如果有认证问题会在API调用时处理
      this.loadCompanyInfo();
      this.loadPageTypes();
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({ title: '页面加载失败', icon: 'none' });
    }
  },

  onShow: async function() {
    // 从裁剪页面返回时处理各种类型的图片
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    // 处理公司logo裁剪后的图片
    if (currentPage.data.croppedLogo) {
      console.log('检测到公司logo裁剪结果:', currentPage.data.croppedLogo);
      console.log('当前companyLogoTemp值:', this.data.companyLogoTemp);
      
      // 立即设置裁剪后的logo，确保页面能够显示
      this.setData({
        companyLogoTemp: currentPage.data.croppedLogo,
        companyCustomImageMode: true
      }, () => {
        // 设置完成后的回调，确保数据已更新
        console.log('Logo设置完成，当前companyLogoTemp值:', this.data.companyLogoTemp);
      });
      
      // 清除页面临时数据
      currentPage.setData({ 
        croppedLogo: '',
        selectedLogo: '',
        customLogoMode: false
      });
      
      // 移除了无意义的提示信息，因为logo应该立即显示
    }
    
    // 处理轮播图裁剪后的图片
    if (currentPage.data.croppedImage && currentPage.data.imageType === 'banner') {
      // 只设置裁剪后的图片路径以显示缩略图，不立即上传
      console.log('设置轮播图临时图片:', currentPage.data.croppedImage);
      const index = this.data.editingBannerIndex;
      if (index !== undefined && index >= 0) {
        const banners = this.data.banners;
        banners[index].image_url = currentPage.data.croppedImage; // 先显示本地裁剪后的图片
        banners[index].customImageMode = true;
        this.setData({ banners });
      }
      
      // 清除页面临时数据
      currentPage.setData({
        croppedImage: '',
        imageType: ''
      });
      this.setData({ editingBannerIndex: undefined });
    }
    // 处理新轮播图裁剪后的图片
    else if (currentPage.data.croppedImage && currentPage.data.imageType === 'newBanner') {
      // 只设置裁剪后的图片路径以显示缩略图，不立即上传
      console.log('设置新轮播图临时图片:', currentPage.data.croppedImage);
      this.setData({
        'editBannerData.image_url': currentPage.data.croppedImage,
        bannerCustomImageMode: true
      });
      
      // 清除页面临时数据
      currentPage.setData({
        croppedImage: '',
        imageType: ''
      });
    }
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
    
    // 根据标签页加载对应数据
    if (tab === 'messages') {
      this.loadMessages();
    } else if (tab === 'faq') {
      this.loadFaqList();
    }
  },

  // 页面类型切换
  onPageTypeChange(e) {
    const pageType = e.currentTarget.dataset.type;
    this.setData({ selectedPageType: pageType });
    this.loadBanners(); // 重新加载对应页面类型的轮播图
  },

    // ==================== 页面类型管理 ====================

  // 加载页面类型配置
  async loadPageTypes() {
    try {
      // 从数据库获取所有存在的页面类型
      const res = await adminApi.getBanners();
      
      if (res.success && res.data) {
        // 提取所有不重复的页面类型
        const pageTypeSet = new Set();
        const pageTypeLabels = {};
        
        // 页面类型中文映射
        const labelMapping = {
          'customer_home': '顾客端-首页',
          'customer_category': '顾客端-分类页',
          'customer_profile': '顾客端-我的页',
          'partner_products': '合伙人端-选品页',
          'partner_center': '合伙人端-中心页'
        };
        
        res.data.forEach(banner => {
          if (banner.page_type) {
            pageTypeSet.add(banner.page_type);
            pageTypeLabels[banner.page_type] = labelMapping[banner.page_type] || banner.page_type;
          }
        });
        
        // 转换为数组格式
        const pageTypes = Array.from(pageTypeSet).map(type => ({
          value: type,
          label: labelMapping[type] || type
        }));
        
        // 按预定义顺序排序
        const order = ['customer_home', 'customer_category', 'customer_profile', 'partner_products', 'partner_center'];
        pageTypes.sort((a, b) => {
          const aIndex = order.indexOf(a.value);
          const bIndex = order.indexOf(b.value);
          return (aIndex === -1 ? 999 : aIndex) - (bIndex === -1 ? 999 : bIndex);
        });
        
        this.setData({
          pageTypes,
          pageTypeLabels,
          selectedPageType: pageTypes.length > 0 ? pageTypes[0].value : ''
        });
        
        // 加载默认选中类型的轮播图
        if (pageTypes.length > 0) {
          this.loadBanners();
        }
      } else {
        // 如果没有数据，使用默认配置
        this.initDefaultPageTypes();
      }
    } catch (error) {
      console.error('加载页面类型失败:', error);
      this.initDefaultPageTypes();
    }
  },

  // 初始化默认页面类型
  initDefaultPageTypes() {
    const defaultPageTypes = [
      { value: 'customer_home', label: '顾客端-首页' },
      { value: 'customer_category', label: '顾客端-分类页' },
      { value: 'customer_profile', label: '顾客端-我的页' },
      { value: 'partner_products', label: '合伙人端-选品页' },
      { value: 'partner_center', label: '合伙人端-中心页' }
    ];
    
    const pageTypeLabels = {};
    defaultPageTypes.forEach(type => {
      pageTypeLabels[type.value] = type.label;
    });
    
    this.setData({
      pageTypes: defaultPageTypes,
      pageTypeLabels,
      selectedPageType: 'customer_home'
    });
    
    this.loadBanners();
  },

  // ==================== 公司信息管理 ====================

  // 加载公司信息
  async loadCompanyInfo() {
    try {
      wx.showLoading({ title: '加载中...' });
      const res = await adminApi.getCompanyInfo();
      wx.hideLoading();
      
      if (res.success && res.data) {
        console.log('加载公司信息成功:', res.data);
        // 处理null值，确保所有字段都有默认值
        const cleanedData = {
          id: res.data.id || null,
          company_name: res.data.company_name || '',
          company_description: res.data.company_description || '',
          company_address: res.data.company_address || '',
          company_phone: res.data.company_phone || '',
          company_email: res.data.company_email || '',
          company_website: res.data.company_website || '',
          business_hours: res.data.business_hours || '',
          company_logo: res.data.company_logo || '',
          contact_person: res.data.contact_person || '',
          fax: res.data.fax || '',
          postal_code: res.data.postal_code || '',
          company_type: res.data.company_type || '',
          established_date: res.data.established_date || '',
          registration_number: res.data.registration_number || '',
          social_credit_code: res.data.social_credit_code || '',
          legal_representative: res.data.legal_representative || '',
          registered_capital: res.data.registered_capital || null,
          business_scope: res.data.business_scope || '',
          company_status: res.data.company_status || 'active',
          createTime: res.data.createTime,
          updateTime: res.data.updateTime
        };
        console.log('清理后的公司信息:', cleanedData);
        this.setData({
          companyInfo: cleanedData
          // 移除companyLogoTemp的自动设置，它应该只在用户选择新图片时才被设置
          // companyLogoTemp应该保持为空字符串，除非用户主动选择了新的Logo
        });
      } else {
        console.log('加载公司信息失败或无数据:', res);
        // 检查是否是认证错误
        if (res.message && (res.message.includes('认证') || res.message.includes('登录') || res.message.includes('token'))) {
          wx.showModal({
            title: '登录已过期',
            content: '请重新登录后使用管理功能',
            showCancel: false,
            success: () => {
              loginStateManager.clearLoginState();
              wx.redirectTo({ url: '/pages/auth/auth' });
            }
          });
          return;
        }
        // 如果没有公司信息，设置空对象
        this.setData({ companyInfo: {} });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载公司信息失败:', error);
      
      // 检查是否是认证相关错误
      if (error.message && (error.message.includes('请先登录') || error.message.includes('认证') || error.code === 401)) {
        wx.showModal({
          title: '登录已过期',
          content: '请重新登录后使用管理功能',
          showCancel: false,
          success: () => {
            loginStateManager.clearLoginState();
            wx.redirectTo({ url: '/pages/auth/auth' });
          }
        });
      } else {
        wx.showToast({ title: '加载公司信息失败', icon: 'none' });
      }
    }
  },

  // 刷新公司信息
  refreshCompanyInfo() {
    this.loadCompanyInfo();
    wx.showToast({ title: '刷新成功', icon: 'success' });
  },

  // 公司信息输入框变化
  onCompanyInputChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`companyInfo.${field}`]: value
    });
  },

  // 更换公司Logo
  changeCompanyLogo() {
    console.log('开始选择公司logo');
    // 先尝试使用chooseMedia API
    try {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: res => {
          console.log('选择logo成功:', res);
          const file = res.tempFiles[0];
          if (file.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
            wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
            return;
          }
          // 跳转到裁剪页面
          wx.navigateTo({
            url: `/admin/cropper/cropper?src=${encodeURIComponent(file.tempFilePath)}&type=logo`
          });
        },
        fail: err => {
          console.error('chooseMedia 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消选择logo');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才使用备选方案
          this.changeCompanyLogoFallback();
        }
      });
    } catch (error) {
      console.error('chooseMedia 异常:', error);
      // 如果chooseMedia出现异常，尝试使用chooseImage作为备选方案
      this.changeCompanyLogoFallback();
    }
  },

  // 更换公司logo备选方案
  changeCompanyLogoFallback() {
    console.log('使用chooseImage作为备选方案选择logo');
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        console.log('chooseImage选择logo成功:', res);
        const tempFilePath = res.tempFilePaths[0];
        const tempFile = res.tempFiles[0];
        if (tempFile && tempFile.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
          wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
          return;
        }
        // 跳转到裁剪页面
        wx.navigateTo({
          url: `/admin/cropper/cropper?src=${encodeURIComponent(tempFilePath)}&type=logo`
        });
      },
      fail: err => {
        console.error('chooseImage 失败:', err);
        // 检查是否是用户取消操作
        if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
          console.log('用户取消选择logo');
          return; // 用户取消，直接返回
        }
        // 其他错误情况才显示提示
        wx.showToast({
          title: '选择图片失败，请检查相关权限',
          icon: 'none'
        });
      }
    });
  },

  // 使用chooseImage作为备选方案


  // 预览公司Logo
  previewCompanyLogo() {
    const logoUrl = this.data.companyInfo.company_logo;
    if (logoUrl && logoUrl !== '/images/icons2/公司.png') {
      wx.previewImage({
        urls: [logoUrl],
        current: logoUrl
      });
    } else {
      wx.showToast({ title: '暂无Logo可预览', icon: 'none' });
    }
  },



  // 保存公司信息变更
  async saveCompanyChanges() {
    const { companyInfo, companyLogoTemp } = this.data;
    
    // 验证必填字段
    if (!companyInfo.company_name || !companyInfo.company_name.trim()) {
      wx.showToast({ title: '请输入公司名称', icon: 'none' });
      return;
    }

    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再进行操作',
        showCancel: false,
        success: () => {
          wx.redirectTo({ url: '/pages/auth/auth' });
        }
      });
      return;
    }

    try {
      wx.showLoading({ title: '保存中...' });
      
      // 1. 上传Logo图片（如果有新选择的图片）
      let logoUrl = companyLogoTemp || companyInfo.company_logo;
      console.log('=== Logo保存调试信息 ===');
      console.log('companyLogoTemp值:', companyLogoTemp);
      console.log('companyLogoTemp类型:', typeof companyLogoTemp);
      console.log('companyInfo.company_logo:', companyInfo.company_logo);
      console.log('初始logoUrl:', logoUrl);
      console.log('自定义Logo模式:', this.data.companyCustomImageMode);

      // 判断是否需要上传新Logo - 检查是否为临时文件路径
      const isTempFilePath = companyLogoTemp &&
                            (companyLogoTemp.startsWith('wxfile://') ||
                             companyLogoTemp.startsWith('http://tmp') ||
                             companyLogoTemp.includes('tmp'));

      const needUploadLogo = isTempFilePath;

      if (needUploadLogo) {
        console.log('检测到新Logo需要上传:', companyLogoTemp);
        console.log('上传前Logo URL:', logoUrl);
        try {
          // 使用company目录上传Logo文件
          logoUrl = await uploadFile(companyLogoTemp, 'company');
          console.log('Logo上传成功，新URL:', logoUrl);
          console.log('上传前后URL对比:', {
            before: companyLogoTemp,
            after: logoUrl
          });
        } catch (uploadError) {
          console.error('Logo上传失败:', uploadError);
          wx.hideLoading();
          wx.showToast({ title: 'Logo上传失败，请重试', icon: 'none' });
          return;
        }
      } else {
        console.log('无需上传Logo，使用现有URL:', logoUrl);
        console.log('判断条件:', {
          hasCompanyLogoTemp: !!companyLogoTemp,
          companyLogoTemp: companyLogoTemp,
          currentLogo: companyInfo.company_logo,
          isTempFilePath: isTempFilePath,
          needUploadLogo: needUploadLogo
        });
      }
      
      // 2. 准备保存的数据，过滤掉不需要的字段
      const allowedFields = [
        'company_name', 'company_description', 'company_address', 'company_phone',
        'company_email', 'company_website', 'business_hours', 'company_logo',
        'contact_person', 'fax', 'postal_code', 'company_type', 'established_date',
        'registration_number', 'social_credit_code', 'legal_representative',
        'registered_capital', 'business_scope', 'company_status'
      ];

      const saveData = {};
      allowedFields.forEach(field => {
        if (companyInfo[field] !== undefined) {
          let value = companyInfo[field];

          // 确保数字字段的正确类型转换
          if (field === 'registered_capital' && value !== null && value !== '') {
            value = parseFloat(value) || null;
          }

          // 确保日期字段的正确格式
          if (field === 'established_date' && value) {
            // 确保日期格式为 YYYY-MM-DD
            if (typeof value === 'string' && value.includes('T')) {
              value = value.split('T')[0]; // 去掉时间部分
            }
          }

          // 确保字符串字段不为null且已trim
          if (typeof value === 'string') {
            value = value.trim();
          }

          saveData[field] = value;
        }
      });

      // 使用上传后的Logo URL
      saveData.company_logo = logoUrl;

      console.log('=== 最终保存数据检查 ===');
      console.log('logoUrl值:', logoUrl);
      console.log('saveData.company_logo值:', saveData.company_logo);
      console.log('准备保存的数据:', saveData);
      console.log('company_logo字段是否存在:', 'company_logo' in saveData);
      console.log('company_logo字段值类型:', typeof saveData.company_logo);
      
      let res;
      console.log('开始调用API，公司ID:', companyInfo.id);
      console.log('API调用数据:', JSON.stringify(saveData, null, 2));

      if (companyInfo.id) {
        // 更新现有公司信息
        console.log('调用更新API，ID:', companyInfo.id);
        res = await adminApi.updateCompanyInfo(companyInfo.id, saveData);
      } else {
        // 创建新的公司信息
        console.log('调用创建API');
        res = await adminApi.createCompanyInfo(saveData);
      }

      console.log('API调用结果:', res);
      
      wx.hideLoading();
      
      if (res && res.success) {
        wx.showToast({ title: '保存成功', icon: 'success' });
        // 更新本地数据
        const updatedCompanyInfo = {
          ...companyInfo,
          ...saveData,
          company_logo: logoUrl, // 确保Logo URL被正确更新
          updateTime: Date.now()
        };

        this.setData({
          companyInfo: updatedCompanyInfo,
          companyLogoTemp: logoUrl, // 设置为上传后的Logo URL
          companyCustomImageMode: false // 重置自定义模式标志
        });

        console.log('更新后的公司信息:', updatedCompanyInfo);
        // 不需要重新加载，避免覆盖已更新的数据
        console.log('公司信息保存成功，Logo已更新为:', logoUrl);
      } else {
        wx.showToast({ title: res.message || '保存失败', icon: 'none' });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('保存公司信息失败:', error);
      
      // 检查是否是认证相关错误
      if (error.code === 401 || (error.message && error.message.includes('认证'))) {
        wx.showModal({
          title: '登录已过期',
          content: '请重新登录后再试',
          showCancel: false,
          success: () => {
            loginStateManager.clearLoginState();
            wx.redirectTo({ url: '/pages/auth/auth' });
          }
        });
      } else {
        wx.showToast({ title: '保存失败', icon: 'none' });
      }
    }
  },

  // ==================== 轮播图管理 ====================
  
  // 加载轮播图列表
  async loadBanners() {
    try {
      wx.showLoading({ title: '加载中...' });
      const params = {
        page_type: this.data.selectedPageType
      };
      const res = await adminApi.getBanners(params);
      wx.hideLoading();
      
      if (res.success && res.data) {
        this.setData({ banners: res.data });
      } else {
        this.setData({ banners: [] });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载轮播图失败:', error);
      wx.showToast({ title: '加载轮播图失败', icon: 'none' });
      this.setData({ banners: [] });
    }
  },

  // 刷新轮播图
  refreshBanners() {
    this.loadBanners();
    wx.showToast({ title: '刷新成功', icon: 'success' });
  },

  // 添加轮播图
  addBanner() {
    // 找到当前选中页面类型的索引
    const pageTypeIndex = this.data.pageTypes.findIndex(item => item.value === this.data.selectedPageType);
    
    this.setData({
      editBannerData: {
        title: '',
        image_url: '',
        link_url: '',
        page_type: this.data.selectedPageType,
        sort_order: 0,
        is_active: true
      },
      editBannerIndex: -1,
      editBannerPageTypeIndex: pageTypeIndex,
      pageTypeInputMode: 'select', // 默认选择模式
      customPageType: '', // 清空自定义输入
      showBannerEditModal: true
    });
  },

  // 编辑轮播图
  editBanner(e) {
    const index = e.currentTarget.dataset.index;
    const banner = this.data.banners[index];
    // 找到页面类型的索引
    const pageTypeIndex = this.data.pageTypes.findIndex(item => item.value === banner.page_type);
    
    // 判断是否为已有类型
    const isExistingType = pageTypeIndex >= 0;
    
    this.setData({
      editBannerData: { ...banner },
      editBannerIndex: index,
      editBannerPageTypeIndex: isExistingType ? pageTypeIndex : -1,
      pageTypeInputMode: isExistingType ? 'select' : 'input',
      customPageType: isExistingType ? '' : banner.page_type,
      showBannerEditModal: true
    });
  },

  // 删除轮播图
  deleteBanner(e) {
    const { index, id } = e.currentTarget.dataset;
    const banner = this.data.banners[index];
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除轮播图"${banner.title || '无标题'}"吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '删除中...' });
            const result = await adminApi.deleteBanner(id || banner.id);
            wx.hideLoading();
            
            if (result.success) {
              wx.showToast({ title: '删除成功', icon: 'success' });
              this.loadBanners(); // 重新加载
            } else {
              wx.showToast({ title: result.message || '删除失败', icon: 'none' });
            }
          } catch (error) {
            wx.hideLoading();
            console.error('删除轮播图失败:', error);
            wx.showToast({ title: '删除失败', icon: 'none' });
          }
        }
      }
    });
  },

  // 关闭轮播图编辑弹窗
  closeBannerEditModal() {
    this.setData({
      showBannerEditModal: false,
      editBannerData: {},
      editBannerIndex: -1,
      editBannerPageTypeIndex: -1,
      pageTypeInputMode: 'select',
      customPageType: ''
    });
  },

  // 轮播图字段输入
  onBannerFieldInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`editBannerData.${field}`]: value
    });
  },

  // 轮播图启用状态切换
  onBannerActiveChange(e) {
    this.setData({
      'editBannerData.is_active': e.detail.value
    });
  },

  // 轮播图页面类型选择
  onBannerPageTypeChange(e) {
    const index = e.detail.value;
    const pageType = this.data.pageTypes[index];
    this.setData({
      editBannerPageTypeIndex: index,
      'editBannerData.page_type': pageType.value
    });
  },

  // 切换页面类型输入模式
  switchPageTypeMode(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({
      pageTypeInputMode: mode
    });
    
    // 切换模式时清空对应的数据
    if (mode === 'select') {
      this.setData({
        customPageType: '',
        'editBannerData.page_type': this.data.editBannerPageTypeIndex >= 0 ? 
          this.data.pageTypes[this.data.editBannerPageTypeIndex].value : ''
      });
    } else {
      this.setData({
        editBannerPageTypeIndex: -1,
        'editBannerData.page_type': this.data.customPageType
      });
    }
  },

  // 自定义页面类型输入
  onCustomPageTypeInput(e) {
    const value = e.detail.value;
    this.setData({
      customPageType: value,
      'editBannerData.page_type': value
    });
  },

  // 轮播图输入框变化
  onBannerInputChange(e) {
    const { index, field } = e.currentTarget.dataset;
    const value = e.detail.value;
    const banners = this.data.banners;
    
    banners[index][field] = field === 'sort_order' ? parseInt(value) || 0 : value;
    this.setData({ banners });
  },

  // 轮播图开关变化
  onBannerSwitchChange(e) {
    const { index, field } = e.currentTarget.dataset;
    const value = e.detail.value;
    const banners = this.data.banners;
    
    banners[index][field] = value;
    this.setData({ banners });
  },

  // 保存轮播图修改
  async saveBannerChanges(e) {
    const { index, id } = e.currentTarget.dataset;
    const banner = this.data.banners[index];

    if (!banner.image_url || !banner.image_url.trim()) {
      wx.showToast({ title: '请先上传轮播图片', icon: 'none' });
      return;
    }

    try {
      wx.showLoading({ title: '保存中...' });

      // 1. 上传图片（如果有新选择的图片）
      let imageUrl = banner.image_url;

      // 判断是否是临时文件路径（需要上传的图片）
      const isTempFilePath = imageUrl && (imageUrl.startsWith('wxfile://') ||
                            imageUrl.startsWith('http://tmp') ||
                            imageUrl.startsWith('https://tmp') ||
                            imageUrl.includes('tmp_') ||
                            imageUrl.includes('temp'));

      const needUploadBanner = banner.customImageMode && isTempFilePath;

      if (needUploadBanner) {
        try {
          wx.showLoading({ title: '正在上传图片...' });
          imageUrl = await uploadFile(imageUrl, 'banners');
        } catch (uploadError) {
          console.error('轮播图上传失败:', uploadError);
          wx.hideLoading();
          wx.showToast({ title: '图片上传失败，请重试', icon: 'none' });
          return;
        }
      }

      // 2. 准备保存的数据
      const saveData = {
        ...banner,
        image_url: imageUrl
      };

      const res = await adminApi.updateBanner(id, saveData);

      wx.hideLoading();

      if (res.success) {
        wx.showToast({ title: '保存成功', icon: 'success' });
        // 更新本地数据
        const banners = this.data.banners;
        banners[index] = { ...banners[index], ...saveData, updated_at: Date.now() };
        this.setData({ banners });
      } else {
        wx.showToast({ title: res.message || '保存失败', icon: 'none' });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('保存轮播图失败:', error);
      wx.showToast({ title: '保存失败', icon: 'none' });
    }
  },

  // 更换轮播图图片
  changeBannerImage(e) {
    const { index } = e.currentTarget.dataset;
    
    // 存储当前编辑的轮播图索引，用于后续处理
    this.setData({ editingBannerIndex: index });
    
    console.log('开始选择轮播图图片');
    // 先尝试使用chooseMedia API
    try {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: res => {
          console.log('选择轮播图图片成功:', res);
          const file = res.tempFiles[0];
          if (file.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
            wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
            return;
          }
          // 跳转到裁剪页面
          wx.navigateTo({
            url: `/admin/cropper/cropper?src=${encodeURIComponent(file.tempFilePath)}&type=banner`
          });
        },
        fail: err => {
          console.error('chooseMedia 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消选择图片');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才使用备选方案
          this.changeBannerImageFallback();
        }
      });
    } catch (error) {
      console.error('chooseMedia API 不支持，使用备选方案:', error);
      // 如果chooseMedia不支持，直接使用chooseImage
      this.changeBannerImageFallback();
    }
  },

  // 更换轮播图图片备选方案
  changeBannerImageFallback() {
    console.log('使用chooseImage作为备选方案');
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        console.log('chooseImage成功:', res);
        const tempFilePath = res.tempFilePaths[0];
        const tempFile = res.tempFiles[0];
        if (tempFile && tempFile.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
          wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
          return;
        }
        // 跳转到裁剪页面
        wx.navigateTo({
          url: `/admin/cropper/cropper?src=${encodeURIComponent(tempFilePath)}&type=banner`
        });
      },
      fail: err => {
        console.error('chooseImage 失败:', err);
        // 检查是否是用户取消操作
        if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
          console.log('用户取消选择图片');
          return; // 用户取消，直接返回
        }
        // 其他错误情况才显示提示
        wx.showToast({
          title: '选择图片失败，请检查相关权限',
          icon: 'none'
        });
      }
    });
  },

  // 预览图片
  previewImage(e) {
    const { url } = e.currentTarget.dataset;
    if (url) {
      wx.previewImage({
        urls: [url],
        current: url
      });
    }
  },

  // 上传轮播图片
  uploadBannerImage() {
    console.log('开始选择新轮播图片');
    // 先尝试使用chooseMedia API
    try {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: res => {
          console.log('选择新轮播图片成功:', res);
          const file = res.tempFiles[0];
          if (file.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
            wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
            return;
          }
          // 跳转到裁剪页面
          wx.navigateTo({
            url: `/admin/cropper/cropper?src=${encodeURIComponent(file.tempFilePath)}&type=newBanner`
          });
        },
        fail: err => {
          console.error('chooseMedia 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消选择新轮播图片');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才使用备选方案
          this.uploadBannerImageFallback();
        }
      });
    } catch (error) {
      console.error('chooseMedia 异常:', error);
      // 如果chooseMedia出现异常，尝试使用chooseImage作为备选方案
      this.uploadBannerImageFallback();
    }
  },

  // 上传轮播图片备选方案
  uploadBannerImageFallback() {
    console.log('使用chooseImage作为备选方案选择新轮播图片');
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        console.log('chooseImage选择新轮播图片成功:', res);
        const tempFilePath = res.tempFilePaths[0];
        const tempFile = res.tempFiles[0];
        if (tempFile && tempFile.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
          wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
          return;
        }
        // 跳转到裁剪页面
        wx.navigateTo({
          url: `/admin/cropper/cropper?src=${encodeURIComponent(tempFilePath)}&type=newBanner`
        });
      },
      fail: err => {
        console.error('chooseImage 失败:', err);
        // 检查是否是用户取消操作
        if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
          console.log('用户取消选择新轮播图片');
          return; // 用户取消，直接返回
        }
        // 其他错误情况才显示提示
        wx.showToast({
          title: '选择图片失败，请检查相关权限',
          icon: 'none'
        });
      }
    });
  },

  // 预览轮播图片
  previewBannerImage() {
    if (this.data.editBannerData.image_url) {
      wx.previewImage({
        urls: [this.data.editBannerData.image_url]
      });
    }
  },

  // 删除轮播图片
  deleteBannerImage() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            'editBannerData.image_url': ''
          });
        }
      }
    });
  },

  // 保存轮播图信息
  async saveBannerInfo() {
    const { editBannerData } = this.data;
    
    // 验证必填字段
    if (!editBannerData.image_url || !editBannerData.image_url.trim()) {
      wx.showToast({ title: '请上传轮播图片', icon: 'none' });
      return;
    }
    
    if (!editBannerData.page_type || !editBannerData.page_type.trim()) {
      wx.showToast({ title: '请选择或输入页面类型', icon: 'none' });
      return;
    }

    // 验证自定义页面类型格式
    if (this.data.pageTypeInputMode === 'input') {
      const pageTypePattern = /^[a-z][a-z0-9_]*$/;
      if (!pageTypePattern.test(editBannerData.page_type)) {
        wx.showToast({ title: '页面类型格式不正确，请使用小写字母、数字和下划线', icon: 'none' });
        return;
      }
    }

    try {
      wx.showLoading({ title: '保存中...' });

      // 1. 上传图片（如果有新选择的图片）
      let imageUrl = editBannerData.image_url;

      // 判断是否是临时文件路径（需要上传的图片）
      const isTempFilePath = imageUrl && (imageUrl.startsWith('wxfile://') ||
                            imageUrl.startsWith('http://tmp') ||
                            imageUrl.startsWith('https://tmp') ||
                            imageUrl.includes('tmp_') ||
                            imageUrl.includes('temp'));

      const needUploadBanner = this.data.bannerCustomImageMode && isTempFilePath;

      if (needUploadBanner) {
        try {
          wx.showLoading({ title: '正在上传图片...' });
          imageUrl = await uploadFile(imageUrl, 'banners');
        } catch (uploadError) {
          console.error('轮播图上传失败:', uploadError);
          wx.hideLoading();
          wx.showToast({ title: '图片上传失败，请重试', icon: 'none' });
          return;
        }
      }

      // 2. 准备保存的数据
      const saveData = {
        ...editBannerData,
        image_url: imageUrl
      };
      
      let res;
      if (editBannerData.id) {
        // 更新现有轮播图
        res = await adminApi.updateBanner(editBannerData.id, saveData);
      } else {
        // 创建新轮播图
        res = await adminApi.createBanner(saveData);
      }

      wx.hideLoading();

      if (res.success) {
        wx.showToast({ title: '保存成功', icon: 'success' });
        this.setData({
          showBannerEditModal: false,
          bannerCustomImageMode: false // 重置自定义图片模式
        });

        // 如果是新创建的轮播图且使用了自定义页面类型，需要刷新页面类型列表
        if (!editBannerData.id && this.data.pageTypeInputMode === 'input') {
          this.loadPageTypes(); // 重新加载页面类型
        }

        this.loadBanners(); // 重新加载轮播图
      } else {
        wx.showToast({ title: res.message || '保存失败', icon: 'none' });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('保存轮播图失败:', error);
      wx.showToast({ title: '保存失败', icon: 'none' });
    }
  },

  // ==================== 消息管理 ====================

  // 加载消息列表
  async loadMessages() {
    try {
      this.setData({ messageLoading: true });
      wx.showLoading({ title: '加载中...' });
      
      const params = {
        page: this.data.messagePage,
        pageSize: this.data.messagePageSize,
        filter: this.data.messageFilter
      };
      
      const res = await adminApi.getMessages(params);
      wx.hideLoading();
      
      if (res.success && res.data) {
        // 格式化消息数据
        const messages = res.data.map(msg => ({
          ...msg,
          displayTime: this.formatTime(msg.createTime),
          isBlocked: msg.is_blocked || false
        }));
        
        this.setData({ 
          messages: this.data.messagePage === 1 ? messages : [...this.data.messages, ...messages],
          messageLoading: false
        });
      } else {
        this.setData({ 
          messages: this.data.messagePage === 1 ? [] : this.data.messages,
          messageLoading: false
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载消息失败:', error);
      wx.showToast({ title: '加载消息失败', icon: 'none' });
      this.setData({ messageLoading: false });
    }
  },

  // 切换消息筛选
  switchMessageFilter(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({ 
      messageFilter: filter,
      messagePage: 1,
      messages: []
    });
    this.loadMessages();
  },

  // 刷新消息
  refreshMessages() {
    this.setData({ 
      messagePage: 1,
      messages: []
    });
    this.loadMessages();
    wx.showToast({ title: '刷新成功', icon: 'success' });
  },

  // 切换消息屏蔽状态
  async toggleMessageBlock(e) {
    const { id, index } = e.currentTarget.dataset;
    const isBlocked = e.detail.value;
    
    try {
      wx.showLoading({ title: '处理中...' });
      const res = await adminApi.toggleMessageBlock(id, isBlocked);
      wx.hideLoading();
      
      if (res.success) {
        // 更新本地数据
        const messages = this.data.messages;
        messages[index].isBlocked = isBlocked;
        this.setData({ messages });
        
        wx.showToast({ 
          title: isBlocked ? '已屏蔽' : '已取消屏蔽', 
          icon: 'success' 
        });
      } else {
        wx.showToast({ title: res.message || '操作失败', icon: 'none' });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('切换消息屏蔽状态失败:', error);
      wx.showToast({ title: '操作失败', icon: 'none' });
    }
  },

  // ==================== 常见问题管理 ====================

  // 加载常见问题列表
  async loadFaqList() {
    try {
      wx.showLoading({ title: '加载中...' });
      const res = await adminApi.getFaqList();
      wx.hideLoading();
      
      if (res.success && res.data) {
        this.setData({ faqList: res.data });
      } else {
        this.setData({ faqList: [] });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载常见问题失败:', error);
      wx.showToast({ title: '加载常见问题失败', icon: 'none' });
      this.setData({ faqList: [] });
    }
  },

  // 添加常见问题
  addFaq() {
    this.setData({
      editFaqData: {
        question: '',
        answer: '',
        sort_order: 0,
        is_active: true
      },
      editFaqIndex: -1,
      showFaqEditModal: true
    });
  },

  // 编辑常见问题
  editFaq(e) {
    const index = e.currentTarget.dataset.index;
    const faq = this.data.faqList[index];
    
    this.setData({
      editFaqData: { ...faq },
      editFaqIndex: index,
      showFaqEditModal: true
    });
  },

  // 删除常见问题
  deleteFaq(e) {
    const { index, id } = e.currentTarget.dataset;
    const faq = this.data.faqList[index];
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除问题"${faq.question}"吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '删除中...' });
            const result = await adminApi.deleteFaq(id || faq.id);
            wx.hideLoading();
            
            if (result.success) {
              wx.showToast({ title: '删除成功', icon: 'success' });
              this.loadFaqList(); // 重新加载
            } else {
              wx.showToast({ title: result.message || '删除失败', icon: 'none' });
            }
          } catch (error) {
            wx.hideLoading();
            console.error('删除常见问题失败:', error);
            wx.showToast({ title: '删除失败', icon: 'none' });
          }
        }
      }
    });
  },

  // 关闭常见问题编辑弹窗
  closeFaqEditModal() {
    this.setData({
      showFaqEditModal: false,
      editFaqData: {},
      editFaqIndex: -1
    });
  },

  // 常见问题字段输入
  onFaqFieldInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`editFaqData.${field}`]: value
    });
  },

  // 常见问题启用状态切换
  onFaqActiveChange(e) {
    this.setData({
      'editFaqData.is_active': e.detail.value
    });
  },

  // 常见问题输入框变化
  onFaqInputChange(e) {
    const { index, field } = e.currentTarget.dataset;
    const value = e.detail.value;
    const faqList = this.data.faqList;
    
    faqList[index][field] = field === 'sort_order' ? parseInt(value) || 0 : value;
    this.setData({ faqList });
  },

  // 常见问题开关变化
  onFaqSwitchChange(e) {
    const { index, field } = e.currentTarget.dataset;
    const value = e.detail.value;
    const faqList = this.data.faqList;
    
    faqList[index][field] = value;
    this.setData({ faqList });
  },

  // 保存常见问题修改
  async saveFaqChanges(e) {
    const { index, id } = e.currentTarget.dataset;
    const faq = this.data.faqList[index];
    
    if (!faq.question || !faq.question.trim()) {
      wx.showToast({ title: '请输入问题标题', icon: 'none' });
      return;
    }
    
    if (!faq.answer || !faq.answer.trim()) {
      wx.showToast({ title: '请输入问题答案', icon: 'none' });
      return;
    }
    
    try {
      wx.showLoading({ title: '保存中...' });
      const res = await adminApi.updateFaq(id, faq);
      wx.hideLoading();
      
      if (res.success) {
        wx.showToast({ title: '保存成功', icon: 'success' });
        // 更新本地数据的updated_at时间戳
        const faqList = this.data.faqList;
        faqList[index].updated_at = Date.now();
        this.setData({ faqList });
      } else {
        wx.showToast({ title: res.message || '保存失败', icon: 'none' });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('保存常见问题失败:', error);
      wx.showToast({ title: '保存失败', icon: 'none' });
    }
  },

  // 保存常见问题信息
  async saveFaqInfo() {
    const { editFaqData } = this.data;
    
    // 验证必填字段
    if (!editFaqData.question || !editFaqData.question.trim()) {
      wx.showToast({ title: '请输入问题标题', icon: 'none' });
      return;
    }
    
    if (!editFaqData.answer || !editFaqData.answer.trim()) {
      wx.showToast({ title: '请输入问题答案', icon: 'none' });
      return;
    }

    try {
      wx.showLoading({ title: '保存中...' });
      
      let res;
      if (editFaqData.id) {
        // 更新现有常见问题
        res = await adminApi.updateFaq(editFaqData.id, editFaqData);
      } else {
        // 创建新常见问题
        res = await adminApi.createFaq(editFaqData);
      }
      
      wx.hideLoading();
      
      if (res.success) {
        wx.showToast({ title: '保存成功', icon: 'success' });
        this.setData({ showFaqEditModal: false });
        this.loadFaqList(); // 重新加载常见问题
      } else {
        wx.showToast({ title: res.message || '保存失败', icon: 'none' });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('保存常见问题失败:', error);
      wx.showToast({ title: '保存失败', icon: 'none' });
    }
  },

  // ==================== 工具方法 ====================

  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    // 小于1分钟
    if (diff < 60000) {
      return '刚刚';
    }
    // 小于1小时
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    }
    // 小于24小时
    if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前';
    }
    // 小于30天
    if (diff < 2592000000) {
      return Math.floor(diff / 86400000) + '天前';
    }
    
    // 超过30天显示具体日期
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  // 空的事件处理函数，用于阻止事件冒泡
  preventBubble() {
    // 什么都不做，只是阻止事件冒泡
  }
});