/**
 * 管理员消息控制器
 */
const messageService = require('../services/messageService');
const User = require('../models/User');

// 获取所有消息（管理员权限）
exports.getMessages = async (req, res) => {
  try {
    console.log('[管理员消息控制器] 收到获取消息请求');
    console.log('[管理员消息控制器] 请求参数:', req.query);

    const { page = 1, pageSize = 20, filter = 'all' } = req.query;

    // 构建查询条件
    let whereClause = '';
    let queryParams = [];

    if (filter === 'private') {
      // 个人聊天消息
      whereClause = 'WHERE (m.type = "private" OR m.type = "text")';
    } else if (filter === 'group') {
      // 群聊消息
      whereClause = 'WHERE m.type = "group"';
    }

    // 查询消息列表
    const offset = (page - 1) * pageSize;
    const query = `
      SELECT 
        m.id,
        m.senderId,
        m.receiverId,
        m.content,
        m.type,
        m.createTime,
        m.isRead,
        m.is_blocked,
        s.nickname as senderName,
        s.avatar as senderAvatar,
        r.nickname as receiverName,
        r.avatar as receiverAvatar
      FROM messages m
              LEFT JOIN users s ON m.senderId = s.user_id
        LEFT JOIN users r ON m.receiverId = r.user_id
      ${whereClause}
      ORDER BY m.createTime DESC
      LIMIT ? OFFSET ?
    `;

    const messages = await messageService.queryMessages(query, [...queryParams, parseInt(pageSize), offset]);

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM messages m
      ${whereClause}
    `;
    
    const countResult = await messageService.queryMessages(countQuery, queryParams);
    const total = countResult[0]?.total || 0;

    console.log('[管理员消息控制器] 查询结果:', messages.length, '条消息');

    res.json({
      success: true,
      data: messages,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total,
        totalPages: Math.ceil(total / pageSize)
      },
      message: '获取消息成功'
    });

  } catch (error) {
    console.error('[管理员消息控制器] 获取消息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取消息失败',
      error: error.message
    });
  }
};

// 切换消息屏蔽状态
exports.toggleMessageBlock = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_blocked } = req.body;

    console.log('[管理员消息控制器] 切换消息屏蔽状态:', id, is_blocked);

    const result = await messageService.toggleMessageBlock(id, is_blocked);

    if (result.success) {
      res.json({
        success: true,
        message: is_blocked ? '消息已屏蔽' : '消息已取消屏蔽'
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message || '操作失败'
      });
    }

  } catch (error) {
    console.error('[管理员消息控制器] 切换消息屏蔽状态失败:', error);
    res.status(500).json({
      success: false,
      message: '操作失败',
      error: error.message
    });
  }
};

// 删除消息
exports.deleteMessage = async (req, res) => {
  try {
    const { id } = req.params;

    console.log('[管理员消息控制器] 删除消息:', id);

    const result = await messageService.deleteMessage(id);

    if (result.success) {
      res.json({
        success: true,
        message: '消息删除成功'
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message || '删除失败'
      });
    }

  } catch (error) {
    console.error('[管理员消息控制器] 删除消息失败:', error);
    res.status(500).json({
      success: false,
      message: '删除失败',
      error: error.message
    });
  }
};

// 获取消息统计信息
exports.getMessageStats = async (req, res) => {
  try {
    console.log('[管理员消息控制器] 获取消息统计信息');

    const stats = await messageService.getMessageStats();

    res.json({
      success: true,
      data: stats,
      message: '获取统计信息成功'
    });

  } catch (error) {
    console.error('[管理员消息控制器] 获取消息统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计信息失败',
      error: error.message
    });
  }
}; 