const { userApi } = require('../../utils/api');

Page({
  data: {
    address: {
      id: '',
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: '',
      isDefault: false
    },
    regionArray: ['北京市', '北京市', '东城区'], // 地区选择器的值
    regionText: '', // 地区文本显示
    isEdit: false, // 是否为编辑模式
    loading: false
  },

  onLoad: function(options) {
    console.log('收货地址编辑页面加载');
    
    // 判断是新增还是编辑
    if (options.id) {
      this.setData({
        isEdit: true
      });
      this.getAddressDetail(options.id);
    } else {
      this.setData({
        isEdit: false
      });
    }
  },

  // 获取地址详情
  getAddressDetail: function(id) {
    console.log('获取地址详情:', id);
    
    userApi.getAddressList()
      .then(res => {
        if (res && res.success && res.data) {
          const address = res.data.find(item => item.id == id);
          if (address) {
            // 设置地区选择器的初始值
            const regionArray = [
              address.province || '北京市',
              address.city || '北京市',
              address.district || '东城区'
            ];
            const regionText = regionArray.join(' ');
            
            this.setData({
              address: address,
              regionArray: regionArray,
              regionText: regionText
            });
          }
        }
      })
      .catch(err => {
        console.error('获取地址详情失败:', err);
        wx.showToast({
          title: '获取地址详情失败',
          icon: 'none'
        });
      });
  },

  // 收货人姓名输入
  onNameInput: function(e) {
    this.setData({
      'address.name': e.detail.value
    });
  },

  // 手机号码输入
  onPhoneInput: function(e) {
    this.setData({
      'address.phone': e.detail.value
    });
  },

  // 详细地址输入
  onDetailInput: function(e) {
    this.setData({
      'address.detail': e.detail.value
    });
  },

  // 默认地址开关
  onDefaultChange: function(e) {
    this.setData({
      'address.isDefault': e.detail.value
    });
  },

  // 地区选择器改变事件
  onRegionChange: function(e) {
    console.log('地区选择结果:', e.detail.value);
    const regionArray = e.detail.value;
    
    this.setData({
      'address.province': regionArray[0],
      'address.city': regionArray[1],
      'address.district': regionArray[2],
      regionArray: regionArray
    });
    
    // 显示选择结果
    wx.showToast({
      title: '地区选择成功',
      icon: 'success'
    });
  },

  // 兼容旧版本的selectRegion方法
  selectRegion: function() {
    console.log('selectRegion被调用');
    // 这个方法是为了兼容旧版本，现在直接使用picker组件
  },

  // 地区选择器改变事件
  onRegionChange: function(e) {
    console.log('地区选择结果:', e.detail.value);
    const regionArray = e.detail.value;
    const regionText = regionArray.join(' ');

    this.setData({
      'address.province': regionArray[0],
      'address.city': regionArray[1],
      'address.district': regionArray[2],
      regionArray: regionArray,
      regionText: regionText
    });

    // 显示选择结果
    wx.showToast({
      title: '地区选择成功',
      icon: 'success'
    });
  },



  // 验证表单
  validateForm: function() {
    const address = this.data.address;
    
    if (!address.name || address.name.trim() === '') {
      wx.showToast({
        title: '请输入收货人姓名',
        icon: 'none'
      });
      return false;
    }
    
    if (!address.phone || address.phone.trim() === '') {
      wx.showToast({
        title: '请输入手机号码',
        icon: 'none'
      });
      return false;
    }
    
    // 验证手机号格式
    const phoneReg = /^1[3-9]\d{9}$/;
    if (!phoneReg.test(address.phone)) {
      wx.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      });
      return false;
    }
    
    if (!address.province || !address.city || !address.district) {
      wx.showToast({
        title: '请选择所在地区',
        icon: 'none'
      });
      return false;
    }
    
    if (!address.detail || address.detail.trim() === '') {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  // 保存地址
  saveAddress: function() {
    if (!this.validateForm()) {
      return;
    }
    
    this.setData({ loading: true });
    
    const address = this.data.address;
    const apiCall = this.data.isEdit ? 
      userApi.updateAddress(address.id, address) : 
      userApi.addAddress(address);
    
    apiCall.then(res => {
      this.setData({ loading: false });
      
      if (res && res.success) {
        wx.showToast({
          title: this.data.isEdit ? '修改成功' : '添加成功',
          icon: 'success'
        });
        
        // 延迟返回，让用户看到成功提示
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: res.message || '操作失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      this.setData({ loading: false });
      console.error('保存地址失败:', err);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    });
  },

  // 删除地址
  deleteAddress: function() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个收货地址吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ loading: true });
          
          userApi.deleteAddress(this.data.address.id)
            .then(res => {
              this.setData({ loading: false });
              
              if (res && res.success) {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                setTimeout(() => {
                  wx.navigateBack();
                }, 1500);
              } else {
                wx.showToast({
                  title: res.message || '删除失败',
                  icon: 'none'
                });
              }
            })
            .catch(err => {
              this.setData({ loading: false });
              console.error('删除地址失败:', err);
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            });
        }
      }
    });
  }
}); 