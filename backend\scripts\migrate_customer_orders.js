/**
 * 数据库迁移脚本：创建顾客订单专用表结构
 * 解决订单拆分导致的数据量爆炸和查询性能问题
 * 
 * 使用方法：
 * node backend/scripts/migrate_customer_orders.js
 */

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'morebuy',
  charset: 'utf8mb4'
};

async function migrateDatabase() {
  let connection;
  
  try {
    console.log('🔄 开始数据库迁移：创建顾客订单专用表结构...');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 创建顾客主订单表
    console.log('➕ 创建顾客主订单表 customer_orders...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS customer_orders (
        id INT NOT NULL AUTO_INCREMENT COMMENT '主订单ID',
        order_no VARCHAR(50) NOT NULL COMMENT '订单号',
        user_id VARCHAR(32) NOT NULL COMMENT '顾客用户ID',
        salesman_id VARCHAR(32) NULL COMMENT '销售人ID',
        delivery_method ENUM('express', 'self') NOT NULL DEFAULT 'express' COMMENT '配送方式：express=快递，self=自提',
        address_id VARCHAR(50) NULL COMMENT '收货地址ID（快递时使用）',
        store_id VARCHAR(50) NULL COMMENT '自提门店ID（自提时使用）',
        payment_methods JSON NULL COMMENT '支付方式JSON数组',
        total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
        total_quantity INT NOT NULL DEFAULT 0 COMMENT '商品总数量',
        sub_order_count INT NOT NULL DEFAULT 0 COMMENT '子订单数量',
        status VARCHAR(32) NOT NULL DEFAULT '待支付' COMMENT '订单状态',
        paid_at BIGINT NULL COMMENT '支付时间',
        shipped_at BIGINT NULL COMMENT '发货时间',
        completed_at BIGINT NULL COMMENT '完成时间',
        created_at BIGINT NOT NULL COMMENT '创建时间',
        updated_at BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY uk_order_no (order_no),
        KEY idx_user_id (user_id),
        KEY idx_salesman_id (salesman_id),
        KEY idx_status (status),
        KEY idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='顾客主订单表'
    `);
    console.log('✅ customer_orders 表创建成功');
    
    // 2. 创建顾客子订单表
    console.log('➕ 创建顾客子订单表 customer_sub_orders...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS customer_sub_orders (
        id INT NOT NULL AUTO_INCREMENT COMMENT '子订单ID',
        main_order_id INT NOT NULL COMMENT '主订单ID',
        sub_order_no VARCHAR(50) NOT NULL COMMENT '子订单号',
        store_no VARCHAR(32) NOT NULL COMMENT '门店编号',
        store_type ENUM('subscribe', 'salesman', 'platform') NOT NULL COMMENT '门店类型：subscribe=订阅门店，salesman=销售人门店，platform=平台总部',
        sub_total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '子订单金额',
        sub_total_quantity INT NOT NULL DEFAULT 0 COMMENT '子订单商品数量',
        status VARCHAR(32) NOT NULL DEFAULT '待支付' COMMENT '子订单状态',
        shipped_at BIGINT NULL COMMENT '发货时间',
        completed_at BIGINT NULL COMMENT '完成时间',
        created_at BIGINT NOT NULL COMMENT '创建时间',
        updated_at BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY uk_sub_order_no (sub_order_no),
        KEY idx_main_order_id (main_order_id),
        KEY idx_store_no (store_no),
        KEY idx_store_type (store_type),
        KEY idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='顾客子订单表'
    `);
    console.log('✅ customer_sub_orders 表创建成功');
    
    // 3. 创建顾客订单商品明细表
    console.log('➕ 创建顾客订单商品明细表 customer_order_items...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS customer_order_items (
        id INT NOT NULL AUTO_INCREMENT COMMENT '明细ID',
        main_order_id INT NOT NULL COMMENT '主订单ID',
        sub_order_id INT NOT NULL COMMENT '子订单ID',
        product_id VARCHAR(50) NOT NULL COMMENT '商品ID',
        product_name VARCHAR(255) NOT NULL COMMENT '商品名称',
        product_image VARCHAR(500) NULL COMMENT '商品图片',
        product_price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
        quantity INT NOT NULL COMMENT '购买数量',
        subtotal DECIMAL(10,2) NOT NULL COMMENT '小计金额',
        created_at BIGINT NOT NULL COMMENT '创建时间',
        PRIMARY KEY (id),
        KEY idx_main_order_id (main_order_id),
        KEY idx_sub_order_id (sub_order_id),
        KEY idx_product_id (product_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='顾客订单商品明细表'
    `);
    console.log('✅ customer_order_items 表创建成功');
    
    // 4. 为原orders表添加订单来源标识
    console.log('➕ 为orders表添加order_source字段...');
    try {
      await connection.execute(`
        ALTER TABLE orders 
        ADD COLUMN order_source ENUM('legacy', 'customer', 'store') DEFAULT 'legacy' 
        COMMENT '订单来源：legacy=历史订单，customer=顾客订单，store=门店订单' AFTER type
      `);
      console.log('✅ order_source字段添加成功');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  order_source字段已存在，跳过添加');
      } else {
        throw error;
      }
    }
    
    // 5. 创建索引
    console.log('➕ 创建索引...');
    try {
      await connection.execute(`CREATE INDEX idx_order_source ON orders (order_source)`);
      console.log('✅ 索引 idx_order_source 创建成功');
    } catch (error) {
      if (error.code === 'ER_DUP_KEYNAME') {
        console.log('ℹ️  索引 idx_order_source 已存在，跳过创建');
      } else {
        throw error;
      }
    }
    
    // 6. 更新现有订单的来源标识
    console.log('🔄 更新现有订单的来源标识...');
    const [updateResult] = await connection.execute(`
      UPDATE orders SET order_source = CASE 
        WHEN type IN ('cart', 'normal') THEN 'customer'
        WHEN type IN ('purchase', 'transfer') THEN 'store'
        ELSE 'legacy'
      END WHERE order_source = 'legacy'
    `);
    console.log(`✅ 已更新 ${updateResult.affectedRows} 个订单的来源标识`);
    
    // 7. 创建视图
    console.log('➕ 创建查询视图...');
    await connection.execute(`
      CREATE OR REPLACE VIEW v_customer_order_summary AS
      SELECT 
        co.id,
        co.order_no,
        co.user_id,
        co.salesman_id,
        co.delivery_method,
        co.total_amount,
        co.total_quantity,
        co.sub_order_count,
        co.status,
        co.created_at,
        co.updated_at,
        GROUP_CONCAT(DISTINCT cso.store_no) as involved_stores,
        GROUP_CONCAT(DISTINCT cso.store_type) as store_types
      FROM customer_orders co
      LEFT JOIN customer_sub_orders cso ON co.id = cso.main_order_id
      GROUP BY co.id
    `);
    console.log('✅ 视图 v_customer_order_summary 创建成功');
    
    // 8. 验证表创建结果
    console.log('🔍 验证表创建结果...');
    const [tables] = await connection.execute(`
      SELECT 
        TABLE_NAME,
        TABLE_COMMENT,
        TABLE_ROWS
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? 
      AND TABLE_NAME IN ('customer_orders', 'customer_sub_orders', 'customer_order_items')
    `, [dbConfig.database]);
    
    console.log('📊 创建的表:');
    console.table(tables);
    
    // 9. 显示orders表的订单来源分布
    console.log('📊 orders表订单来源分布:');
    const [sourceStats] = await connection.execute(`
      SELECT 
        order_source,
        type,
        COUNT(*) as count
      FROM orders 
      GROUP BY order_source, type
      ORDER BY order_source, type
    `);
    console.table(sourceStats);
    
    console.log('🎉 数据库迁移完成！');
    console.log('');
    console.log('📋 迁移总结:');
    console.log('✅ 创建了专门的顾客订单表结构');
    console.log('✅ 分离了顾客订单和门店订单');
    console.log('✅ 支持大规模订单拆分');
    console.log('✅ 优化了查询性能');
    console.log('✅ 保持了数据一致性');
    console.log('');
    console.log('🔄 下一步操作:');
    console.log('1. 更新后端代码使用新的顾客订单表');
    console.log('2. 迁移现有顾客订单数据（如果需要）');
    console.log('3. 测试新的订单拆分功能');
    console.log('4. 更新前端查询接口');
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行迁移
migrateDatabase();
