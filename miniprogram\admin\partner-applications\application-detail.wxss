/* admin/partner-applications/application-detail.wxss */
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

.card {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.status.pending {
  background-color: #fef6e6;
  color: #fa8c16;
}

.status.approved {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status.rejected {
  background-color: #fff1f0;
  color: #f5222d;
}

.card-body {
  padding: 30rpx;
}

.applicant-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

.user-info {
  flex: 1;
}

.name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.nickname {
  font-size: 28rpx;
  color: #666;
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  line-height: 1.6;
}

.label {
  width: 160rpx;
  color: #999;
}

.value {
  flex: 1;
  color: #333;
}

.value.phone {
  color: #1890ff;
}

.call-icon {
  margin-left: 10rpx;
}

.value.remark {
  background-color: #f9f9f9;
  padding: 16rpx;
  border-radius: 8rpx;
  word-break: break-all;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
}

.btn-reject, .btn-approve {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 30rpx;
}

.btn-reject {
  background-color: #fff;
  color: #f5222d;
  border: 1rpx solid #f5222d;
}

.btn-approve {
  background-color: #1aad19;
  color: #fff;
}

/* 备注弹窗 */
.remark-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.remark-content {
  width: 80%;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
}

.remark-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}

.remark-input {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
}

.remark-buttons {
  display: flex;
  justify-content: space-between;
}

.btn-cancel, .btn-confirm {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 30rpx;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.btn-confirm {
  background-color: #1aad19;
  color: #fff;
}