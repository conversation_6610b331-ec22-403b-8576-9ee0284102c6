const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'molI2505$',
  database: 'morebuy'
};

async function continueMigration() {
  let connection;
  
  try {
    console.log('🔗 连接到数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 步骤1: 删除主键约束
    console.log('\n🔄 步骤1: 删除主键约束');
    await connection.execute('ALTER TABLE users DROP PRIMARY KEY');
    console.log('✅ 步骤1完成');
    
    // 步骤2: 添加新的自增id字段作为主键
    console.log('\n🔄 步骤2: 添加新的自增id字段作为主键');
    await connection.execute('ALTER TABLE users ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST');
    console.log('✅ 步骤2完成');
    
    // 步骤3: 为user_id字段添加唯一索引
    console.log('\n🔄 步骤3: 为user_id字段添加唯一索引');
    await connection.execute('ALTER TABLE users ADD UNIQUE INDEX idx_user_id (user_id)');
    console.log('✅ 步骤3完成');
    
    // 验证迁移结果
    console.log('\n🔍 验证迁移结果...');
    
    // 检查表结构
    const [columns] = await connection.execute('DESCRIBE users');
    console.log('📊 users表结构:');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Key ? `(${col.Key})` : ''} ${col.Extra ? `[${col.Extra}]` : ''}`);
    });
    
    // 检查数据
    const [users] = await connection.execute('SELECT id, user_id, nickname, phone FROM users LIMIT 5');
    console.log('\n📋 用户数据示例:');
    users.forEach(user => {
      console.log(`  - ID: ${user.id}, UserID: ${user.user_id}, 昵称: ${user.nickname}`);
    });
    
    console.log('\n🎉 数据库迁移完成！');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行迁移
continueMigration()
  .then(() => {
    console.log('✅ 所有操作完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 操作失败:', error);
    process.exit(1);
  }); 