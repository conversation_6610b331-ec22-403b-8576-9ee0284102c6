/* pages/order/list.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 30rpx;
}

/* 标签栏样式 */
.tabs-container {
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  z-index: 100;
  white-space: nowrap;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tabs {
  display: flex;
  padding: 0 20rpx;
  height: 88rpx;
  align-items: center;
}

.tab-item {
  position: relative;
  padding: 0 30rpx;
  height: 88rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.tab-item.active {
  color: #ff6b00;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff6b00;
  border-radius: 2rpx;
}

/* 订单列表样式 */
.order-list {
  padding: 20rpx;
}

.order-item {
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-no {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 26rpx;
  color: #ff6b00;
  font-weight: 500;
}

/* 订单商品 */
.order-products {
  padding: 0 24rpx;
}

.product-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-specs {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.product-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.product-price {
  font-size: 28rpx;
  color: #ff6b00;
  font-weight: 500;
}

.product-qty {
  font-size: 24rpx;
  color: #999;
}

/* 订单金额 */
.order-total {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20rpx 24rpx;
  border-top: 1rpx solid #f0f0f0;
  font-size: 26rpx;
  color: #666;
}

.order-total text:last-child {
  margin-left: 20rpx;
  color: #333;
  font-weight: 500;
}

/* 订单操作按钮 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20rpx 24rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.action-btn {
  padding: 0 24rpx;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-left: 20rpx;
  background-color: #f5f5f5;
  color: #666;
  text-align: center;
  border: 1rpx solid #e0e0e0;
}

.action-btn.primary {
  background-color: #ff6b00;
  color: #fff;
  border: none;
}

.action-btn.cancel {
  background-color: #fff;
  color: #999;
  border: 1rpx solid #e0e0e0;
}

/* 加载中 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #999;
}