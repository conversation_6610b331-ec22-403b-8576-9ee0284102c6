-- 插入常见问题示例数据
INSERT INTO `faq` (`question`, `answer`, `sort_order`, `is_active`, `createTime`, `updateTime`) VALUES
('如何注册账号？', '点击首页的"登录"按钮，使用微信授权即可快速注册账号。', 1, 1, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
('如何修改个人信息？', '进入"我的"页面，点击"账号设置"即可修改头像、昵称等个人信息。', 2, 1, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
('如何查看订单？', '在"我的"页面的"我的订单"区域可以查看所有订单状态。', 3, 1, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
('如何申请退款？', '在订单详情页面点击"申请退款"按钮，填写退款原因即可。', 4, 1, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
('如何联系客服？', '在"我的"页面点击"在线客服"即可联系客服人员。', 5, 1, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
('如何成为VIP会员？', '在"我的"页面点击"会员中心"，选择相应的会员等级进行开通。', 6, 1, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
('如何查看物流信息？', '在订单详情页面点击"查看物流"即可查看最新的物流信息。', 7, 1, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
('如何添加收货地址？', '在"我的"页面点击"收货地址"，然后点击"添加新地址"即可。', 8, 1, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000); 