const db = require('../config/db');

/**
 * 获取门店资金信息
 * GET /api/store/funds/:storeNo
 */
exports.getStoreFunds = async (req, res) => {
  try {
    const { storeNo } = req.params;
    
    if (!storeNo) {
      return res.status(400).json({ success: false, message: '缺少门店编号' });
    }
    
    // 查询门店信息
    const storeRows = await db.query('SELECT id, store_no, name, capital FROM stores WHERE store_no = ?', [storeNo]);
    if (storeRows.length === 0) {
      return res.status(404).json({ success: false, message: '门店不存在' });
    }
    
    const store = storeRows[0];
    
    // 查询门店资金信息
    const fundRows = await db.query('SELECT * FROM store_funds WHERE store_id = ?', [store.id]);
    let fundInfo = null;
    
    if (fundRows.length > 0) {
      fundInfo = fundRows[0];
    } else {
      // 如果门店资金记录不存在，创建默认记录
      const now = Date.now();
      await db.query('INSERT INTO store_funds (store_id, total_shares, capital, profit, reserve_fund, bonus_pool, update_time) VALUES (?, ?, ?, ?, ?, ?, ?)', 
        [store.id, 0, store.capital || 0, 0, 0, 0, now]);
      
      fundInfo = {
        store_id: store.id,
        total_shares: 0,
        capital: store.capital || 0,
        profit: 0,
        reserve_fund: 0,
        bonus_pool: 0,
        update_time: now
      };
    }
    
    res.json({
      success: true,
      data: {
        store: {
          id: store.id,
          store_no: store.store_no,
          name: store.name
        },
        funds: fundInfo
      }
    });
    
  } catch (error) {
    console.error('获取门店资金信息失败:', error);
    res.status(500).json({ success: false, message: '获取门店资金信息失败', error: error.message });
  }
};

/**
 * 记录门店资金变动
 * POST /api/store/funds/record
 */
exports.createFundRecord = async (req, res) => {
  try {
    const { store_no, type, amount, description, voucher_images } = req.body;
    
    if (!store_no || !type || !amount) {
      return res.status(400).json({ success: false, message: '缺少必要参数' });
    }
    
    // 查询门店信息
    const storeRows = await db.query('SELECT id FROM stores WHERE store_no = ?', [store_no]);
    if (storeRows.length === 0) {
      return res.status(404).json({ success: false, message: '门店不存在' });
    }
    
    const storeId = storeRows[0].id;
    
    // 生成凭证编号（与管理端保持一致）
    const now = new Date();
    const timeStr = now.getFullYear().toString() +
      String(now.getMonth() + 1).padStart(2, '0') +
      String(now.getDate()).padStart(2, '0') +
      String(now.getHours()).padStart(2, '0') +
      String(now.getMinutes()).padStart(2, '0') +
      String(now.getSeconds()).padStart(2, '0');
    
    // 先插入记录，获取自增ID
    const insertResult = await db.query('INSERT INTO fund_records (store_id, type, amount, description, created_at, voucher_images) VALUES (?, ?, ?, ?, ?, ?)', 
      [store_no, type, amount, description, now, voucher_images ? JSON.stringify(voucher_images) : null]);
    
    const recordId = insertResult.insertId;
    
    // 生成凭证号：分类缩写+时间戳+自增ID
    const typeShort = type === 'purchase_payment' ? 'HK' : 'QT'; // 采购支付对应"支付货款"
    const voucherNo = `${typeShort}${timeStr}-${String(recordId).padStart(4, '0')}`;
    
    // 更新凭证号
    await db.query('UPDATE fund_records SET voucher_no = ? WHERE id = ?', [voucherNo, recordId]);
    
    // 更新门店资金信息
    if (type === 'purchase_payment') {
      // 采购支付：扣减股本金
      await db.query('UPDATE store_funds SET capital = capital - ?, update_time = ? WHERE store_id = ?', 
        [Math.abs(amount), Date.now(), storeId]);
      
      // 同时更新stores表的capital字段
      await db.query('UPDATE stores SET capital = capital - ? WHERE id = ?', 
        [Math.abs(amount), storeId]);
    } else if (type === 'profit_income') {
      // 利润收入：增加利润
      await db.query('UPDATE store_funds SET profit = profit + ?, update_time = ? WHERE store_id = ?', 
        [Math.abs(amount), Date.now(), storeId]);
    }
    
    res.json({
      success: true,
      data: {
        voucher_no: voucherNo,
        record_id: recordId
      }
    });
    
  } catch (error) {
    console.error('记录门店资金变动失败:', error);
    res.status(500).json({ success: false, message: '记录门店资金变动失败', error: error.message });
  }
};

/**
 * 获取门店资金变动记录
 * GET /api/store/funds/records/:storeNo
 */
exports.getFundRecords = async (req, res) => {
  try {
    const { storeNo } = req.params;
    const { limit = 20, offset = 0 } = req.query;
    
    if (!storeNo) {
      return res.status(400).json({ success: false, message: '缺少门店编号' });
    }
    
    // 查询资金变动记录
    const records = await db.query(
      'SELECT * FROM fund_records WHERE store_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?', 
      [storeNo, parseInt(limit), parseInt(offset)]
    );
    
    // 查询总记录数
    const countRows = await db.query('SELECT COUNT(*) as total FROM fund_records WHERE store_id = ?', [storeNo]);
    const total = countRows[0].total;
    
    res.json({
      success: true,
      data: {
        records: records,
        total: total,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
    
  } catch (error) {
    console.error('获取门店资金变动记录失败:', error);
    res.status(500).json({ success: false, message: '获取门店资金变动记录失败', error: error.message });
  }
}; 