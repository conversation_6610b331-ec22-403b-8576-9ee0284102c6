/**
 * 公司信息模型
 */
const db = require('../config/db');

class CompanyInfo {
  /**
   * 获取公司信息
   * @param {number} id - 公司信息ID，如果不传则获取第一条记录
   * @returns {Object|null} 公司信息对象
   */
  static async findById(id = null) {
    try {
      let sql, params;
      if (id) {
        sql = 'SELECT * FROM company_info WHERE id = ?';
        params = [id];
      } else {
        sql = 'SELECT * FROM company_info ORDER BY id ASC LIMIT 1';
        params = [];
      }
      
      const result = await db.query(sql, params);
      return result.length > 0 ? result[0] : null;
    } catch (error) {
      console.error('获取公司信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有公司信息
   * @returns {Array} 公司信息列表
   */
  static async findAll() {
    try {
      const sql = 'SELECT * FROM company_info ORDER BY createTime DESC';
      const result = await db.query(sql);
      return result;
    } catch (error) {
      console.error('获取公司信息列表失败:', error);
      throw error;
    }
  }

  /**
   * 创建公司信息
   * @param {Object} companyData - 公司信息数据
   * @returns {Object} 创建结果
   */
  static async create(companyData) {
    try {
      const {
        company_name,
        company_description,
        company_address,
        company_phone,
        company_email,
        company_website,
        business_hours,
        company_logo,
        contact_person,
        fax,
        postal_code,
        company_type,
        established_date,
        registration_number,
        social_credit_code,
        legal_representative,
        registered_capital,
        business_scope,
        company_status = 'active'
      } = companyData;

      const currentTime = Date.now();
      
      const sql = `
        INSERT INTO company_info (
          company_name, company_description, company_address, company_phone,
          company_email, company_website, business_hours, company_logo,
          contact_person, fax, postal_code, company_type, established_date,
          registration_number, social_credit_code, legal_representative,
          registered_capital, business_scope, company_status, createTime, updateTime
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const params = [
        company_name, company_description, company_address, company_phone,
        company_email, company_website, business_hours, company_logo,
        contact_person, fax, postal_code, company_type, established_date,
        registration_number, social_credit_code, legal_representative,
        registered_capital, business_scope, company_status, currentTime, currentTime
      ];

      const result = await db.query(sql, params);
      return {
        success: true,
        id: result.insertId,
        message: '公司信息创建成功'
      };
    } catch (error) {
      console.error('创建公司信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新公司信息
   * @param {number} id - 公司信息ID
   * @param {Object} companyData - 更新的公司信息数据
   * @returns {Object} 更新结果
   */
  static async update(id, companyData) {
    try {
      console.log('CompanyInfo.update - 接收到的参数:');
      console.log('- ID:', id);
      console.log('- 数据:', companyData);
      
      const updateFields = [];
      const params = [];

      // 动态构建更新字段
      const allowedFields = [
        'company_name', 'company_description', 'company_address', 'company_phone',
        'company_email', 'company_website', 'business_hours', 'company_logo',
        'contact_person', 'fax', 'postal_code', 'company_type', 'established_date',
        'registration_number', 'social_credit_code', 'legal_representative',
        'registered_capital', 'business_scope', 'company_status'
      ];

      allowedFields.forEach(field => {
        if (companyData[field] !== undefined) {
          updateFields.push(`${field} = ?`);
          params.push(companyData[field]);
          console.log(`CompanyInfo.update - 添加字段: ${field} = ${companyData[field]}`);
        }
      });

      if (updateFields.length === 0) {
        return { success: false, message: '没有需要更新的字段' };
      }

      // 添加更新时间
      updateFields.push('updateTime = ?');
      params.push(Date.now());
      params.push(id);

      const sql = `UPDATE company_info SET ${updateFields.join(', ')} WHERE id = ?`;
      console.log('CompanyInfo.update - 执行SQL:', sql);
      console.log('CompanyInfo.update - 参数:', params);
      
      const result = await db.query(sql, params);
      console.log('CompanyInfo.update - 执行结果:', result);

      if (result.affectedRows > 0) {
        console.log('CompanyInfo.update - 更新成功');
        return { success: true, message: '公司信息更新成功' };
      } else {
        console.log('CompanyInfo.update - 没有行被更新');
        return { success: false, message: '公司信息不存在' };
      }
    } catch (error) {
      console.error('更新公司信息失败:', error);
      throw error;
    }
  }

  /**
   * 删除公司信息
   * @param {number} id - 公司信息ID
   * @returns {Object} 删除结果
   */
  static async delete(id) {
    try {
      const sql = 'DELETE FROM company_info WHERE id = ?';
      const result = await db.query(sql, [id]);

      if (result.affectedRows > 0) {
        return { success: true, message: '公司信息删除成功' };
      } else {
        return { success: false, message: '公司信息不存在' };
      }
    } catch (error) {
      console.error('删除公司信息失败:', error);
      throw error;
    }
  }

  /**
   * 根据公司名称搜索
   * @param {string} name - 公司名称关键词
   * @returns {Array} 搜索结果
   */
  static async searchByName(name) {
    try {
      const sql = 'SELECT * FROM company_info WHERE company_name LIKE ? ORDER BY createTime DESC';
      const result = await db.query(sql, [`%${name}%`]);
      return result;
    } catch (error) {
      console.error('搜索公司信息失败:', error);
      throw error;
    }
  }
}

module.exports = CompanyInfo;
