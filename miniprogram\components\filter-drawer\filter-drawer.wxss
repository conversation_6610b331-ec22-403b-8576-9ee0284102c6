/* components/filter-drawer/filter-drawer.wxss */
/* 商品筛选抽屉样式 */

.filter-drawer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.filter-drawer.show {
  visibility: visible;
  opacity: 1;
}

/* 遮罩层 */
.filter-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.filter-drawer.show .filter-mask {
  opacity: 1;
}

/* 筛选内容 */
.filter-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  max-height: 80vh;
  background-color: #FFFFFF;
  border-radius: 16px 16px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
}

.filter-drawer.show .filter-content {
  transform: translateY(0);
}

/* 头部 */
.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.filter-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 24px;
  color: #999;
}

/* 筛选内容区域 */
.filter-body {
  flex: 1;
  padding: 16px;
  max-height: 60vh;
}

/* 筛选区块 */
.filter-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

/* 价格范围 */
.price-inputs {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.price-input {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.price-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.price-value {
  height: 36px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 0 8px;
  font-size: 14px;
  color: #333;
  background-color: #f8f8f8;
}

.price-separator {
  margin: 0 12px;
  color: #999;
  font-size: 14px;
}

.price-slider {
  margin: 0 8px;
}

/* 排序选项 */
.sort-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f8f8;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.sort-option.active {
  background-color: #FFF2F0;
  border: 1px solid #FF6B35;
}

.sort-label {
  font-size: 14px;
  color: #333;
}

.sort-check {
  width: 20px;
  height: 20px;
  background-color: #FF6B35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  color: #FFFFFF;
  font-size: 12px;
  font-weight: bold;
}

/* 库存状态 */
.stock-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.stock-label {
  font-size: 14px;
  color: #333;
}

.stock-switch {
  width: 44px;
  height: 24px;
  background-color: #e0e0e0;
  border-radius: 12px;
  position: relative;
  transition: all 0.3s ease;
}

.stock-switch.active {
  background-color: #FF6B35;
}

.switch-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background-color: #FFFFFF;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stock-switch.active .switch-thumb {
  left: 22px;
}

/* 分类选项 */
.category-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.category-option {
  padding: 8px 12px;
  background-color: #f8f8f8;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.category-option.active {
  background-color: #FFF2F0;
  border: 1px solid #FF6B35;
}

.category-label {
  font-size: 12px;
  color: #333;
}

.category-check {
  width: 16px;
  height: 16px;
  background-color: #FF6B35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-check .check-icon {
  color: #FFFFFF;
  font-size: 10px;
  font-weight: bold;
}

/* 底部操作按钮 */
.filter-footer {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.reset-btn, .apply-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.reset-btn {
  background-color: #f8f8f8;
  color: #666;
  border: 1px solid #e0e0e0;
}

.reset-btn:active {
  background-color: #f0f0f0;
}

.apply-btn {
  background-color: #FF6B35;
  color: #FFFFFF;
}

.apply-btn:active {
  background-color: #E55A2B;
}
