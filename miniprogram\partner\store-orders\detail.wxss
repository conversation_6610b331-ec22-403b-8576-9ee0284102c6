/* partner/store-orders/detail.wxss */
/* 门店订单详情页面样式 */

.order-detail-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
}

/* 加载中和错误状态 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1aad19;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text,
.error-text {
  font-size: 28rpx;
  color: #999;
  margin: 20rpx 0;
}

.retry-btn {
  margin-top: 30rpx;
  background-color: #1aad19;
  color: #fff;
  font-size: 28rpx;
  padding: 0 40rpx;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.order-type-tag {
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #ffffff;
  min-width: 120rpx;
  text-align: center;
}

.order-type-tag.purchase {
  background-color: #1aad19;
}

.order-type-tag.transfer {
  background-color: #ff9800;
}

.order-status {
  text-align: right;
}

.status-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #1aad19;
}

.status-text.pending_review,
.status-text.pending_shipment {
  color: #ff9800;
}

.status-text.shipped {
  color: #2196f3;
}

.status-text.reviewed,
.status-text.received {
  color: #1aad19;
}

/* 订单信息卡片 */
.order-info-card {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 商品列表卡片 */
.products-card {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
}

.product-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background-color: #f9f9f9;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-specs {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.product-price-qty {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.price-info,
.qty-info,
.amount-info {
  display: flex;
  align-items: center;
}

.price-label,
.qty-label,
.amount-label {
  font-size: 24rpx;
  color: #666;
  min-width: 70rpx;
}

.price-value,
.amount-value {
  font-size: 26rpx;
  color: #ff6b00;
  font-weight: 500;
}

.qty-value {
  font-size: 26rpx;
  color: #333;
}

/* 金额卡片 */
.amount-card {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.amount-row:last-child {
  margin-bottom: 0;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
}

.amount-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.amount-value.price {
  font-size: 32rpx;
  color: #ff6b00;
}

/* 操作按钮 */
.action-bar {
  display: flex;
  justify-content: flex-end;
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  bottom: 0;
}

.action-btn {
  margin-left: 20rpx;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  background-color: #f5f5f5;
  color: #666;
}

.action-btn.primary {
  background-color: #1aad19;
  color: #ffffff;
} 