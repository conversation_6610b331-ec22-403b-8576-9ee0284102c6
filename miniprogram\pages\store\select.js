const { userApi } = require('../../utils/api');

Page({
  data: {
    storeList: [], // 门店列表
    loading: true
  },

  onLoad: function(options) {
    console.log('自提门店选择页面加载');
    this.getStoreList();
  },

  // 获取门店列表
  getStoreList: function() {
    console.log('开始获取门店列表');
    
    this.setData({ loading: true });
    
    userApi.getSalesmanStores()
      .then(res => {
        console.log('销售人门店API响应:', res);
        
        if (res && res.success) {
          if (res.data && res.data.length > 0) {
            console.log('获取到销售人门店列表:', res.data);
            this.setData({
              storeList: res.data,
              loading: false
            });
          } else {
            console.log('销售人暂无门店');
            this.setData({
              storeList: [],
              loading: false
            });
          }
        } else {
          console.log('获取销售人门店失败:', res.message);
          this.setData({
            storeList: [],
            loading: false
          });
        }
      })
      .catch(err => {
        console.error('获取销售人门店失败:', err);
        this.setData({
          storeList: [],
          loading: false
        });
      });
  },

  // 选择门店
  selectStore: function(e) {
    const store = e.currentTarget.dataset.store;
    console.log('选择门店:', store);
    
    // 显示选择提示
    wx.showToast({
      title: '已选择门店',
      icon: 'success'
    });
    
    // 返回上一页并传递选中的门店信息
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];
    
    if (prevPage && prevPage.route === 'pages/order/create') {
      prevPage.setData({
        storeInfo: store
      });
    }
    
    // 延迟返回，让用户看到选择成功的提示
    setTimeout(() => {
      wx.navigateBack();
    }, 1000);
  }
}); 