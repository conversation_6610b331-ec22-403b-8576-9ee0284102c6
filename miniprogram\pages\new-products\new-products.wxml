<!--pages/new-products/new-products.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <icon class="search-icon" type="search" size="16" color="#999"></icon>
      <input class="search-input" placeholder="搜索新品商品" value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="onSearchConfirm" />
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="filter-item {{sortType === 'default' ? 'active' : ''}}" bindtap="onSortTap" data-type="default">
      <text>默认</text>
    </view>
    <view class="filter-item {{sortType === 'price_asc' ? 'active' : ''}}" bindtap="onSortTap" data-type="price_asc">
      <text>价格</text>
      <text class="sort-icon {{sortType === 'price_asc' ? 'active' : ''}}">↑</text>
    </view>
    <view class="filter-item {{sortType === 'price_desc' ? 'active' : ''}}" bindtap="onSortTap" data-type="price_desc">
      <text>价格</text>
      <text class="sort-icon {{sortType === 'price_desc' ? 'active' : ''}}">↓</text>
    </view>
    <view class="filter-item {{sortType === 'time' ? 'active' : ''}}" bindtap="onSortTap" data-type="time">
      <text>最新</text>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="product-list">
    <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="onProductTap" data-id="{{item.id}}">
      <!-- 商品图片 -->
      <view class="product-image-wrapper">
        <image class="product-image" src="{{imgErrorMap[item.id] ? '/images/mo/mogoods.jpg' : (item.imageUrl || '/images/mo/mogoods.jpg')}}" mode="aspectFill" binderror="onProductImgError" data-id="{{item.id}}"></image>
        <!-- 新品标签 -->
        <view class="new-tag">新品</view>
      </view>
      
      <!-- 商品信息 -->
      <view class="product-info">
        <view class="product-name">{{item.name}}</view>
        <view class="product-desc" wx:if="{{item.description}}">{{item.description}}</view>
        
        <!-- 价格信息 -->
        <view class="price-wrapper">
          <view class="current-price">¥{{item.price}}</view>
          <view class="original-price" wx:if="{{item.originalPrice && item.originalPrice > item.price}}">¥{{item.originalPrice}}</view>
        </view>
        
        <!-- 商品标签 -->
        <view class="product-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <text class="tag" wx:for="{{item.tags}}" wx:key="index" wx:for-item="tag">{{tag}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-wrapper" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && products.length > 0}}">
    <text>没有更多商品了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && products.length === 0}}">
    <image class="empty-icon" src="/images/mo/mogoods.jpg" mode="aspectFit"></image>
    <text class="empty-text">暂无新品商品</text>
  </view>
</view>