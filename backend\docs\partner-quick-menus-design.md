# 合伙人端快捷菜单数据表架构设计

## 📋 **需求分析**

### 业务需求
- 合伙人端【选品】页需要独立的快捷菜单
- 菜单包括：门店合伙人、门店库存、在线客服、分享门店
- 与顾客端快捷菜单分离，支持不同权限控制
- 支持门店级别的个性化配置

### 技术需求
- 支持多端菜单配置（顾客端、合伙人端、管理端）
- 权限控制和角色验证
- 可扩展的菜单类型（页面、功能、外部链接、弹窗）
- 支持动态配置和个性化定制

## 🏗️ **架构设计**

### 方案选择
采用 **分层架构** + **向后兼容** 的设计方案，提供两种实现方式：

#### 方案一：扩展现有表结构（推荐）
- **优点**：向后兼容，实现简单，统一管理
- **适用场景**：中小型项目，菜单配置相对简单

#### 方案二：独立表结构
- **优点**：功能完整，支持复杂配置，可扩展性强
- **适用场景**：大型项目，需要复杂权限控制和个性化配置

## 📊 **数据表设计**

### 方案一：扩展现有表结构

#### 1. 修改 `quick_menus` 表
```sql
ALTER TABLE `quick_menus` 
ADD COLUMN `target_platform` ENUM('customer', 'partner', 'admin', 'all') NOT NULL DEFAULT 'customer',
ADD COLUMN `required_roles` JSON NULL,
ADD COLUMN `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
```

**字段说明：**
- `target_platform`: 目标平台标识
- `required_roles`: 所需角色权限（JSON格式）
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 方案二：独立表结构

#### 1. 合伙人快捷菜单配置表 `partner_quick_menus`
```sql
CREATE TABLE `partner_quick_menus` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(50) NOT NULL COMMENT '菜单名称',
  `icon` VARCHAR(200) NOT NULL COMMENT '图标路径',
  `link_type` ENUM('page', 'function', 'external', 'modal') NOT NULL DEFAULT 'page',
  `link_url` VARCHAR(500) NOT NULL COMMENT '链接地址或功能标识',
  `function_params` JSON NULL COMMENT '功能参数',
  `required_permissions` JSON NULL COMMENT '所需权限',
  `display_condition` JSON NULL COMMENT '显示条件',
  `sort_order` INT NOT NULL DEFAULT 0,
  `is_active` TINYINT(1) NOT NULL DEFAULT 1,
  `is_system` TINYINT(1) NOT NULL DEFAULT 0,
  `description` VARCHAR(200) NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

#### 2. 门店级别菜单个性化配置表 `store_quick_menu_config`
```sql
CREATE TABLE `store_quick_menu_config` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `store_id` VARCHAR(50) NOT NULL COMMENT '门店ID',
  `menu_id` INT NOT NULL COMMENT '菜单ID',
  `is_enabled` TINYINT(1) NOT NULL DEFAULT 1,
  `custom_name` VARCHAR(50) NULL COMMENT '自定义菜单名称',
  `custom_icon` VARCHAR(200) NULL COMMENT '自定义图标路径',
  `custom_sort_order` INT NULL COMMENT '自定义排序',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_store_menu` (`store_id`, `menu_id`)
);
```

#### 3. 合伙人菜单视图 `v_partner_menus`
```sql
CREATE VIEW `v_partner_menus` AS
SELECT 
    pm.id,
    COALESCE(smc.custom_name, pm.name) AS name,
    COALESCE(smc.custom_icon, pm.icon) AS icon,
    pm.link_type,
    pm.link_url,
    pm.function_params,
    pm.required_permissions,
    pm.display_condition,
    COALESCE(smc.custom_sort_order, pm.sort_order) AS sort_order,
    pm.is_active AND COALESCE(smc.is_enabled, 1) AS is_enabled,
    smc.store_id
FROM `partner_quick_menus` pm
LEFT JOIN `store_quick_menu_config` smc ON pm.id = smc.menu_id
WHERE pm.is_active = 1
ORDER BY sort_order ASC;
```

## 🔌 **API 接口设计**

### 1. 获取合伙人端菜单
```
GET /api/quick-menus/partner
Authorization: Bearer token
```

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "门店合伙人",
      "icon": "/images/icons2/门店合伙人.png",
      "link_type": "function",
      "link_url": "viewStorePartners",
      "sort_order": 1
    }
  ]
}
```

### 2. 获取门店个性化菜单
```
GET /api/quick-menus/partner/store/{storeId}
Authorization: Bearer token
```

### 3. 通用菜单接口（支持平台筛选）
```
GET /api/quick-menus?platform=partner
```

## 🎯 **前端实现**

### 1. 菜单数据获取
```javascript
// 优先使用合伙人专用API
getQuickMenus: function() {
  return request({
    url: '/api/quick-menus/partner',
    method: 'GET',
    requireAuth: true
  }).then(res => {
    if (res.success) {
      this.setData({ quickMenus: res.data });
    }
  }).catch(err => {
    // 回退到通用API
    return this.getQuickMenusFallback();
  });
}
```

### 2. 菜单功能实现
```javascript
// 功能执行分发
executePartnerFunction: function(functionName, params = {}) {
  switch (functionName) {
    case 'viewStorePartners':
      this.viewStorePartners();
      break;
    case 'viewStoreInventory':
      this.viewStoreInventory();
      break;
    case 'contactService':
      this.contactService(params);
      break;
    case 'shareStore':
      this.shareStore(params);
      break;
  }
}
```

## 📝 **默认菜单配置**

### 合伙人端默认菜单
1. **门店合伙人** - 查看和管理门店合伙人信息
2. **门店库存** - 查看门店商品库存情况
3. **在线客服** - 联系在线客服获取帮助
4. **分享门店** - 分享门店给其他用户

### 扩展菜单（可选）
5. **销售统计** - 查看门店销售数据统计
6. **佣金明细** - 查看佣金收入明细

## 🚀 **部署步骤**

### 1. 数据库迁移
```bash
# 执行SQL脚本
mysql -u username -p database_name < backend/scripts/create_partner_quick_menus.sql
```

### 2. 后端部署
- 部署更新的API接口
- 确保权限中间件正常工作

### 3. 前端部署
- 更新合伙人端选品页面
- 测试菜单功能

### 4. 测试验证
- 验证菜单显示正常
- 测试各功能跳转
- 检查权限控制

## 🔧 **扩展性设计**

### 1. 权限控制
```json
{
  "required_permissions": ["partner:view", "store:manage"],
  "display_condition": {
    "store_level": ">=2",
    "has_products": true
  }
}
```

### 2. 功能参数
```json
{
  "function_params": {
    "type": "partner",
    "share_type": "store"
  }
}
```

### 3. 个性化配置
- 门店级别的菜单定制
- 支持自定义图标和名称
- 灵活的排序配置

## 📈 **性能优化**

### 1. 数据库优化
- 添加必要的索引
- 使用视图简化复杂查询
- 缓存常用配置

### 2. 前端优化
- 菜单数据缓存
- 懒加载图标资源
- 错误降级处理

## 🔒 **安全考虑**

### 1. 权限验证
- API级别的权限检查
- 角色基础的访问控制
- 菜单级别的权限过滤

### 2. 数据验证
- 输入参数验证
- SQL注入防护
- XSS攻击防护

## 📊 **监控指标**

### 1. 业务指标
- 菜单点击率统计
- 功能使用频率
- 用户行为分析

### 2. 技术指标
- API响应时间
- 错误率监控
- 缓存命中率 