订单业务逻辑

一、顾客与门店的关系
》拥有合伙人身份的用户推荐新用户注册时，分享链接所带参数的门店，即为该新用户的【订阅门店】；（例如用户A是合伙人，向用户B分享平台，分享链接带了”门店1“的参数，则”门店1“就是用户B的【订阅门店】。
》新用户注册时，其推荐人的订阅门店，即是该新用户的订阅门店；
》用户的销售人如果变更（管理端操作），则【订阅门店】也会变更到新销售人名下的门店（销售人最先加入合伙人的门店）。


二、顾客订单
》逻辑1：顾客用户在平台购买商品的订单，业绩归属其【销售人】，并首先划归顾客用户的【订阅门店】；
》逻辑2：【订阅门店】按订单商品的库存匹配量获得订单量（例如顾客购买A商品10件，库存只要不少于10件，即可完全匹配，获得订单量为10件；而如果门店库存只有5件，则只能匹配5件，此时门店获得订单量为5件）；
》逻辑3：如果该【销售人】名下有多家门店，剩余的订单量则匹配到销售人名下的其他门店；
》逻辑4：如果该【销售人】名下所有门店匹配订单后，订单量仍有剩余，则匹配给平台总部（订单量不会超过平台的库存量，平台库存量作为订单限制，超过则无法下单）。


二、门店订单
》门店采购订单——用于门店向平台总部采购，订单确认后则形成门店的【云端库存】，当顾客订单归属该门店（选择快递方式）时，以此库存进行匹配；
》门店移库订单——用于门店将【云端库存】调拨到线下存储点，当顾客订单归属该门店（选择自提方式）时，以此库存进行匹配（顾客需要到该存储点自提并核销）
