.orders-container {
  background: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.first-level-tabs {
  display: flex;
  gap: 16rpx;
  padding: 40rpx 24rpx 0 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
  overflow-x: auto;
  white-space: nowrap;
  min-height: 72rpx;
  align-items: center;
  box-sizing: border-box;
  padding-bottom: 24rpx;
}
.first-tab {
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #888;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  line-height: 56rpx;
  background: none;
  border-radius: 0;
  display: inline-block;
  white-space: nowrap;
  border-bottom: 4rpx solid transparent;
}
.first-tab.active {
  color: #222;
  font-weight: bold;
  border-bottom: 4rpx solid #FF4D4F;
  background: none;
}
.second-level-tabs {
  display: flex;
  gap: 12rpx;
  padding: 28rpx 24rpx 0 24rpx;
  background: #fff;
  overflow-x: auto;
  white-space: nowrap;
  min-height: 80rpx;
  align-items: center;
  box-sizing: border-box;
  justify-content: center;
  padding-bottom: 24rpx;
}
.second-tab {
  padding: 12rpx 36rpx;
  border-radius: 20rpx;
  background: #f2f2f2;
  color: #222;
  font-size: 26rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-block;
  white-space: nowrap;
}
.second-tab.active {
  background: #222;
  color: #fff;
  font-weight: bold;
}
.order-list-placeholder {
  margin: 48rpx 24rpx 0 24rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx #eee;
  padding: 48rpx 0;
  text-align: center;
  font-size: 32rpx;
  color: #222;
} 