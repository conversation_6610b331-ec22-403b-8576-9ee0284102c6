-- =====================================================
-- users表结构重构SQL脚本
-- 将现有id字段重命名为user_id，并添加新的自增id字段
-- =====================================================

-- 步骤1: 将现有id字段重命名为user_id（保持原有数据不变）
ALTER TABLE users CHANGE COLUMN id user_id VARCHAR(50) NOT NULL;

-- 步骤2: 添加新的自增id字段作为主键
ALTER TABLE users ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST;

-- 步骤3: 为user_id字段添加唯一索引
ALTER TABLE users ADD UNIQUE INDEX idx_user_id (user_id);

-- 步骤4: 验证表结构
-- 执行以下查询来验证表结构是否正确：
-- DESCRIBE users;

-- 步骤5: 验证数据完整性
-- 执行以下查询来验证数据是否正确迁移：
-- SELECT id, user_id, nickname, phone FROM users LIMIT 10;

-- =====================================================
-- 注意事项：
-- 1. 执行前请务必备份数据库
-- 2. 建议在测试环境先执行验证
-- 3. 执行过程中可能需要较长时间，请耐心等待
-- 4. 如果表中有大量数据，建议在低峰期执行
-- ===================================================== 