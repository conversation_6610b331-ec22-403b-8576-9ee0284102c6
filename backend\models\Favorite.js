/**
 * 收藏模型
 */
const db = require('../config/db');

class Favorite {
  static async create(data) {
    const { id, userId, productId, name, price, image } = data;
    const sql = `
      INSERT INTO favorites (id, userId, productId, name, price, image, createTime)
      VALUES (?, ?, ?, ?, ?, ?, NOW())
    `;
    const params = [id, userId, productId, name, price, image];
    
    try {
      const result = await db.query(sql, params);
      return { id, ...data };
    } catch (error) {
      console.error('创建收藏失败:', error);
      throw error;
    }
  }

  static async findByUserAndProduct(userId, productId) {
    const sql = `
      SELECT * FROM favorites 
      WHERE userId = ? AND productId = ? COLLATE utf8mb4_0900_ai_ci
    `;
    const params = [userId, productId];
    
    try {
      const results = await db.query(sql, params);
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      console.error('查询收藏失败:', error);
      throw error;
    }
  }

  static async findAll(userId) {
    const sql = `
      SELECT f.*, p.name, p.price, p.originalPrice, p.images, p.shopName
      FROM favorites f
      LEFT JOIN products p ON f.productId = p.id COLLATE utf8mb4_0900_ai_ci
      WHERE f.userId = ?
      ORDER BY f.createTime DESC
    `;
    const params = [userId];
    
    try {
      const results = await db.query(sql, params);
      return results;
    } catch (error) {
      console.error('获取收藏列表失败:', error);
      throw error;
    }
  }

  static async delete(id) {
    const sql = `DELETE FROM favorites WHERE id = ?`;
    const params = [id];
    
    try {
      const result = await db.query(sql, params);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除收藏失败:', error);
      throw error;
    }
  }

  static async deleteByUserAndProduct(userId, productId) {
    const sql = `DELETE FROM favorites WHERE userId = ? AND productId = ? COLLATE utf8mb4_0900_ai_ci`;
    const params = [userId, productId];
    
    try {
      const result = await db.query(sql, params);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除收藏失败:', error);
      throw error;
    }
  }

  static async count(userId) {
    const sql = `SELECT COUNT(*) as count FROM favorites WHERE userId = ?`;
    const params = [userId];
    
    try {
      const results = await db.query(sql, params);
      return results[0].count;
    } catch (error) {
      console.error('获取收藏数量失败:', error);
      throw error;
    }
  }
}

module.exports = Favorite; 