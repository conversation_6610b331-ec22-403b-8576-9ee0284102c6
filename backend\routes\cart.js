/**
 * 购物车路由
 */
const express = require('express');
const { body, param, query } = require('express-validator');
const cartController = require('../controllers/cartController');
const { checkAuth } = require('../middleware/auth');
const validate = require('../middleware/validation');

const router = express.Router();

// 获取购物车
router.get('/', checkAuth, cartController.getCart);

// 根据ID获取购物车商品
router.get('/byIds', [
  checkAuth,
  query('ids').isArray().withMessage('商品ID列表必须是数组'),
  query('ids.*').isString().withMessage('商品ID必须是字符串'),
  validate
], cartController.getCartItemsByIds);

// 获取购物车数量
router.get('/count', checkAuth, cartController.getCartCount);

// 添加到购物车
router.post('/', [
  checkAuth,
  body('productId').isString().withMessage('商品ID必须是字符串'),
  body('quantity').optional().isInt({ min: 1 }).withMessage('数量必须是大于0的整数'),
  validate
], cartController.addToCart);

// 批量删除购物车商品（必须在参数路由之前）
router.delete('/batch', [
  checkAuth,
  body('ids').isArray().withMessage('商品ID列表必须是数组'),
  body('ids.*').isString().withMessage('商品ID必须是字符串'),
  validate
], cartController.batchRemoveFromCart);

// 清空购物车（必须在参数路由之前）
router.delete('/clear', checkAuth, cartController.clearCart);

// 更新购物车
router.put('/:id', [
  checkAuth,
  param('id').isString().withMessage('购物车项ID必须是字符串'),
  body('quantity').isInt({ min: 1 }).withMessage('数量必须是大于0的整数'),
  validate
], cartController.updateCart);

// 从购物车中删除
router.delete('/:id', [
  checkAuth,
  param('id').isString().withMessage('购物车项ID必须是字符串'),
  validate
], cartController.removeFromCart);

module.exports = router;
