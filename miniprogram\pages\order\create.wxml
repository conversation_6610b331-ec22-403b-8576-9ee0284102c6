<view class="container">
  <!-- 配送方式切换按钮 -->
  <view class="section">
    <view class="delivery-toggle">
      <view class="toggle-item {{deliveryMethod === 'express' ? 'active' : ''}}" bindtap="switchDelivery" data-type="express">
        <text>快递</text>
      </view>
      <view class="toggle-item {{deliveryMethod === 'self' ? 'active' : ''}}" bindtap="switchDelivery" data-type="self">
        <text>自提</text>
      </view>
    </view>
  </view>

  <!-- 收货地址/自提门店 -->
  <view class="section">
    <view class="address-header">
      <view class="address-label">
        <text wx:if="{{deliveryMethod === 'express'}}">收货</text>
        <text wx:if="{{deliveryMethod === 'express'}}">地址</text>
        <text wx:if="{{deliveryMethod === 'self'}}">自提</text>
        <text wx:if="{{deliveryMethod === 'self'}}">门店</text>
      </view>
      <view class="address-content" bindtap="{{deliveryMethod === 'express' ? 'selectAddress' : 'selectStore'}}">
        <view wx:if="{{deliveryMethod === 'express' && addressInfo.id}}" class="address-info">
          <view class="name">{{addressInfo.name}}</view>
          <view class="phone">{{addressInfo.phone}}</view>
          <view class="address">{{addressInfo.address}}</view>
        </view>
        <view wx:elif="{{deliveryMethod === 'self' && storeInfo.id}}" class="store-info">
          <view class="store-name">{{storeInfo.name}}</view>
          <view class="store-no" wx:if="{{storeInfo.store_no}}">{{storeInfo.store_no}}</view>
          <view class="store-address">{{storeInfo.province}}{{storeInfo.city}}{{storeInfo.district}}{{storeInfo.address || ''}}</view>
          <view class="store-hours" wx:if="{{storeInfo.business_hours}}">营业时间: {{storeInfo.business_hours}}</view>
        </view>
        <view wx:else class="no-address">
          <text wx:if="{{deliveryMethod === 'express'}}">请设置收货地址</text>
          <text wx:if="{{deliveryMethod === 'self'}}">请选择自提门店</text>
        </view>
        <view class="arrow">></view>
      </view>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="section">
    <view class="section-title">商品信息</view>
    <view class="product-list">
      <block wx:if="{{cartItems.length > 0}}">
        <view class="product-item" wx:for="{{cartItems}}" wx:key="id">
          <image class="product-image" src="{{item.image}}" mode="aspectFill" binderror="onImageError" data-index="{{index}}"></image>
          <view class="product-info">
            <view class="product-name">{{item.name}}</view>
            <view class="product-spec">{{item.spec}}</view>
            <view class="product-price">¥{{item.price}}</view>
          </view>
          <view class="product-quantity">x{{item.quantity}}</view>
        </view>
      </block>
      <view wx:else class="empty-products">
        <text>暂无商品信息</text>
      </view>
    </view>
  </view>

  <!-- 支付方式 -->
  <view class="section">
    <view class="section-title">支付方式</view>
    <view class="payment-list">
      <view class="payment-item">
        <view class="payment-icon">💳</view>
        <view class="payment-name">微信支付</view>
        <view class="payment-check" bindtap="toggleWechatPay">
          <view class="checkbox {{wechatPaySelected ? 'checked' : ''}}">
            <view wx:if="{{wechatPaySelected}}" class="checkmark">✓</view>
          </view>
        </view>
      </view>
      <view class="payment-item">
        <view class="payment-icon">💰</view>
        <view class="payment-name">余额支付</view>
        <view class="payment-switch">
          <switch checked="{{balancePayEnabled}}" bindchange="toggleBalancePay" color="#e74c3c" />
        </view>
      </view>
    </view>
  </view>

  <!-- 订单金额 -->
  <view class="section">
    <view class="section-title">订单金额</view>
    <view class="amount-info">
      <view class="amount-item">
        <text>商品金额</text>
        <text>¥{{totalAmount}}</text>
      </view>
      <view class="amount-item">
        <text>运费</text>
        <text>¥0.00</text>
      </view>
      <view class="amount-item total">
        <text>实付金额</text>
        <text class="total-amount">¥{{totalAmount}}</text>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <view class="amount-display">
      <text class="amount-label">应付</text>
      <text class="amount-value">¥{{totalAmount}}</text>
    </view>
    <view class="submit-btn" bindtap="submitOrder">提交订单</view>
  </view>
</view>