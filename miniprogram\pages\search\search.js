// pages/search/search.js
const { productApi } = require('../../utils/api');

Page({
  data: {
    keyword: '',
    searchHistory: [],
    loading: false,
    showResults: false,
    searchType: 'product', // 默认搜索类型为商品
    productResults: [], // 存储商品搜索结果
    imgErrorMap: {} // 存储图片加载错误的商品ID
  },

  onLoad: function (options) {
    // 从本地存储获取搜索历史
    this.getSearchHistory();

    // 设置搜索类型为商品
    this.setData({
      searchType: 'product',
      placeholder: '搜索商品'
    });

    // 如果有关键词参数，直接搜索
    if (options.keyword) {
      const keyword = decodeURIComponent(options.keyword);
      this.setData({ keyword });
      this.search(keyword);
    }
  },

  // 获取搜索历史
  getSearchHistory: function() {
    try {
      const history = wx.getStorageSync('searchHistory') || [];
      this.setData({
        searchHistory: history
      });
    } catch (e) {
      console.error('获取搜索历史失败', e);
    }
  },

  // 保存搜索历史
  saveSearchHistory: function(keyword) {
    if (!keyword) return;

    try {
      let history = wx.getStorageSync('searchHistory') || [];

      // 如果已存在相同关键词，先移除
      history = history.filter(item => item !== keyword);

      // 添加到历史记录开头
      history.unshift(keyword);

      // 限制最多保存10条
      if (history.length > 10) {
        history = history.slice(0, 10);
      }

      // 保存到本地存储
      wx.setStorageSync('searchHistory', history);

      // 更新数据
      this.setData({
        searchHistory: history
      });
    } catch (e) {
      console.error('保存搜索历史失败', e);
    }
  },

  // 清空搜索历史
  clearHistory: function() {
    wx.showModal({
      title: '提示',
      content: '确定要清空搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('searchHistory');
            this.setData({
              searchHistory: []
            });
          } catch (e) {
            console.error('清空搜索历史失败', e);
          }
        }
      }
    });
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      keyword: e.detail.value
    });

    // 如果清空了输入，隐藏结果
    if (!e.detail.value) {
      this.setData({
        showResults: false
      });
    }
  },
  // 点击清除按钮
  clearSearch: function() {
    this.setData({
      keyword: '',
      showResults: false
    });
  },

  // 点击搜索历史项
  onHistoryTap: function(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({ keyword });
    this.search(keyword);
  },

  // 搜索确认
  onSearch: function() {
    const keyword = this.data.keyword.trim();
    if (keyword) {
      this.search(keyword);
    }
  },

  // 执行搜索
  search: function(keyword) {
    if (!keyword) return;

    this.setData({
      loading: true,
      showResults: true
    });

    // 保存到搜索历史
    this.saveSearchHistory(keyword);

    // 搜索商品
    this.searchProducts(keyword);
  },

  // 搜索商品
  searchProducts: function(keyword) {
    // 使用API搜索商品
    productApi.getProducts({
      keyword: keyword,
      page: 1,
      pageSize: 20
    })
    .then(res => {
      console.log('商品搜索结果:', res);
      if (res.success) {
        // 检查数据结构，确保data是数组
        let products = [];
        if (res.data && Array.isArray(res.data)) {
          products = res.data;
        } else if (res.data && res.data.products && Array.isArray(res.data.products)) {
          products = res.data.products;
        } else if (res.data && res.data.list && Array.isArray(res.data.list)) {
          products = res.data.list;
        } else if (res.data && res.data.data && res.data.data.list && Array.isArray(res.data.data.list)) {
          // 处理嵌套的数据结构
          products = res.data.data.list;
        } else {
          console.warn('API返回的数据结构不符合预期:', res.data);
          products = [];
        }
        
        // 处理商品数据
        products = products.map(product => {
          // 处理商品图片
          if (product.images && typeof product.images === 'string') {
            try {
              // 尝试解析JSON格式的图片数组
              product.images = JSON.parse(product.images);
            } catch (e) {
              // 解析失败时，尝试简单处理
              if (product.images.includes(',')) {
                product.images = product.images.split(',');
              } else {
                product.images = [product.images];
              }
            }
          }
          
          // 如果没有图片、图片不是数组或者是空数组，直接使用默认图片
          if (!product.images || !Array.isArray(product.images) || product.images.length === 0) {
            product.images = ['/images/mo/mogoods.jpg'];
          }
          
          return product;
        });
        
        this.setData({
          productResults: products,
          loading: false
        });
      } else {
        this.setData({
          productResults: [],
          loading: false
        });
        wx.showToast({
          title: res.message || '搜索失败，请重试',
          icon: 'none'
        });
      }
    })
    .catch(err => {
      console.error('搜索商品出错:', err);
      this.setData({
        productResults: [],
        loading: false
      });
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      });
    });
  },





  // 点击商品项
  onProductTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product/detail?id=${id}`
    });
  },
  
  // 处理图片加载错误
  onImageError: function(e) {
    const id = e.currentTarget.dataset.id;
    
    // 设置该商品的图片加载错误标记
    this.setData({
      [`imgErrorMap.${id}`]: true
    });
  }
})
