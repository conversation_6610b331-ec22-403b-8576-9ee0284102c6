.partner-change-page {
  min-height: 100vh;
  background: #f7f7f7;
  padding: 24rpx 0;
}
.store-search-bar {
  background: #fff;
  padding: 20rpx 24rpx 0 24rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}
.store-search-bar input {
  width: 100%;
  height: 64rpx;
  border-radius: 32rpx;
  background: #f5f5f5;
  border: none;
  padding: 0 32rpx;
  font-size: 30rpx;
}
.dropdown-list {
  position: absolute;
  left: 24rpx;
  right: 24rpx;
  top: 80rpx;
  background: #fff;
  border: 1rpx solid #eee;
  border-radius: 12rpx;
  z-index: 10;
  box-shadow: 0 2rpx 12rpx #eee;
}
.dropdown-list view {
  padding: 18rpx 0 18rpx 32rpx;
  font-size: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.dropdown-list view:last-child {
  border-bottom: none;
}
.partner-list {
  margin: 32rpx 24rpx 0 24rpx;
}
.partner-item {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx #eee;
  padding: 18rpx;
  margin-bottom: 24rpx;
  position: relative;
}
.avatar {
  width: 86rpx;
  height: 86rpx;
  border-radius: 50%;
  margin-right: 18rpx;
  background: #fafafa;
  border: 2rpx solid #f0f0f0;
}
.info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}
.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.phone {
  font-size: 28rpx;
  color: #888;
}
.type, .percent, .join-time, .join-way {
  font-size: 26rpx;
  color: #888;
}
.status {
  position: absolute;
  right: 24rpx;
  top: 18rpx;
  font-size: 24rpx;
  font-weight: bold;
  padding: 4rpx 18rpx;
  border-radius: 16rpx;
  min-width: 60rpx;
  text-align: center;
}
.status-on {
  background: #e6f9ed;
  color: #1bc47d;
  border: 1rpx solid #1bc47d;
}
.status-frozen {
  background: #fff1f0;
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}
.partner-actions {
  display: flex;
  gap: 24rpx;
  padding: 10rpx 24rpx 10rpx 24rpx;
  background: #fff;
  min-height: 100rpx;
  align-items: center;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx #f0f0f0;
  margin-bottom: 8rpx;
}
.add-partner-btn {
  width: 120rpx;
  height: 120rpx;
  border: 2rpx dashed #bbb;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 48rpx auto 0 auto;
  background: #fff;
  font-size: 80rpx;
  color: #bbb;
  transition: border-color 0.2s;
}
.add-partner-btn:active {
  border-color: #ff4d4f;
  color: #ff4d4f;
}
.partner-action-btn {
  flex: 1;
  height: 64rpx;
  background: #ff4d4f;
  color: #fff;
  border-radius: 12rpx;
  font-size: 24rpx;
  border: none;
  padding: 0 8rpx;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 64rpx;
  letter-spacing: 6rpx;
  font-weight: 500;
}
.partner-action-btn:nth-child(4) {
  letter-spacing: 12rpx;
}
.form-group {
  margin-bottom: 32rpx;
  position: relative;
  padding: 0 24rpx;
  background: #fff;
  display: flex;
  align-items: center;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx #f0f0f0;
  min-height: 112rpx;
}
.form-label {
  font-size: 34rpx;
  color: #222;
  margin-bottom: 0;
  margin-right: 18rpx;
  white-space: nowrap;
  font-weight: bold;
}
.form-input {
  flex: 1;
  width: auto;
  height: 64rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
  color: #222;
  padding: 0 24rpx;
}
.dropdown-list {
  position: absolute;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 0 0 12rpx 12rpx;
  box-shadow: 0 8rpx 32rpx #ccc;
  z-index: 10;
  max-height: 320rpx;
  overflow-y: auto;
  margin-top: -8rpx;
}
.dropdown-item {
  padding: 18rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fff;
}
.dropdown-item:last-child {
  border-bottom: none;
}
.dropdown-item:active {
  background: #f5f5f5;
}
.dropdown-empty {
  text-align: center;
  color: #aaa;
  font-size: 26rpx;
  padding: 24rpx 0;
  background: #fff;
}
.dropdown-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  z-index: 9;
  background: transparent;
} 