-- 创建轮播图表
CREATE TABLE IF NOT EXISTS `banners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT '' COMMENT '轮播图标题',
  `image_url` varchar(500) NOT NULL COMMENT '轮播图片链接',
  `link_url` varchar(500) DEFAULT '' COMMENT '点击跳转链接',
  `page_type` varchar(50) DEFAULT 'customer_home' COMMENT '页面类型：customer_home-顾客首页, customer_category-顾客分类页, customer_profile-顾客我的页, partner_products-合伙人选品页, partner_center-合伙人中心页',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重，数字越小越靠前',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用：1-启用，0-禁用',
  `created_at` bigint(20) NOT NULL COMMENT '创建时间戳',
  `updated_at` bigint(20) NOT NULL COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_page_type` (`page_type`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='轮播图表';

-- 插入示例轮播图数据
INSERT INTO `banners` (`title`, `image_url`, `link_url`, `page_type`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
-- 顾客端首页轮播图
('企业服务专场', '/images/lunbo/001.jpeg', '', 'customer_home', 1, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('知识产权保护与商标注册', '/images/lunbo/002.jpg', '', 'customer_home', 2, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('企业并购与资产重组', '/images/lunbo/003.png', '', 'customer_home', 3, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 顾客端分类页轮播图
('分类专区优惠', '/images/lunbo/001.jpeg', '', 'customer_category', 1, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 顾客端我的页轮播图  
('会员专享福利', '/images/lunbo/002.jpg', '', 'customer_profile', 1, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 合伙人选品页轮播图
('选品攻略指南', '/images/lunbo/003.png', '', 'partner_products', 1, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 合伙人中心页轮播图
('合伙人专区', '/images/lunbo/001.jpeg', '', 'partner_center', 1, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000); 