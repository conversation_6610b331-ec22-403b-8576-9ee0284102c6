// partner/store-partners/store-partners.js
const { partnerApi, storeApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');

Page({
  data: {
    // 门店相关
    storeList: [],
    selectedStore: null,
    
    // 当前用户在该门店的状况
    userStatus: {
      capital: 0, // 股本金
      role: '',   // 角色
      percent: 0  // 股份比例
    },
    
    // 合伙人列表
    partnerList: [],
    loadingPartners: false,
    partnerCount: 0,
    
    // 用户信息
    userInfo: null
  },

  onLoad(options) {
    // 页面加载时恢复全局登录状态
    loginStateManager.restoreLoginStateToGlobal();
    
    // 获取用户信息
    const app = getApp();
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      });
    }
    
    // 优先从本地存储读取门店信息，与合伙人页面保持同步
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    console.log('门店合伙人页面 - 本地门店数据检查:', {
      storedStoreList: !!storedStoreList,
      storeCount: storedStoreList ? storedStoreList.length : 0,
      storedSelectedStore: !!storedSelectedStore
    });
    
    if (storedStoreList && storedStoreList.length > 0) {
      const selectedStore = storedSelectedStore || storedStoreList[0];
      this.setData({
        storeList: storedStoreList,
        selectedStore: selectedStore
      });
      console.log('门店合伙人页面 - 从本地存储读取门店信息:', selectedStore);
      
      // 加载该门店的合伙人数据
      this.loadPartnerData(selectedStore);
    } else {
      // 如果没有本地存储的门店数据，则从API获取
      if (options.storeNo) {
        this.loadStoreByNo(options.storeNo);
      } else {
        this.loadStoreList();
      }
    }
  },

  onShow() {
    this.checkUserLogin();
    
    // 每次显示页面时，都从本地存储同步门店选择状态
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    if (storedStoreList && storedStoreList.length > 0) {
      const selectedStore = storedSelectedStore || storedStoreList[0];
      
      // 检查当前选择的门店是否与存储的门店不同
      if (!this.data.selectedStore || 
          this.data.selectedStore.id !== selectedStore.id ||
          this.data.selectedStore.store_no !== selectedStore.store_no) {
        
        this.setData({
          storeList: storedStoreList,
          selectedStore: selectedStore
        });
        
        console.log('门店合伙人页面 - 同步门店选择状态:', selectedStore);
        
        // 重新加载该门店的合伙人数据
        this.loadPartnerData(selectedStore);
      }
    }
  },

  checkUserLogin() {
    const app = getApp();
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '请先登录',
        content: '查看门店合伙人信息需要先登录',
        showCancel: true,
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/auth/auth' });
          }
        }
      });
      return;
    }
  },

  // 门店选择变化
  onStoreChange(e) {
    const index = e.detail.value;
    this.selectStoreByIndex(index);
  },

  // 根据索引选择门店
  selectStoreByIndex(index) {
    if (index >= 0 && index < this.data.storeList.length) {
      const store = this.data.storeList[index];
      this.selectStoreByData(store);
    }
  },

  // 根据门店数据选择门店
  selectStoreByData(store) {
    console.log('选择门店:', store);
    
    this.setData({
      selectedStore: store
    });
    
    // 保存到本地存储
    wx.setStorageSync('partnerSelectedStore', store);
    
    // 加载该门店的合伙人数据
    this.loadPartnerData(store);
  },

  // 加载门店列表
  loadStoreList() {
    storeApi.getMyStores().then(res => {
      if (res.success && res.data) {
        const storeList = res.data;
        console.log('获取门店列表成功:', storeList);
        
        this.setData({ storeList });
        
        // 保存到本地存储
        wx.setStorageSync('partnerStoreList', storeList);
        
        if (storeList.length > 0) {
          const selectedStore = storeList[0];
          this.setData({ selectedStore });
          wx.setStorageSync('partnerSelectedStore', selectedStore);
          
          // 加载该门店的合伙人数据
          this.loadPartnerData(selectedStore);
        }
      } else {
        console.error('获取门店列表失败:', res);
        wx.showToast({
          title: '获取门店信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取门店列表错误:', err);
      wx.showToast({
        title: '网络错误，请稍后再试',
        icon: 'none'
      });
    });
  },

  // 根据门店号加载门店信息
  loadStoreByNo(storeNo) {
    storeApi.getStoreByNo(storeNo).then(res => {
      if (res.success && res.data) {
        const store = res.data;
        console.log('根据门店号获取门店信息:', store);
        
        this.setData({
          storeList: [store],
          selectedStore: store
        });
        
        // 保存到本地存储
        wx.setStorageSync('partnerStoreList', [store]);
        wx.setStorageSync('partnerSelectedStore', store);
        
        // 加载该门店的合伙人数据
        this.loadPartnerData(store);
      } else {
        console.error('根据门店号获取门店信息失败:', res);
        wx.showToast({
          title: '门店信息不存在',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('根据门店号获取门店信息错误:', err);
      wx.showToast({
        title: '网络错误，请稍后再试',
        icon: 'none'
      });
    });
  },

  // 加载合伙人数据
  loadPartnerData(store) {
    if (!store || !store.id) {
      console.error('门店信息不完整，无法加载合伙人数据');
      return;
    }
    
    this.setData({ loadingPartners: true });
    
    partnerApi.getStorePartners(store.id).then(res => {
      this.setData({ loadingPartners: false });
      
      if (res.success && res.data) {
        const partnerList = res.data.partners || [];
        console.log('获取门店合伙人数据成功:', partnerList);
        
        // 查找当前用户在该门店的状况
        const userStatus = this.findUserStatus(partnerList);
        
        this.setData({
          partnerList,
          partnerCount: partnerList.length,
          userStatus
        });
      } else {
        console.error('获取门店合伙人数据失败:', res);
        wx.showToast({
          title: res.message || '获取合伙人信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      this.setData({ loadingPartners: false });
      console.error('获取门店合伙人数据错误:', err);
      wx.showToast({
        title: '网络错误，请稍后再试',
        icon: 'none'
      });
    });
  },

  // 查找当前用户在该门店的状况
  findUserStatus(partners) {
    const app = getApp();
    const currentUserId = app.globalData.userInfo ? app.globalData.userInfo._id : null;
    
    if (!currentUserId) {
      return {
        capital: 0,
        role: '未登录',
        percent: 0
      };
    }
    
    const userPartner = partners.find(partner => partner.user_id === currentUserId);
    
    if (userPartner) {
      return {
        capital: userPartner.amount || 0,
        role: userPartner.type || '合伙人',
        percent: userPartner.percent || 0
      };
    } else {
      return {
        capital: 0,
        role: '非合伙人',
        percent: 0
      };
    }
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.selectedStore) {
      this.loadPartnerData(this.data.selectedStore);
    }
    wx.stopPullDownRefresh();
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '门店合伙人信息',
      path: '/partner/store-partners/store-partners'
    };
  },

  // 发送私信
  sendPrivateMessage(e) {
    const userId = e.currentTarget.dataset.userId;
    const userName = e.currentTarget.dataset.userName;
    
    console.log('发送私信给用户:', userId, userName);
    
    wx.navigateTo({
      url: `/partner/messages/chat?targetUserId=${userId}&targetUserName=${encodeURIComponent(userName)}`
    });
  }
});