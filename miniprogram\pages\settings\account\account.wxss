/* 账户设置页面样式 */
.page-content {
  background: #f7f7f7;
  min-height: 100vh;
  padding: 24rpx 0 40rpx 0;
}

/* 设置列表 */
.settings-list {
  margin: 0 24rpx;
}

.settings-group {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.group-title {
  font-size: 24rpx;
  color: #999;
  padding: 24rpx 24rpx 16rpx 24rpx;
  background: #fafafa;
}

.settings-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.settings-item:last-child {
  border-bottom: none;
}

.settings-item:active {
  background-color: #f5f5f5;
}

.item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.item-text {
  font-size: 28rpx;
  color: #333;
}

.logout-text {
  color: #ff3b30;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
} 