// pages/new-products/new-products.js
const api = require('../../utils/api');
const request = require('../../utils/request');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    products: [], // 商品列表
    searchKeyword: '', // 搜索关键词
    sortType: 'default', // 排序类型：default, price_asc, price_desc, time
    page: 1, // 当前页码
    pageSize: 50, // 每页数量（增加以确保筛选后有足够的新品）
    loading: false, // 加载状态
    hasMore: true, // 是否还有更多数据
    imgErrorMap: {} // 图片加载错误映射
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('[新品上市] 页面加载');
    this.loadProducts();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 页面显示时可以刷新数据
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    console.log('[新品上市] 下拉刷新');
    this.refreshProducts();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    console.log('[新品上市] 上拉加载更多');
    this.loadMoreProducts();
  },

  /**
   * 加载新品商品列表
   */
  loadProducts: function() {
    if (this.data.loading) {
      console.log('[新品上市] 正在加载中，跳过重复请求');
      return;
    }

    console.log('[新品上市] 开始加载商品列表，页码:', this.data.page);
    this.setData({ loading: true });

    const params = {
      page: this.data.page,
      pageSize: this.data.pageSize,
      // 暂时移除isNew参数，改为在前端筛选
      // isNew: 1, // 只获取新品
      keyword: this.data.searchKeyword || undefined,
      sortType: this.getSortParam()
    };

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === undefined || params[key] === '') {
        delete params[key];
      }
    });

    console.log('[新品上市] 请求参数:', params);

    request.get(api.getProducts, params, { requireAuth: false })
      .then(res => {
        console.log('[新品上市] 商品列表获取成功:', res);
        const allProducts = res.data?.list || [];
        
        // 在前端筛选出新品商品（isNew为1的商品）
        const newProducts = allProducts.filter(product => {
          return product.isNew === 1 || product.isNew === '1';
        });
        
        console.log('[新品上市] 筛选后的新品数量:', newProducts.length);
        console.log('[新品上市] 原始商品数量:', allProducts.length);
        console.log('[新品上市] 新品商品详情:', newProducts.map(p => ({ id: p.id, name: p.name, isNew: p.isNew })));
        
        if (this.data.page === 1) {
          // 首次加载或刷新
          this.setData({
            products: newProducts,
            // 由于前端筛选，需要根据实际获取的商品数量判断是否还有更多
            hasMore: allProducts.length >= this.data.pageSize
          });
        } else {
          // 加载更多
          this.setData({
            products: [...this.data.products, ...newProducts],
            // 由于前端筛选，需要根据实际获取的商品数量判断是否还有更多
            hasMore: allProducts.length >= this.data.pageSize
          });
        }
      })
      .catch(err => {
        console.error('[新品上市] 商品列表获取失败:', err);
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ loading: false });
        wx.stopPullDownRefresh();
      });
  },

  /**
   * 刷新商品列表
   */
  refreshProducts: function() {
    console.log('[新品上市] 刷新商品列表');
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadProducts();
  },

  /**
   * 加载更多商品
   */
  loadMoreProducts: function() {
    if (this.data.loading || !this.data.hasMore) {
      console.log('[新品上市] 跳过加载更多 - loading:', this.data.loading, 'hasMore:', this.data.hasMore);
      return;
    }

    console.log('[新品上市] 加载更多商品，当前页:', this.data.page, '-> 下一页:', this.data.page + 1);
    this.setData({
      page: this.data.page + 1
    });
    this.loadProducts();
  },

  /**
   * 搜索输入
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 搜索确认
   */
  onSearchConfirm: function() {
    console.log('[新品上市] 搜索确认，关键词:', this.data.searchKeyword);
    this.refreshProducts();
  },

  /**
   * 排序点击
   */
  onSortTap: function(e) {
    const sortType = e.currentTarget.dataset.type;
    console.log('[新品上市] 切换排序:', sortType);
    
    this.setData({
      sortType: sortType
    });
    this.refreshProducts();
  },

  /**
   * 获取排序参数
   */
  getSortParam: function() {
    switch (this.data.sortType) {
      case 'price_asc':
        return 'price_asc';
      case 'price_desc':
        return 'price_desc';
      case 'time':
        return 'default'; // 最新商品使用默认排序（按ID倒序）
      default:
        return 'default';
    }
  },

  /**
   * 商品点击
   */
  onProductTap: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('[新品上市] 点击商品:', productId);
    
    wx.navigateTo({
      url: `/pages/product/detail?id=${productId}`
    });
  },

  /**
   * 商品图片加载错误
   */
  onProductImgError: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('[新品上市] 商品图片加载失败:', productId);
    
    this.setData({
      [`imgErrorMap.${productId}`]: true
    });
  }
});