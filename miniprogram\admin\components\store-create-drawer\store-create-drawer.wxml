<view class="create-drawer-container {{visible ? 'visible' : ''}}" catchtouchmove="preventTouchMove">
  <view class="create-drawer-mask" bindtap="onCancel" catch:touchmove="preventTouchMove"></view>
  <view class="create-drawer-panel" catch:touchmove="catchTouchMove">
    <view class="create-drawer-header">创建门店</view>
    <view class="create-drawer-form">
      <!-- 门店形象照 - 放到最前面，增大显示区域 -->
      <view class="form-group">
        <view class="form-label">门店形象</view>
        <view class="image-upload-area">
          <image 
            class="store-image" 
            src="{{storeImageTemp || storeImage || '/images/icons2/店铺.png'}}" 
            mode="aspectFill"
            bindtap="previewStoreImage"
          />
          <view class="upload-hint" bindtap="chooseStoreImage">点击选择图片</view>
          <view wx:if="{{storeImageTemp || storeImage}}" class="delete-image-btn" bindtap="deleteStoreImage">
            <image src="/images/icons2/关闭.png" class="delete-icon"></image>
          </view>
        </view>
      </view>

      <!-- 门店名称 -->
      <view class="form-group">
        <view class="form-label">门店名称</view>
        <input 
          class="form-input" 
          placeholder="请输入门店名称" 
          value="{{storeName}}"
          bindinput="onNameInput"
        />
      </view>

      <!-- 门店级别 -->
      <view class="form-group">
        <view class="form-label">门店级别</view>
        <picker 
          class="form-picker" 
          mode="selector" 
          range="{{levelTitles}}" 
          value="{{levelIndex}}"
          bindchange="onLevelChange"
        >
          <view class="picker-text">
            <text wx:if="{{levelTitles[levelIndex]}}">{{levelTitles[levelIndex]}}</text>
            <text wx:else style="color:#bbb">请选择门店级别</text>
          </view>
        </picker>
      </view>

      <!-- 所属区县 -->
      <view class="form-group">
        <view class="form-label">所属区县</view>
        <picker 
          class="form-picker" 
          mode="region" 
          bindchange="onRegionChange" 
          value="{{region}}"
        >
          <view class="picker-text">
            <text wx:if="{{regionText !== '选择省市区'}}">{{regionText}}</text>
            <text wx:else style="color:#bbb">请选择省市区</text>
          </view>
        </picker>
      </view>
    </view>
    <view class="create-drawer-footer">
      <button class="footer-btn cancel" bindtap="onCancel">取消</button>
      <button class="footer-btn confirm" bindtap="onConfirm">确定</button>
    </view>
  </view>
</view> 