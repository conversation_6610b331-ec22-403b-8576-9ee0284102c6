-- ========================================
-- Users表结构迁移脚本
-- 将现有的id字段改为user_id，并添加新的自增id字段
-- 执行前请务必备份数据库！
-- ========================================

-- 第一步：备份当前users表（可选，但强烈建议）
-- CREATE TABLE users_backup AS SELECT * FROM users;

-- 第二步：添加新的自增id字段
ALTER TABLE users ADD COLUMN new_id INT NOT NULL AUTO_INCREMENT FIRST;

-- 第三步：将现有的id字段重命名为user_id
ALTER TABLE users CHANGE COLUMN id user_id VARCHAR(32) NOT NULL COMMENT '用户ID（业务ID）';

-- 第四步：将新添加的id字段重命名为id并设置为主键
ALTER TABLE users CHANGE COLUMN new_id id INT NOT NULL AUTO_INCREMENT COMMENT '自增主键ID';

-- 第五步：设置id为主键
ALTER TABLE users DROP PRIMARY KEY;
ALTER TABLE users ADD PRIMARY KEY (id);

-- 第六步：为user_id添加唯一索引
ALTER TABLE users ADD UNIQUE INDEX uk_user_id (user_id);

-- 第七步：更新相关外键引用（如果有的话）
-- 注意：这里需要根据实际情况调整，因为可能涉及多个表

-- 更新user_roles表的user_id字段引用
-- 如果user_roles表存在且引用了users.id
-- ALTER TABLE user_roles DROP FOREIGN KEY IF EXISTS fk_user_roles_user_id;
-- ALTER TABLE user_roles ADD CONSTRAINT fk_user_roles_user_id FOREIGN KEY (user_id) REFERENCES users(user_id);

-- 更新partners表的user_id字段引用
-- 如果partners表存在且引用了users.id
-- ALTER TABLE partners DROP FOREIGN KEY IF EXISTS fk_partners_user_id;
-- ALTER TABLE partners ADD CONSTRAINT fk_partners_user_id FOREIGN KEY (user_id) REFERENCES users(user_id);

-- 更新其他可能引用users.id的表...

-- 第八步：验证迁移结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_KEY,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users' 
AND TABLE_SCHEMA = DATABASE()
ORDER BY ORDINAL_POSITION;

-- 第九步：检查索引
SHOW INDEX FROM users;

-- 第十步：验证数据完整性
SELECT COUNT(*) as total_users FROM users;
SELECT COUNT(*) as users_with_user_id FROM users WHERE user_id IS NOT NULL;
SELECT COUNT(*) as users_with_id FROM users WHERE id IS NOT NULL;

-- 验证user_id的唯一性
SELECT user_id, COUNT(*) as count 
FROM users 
GROUP BY user_id 
HAVING count > 1;

-- 如果发现重复的user_id，需要手动处理
-- 例如：UPDATE users SET user_id = CONCAT(user_id, '_', id) WHERE id IN (SELECT id FROM (SELECT id FROM users u1 WHERE EXISTS (SELECT 1 FROM users u2 WHERE u2.user_id = u1.user_id AND u2.id < u1.id)) AS duplicates); 