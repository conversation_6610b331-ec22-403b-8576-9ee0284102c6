/**
 * 登录状态管理工具
 * 用于管理登录状态和确保数据一致性
 */

// 导入用户API
const { userApi } = require('./api');

// 登录状态存储键
const LOGIN_STATE_KEY = 'login_state';
const TOKEN_KEY = 'token';
const USER_INFO_KEY = 'userInfo';

/**
 * 保存登录状态
 * @param {Object} userInfo 用户信息
 * @param {String} token 用户令牌
 * @param {Boolean} isLogin 是否登录
 */
function saveLoginState(userInfo, token, isLogin = true) {
  if (!userInfo || !token) {
    // 保存登录状态失败: 无效的用户信息或令牌
    return false;
  }

  // 确保用户信息中有id字段
  if (!userInfo.id && userInfo._id) {
    // 用户信息中使用_id替代id，正在规范化
    userInfo.id = userInfo._id;
  }

  if (!userInfo.id) {
    // 保存登录状态失败: 用户信息中缺少id字段
    return false;
  }

  // 记录当前时间戳，用于后续判断token是否过期
  const timestamp = Date.now();
  
  // 创建登录状态对象
  const loginState = {
    userId: userInfo.id,
    token: token,
    isLogin: isLogin,
    timestamp: timestamp
  };

  try {
    // 保存登录状态
    wx.setStorageSync(LOGIN_STATE_KEY, loginState);

    // 保存用户信息和令牌
    wx.setStorageSync(USER_INFO_KEY, userInfo);
    wx.setStorageSync(TOKEN_KEY, token);
    wx.setStorageSync('tokenTimestamp', timestamp); // 新增：单独保存时间戳

    // 清除登录弹窗标记
    wx.removeStorageSync('loginModalShown');

    // 更新全局数据
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.userInfo = userInfo;
      app.globalData.isLogin = true;
      app.globalData.needRefreshProfile = true;
      app.globalData.token = token; // 同步全局token
      app.globalData.tokenTimestamp = timestamp; // 新增：同步全局时间戳
    }

    // 登录状态已保存
    return true;
  } catch (error) {
    // 保存登录状态失败
    return false;
  }
}

/**
 * 获取登录状态
 * @returns {Object|null} 登录状态对象或null
 */
function getLoginState() {
  try {
    const loginState = wx.getStorageSync(LOGIN_STATE_KEY);
    if (loginState && !loginState.timestamp) {
      // 确保登录状态包含时间戳
      loginState.timestamp = wx.getStorageSync('tokenTimestamp') || Date.now();
    }
    return loginState || null;
  } catch (error) {
    // 获取登录状态失败
    return null;
  }
}

/**
 * 清除登录状态
 */
function clearLoginState() {
  try {
    console.log('开始清除登录状态');
    
    // 清除本地存储
    wx.removeStorageSync(LOGIN_STATE_KEY);
    wx.removeStorageSync(TOKEN_KEY);
    wx.removeStorageSync(USER_INFO_KEY);
    wx.removeStorageSync('tokenTimestamp');
    wx.removeStorageSync('loginModalShown');
    
    // 更新全局数据
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.isLogin = false;
      app.globalData.userInfo = null;
      app.globalData.token = null;
      app.globalData.tokenTimestamp = 0; // 使用0而不是null
      console.log('已清除全局登录状态');
    } else {
      console.log('无法获取全局数据，仅清除了本地存储');
    }
    
    console.log('登录状态已完全清除');
    return true;
  } catch (error) {
    console.error('清除登录状态失败:', error);
    return false;
  }
}

/**
 * 验证登录状态
 * @returns {Promise} 包含验证结果的Promise
 */
function validateLoginState() {
  return new Promise((resolve) => {
    const loginState = getLoginState();
    // 验证登录状态
    console.log('开始验证登录状态:', loginState);

    if (!loginState) {
      // 未找到登录状态
      console.warn('未找到登录状态');
      resolve({
        isValid: false,
        message: '未登录'
      });
      return;
    }

    const token = wx.getStorageSync(TOKEN_KEY);
    const userInfo = wx.getStorageSync(USER_INFO_KEY);
    // 验证登录状态
    console.log('本地token和userInfo状态:', !!token, !!userInfo);

    if (!token || !userInfo) {
      // 登录状态不完整
      console.warn('登录状态不完整，清除登录状态');
      clearLoginState();
      resolve({
        isValid: false,
        message: '登录状态不完整'
      });
      return;
    }

    // 确保用户信息中有id字段
    if (!userInfo.id && userInfo._id) {
      // 用户信息中使用_id替代id，正在规范化
      console.log('用户信息中使用_id替代id，正在规范化');
      userInfo.id = userInfo._id;
      // 更新存储
      wx.setStorageSync(USER_INFO_KEY, userInfo);
    }

    // 验证用户ID一致性
    if (loginState.userId !== userInfo.id) {
      // 用户ID不一致
      console.warn('登录状态ID与用户信息ID不一致:', loginState.userId, userInfo.id);
      clearLoginState();
      resolve({
        isValid: false,
        message: '用户ID不一致'
      });
      return;
    }

    // 验证令牌一致性
    if (loginState.token !== token) {
      // 令牌不一致
      console.warn('登录状态token与存储token不一致');
      clearLoginState();
      resolve({
        isValid: false,
        message: '令牌不一致'
      });
      return;
    }

    // 在服务器验证前，先返回本地状态为有效，避免用户体验中断
    // 这样用户可以立即看到已登录状态，而不必等待网络请求完成
    const localValidResult = {
      isValid: true,
      message: '使用本地登录状态',
      userInfo: userInfo,
      usingLocalState: true
    };
    
    // 检查token是否过期（简单检查：如果token存在超过24小时，则可能过期）
    const now = Date.now();
    const tokenTimestamp = loginState.timestamp || wx.getStorageSync('tokenTimestamp') || 0;
    const tokenAge = now - tokenTimestamp;
    const ONE_DAY = 24 * 60 * 60 * 1000; // 24小时，单位毫秒
    
    console.log('token年龄(小时):', tokenAge / (60 * 60 * 1000));
    
    // 简化token验证逻辑，统一使用24小时验证
    const TOKEN_AGE_LIMIT = ONE_DAY; // 统一使用24小时验证

    if (tokenAge > TOKEN_AGE_LIMIT) {
      console.warn('登录状态可能已过期，将进行服务器验证');
      // 不立即返回本地状态，而是等待服务器验证
    } else {
      // 立即解析为有效，让UI可以立即显示登录状态
      console.log('本地token验证通过，立即返回有效状态');
      resolve(localValidResult);
    }

    // 然后在后台继续验证服务器状态
    console.log('开始后台验证服务器登录状态，用户ID:', userInfo && userInfo.id);
    userApi.getUserInfo(userInfo && userInfo.id)
      .then(res => {
        // 后台验证服务器登录状态结果
        console.log('服务器验证结果:', res);

        if (res.success) {
          // 确保服务器返回的用户信息中有id字段
          if (!res.data.id && res.data._id) {
            // 服务器返回的用户信息中使用_id替代id
            res.data.id = res.data._id;
          }

          // 验证服务器返回的用户ID
          if (res.data.id !== loginState.userId) {
            // 服务器返回的用户ID与本地不一致
            console.warn('本地ID与服务器ID不一致:', loginState.userId, res.data.id);

            // 更新本地用户信息
            saveLoginState(res.data, token);
            console.log('已更新本地用户信息与服务器同步');
          } else {
            // 更新本地用户信息，确保最新
            saveLoginState(res.data, token);
            console.log('已更新本地用户信息');
          }

          // 更新全局状态
          const app = getApp();
          if (app && app.globalData) {
            app.globalData.userInfo = res.data;
            app.globalData.isLogin = true;
            app.globalData.needRefreshProfile = true;
            app.globalData.token = token; // 确保全局token也被更新
            console.log('已更新全局状态');
          }
          
          // 如果前面没有立即返回结果，这里返回服务器验证通过的结果
          if (tokenAge > TOKEN_AGE_LIMIT) {
            console.log('服务器验证通过，返回有效状态');
            resolve({
              isValid: true,
              message: '服务器验证通过',
              userInfo: res.data
            });
          }
        } else {
          console.warn('服务器验证失败:', res.message);

          // 如果是未登录错误或令牌无效，提示用户重新登录
          if (res.message === '未登录' || res.message === '认证令牌无效') {
            console.warn('服务器报告未登录或令牌无效');
            
            // 检查当前页面路径，避免在订单创建页面清除登录状态
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            const currentRoute = currentPage ? currentPage.route : '';
            
            // 如果是订单创建页面，不清除登录状态
            if (currentRoute === 'pages/order/create') {
              console.log('订单创建页面检测到token无效，但不清除登录状态');
              // 如果前面没有立即返回结果，这里返回服务器验证失败的结果
              if (tokenAge > TOKEN_AGE_LIMIT) {
                console.log('服务器验证失败，但不清除登录状态，返回无效状态');
                resolve({
                  isValid: false,
                  message: res.message
                });
              }
              return;
            }
            
            // 对于非订单创建页面，清除登录状态
            clearLoginState();
            
            // 如果前面没有立即返回结果，这里返回服务器验证失败的结果
            if (tokenAge > TOKEN_AGE_LIMIT) {
              console.log('服务器验证失败，返回无效状态');
              resolve({
                isValid: false,
                message: res.message
              });
            }
            
            // 避免重复弹窗
            const loginModalShown = wx.getStorageSync('loginModalShown');
            if (!loginModalShown && tokenAge <= ONE_DAY) { // 只有之前认为token有效时才提示
              // 设置标记，避免重复弹窗
              wx.setStorageSync('loginModalShown', true);
              // 5分钟后清除标记
              setTimeout(() => {
                wx.removeStorageSync('loginModalShown');
              }, 5 * 60 * 1000);
              
              wx.showModal({
                title: '登录已过期',
                content: '请重新登录',
                showCancel: false,
                success: () => {
                  wx.redirectTo({ url: '/pages/auth/auth' });
                }
              });
            }
          }
        }
      })
      .catch(err => {
        console.error('后台验证登录状态出错:', err);
        // 网络错误时不做任何处理，保留本地登录状态
        console.log('网络错误，继续使用本地登录状态');
        
        // 如果前面没有立即返回结果，这里返回网络错误但仍然使用本地状态
        if (tokenAge > TOKEN_AGE_LIMIT) {
          console.log('网络错误但仍使用本地状态，返回有效状态');
          resolve(localValidResult);
        }
      });
  });
}

/**
 * 登录
 * @param {Object} loginParams 登录参数
 * @param {Function} loginMethod 登录方法
 * @returns {Promise} 包含登录结果的Promise
 */
function login(loginParams, loginMethod) {
  return new Promise((resolve, reject) => {
    // 清除之前的登录状态
    clearLoginState();

    console.log('开始登录流程，参数:', loginParams);

    // 调用登录方法
    loginMethod(loginParams)
      .then(res => {
        console.log('登录响应:', res);

        if (res.success) {
          // 确保用户信息中有id字段
          const userInfo = res.data.userInfo || res.data.user;
          const token = res.data.token;

          if (!userInfo) {
            console.error('登录响应中缺少用户信息');
            resolve({
              success: false,
              message: '登录响应格式错误'
            });
            return;
          }

          if (!token) {
            console.error('登录响应中缺少令牌');
            resolve({
              success: false,
              message: '登录响应格式错误'
            });
            return;
          }

          // 确保用户信息中有id字段
          if (!userInfo.id && userInfo._id) {
            // 用户信息中使用_id替代id，正在规范化
            userInfo.id = userInfo._id;
          }

          // 保存登录状态
          const saveResult = saveLoginState(userInfo, token);
          console.log('保存登录状态结果:', saveResult);

          resolve({
            success: true,
            message: '登录成功',
            data: {
              token: token,
              userInfo: userInfo
            }
          });
        } else {
          resolve({
            success: false,
            message: res.message || '登录失败'
          });
        }
      })
      .catch(err => {
        console.error('登录出错:', err);
        reject(err);
      });
  });
}

/**
 * 登出
 */
function logout() {
  return new Promise((resolve) => {
    // 清除登录状态
    clearLoginState();

    resolve({
      success: true,
      message: '已登出'
    });
  });
}

/**
 * 恢复本地登录状态到全局变量（用于热重载后自动同步）
 */
function restoreLoginStateToGlobal() {
  try {
    const app = getApp();
    const loginState = getLoginState();
    const userInfo = wx.getStorageSync(USER_INFO_KEY);
    const token = wx.getStorageSync(TOKEN_KEY);
    const tokenTimestamp = wx.getStorageSync('tokenTimestamp');
    
    console.log('尝试恢复登录状态到全局变量:', 
      '登录状态:', !!loginState, 
      '用户信息:', !!userInfo, 
      'token:', !!token, 
      '时间戳:', tokenTimestamp);
    
    if (loginState && loginState.isLogin && userInfo && token && app && app.globalData) {
      // 确保token一致性
      if (loginState.token !== token) {
        console.warn('登录状态中的token与存储的token不一致，使用存储的token');
      }
      
      // 确保时间戳存在
      const finalTimestamp = tokenTimestamp || loginState.timestamp || Date.now();
      
      // 更新全局数据
      app.globalData.userInfo = userInfo;
      app.globalData.isLogin = true;
      app.globalData.token = token;
      app.globalData.tokenTimestamp = finalTimestamp;
      app.globalData.needRefreshProfile = true;
      
      console.log('已恢复登录状态到全局变量，token时间戳:', new Date(finalTimestamp).toLocaleString());
      return true;
    }
    
    console.log('无需恢复登录状态到全局变量');
    return false;
  } catch (e) {
    console.error('恢复登录状态到全局变量失败:', e);
    return false;
  }
}

module.exports = {
  saveLoginState,
  getLoginState,
  clearLoginState,
  validateLoginState,
  login,
  logout,
  restoreLoginStateToGlobal // 新增导出
};
