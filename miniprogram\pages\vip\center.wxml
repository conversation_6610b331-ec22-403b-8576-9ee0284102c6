<view class="vip-center-container">
  <!-- 用户信息区 -->  <view class="user-info">
    <image class="avatar" src="{{userInfo.avatarUrl || '/images/icons2/默认头像.png'}}" mode="aspectFill"></image>
    <view class="user-details">
      <view class="nickname">{{userInfo.nickName || '昵称'}}</view>
      <view class="user-id">ID: {{userInfo.id || 'XXXXXXXXXX'}}</view>
    </view>
    <view class="vip-info" wx:if="{{!loading.vipInfo}}">
      <view class="vip-label">{{identityLabel}}</view>
      <view class="vip-expire" wx:if="{{vipInfo.expireDate}}">有效期至{{vipInfo.expireDate}}</view>
      <view class="vip-expire" wx:else>暂无会员权益</view>
    </view>
    <view class="vip-info" wx:else>
      <view class="vip-loading">正在加载...</view>
    </view>
  </view>

  <!-- VIP权益展示区 -->
  <view class="vip-benefits-display" wx:if="{{!loading.vipInfo && vipInfo.level_code && vipInfo.level_code !== 'free'}}">
    <view class="benefits-header">
      <view class="benefits-title">当前会员权益</view>
      <view class="benefits-subtitle">{{vipInfo.levelName || 'VIP会员'}}</view>
    </view>
    <view class="benefits-content">
      <view class="benefit-item" wx:for="{{currentVipBenefits}}" wx:key="*this">
        <view class="benefit-icon">✓</view>
        <view class="benefit-text">{{item}}</view>
      </view>
    </view>
  </view>

  <!-- 非VIP用户提示区 -->
  <view class="non-vip-section" wx:if="{{!loading.vipInfo && (!vipInfo.level_code || vipInfo.level_code === 'free')}}">
    <view class="non-vip-content">
      <view class="non-vip-icon">👑</view>
      <view class="non-vip-title">开通会员享专属权益</view>
      <view class="non-vip-desc">成为VIP会员，享受专属服务与优惠</view>
    </view>
  </view>

  <!-- 会员权益卡片区 -->  <view class="vip-benefit-section">
    <!-- 加载状态显示 -->
    <view class="loading-container" wx:if="{{loading.vipProducts || loading.vipBenefits}}">
      <view class="loading-spinner"></view>
      <text>正在加载会员产品...</text>
    </view>

    <!-- 会员产品轮播 -->
    <swiper class="benefit-swiper" wx:else indicator-dots="true" circular="true" autoplay="{{false}}"
      current="{{currentProductIndex}}" bindchange="onProductSwiperChange"
      previous-margin="120rpx" next-margin="120rpx" display-multiple-items="1" style="height:700rpx;overflow:visible;">
      <block wx:for="{{vipProducts}}" wx:key="id">
        <swiper-item class="benefit-swiper-item">
          <view class="benefit-card {{currentProductIndex === index ? 'active' : ''}}">
            <view class="benefit-title">{{item.level_name}}</view>
            <view class="benefit-price">¥{{item.price}}/年</view>
            <view class="benefit-list">
              <view class="benefit-item" wx:for="{{item.benefits}}" wx:key="*this" wx:for-item="benefit">
                {{benefit}}
              </view>
            </view>
            <button class="renew-btn" bindtap="handleVipAction">{{item.buttonText || '立即开通'}}</button>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <button class="more-vip-btn">更多会员产品</button>
</view>