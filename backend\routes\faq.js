/**
 * 常见问题公开API路由
 * 处理常见问题相关的公开API请求
 */

const express = require('express');
const router = express.Router();
const db = require('../utils/db');

/**
 * 获取常见问题列表（公开接口）
 * GET /api/faq/list
 */
router.get('/list', async (req, res) => {
  try {
    console.log('获取常见问题列表（公开接口）');
    
    const query = `
      SELECT 
        id,
        question,
        answer,
        sort_order,
        createTime,
        updateTime
      FROM faq
      WHERE is_active = 1
      ORDER BY sort_order ASC, createTime DESC
    `;
    
    const faqList = await db.query(query);
    
    console.log(`查询到 ${faqList.length} 个启用的常见问题`);
    
    res.json({
      success: true,
      data: faqList,
      message: '获取常见问题列表成功'
    });
    
  } catch (error) {
    console.error('获取常见问题列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取常见问题列表失败',
      error: error.message
    });
  }
});

module.exports = router; 