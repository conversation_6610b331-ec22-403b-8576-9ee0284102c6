// 1. 先打印环境变量
console.log('DB_USER:', process.env.DB_USER);
console.log('MYSQL_USERNAME:', process.env.MYSQL_USERNAME);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD);
console.log('MYSQL_PASSWORD:', process.env.MYSQL_PASSWORD);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('MYSQL_IP:', process.env.MYSQL_IP);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('MYSQL_DATABASE:', process.env.MYSQL_DATABASE);

const fs = require('fs');
const path = require('path');
const envPath = path.resolve(__dirname, '.env'); // 只查找 backend 目录下的 .env
console.log('---.env文件内容---');
if (fs.existsSync(envPath)) {
  console.log(fs.readFileSync(envPath, 'utf-8'));
} else {
  console.log('未找到 .env 文件:', envPath);
}
console.log('------------------');
require('dotenv').config({ path: envPath });
console.log('调试环境变量:', process.env.DB_USER, process.env.DB_PASSWORD, process.env.DB_HOST, process.env.DB_NAME);

require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });
console.log('调试环境变量:', process.env.DB_USER, process.env.DB_PASSWORD, process.env.DB_HOST, process.env.DB_NAME);
const mysql = require('mysql2/promise');
const configFile = require('./config');

// 2. 再声明dbHost等变量
const dbHost = configFile.db.host || process.env.MYSQL_IP || process.env.DB_HOST;
const dbPort = configFile.db.port || process.env.MYSQL_PORT || process.env.DB_PORT || 3306;
const dbUser = configFile.db.user || process.env.MYSQL_USERNAME || process.env.DB_USER;
const dbName = configFile.db.database || process.env.MYSQL_DATABASE || process.env.DB_NAME;
const dbPassword = (configFile.db.password || process.env.MYSQL_PASSWORD || process.env.DB_PASSWORD || '').replace(/"/g, '');

// 3. 变量声明后再打印
console.log('最终用于连接的参数:', dbHost, dbPort, dbUser, dbPassword, dbName);

console.log('数据库配置:');
console.log('- 主机:', dbHost);
console.log('- 端口:', dbPort);
console.log('- 用户:', dbUser);
console.log('- 数据库:', dbName);
console.log('- 密码长度:', dbPassword.length);

console.log('环境变量调试:');
console.log('process.env.DB_HOST:', process.env.DB_HOST);
console.log('process.env.DB_PORT:', process.env.DB_PORT);
console.log('process.env.DB_USER:', process.env.DB_USER);
console.log('process.env.DB_PASSWORD:', process.env.DB_PASSWORD);
console.log('process.env.DB_NAME:', process.env.DB_NAME);
console.log('configFile.db:', configFile.db);

// 尝试多个数据库主机地址
const possibleHosts = [
  dbHost,
  'mysql',
  'morebuy-mysql',
  '**********',
  '**********',
  'localhost'
];

let pool = null;
let currentHost = null;

// 尝试连接数据库
async function createConnection() {
  for (const host of possibleHosts) {
    if (!host) continue;
    
    console.log(`尝试连接数据库主机: ${host}`);
    
    try {
      const testPool = mysql.createPool({
        host: host,
        port: dbPort,
        user: dbUser,
        password: dbPassword,
        database: dbName,
        charset: 'utf8mb4',
        collation: 'utf8mb4_0900_ai_ci',
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0,
        connectTimeout: 10000, // 减少连接超时时间
        acquireTimeout: 10000,
        timeout: 10000,
        enableKeepAlive: true,
        keepAliveInitialDelay: 10000,
        reconnect: true,
        idleTimeout: 300000,
        maxIdle: 5
      });
      
      // 测试连接
      const connection = await testPool.getConnection();
      await connection.ping();
      connection.release();
      
      console.log(`✅ 数据库连接成功! 使用主机: ${host}`);
      pool = testPool;
      currentHost = host;
      break;
      
    } catch (error) {
      console.log(`❌ 连接失败 ${host}:`, error.message);
      continue;
    }
  }
  
  if (!pool) {
    console.error('❌ 所有数据库主机都无法连接');
    // 创建一个默认的pool，但会在查询时失败
    pool = mysql.createPool({
      host: dbHost,
      port: dbPort,
      user: dbUser,
      password: dbPassword,
      database: dbName,
      charset: 'utf8mb4',
      collation: 'utf8mb4_0900_ai_ci',
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
      connectTimeout: 30000,
      acquireTimeout: 30000,
      timeout: 30000,
      enableKeepAlive: true,
      keepAliveInitialDelay: 10000,
      reconnect: true,
      idleTimeout: 300000,
      maxIdle: 5
    });
  }
}

// 立即初始化数据库连接
async function initializeDatabase() {
  await createConnection();

  if (pool && currentHost) {
    let retryCount = 0;
    const maxRetries = 5;

    while (retryCount < maxRetries) {
      try {
        const connection = await pool.getConnection();
        console.log('数据库连接成功!');

        // 测试查询messages表
        try {
          const [rows] = await connection.execute('SELECT COUNT(*) as count FROM messages');
          console.log('消息表中有', rows[0].count, '条记录');
        } catch (err) {
          console.warn('查询消息表失败:', err.message);
        }

        connection.release();
        break; // 连接成功，退出重试循环
      } catch (err) {
        retryCount++;
        console.warn(`数据库连接失败 (尝试 ${retryCount}/${maxRetries}):`, err.message);

        if (retryCount < maxRetries) {
          console.log(`${2}秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, 2000));
        } else {
          console.error('数据库连接最终失败，应用将在运行时重试');
        }
      }
    }
  }
}

// 立即初始化，但不阻塞模块加载
initializeDatabase().catch(err => {
  console.error('数据库初始化失败:', err.message);
});

module.exports = {
  query: async (sql, params = [], retryCount = 0) => {
    const maxRetries = 3;

    // 确保数据库连接池已初始化
    if (!pool) {
      console.log('数据库连接池未初始化，等待初始化完成...');
      // 等待初始化完成，最多等待5秒
      let waitCount = 0;
      while (!pool && waitCount < 50) {
        await new Promise(resolve => setTimeout(resolve, 100));
        waitCount++;
      }
      
      if (!pool) {
        console.error('数据库连接池初始化失败，尝试重新初始化...');
        await initializeDatabase();
        
        if (!pool) {
          throw new Error('数据库连接池初始化失败');
        }
      }
    }

    try {
      console.log('执行SQL:', sql);
      console.log('参数:', params);

      // 确保参数是数组
      const paramArray = Array.isArray(params) ? params : [];

      // 确保参数类型正确
      const safeParams = paramArray.map(param => {
        if (param === undefined || param === null) {
          return null;
        }

        // 如果是数字字符串，转换为数字
        if (typeof param === 'string' && !isNaN(param) && param.trim() !== '') {
          if (param.includes('.')) {
            return parseFloat(param);
          } else {
            return parseInt(param, 10);
          }
        }

        return param;
      });

      console.log('安全参数:', safeParams);

      // 再次检查pool是否可用
      if (!pool) {
        throw new Error('数据库连接池不可用');
      }

      try {
        // 尝试使用 execute 方法
        const [rows] = await pool.execute(sql, safeParams);
        console.log('查询结果:', rows ? `返回${rows.length}行数据` : '无数据');
        return rows;
      } catch (executeError) {
        console.error('execute 方法失败:', executeError.message);
        console.log('尝试使用 query 方法...');

        // 如果 execute 失败，尝试使用 query 方法
        const [rows] = await pool.query(sql, safeParams);
        console.log('查询结果 (query方法):', rows ? `返回${rows.length}行数据` : '无数据');
        return rows;
      }
    } catch (error) {
      // 检查是否是连接错误且可以重试
      const isConnectionError = error.code === 'ECONNRESET' ||
                               error.code === 'ENOTFOUND' ||
                               error.code === 'ETIMEDOUT' ||
                               error.code === 'ECONNREFUSED';

      if (isConnectionError && retryCount < maxRetries) {
        console.warn(`数据库查询失败，正在重试 (${retryCount + 1}/${maxRetries}):`, error.message);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // 递增延迟
        return module.exports.query(sql, params, retryCount + 1);
      }

      console.error('数据库查询错误:', error.message);
      console.error('SQL:', sql);
      console.error('参数:', params);
      throw error;
    }
  },

  // 获取数据库连接（用于事务处理）
  getConnection: async () => {
    // 确保数据库连接池已初始化
    if (!pool) {
      console.log('数据库连接池未初始化，等待初始化完成...');
      // 等待初始化完成，最多等待5秒
      let waitCount = 0;
      while (!pool && waitCount < 50) {
        await new Promise(resolve => setTimeout(resolve, 100));
        waitCount++;
      }
      
      if (!pool) {
        throw new Error('数据库连接池初始化失败');
      }
    }

    try {
      const connection = await pool.getConnection();

      // 保存原始的查询方法
      const originalQuery = connection.query.bind(connection);
      const originalExecute = connection.execute.bind(connection);

      // 为连接添加便捷的查询方法（避免递归调用）
      connection.queryHelper = async (sql, params = []) => {
        try {
          const [rows] = await originalExecute(sql, params);
          return rows;
        } catch (executeError) {
          console.warn('execute 方法失败，尝试使用 query 方法:', executeError.message);
          // 如果 execute 失败，尝试使用原生 query 方法
          const [rows] = await originalQuery(sql, params);
          return rows;
        }
      };

      return connection;
    } catch (error) {
      console.error('获取数据库连接失败:', error);
      throw error;
    }
  },

  // 获取连接池状态
  getPoolStatus: () => {
    if (!pool) {
      return { status: 'not_initialized' };
    }

    return {
      status: 'active',
      host: currentHost,
      connectionLimit: pool.config.connectionLimit,
      acquiredConnections: pool._acquiredConnections ? pool._acquiredConnections.length : 0,
      freeConnections: pool._freeConnections ? pool._freeConnections.length : 0
    };
  }
};
console.log('文件末尾环境变量:', process.env.DB_USER, process.env.DB_PASSWORD, process.env.DB_HOST, process.env.DB_NAME);