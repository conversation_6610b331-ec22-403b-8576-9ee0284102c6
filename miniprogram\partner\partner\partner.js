const loginStateManager = require('../../utils/login-state-manager');
const { partnerApi } = require('../../utils/api');

Page({
  data: {
    userInfo: null,
    isLogin: false,
    loading: true,
    
    // 资金账户数据
    fundAccount: {
      account_balance: 0.00,
      pending_commission: 0.00,
      total_commission: 0.00,
      total_withdrawal: 0.00,
      total_dividend: 0.00
    },
    
    // 推荐统计
    referralStats: {
      referral_count: 0,
      store_count: 0
    },
    
    // 订单统计
    orderStats: {
      pending_shipment: 0,
      shipped: 0,
      signed: 0,
      returns: 0
    },
    
    // 门店列表
    storeList: [],
    selectedStore: null
  },

  onLoad() {
    console.log('=== 合伙人页面 onLoad 开始 ===');
    
    // 检查当前用户的身份信息
    this.debugUserRoles();
    
    // 优先从本地存储读取门店信息
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    console.log('本地门店数据检查:', {
      storedStoreList: !!storedStoreList,
      storeCount: storedStoreList ? storedStoreList.length : 0,
      storedSelectedStore: !!storedSelectedStore
    });
    
    if (storedStoreList && storedStoreList.length > 0) {
      const selectedStore = storedSelectedStore || storedStoreList[0];
      this.setData({
        storeList: storedStoreList,
        selectedStore: selectedStore
      });
      console.log('页面加载时从本地存储读取门店信息:', selectedStore);
    }
    
    this.checkLoginStatus();
    console.log('=== 合伙人页面 onLoad 结束 ===');
  },

  // 调试用户角色信息
  debugUserRoles() {
    console.log('=== 调试用户角色信息 ===');
    const app = getApp();
    const globalData = app.globalData || {};
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    const loginState = loginStateManager.getLoginState();
    
    console.log('全局登录状态:', globalData.isLogin);
    console.log('全局用户信息:', globalData.userInfo);
    console.log('全局当前角色:', globalData.currentRole);
    console.log('本地用户信息:', userInfo);
    console.log('本地token:', !!token);
    console.log('登录状态管理器状态:', loginState);
    
    if (userInfo && userInfo.roles) {
      console.log('用户角色列表:', userInfo.roles);
      const partnerRole = userInfo.roles.find(r => r.role_type === 'partner');
      console.log('合伙人角色:', partnerRole);
      
      if (!partnerRole) {
        console.warn('⚠️ 用户没有合伙人角色！');
        wx.showModal({
          title: '权限提示',
          content: '您当前没有合伙人权限，请联系管理员开通合伙人权限。',
          showCancel: false,
          complete: () => {
            wx.reLaunch({ url: '/pages/profile/profile' });
          }
        });
        return false;
      }
    } else {
      console.warn('⚠️ 用户信息中没有角色数据！');
    }
    
    console.log('=== 调试用户角色信息结束 ===');
    return true;
  },

  // 检查门店数据状态
  checkStoreDataStatus() {
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    console.log('=== 门店数据状态检查 ===');
    console.log('页面数据 - storeList:', this.data.storeList);
    console.log('页面数据 - selectedStore:', this.data.selectedStore);
    console.log('本地存储 - partnerStoreList:', storedStoreList);
    console.log('本地存储 - partnerSelectedStore:', storedSelectedStore);
    console.log('========================');
  },

  onShow() {
    console.log('合伙人页面 onShow');
    
    // 检查门店数据状态
    this.checkStoreDataStatus();
    
    // 检查是否从顾客端切换过来
    const lastPage = wx.getStorageSync('lastPage');
    const isFromCustomerSide = lastPage && lastPage.includes('pages/') && !lastPage.includes('partner/');
    console.log('检测到来源页面:', lastPage, '是否从顾客端切换:', isFromCustomerSide);
    
    this.checkLoginStatus();
    
    // 如果从顾客端切换过来，强制刷新门店数据
    if (isFromCustomerSide && this.data.isLogin) {
      console.log('从顾客端切换过来，强制刷新门店数据');
      this.loadPartnerData(true);
      return;
    }
    
    // 从本地存储读取门店信息，但不覆盖已有的数据
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    if (storedStoreList && storedStoreList.length > 0) {
      const selectedStore = storedSelectedStore || storedStoreList[0];
      
      // 只有当当前页面没有门店数据，或者存储的数据与当前数据不同时才更新
      if (!this.data.storeList || this.data.storeList.length === 0 || 
          !this.data.selectedStore || this.data.selectedStore.id !== selectedStore.id) {
        this.setData({
          storeList: storedStoreList,
          selectedStore: selectedStore
        });
        console.log('从本地存储更新门店信息:', selectedStore);
      } else {
        console.log('门店信息无需更新，保持当前选择:', this.data.selectedStore);
      }
    }
    
    // 再次检查门店数据状态
    this.checkStoreDataStatus();
  },

  checkLoginStatus() {
    console.log('检查登录状态...');
    const app = getApp();
    const globalData = app.globalData || {};
    this.setData({ loading: true });
    
    if (globalData.isLogin && globalData.userInfo) {
      console.log('从全局数据获取登录状态:', globalData.userInfo);
      this.setData({
        userInfo: globalData.userInfo,
        isLogin: true,
        loading: false
      });
      this.loadPartnerData();
      return;
    }
    
    const loginState = loginStateManager.getLoginState();
    const userInfo = wx.getStorageSync('userInfo');
    console.log('从本地存储获取登录状态:', { loginState, userInfo });
    
    if (loginState && loginState.isLogin && userInfo) {
      globalData.userInfo = userInfo;
      globalData.isLogin = true;
      this.setData({
        userInfo: userInfo,
        isLogin: true,
        loading: false
      });
      this.loadPartnerData();
      return;
    }
    
    loginStateManager.validateLoginState().then(result => {
      console.log('验证登录状态结果:', result);
      if (result.isValid) {
        globalData.userInfo = result.userInfo;
        globalData.isLogin = true;
        this.setData({
          userInfo: result.userInfo,
          isLogin: true,
          loading: false
        });
        this.loadPartnerData();
      } else {
        loginStateManager.clearLoginState();
        globalData.userInfo = null;
        globalData.isLogin = false;
        this.setData({
          userInfo: null,
          isLogin: false,
          loading: false
        });
      }
    }).catch((error) => {
      console.error('验证登录状态失败:', error);
      this.setData({
        userInfo: null,
        isLogin: false,
        loading: false
      });
    });
  },

  // 加载合伙人数据
  async loadPartnerData(forceRefreshStores = false) {
    console.log('开始加载合伙人数据...', forceRefreshStores ? '(强制刷新门店数据)' : '');
    if (!this.data.isLogin) {
      console.log('用户未登录，跳过数据加载');
      return;
    }
    
    try {
      wx.showLoading({ title: '加载中...' });
      
      console.log('并行获取所有数据...');
      
      // 获取本地存储的选中门店，用于后续保持用户选择
      const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
      
      // 始终获取最新的门店数据
      const promises = [
        partnerApi.getPartnerStats(),
        partnerApi.getOrderStats(),
        partnerApi.getPartnerStores(),
        partnerApi.getPartnerJoinedStores()
      ];
      
      const results = await Promise.all(promises);
      const [partnerStats, orderStats, partnerStores, joinedStores] = results;
      
      console.log('API响应结果:', {
        partnerStats,
        orderStats,
        partnerStores,
        joinedStores
      });
      
      if (partnerStats.success) {
        console.log('设置资金账户和推荐统计数据:', partnerStats.data);
        this.setData({
          fundAccount: partnerStats.data.fundAccount,
          referralStats: partnerStats.data.referralStats
        });
      } else {
        console.error('合伙人统计API失败:', partnerStats);
      }
      
      if (orderStats.success) {
        console.log('设置订单统计数据:', orderStats.data);
        this.setData({
          orderStats: orderStats.data
        });
      } else {
        console.error('订单统计API失败:', orderStats);
      }
      
      // 处理门店数据
      // 合并两个API的门店数据，并去重
      let allStores = [];
      
      if (partnerStores && partnerStores.success && partnerStores.data) {
        allStores = [...partnerStores.data];
      }
      
      if (joinedStores && joinedStores.success && joinedStores.data) {
        // 将joinedStores中的门店添加到allStores中，避免重复
        joinedStores.data.forEach(store => {
          // 检查是否已存在相同store_no的门店
          const existingIndex = allStores.findIndex(s => s.store_no === store.store_no);
          if (existingIndex === -1) {
            // 不存在则添加
            allStores.push(store);
          }
        });
      }
      
      console.log('合并后的门店列表数据:', allStores);
      
      if (allStores.length > 0) {
        // 尝试保持之前选择的门店
        let selectedStore = null;
        if (storedSelectedStore) {
          // 在新获取的门店列表中查找之前选择的门店
          const previousSelectedStore = allStores.find(store => 
            store.id === storedSelectedStore.id || store.store_no === storedSelectedStore.store_no
          );
          if (previousSelectedStore) {
            selectedStore = previousSelectedStore;
            console.log('保持之前选择的门店:', selectedStore);
          } else {
            // 如果之前选择的门店不在新列表中，选择第一个
            selectedStore = allStores[0];
            console.log('之前选择的门店不在新列表中，选择第一个:', selectedStore);
          }
        } else {
          // 没有之前的选择，选择第一个门店
          selectedStore = allStores[0];
        }
        
        this.setData({
          storeList: allStores,
          selectedStore: selectedStore
        });
        
        // 保存到本地存储，供其他页面使用
        wx.setStorageSync('partnerStoreList', allStores);
        wx.setStorageSync('partnerSelectedStore', selectedStore);
        console.log('门店数据已保存到本地存储:', {
          storeList: allStores,
          selectedStore: selectedStore
        });
      } else {
        console.warn('没有获取到门店数据');
      }
      
      console.log('数据加载完成，当前页面数据:', this.data);
      
    } catch (error) {
      console.error('加载合伙人数据失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 门店选择
  onStoreChange(e) {
    const index = e.detail.value;
    const selectedStore = this.data.storeList[index];
    
    console.log('门店选择前 - 当前门店:', this.data.selectedStore);
    console.log('门店选择前 - 新选择门店:', selectedStore);
    
    this.setData({ selectedStore });
    
    // 保存到本地存储，供其他页面读取
    wx.setStorageSync('partnerSelectedStore', selectedStore);
    wx.setStorageSync('partnerStoreList', this.data.storeList);
    
    console.log('合伙人页面选择门店:', selectedStore);
    console.log('门店选择后 - 当前门店:', this.data.selectedStore);
  },

  // 跳转到门店管理
  goToStoreManage(e) {
    const type = e.currentTarget.dataset.type;
    
    // 检查是否有门店数据
    if (!this.data.storeList || this.data.storeList.length === 0) {
      wx.showToast({
        title: '暂无门店数据',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.selectedStore) {
      wx.showToast({
        title: '请先选择门店',
        icon: 'none'
      });
      return;
    }
    
    const storeNo = this.data.selectedStore.store_no;
    
    // 处理不同类型的跳转
    if (type === 'partners') {
      // 跳转到门店合伙人页面
      wx.navigateTo({
        url: `/partner/store-partners/store-partners?storeNo=${storeNo}`
      });
      return;
    } else if (type === 'inventory') {
      // 跳转到门店库存页面
      wx.navigateTo({
        url: `/partner/inventory/inventory?storeNo=${storeNo}`
      });
      return;
    } else if (type === 'orders') {
      // 跳转到门店订单页面
      wx.navigateTo({
        url: `/partner/store-orders/store-orders?storeNo=${storeNo}`
      });
      return;
    }
    
    // 其他类型的跳转保持原有逻辑
    wx.navigateTo({
      url: `/admin/store/${type}?storeNo=${storeNo}`
    });
  },

  // 跳转到顾客订单页面
  goToOrderManage(e) {
    const status = e.currentTarget.dataset.status;
    wx.navigateTo({
      url: `/partner/customer-orders/customer-orders?status=${status}`
    });
  },

  onSwitchLogin() {
    wx.navigateTo({ url: '/pages/switch-login/switch-login' });
  },

  goToLogin() {
    wx.navigateTo({ url: '/pages/auth/auth' });
  },

  // 其他功能按钮事件处理
  goToAbout() {
    console.log('点击关于我们按钮');
    wx.navigateTo({ url: '/pages/about/index' });
  },

  goToFAQ() {
    console.log('点击常见问题按钮');
    wx.showToast({
      title: '常见问题功能开发中',
      icon: 'none'
    });
  },

  goToOnlineService() {
    console.log('点击在线客服按钮');
    wx.showToast({
      title: '在线客服功能开发中',
      icon: 'none'
    });
  },

  goToAccountSettings() {
    wx.navigateTo({ 
      url: '/pages/settings/account/account'
    });
  }
}); 