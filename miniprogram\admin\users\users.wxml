<view class="user-mgr-container">
  <!-- 顶部四栏整体锁定容器 -->
  <view class="user-mgr-top-sticky">
    <!-- 顶部栏 -->
    <view class="user-mgr-header">
      <view class="user-mgr-search-bar">
        <view class="user-mgr-search-input-container">
          <input class="user-mgr-search-input" placeholder="请输入搜索内容" value="{{searchValue}}" bindinput="onSearchInput" confirm-type="search" bindconfirm="onSearch" />
          <view class="user-mgr-search-btn" bindtap="onSearch">
            <image src="/images/icons2/搜索.png"></image>
          </view>
        </view>
      </view>
    </view>
    <!-- 操作按钮 -->
    <view class="user-mgr-actions">
      <button class="user-mgr-btn" bindtap="onSelectAll">{{allSelected ? '取消全选' : '全选'}}</button>
      <button class="user-mgr-btn" bindtap="onBatchFreeze">批量冻结</button>
      <button class="user-mgr-btn" bindtap="onBatchUnfreeze">批量解冻</button>
      <button class="user-mgr-btn" bindtap="onAddPartner">加入合伙人</button>
    </view>
    <!-- 筛选标签 -->
    <view class="user-mgr-tabs">
      <text class="user-mgr-tab {{tabActive==='all'?'active':''}}" data-type="all" bindtap="onTabChange">全部用户</text>
      <text class="user-mgr-tab {{tabActive==='customer'?'active':''}}" data-type="customer" bindtap="onTabChange">普通顾客</text>
      <text class="user-mgr-tab {{tabActive==='vip'?'active':''}}" data-type="vip" bindtap="onTabChange">VIP会员</text>
      <text class="user-mgr-tab {{tabActive==='partner'?'active':''}}" data-type="partner" bindtap="onTabChange">合伙人</text>
    </view>
    <!-- 表头 -->
    <view class="user-mgr-table-header">
      <text class="sortable-header {{sortField === 'regDate' ? 'sorted-' + sortOrder : ''}}" bindtap="onSortByDate">注册时间</text>
      <text class="sortable-header {{sortField === 'id' ? 'sorted-' + sortOrder : ''}}" bindtap="onSortById">用户ID号</text>
      <text>筛选</text>
    </view>
  </view>
  <!-- 搜索状态提示 -->
  <view wx:if="{{isSearching}}" class="user-mgr-searching">
    <text>正在搜索...</text>
  </view>
  
  <!-- 搜索结果提示 -->
  <view wx:if="{{searchValue && !isSearching}}" class="user-mgr-search-result">
    <text>搜索"{{searchValue}}"的结果：{{userList.length}}个用户</text>
  </view>
  
  <!-- 用户列表 -->
  <block wx:for="{{userList}}" wx:key="id">
    <view class="user-mgr-item">
      <view class="user-mgr-checkbox {{item.selected ? 'selected' : ''}}" bindtap="toggleSelectItem" data-index="{{index}}">
        <image src="{{item.selected ? '/images/icons/checked-box.svg' : '/images/icons/unchecked-box.svg'}}"></image>
      </view>
      <image class="user-mgr-avatar" src="{{item.avatar}}" />
      <view class="user-mgr-info">
        <view class="user-mgr-nickname-row">
          <view class="user-mgr-nickname">{{item.nickname}}</view>
          <view class="user-mgr-role user-mgr-role-{{getRoleClass(item.highestRole)}}">{{item.highestRole || '顾客'}}</view>
        </view>
        <view class="user-mgr-id">ID: {{item.id}}</view>
        <view class="user-mgr-phone">手机号: {{item.phone}}</view>
        <view class="user-mgr-date">注册: {{item.regDate}}</view>
      </view>
      <view class="user-mgr-status-top {{item.status==='正常'?'status-normal':'status-frozen'}}">{{item.status==='正常'?'正常':'冻结'}}</view>
      <view class="user-mgr-edit-btn" bindtap="onEditUser" data-id="{{item.id}}">调整</view>
    </view>
  </block>
  <!-- 底部统计 -->
  <view class="user-mgr-footer">
    共计：{{userList.length}}个用户
    <text wx:if="{{selectedCount > 0}}" class="selected-count">（已选中{{selectedCount}}个）</text>
  </view>
  <!-- 底部导航栏 -->
  <admin-tabbar current="users" />
  
  <!-- 用户调整半屏弹窗 -->
  <view class="user-edit-drawer {{showEditDrawer ? 'show' : ''}}" bindtap="onDrawerMaskTap">
    <view class="user-edit-content" catchtap="onDrawerContentTap">
      <view class="user-edit-header">
        <text class="user-edit-title">用户信息调整</text>
        <view class="user-edit-close" bindtap="onCloseEditDrawer">×</view>
      </view>
      
      <view class="user-edit-body">
        <!-- 用户基本信息 -->
        <view class="user-edit-section">
          <view class="user-edit-field">
            <text class="user-edit-label">用户ID</text>
            <text class="user-edit-value">{{currentUser.id}}</text>
          </view>
          <view class="user-edit-field">
            <text class="user-edit-label">用户名称</text>
            <text class="user-edit-value">{{currentUser.nickname}}</text>
          </view>
          <view class="user-edit-field">
            <text class="user-edit-label">推荐人</text>
            <input class="user-edit-input" value="{{currentUser.referrerId}}" placeholder="请输入推荐人ID" bindinput="onReferrerInput" />
          </view>
          <view class="user-edit-field">
            <text class="user-edit-label">销售人</text>
            <input class="user-edit-input" value="{{currentUser.salesmanId}}" placeholder="请输入销售人ID" bindinput="onSalesmanInput" />
          </view>
          <view class="user-edit-field">
            <text class="user-edit-label">订阅门店</text>
            <input class="user-edit-input" value="{{currentUser.subscribedStore}}" placeholder="请输入门店编号" bindinput="onStoreInput" />
          </view>
        </view>
        
        <!-- 账户余额调整 -->
        <view class="user-edit-section">
          <view class="user-edit-section-title">账户余额调整</view>
          <view class="user-edit-field">
            <text class="user-edit-label">当前余额</text>
            <text class="user-edit-value">¥{{currentUser.accountBalance || '0.00'}}</text>
          </view>
          <view class="user-edit-field">
            <text class="user-edit-label">调整事由</text>
            <picker class="user-edit-picker" range="{{balanceReasons}}" value="{{selectedReasonIndex}}" bindchange="onReasonChange">
              <view class="user-edit-picker-text">{{selectedReasonIndex >= 0 ? balanceReasons[selectedReasonIndex] : '请选择事由'}}</view>
            </picker>
          </view>
          <view class="user-edit-field" wx:if="{{selectedReasonIndex >= 0}}">
            <text class="user-edit-label">调整金额</text>
            <input class="user-edit-input" type="digit" value="{{adjustAmount}}" placeholder="请输入调整金额" bindinput="onAmountInput" />
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="user-edit-actions">
          <button class="user-edit-btn user-edit-btn-cancel" bindtap="onCloseEditDrawer">取消</button>
          <button class="user-edit-btn user-edit-btn-save" bindtap="onSaveUserInfo">保存</button>
        </view>
      </view>
    </view>
  </view>
</view>