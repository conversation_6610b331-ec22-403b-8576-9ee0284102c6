<!--pages/message/message.wxml-->
<scroll-view class="message-container" scroll-y="true" enhanced="true" show-scrollbar="false" enable-flex="true" scroll-anchoring="true" bounces="false">
  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="/images/icons/search.png"></image>
      <input class="search-input"
             placeholder="搜索消息"
             bindinput="onSearchInput"
             value="{{searchKeyword}}"
             confirm-type="search"
             bindconfirm="onSearch"/>
    </view>
    <!-- 添加刷新按钮 -->
    <view class="refresh-btn" bindtap="refreshMessages">
      <image class="refresh-icon" src="/images/icons/refresh.png"></image>
    </view>
  </view>

  <!-- 消息分类导航 -->
  <view class="tab-container">
    <view class="tab-bar">
      <view class="tab-item {{currentTab === index ? 'active' : ''}}"
            wx:for="{{tabs}}"
            wx:key="*this"
            bindtap="switchTab"
            data-index="{{index}}">
        <text>{{item}}</text>
        <view class="tab-line" wx:if="{{currentTab === index}}"></view>
      </view>
    </view>
  </view>

  <!-- 登录提示 -->
  <view class="login-prompt" wx:if="{{!isLogin}}">
    <image class="login-icon" src="../../images/icons/message-large.png"></image>
    <text class="login-text">登录后查看消息</text>
    <view class="login-btn" bindtap="goToLogin">立即登录</view>
  </view>

  <!-- 消息列表 -->
  <view class="message-list" wx:if="{{isLogin && !loading}}">
    <!-- 个人私信 -->
    <block wx:if="{{currentTab === 0}}">
      <view class="message-item-container"
            wx:for="{{filteredMessages}}"
            wx:key="_id"
            data-index="{{index}}"
            bindtouchstart="touchStart"
            bindtouchmove="touchMove"
            bindtouchend="touchEnd">
        <view class="message-item {{item.isRead ? '' : 'unread'}}" style="transform: translateX({{item.slideOffset}}px)">
          <image class="avatar" src="{{item.targetInfo.avatarUrl}}"></image>
          <view class="message-content" bindtap="onMessageTap" data-index="{{index}}">
            <view class="message-header">
              <view class="sender-name">{{item.targetInfo.nickName}}</view>
              <view class="message-time">{{item.createTime}}</view>
            </view>
            <view class="message-text">
              <text>{{item.isFromMe ? '我: ' : ''}}</text>
              <text>{{item.contentType === 'image' ? '[图片]' : item.content}}</text>
            </view>
          </view>
          <view class="unread-badge" wx:if="{{item.unreadCount > 0}}">{{item.unreadCount > 99 ? '99+' : item.unreadCount}}</view>
        </view>
        <view class="delete-btn" bindtap="deleteMessage" data-id="{{item._id}}">删除</view>
      </view>
    </block>

    <!-- 群消息 -->
    <block wx:elif="{{currentTab === 1}}">
      <view class="group-tab-switch">
        <view class="group-tab {{groupTab === 0 ? 'active' : ''}}" bindtap="switchGroupTab" data-index="0" data-text="我的群聊">
          我的群聊
          <view class="group-tab-underline" wx:if="{{groupTab === 0}}"></view>
        </view>
        <view class="group-tab {{groupTab === 1 ? 'active' : ''}}" bindtap="switchGroupTab" data-index="1" data-text="全部群聊">
          全部群聊
          <view class="group-tab-underline" wx:if="{{groupTab === 1}}"></view>
        </view>
      </view>
      <view class="group-tab-content">
        <!-- 群聊加载状态 -->
        <view class="group-loading-container" wx:if="{{groupLoading}}">
          <view class="group-loading-icon"></view>
          <text class="group-loading-text">加载群聊中...</text>
        </view>
        <!-- 我的群聊卡片 -->
        <view wx:elif="{{groupTab === 0}}">
          <view class="group-list" wx:if="{{myGroups.length > 0}}">
            <view class="group-item" wx:for="{{myGroups}}" wx:key="id" bindtap="onGroupTap" data-group="{{item}}" style="animation-delay: {{index * 0.05}}s;">
              <image class="group-avatar" src="{{item.avatar}}"></image>
              <view class="group-info">
                <view class="group-row-1">
                  <text class="group-title-text">{{item.name}}</text>
                  <text wx:if="{{item.role === 'owner'}}" class="owner-tag">群主</text>
                  <button class="member-btn" catchtap="onViewMembers" data-groupid="{{item.id}}" size="mini">群成员</button>
                </view>
                <view class="group-row-2">
                  <view class="group-desc">{{item.description || '暂无群介绍'}}</view>
                  <text class="member-count">({{item.memberCount}}人)</text>
                </view>
              </view>
            </view>
          </view>
          <view class="empty-container" wx:else>
            <image class="empty-icon" src="/images/icons/empty-group.png"></image>
            <text class="empty-text">暂无加入的群聊</text>
          </view>
        </view>
        <!-- 全部群聊卡片 -->
        <view wx:elif="{{groupTab === 1}}">
          <view class="group-list" wx:if="{{publicGroups.length > 0}}">
            <view class="group-item" wx:for="{{publicGroups}}" wx:key="id" bindtap="onPublicGroupTap" data-group="{{item}}" style="animation-delay: {{index * 0.05}}s;">
              <image class="group-avatar" src="{{item.avatar}}"></image>
              <view class="group-info">
                <view class="group-row-1">
                  <text class="group-title-text">{{item.name}}</text>
                  <block wx:if="{{item.role == 'owner'}}">
                    <text class="owner-tag">群主</text>
                  </block>
                  <block wx:if="{{item.role == 'member'}}">
                    <text class="joined-tag">已加入</text>
                  </block>
                  <button class="member-btn" catchtap="onViewMembers" data-groupid="{{item.id}}" size="mini">群成员</button>
                </view>
                <view class="group-row-2">
                  <view class="group-desc">{{item.description || '暂无群介绍'}}</view>
                  <text class="member-count">({{item.memberCount}}人)</text>
                </view>
              </view>
            </view>
          </view>
          <view class="empty-container" wx:else>
            <image class="empty-icon" src="/images/icons/empty-group.png"></image>
            <text class="empty-text">暂无公开群聊</text>
          </view>
        </view>
      </view>
      <!-- 悬浮创建群聊按钮，仅在我的群聊卡片显示 -->
      <view class="create-group-btn-container">
        <view wx:if="{{groupTab === 0}}" class="float-create-group-btn" bindtap="goToCreateGroup">
          <image src="/images/icons2/添加.png" class="float-create-group-icon" />
        </view>
      </view>
    </block>

    <!-- 系统通知 -->
    <block wx:elif="{{currentTab === 2}}">
      <view class="empty-container">
        <image class="empty-icon" src="/images/icons/empty-message.png"></image>
        <text class="empty-text">暂无通知</text>
      </view>
    </block>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:elif="{{loading}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{isLogin && !loading && filteredMessages.length === 0}}">
    <image class="empty-icon" src="/images/icons/empty-message.png"></image>
    <text class="empty-text">暂无消息</text>
  </view>
  <view class="tabbar-container">
    <partner-tabbar selected="2" />
  </view>
</scroll-view>
