const { storeApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');

Page({
  data: {
    searchValue: '',
    activeTab: 0,
    levelList: ['全部', '一星门店', '二星门店', '三星门店', '四星门店', '五星门店'], // 门店级别分类
    selectedLevel: '全部',
    stores: [],
    selectAll: false,
    page: 1,
    pageSize: 20,
    total: 0,
    selectedCount: 0,
    showCreateDrawer: false,
    showEditDrawer: false,
    currentStore: {},
    // 新增：页面类型，用于区分不同的门店管理功能
    pageType: '',
    storeNo: ''
  },

  onLoad(options) {
    // 页面加载时恢复全局登录状态
    loginStateManager.restoreLoginStateToGlobal();
    
    // 获取页面参数
    const pageType = options.type || '';
    const storeNo = options.storeNo || '';
    
    console.log('门店管理页面参数:', { pageType, storeNo });
    
    this.setData({
      pageType,
      storeNo
    });
    
    // 根据页面类型决定是否显示门店列表
    if (pageType === 'orders') {
      // 门店订单页面，不显示门店列表
      this.setData({
        stores: [],
        total: 0
      });
      // TODO: 加载门店订单数据
      wx.showToast({
        title: '门店订单功能开发中',
        icon: 'none'
      });
    } else {
      // 其他类型，显示门店列表
      this.fetchStores();
    }
  },

  // 页面显示时处理从裁剪页面返回的图片数据
  onShow() {
    console.log('门店管理页面显示，检查裁剪图片数据');
    
    // 检查是否有从裁剪页面返回的图片数据
    if (this.data.croppedStoreImage) {
      console.log('发现裁剪后的门店图片:', this.data.croppedStoreImage);
      
      // 如果编辑抽屉是打开状态，将图片数据传递给编辑抽屉组件
      if (this.data.showEditDrawer) {
        const editDrawer = this.selectComponent('#store-edit-drawer');
        if (editDrawer) {
          // 强制更新组件数据，确保图片能正确显示
          editDrawer.setData({
            storeImageTemp: this.data.croppedStoreImage,
            imageUpdateTime: Date.now() // 添加时间戳强制更新
          });
          console.log('已将裁剪图片传递给编辑抽屉组件');
        }
      }
      
      // 如果创建抽屉是打开状态，将图片数据传递给创建抽屉组件
      if (this.data.showCreateDrawer) {
        const createDrawer = this.selectComponent('#store-create-drawer');
        if (createDrawer) {
          // 强制更新组件数据，确保图片能正确显示
          createDrawer.setData({
            storeImageTemp: this.data.croppedStoreImage,
            imageUpdateTime: Date.now() // 添加时间戳强制更新
          });
          console.log('已将裁剪图片传递给创建抽屉组件');
        }
      }
      
      // 清除临时数据
      this.setData({
        croppedStoreImage: ''
      });
    }
  },
  // 门店级别切换
  onLevelTabChange(e) {
    const level = e.currentTarget.dataset.level;
    this.setData({ selectedLevel: level }, () => {
      this.fetchStores();
    });
  },
  // 获取门店列表（支持门店级别筛选）
  async fetchStores() {
    wx.showLoading({ title: '加载中...' });
    try {
      const res = await storeApi.getStores();
      wx.hideLoading();
      if (res.success && Array.isArray(res.data)) {
        let stores = res.data.map(store => ({
          id: store.id,
          store_no: store.store_no,
          name: store.name,
          level: store.level,
          level_title: store.level_title || 'L3', // 使用level_title字段，默认为L3
          province: store.province,
          city: store.city,
          district: store.district,
          address: `${store.province}${store.city}${store.district}`,
          image: store.image && store.image.trim() ? store.image : '/images/icons2/店铺.png'
        }));
        // 动态生成门店级别列表（使用level_title）
        const allLevels = Array.from(new Set(stores.map(s => s.level_title).filter(Boolean)));
        const levelList = ['全部', ...allLevels];
        // 门店级别筛选（根据level_title筛选）
        if (this.data.selectedLevel && this.data.selectedLevel !== '全部') {
          stores = stores.filter(s => s.level_title === this.data.selectedLevel);
        }
        this.setData({ stores, total: stores.length, selectedCount: 0, selectAll: false, levelList });
      } else {
        this.setData({ stores: [], total: 0 });
      }
    } catch (err) {
      wx.hideLoading();
      wx.showToast({ title: '加载门店失败', icon: 'none' });
      this.setData({ stores: [], total: 0 });
    }
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({ searchValue: e.detail.value });
  },
  // 搜索按钮
  onSearch() {
    // 后续可接入API
    this.fetchStores(this.data.tabs[this.data.activeTab], this.data.searchValue);
  },
  // Tab切换
  onTabChange(e) {
    const idx = e.currentTarget.dataset.index;
    this.setData({ activeTab: idx, searchValue: '' });
    this.fetchStores(this.data.tabs[idx], '');
  },
  // 全选/取消
  onSelectAll() {
    // 改为弹出创建门店弹窗
    this.setData({ showCreateDrawer: true });
  },
  onCreateDrawerCancel() {
    this.setData({ showCreateDrawer: false });
  },
  async onCreateDrawerConfirm(e) {
    const { storeName, level, level_title, province, city, district, storeImage } = e.detail;
    
    console.log('创建门店数据:', { storeName, level, level_title, province, city, district, storeImage });
    
    wx.showLoading({ title: '创建中...' });
    try {
      const res = await storeApi.createStore({
        name: storeName,
        level,
        level_title,
        province,
        city,
        district,
        contact_person: '', // 联系人空值
        phone: '',         // 电话空值
        image: storeImage // 使用用户上传的门店图片
      });
      wx.hideLoading();
      if (res.success) {
        wx.showToast({ title: '创建成功', icon: 'success' });
        this.setData({ showCreateDrawer: false });
        // 刷新门店列表
        this.fetchStores();
      } else {
        wx.showToast({ title: res.message || '创建失败', icon: 'none' });
      }
    } catch (err) {
      console.error('创建门店失败:', err);
      wx.hideLoading();
      wx.showToast({ title: '网络异常', icon: 'none' });
    }
  },
  // 取消创建门店
  onCreateDrawerCancel() {
    this.setData({ showCreateDrawer: false });
  },
  // 单选
  onCheckStore(e) {
    const idx = e.currentTarget.dataset.index;
    const stores = this.data.stores.slice();
    stores[idx].checked = !stores[idx].checked;
    const selectedCount = stores.filter(item => item.checked).length;
    this.setData({
      stores,
      selectAll: stores.every(item => item.checked),
      selectedCount
    });
  },
  // 批量升级（合伙人变更）
  onBatchUpgrade() {
    wx.navigateTo({
      url: '/admin/store/partner-change'
    });
  },
  // 批量冻结
  onBatchFreeze() {
    wx.showToast({ title: '批量冻结功能待接入', icon: 'none' });
  },
  // 编辑门店
  onEditStore(e) {
    const index = e.currentTarget.dataset.index;
    const store = this.data.stores[index];
    this.setData({
      currentStore: store,
      showEditDrawer: true
    });
  },

  // 取消编辑弹窗
  onEditDrawerCancel() {
    this.setData({ showEditDrawer: false });
  },

  // 确认编辑保存
  async onEditDrawerConfirm(e) {
    const updatedData = e.detail;
    console.log('编辑门店保存数据:', updatedData);
    
    wx.showLoading({ title: '保存中...' });
    try {
      const res = await storeApi.updateStore(this.data.currentStore.id, updatedData);
      wx.hideLoading();
      if (res.success) {
        wx.showToast({ title: '保存成功', icon: 'success' });
        this.setData({ showEditDrawer: false });
        // 刷新门店列表
        this.fetchStores();
      } else {
        wx.showToast({ title: res.message || '保存失败', icon: 'none' });
      }
    } catch (err) {
      console.error('编辑门店失败:', err);
      wx.hideLoading();
      wx.showToast({ title: '网络异常', icon: 'none' });
    }
  },
  // 资金管理跳转
  onFundManage() {
    wx.navigateTo({
      url: '/admin/store/store-fund'
    });
  }
});