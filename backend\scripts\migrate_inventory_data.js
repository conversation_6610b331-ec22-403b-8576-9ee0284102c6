/**
 * 将store_cloud_stock和store_offline_stock表的数据迁移到store_inventory表
 */
const db = require('../config/db');

async function migrateInventoryData() {
  try {
    console.log('开始迁移库存数据...');
    
    // 开始事务
    const connection = await db.getConnection();
    await connection.beginTransaction();
    
    try {
      // 1. 获取store_cloud_stock表的数据
      console.log('获取云端库存数据...');
      const cloudStocks = await connection.queryHelper(
        'SELECT * FROM store_cloud_stock'
      );
      console.log(`获取到${cloudStocks.length}条云端库存数据`);
      
      // 2. 获取store_offline_stock表的数据
      console.log('获取线下库存数据...');
      const offlineStocks = await connection.queryHelper(
        'SELECT * FROM store_offline_stock'
      );
      console.log(`获取到${offlineStocks.length}条线下库存数据`);
      
      // 3. 将云端库存数据插入到store_inventory表
      console.log('迁移云端库存数据到store_inventory表...');
      let cloudMigratedCount = 0;
      const now = Date.now();
      
      for (const stock of cloudStocks) {
        // 检查store_inventory表中是否已存在该记录
        const existingRecord = await connection.queryHelper(
          'SELECT id FROM store_inventory WHERE store_no = ? AND product_id = ?',
          [stock.store_no, stock.product_id]
        );
        
        if (existingRecord.length === 0) {
          // 不存在，插入新记录
          await connection.queryHelper(
            'INSERT INTO store_inventory (store_no, product_id, cloud_quantity, offline_quantity, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?)',
            [stock.store_no, stock.product_id, stock.quantity, 0, stock.updated_at || now, stock.updated_at || now]
          );
          cloudMigratedCount++;
        } else {
          // 已存在，更新记录
          await connection.queryHelper(
            'UPDATE store_inventory SET cloud_quantity = ?, update_time = ? WHERE store_no = ? AND product_id = ?',
            [stock.quantity, stock.updated_at || now, stock.store_no, stock.product_id]
          );
          cloudMigratedCount++;
        }
      }
      
      console.log(`成功迁移${cloudMigratedCount}条云端库存数据`);
      
      // 4. 将线下库存数据插入到store_inventory表（如果有相同记录则累加库存）
      console.log('迁移线下库存数据到store_inventory表...');
      let offlineMigratedCount = 0;
      
      for (const stock of offlineStocks) {
        // 检查store_inventory表中是否已存在该记录
        const existingRecord = await connection.queryHelper(
          'SELECT id, offline_quantity FROM store_inventory WHERE store_no = ? AND product_id = ?',
          [stock.store_no, stock.product_id]
        );
        
        if (existingRecord.length === 0) {
          // 不存在，插入新记录
          await connection.queryHelper(
            'INSERT INTO store_inventory (store_no, product_id, cloud_quantity, offline_quantity, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?)',
            [stock.store_no, stock.product_id, 0, stock.quantity, stock.updated_at || now, stock.updated_at || now]
          );
          offlineMigratedCount++;
        } else {
          // 已存在，更新记录（设置线下库存）
          await connection.queryHelper(
            'UPDATE store_inventory SET offline_quantity = ?, update_time = ? WHERE store_no = ? AND product_id = ?',
            [stock.quantity, stock.updated_at || now, stock.store_no, stock.product_id]
          );
          offlineMigratedCount++;
        }
      }
      
      console.log(`成功迁移${offlineMigratedCount}条线下库存数据`);
      
      // 提交事务
      await connection.commit();
      console.log('数据迁移成功！');
      
    } catch (error) {
      // 回滚事务
      await connection.rollback();
      throw error;
    } finally {
      // 释放连接
      connection.release();
    }
    
    console.log('库存数据迁移完成！');
    process.exit(0);
    
  } catch (error) {
    console.error('迁移库存数据失败:', error);
    process.exit(1);
  }
}

// 执行迁移
migrateInventoryData();