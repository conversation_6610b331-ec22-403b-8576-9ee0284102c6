// pages/home/<USER>
// 新版首页 - 电商风格
const { productApi, cartApi, favoriteApi } = require('../../utils/api');
const request = require('../../utils/request');

Page({
  data: {
    // 系统信息
    windowWidth: 0,
    
    // 搜索相关
    searchKeyword: '',
    searchWidth: 0,
    
    // 轮播图数据
    banners: [],
    
    // 快捷菜单数据
    quickMenus: [],
    
    // 商品数据
    products: [],
    hotProducts: [], // 热门商品
    newProducts: [], // 新品推荐
    showNewProducts: false, // 控制新品上市模块显示，当新品数量少于5个时不显示
    
    // 加载状态
    loading: true,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    lastDataLength: 0, // 记录最后一次返回的数据长度

    
    // 当前选中的分类
    selectedCategory: null,
    
    // 用户信息
    globalUserInfo: {},
    
    // 推荐商品标签
    recommendTabs: ['推荐', '热销', '新品'],
    currentRecommendTab: 0,
    imgErrorMap: {},
    productTabs: ['默认', '热销中', '性价比', '大品牌'],
    currentProductTab: 0,
    showBackToTop: false,
    
    // 收藏状态管理
    favoriteStatus: {}, // 存储每个商品的收藏状态 {productId: boolean}
    favoriteLoading: {} // 存储每个商品的收藏操作状态 {productId: boolean}
  },

  /**
   * 页面加载
   */
  onLoad: function (options) {
    // 减少日志输出：简化页面加载日志
    // console.log('新版首页加载');
    
    // 处理推荐参数
    if (options.scene || options.referrer) {
      const referrerId = options.scene || options.referrer;
      if (referrerId) {
        console.log('检测到推荐参数:', referrerId);
        wx.setStorageSync('referrerId', referrerId);
        wx.showToast({
          title: '来自好友推荐',
          icon: 'success',
          duration: 2000
        });
      }
    }

    // 初始化数据
    this.initData();
    // 移除重复的 getNewProducts 调用，因为 initData 中已经调用了
    this.getProductsByTab(0);
  },

  /**
   * 页面显示
   */
  onShow: function () {
    // 减少日志输出：简化页面显示日志
    // console.log('新版首页显示');
    this.getQuickMenus(); // 每次显示时刷新快捷菜单
  },

  /**
   * 页面滚动监听，控制回到顶部按钮显示
   */
  onPageScroll: function(e) {
    this.setData({ showBackToTop: e.scrollTop > 300 });
  },
  /**
   * 回到顶部
   */
  onBackToTop: function() {
    wx.pageScrollTo({ scrollTop: 0, duration: 300 });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    console.log('下拉刷新');
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    });
    
    this.initData().finally(() => {
      wx.stopPullDownRefresh();
      this.setData({ refreshing: false });
    });
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function() {
    if (!this.data.hasMore || this.data.loading) {
      console.log('无需加载更多或正在加载中');
      return;
    }
    
    console.log('上拉加载更多商品，当前页:', this.data.page);
    this.loadMoreProducts();
  },



  /**
   * 初始化数据
   */
  initData: function() {
    const promises = [
      this.getBanners(),
      this.getQuickMenus(),
      // 移除 this.getProducts() 因为 getProductsByTab(0) 会调用它
      this.getHotProducts(),
      this.getNewProducts()
    ];
    
    return Promise.all(promises).finally(() => {
      this.setData({ loading: false });
    });
  },

  /**
   * 获取轮播图数据
   */
  getBanners: function() {
    return productApi.getBanners('customer_home').then(res => {
      if (res.success && Array.isArray(res.data)) {
        // 减少日志输出：简化轮播图获取成功日志
        // console.log('获取轮播图成功:', res.data);
        // 为轮播图数据添加默认字段
        const banners = res.data.map(banner => ({
          ...banner,
          imageUrl: banner.imageUrl || banner.image_url || '/images/icons2/默认商品.png',
          linkUrl: banner.linkUrl || banner.link_url || '',
          title: banner.title || ''
        }));
        this.setData({ banners });
      } else {
        console.warn('获取轮播图失败，使用默认数据');
        this.setData({
          banners: [
            {
              id: 1,
              title: '欢迎使用',
              imageUrl: '/images/icons2/默认商品.png',
              linkUrl: ''
            }
          ]
        });
      }
    }).catch(err => {
      console.error('获取轮播图错误:', err);
      this.setData({
        banners: [
          {
            id: 1,
            title: '欢迎使用',
            imageUrl: '/images/icons2/默认商品.png',
            linkUrl: ''
          }
        ]
      });
    });
  },

  /**
   * 获取快捷菜单数据（顾客端专用）
   * 明确设置不需要登录验证，并指定平台为顾客端
   */
  getQuickMenus: function() {
    // 减少日志输出：简化快捷菜单请求日志
    console.log('[快捷菜单] 开始请求顾客端快捷菜单');
    return request({
      url: '/api/quick-menus?platform=customer', // 添加平台筛选参数，只获取顾客端菜单
      method: 'GET',
      requireAuth: false // 明确设置不需要登录验证，确保未登录状态下也能获取快捷菜单
    }).then(res => {
      console.log('[快捷菜单] 接口返回原始数据:', res);
      if (res.success && Array.isArray(res.data)) {
        console.log('[快捷菜单] 原始菜单数量:', res.data.length);
        console.log('[快捷菜单] 原始菜单列表:', res.data.map(menu => ({
          id: menu.id,
          name: menu.name,
          link_url: menu.link_url,
          target_platform: menu.target_platform
        })));
        
        // 后端已经根据platform=customer参数和target_platform字段进行了正确的过滤
        // 前端不需要再做重复过滤，直接使用后端返回的数据
        console.log('[快捷菜单] 最终菜单数量:', res.data.length);
        console.log('[快捷菜单] 最终菜单列表:', res.data.map(menu => ({
          id: menu.id,
          name: menu.name,
          link_url: menu.link_url
        })));
        this.setData({ quickMenus: res.data });
      } else {
        console.warn('顾客端快捷菜单接口返回异常:', res);
        this.setData({ quickMenus: [] });
      }
    }).catch(err => {
      console.error('获取顾客端快捷菜单失败:', err);
      this.setData({ quickMenus: [] });
    });
  },

  /**
   * 获取商品列表
   */
  getProducts: function() {
    const params = {
      page: this.data.page,
      pageSize: this.data.pageSize
    };
    
    if (this.data.selectedCategory) {
      params.categoryId = this.data.selectedCategory;
    }
    
    // 如果有当前选中的商品分类标签，传递相应的参数
    if (this.data.currentProductTab > 0) {
      const tabIndex = this.data.currentProductTab;
      if (tabIndex === 1) {
        params.sortType = 'sales';
      } else if (tabIndex === 2) {
        params.sortType = 'price';
        params.category = '性价比';
      } else if (tabIndex === 3) {
        params.sortType = 'brand';
        params.category = '大品牌';
      }
    }
    
    console.log('[首页商品列表] 请求参数:', params);
    console.log('[首页商品列表] 当前页码:', this.data.page, '页面大小:', this.data.pageSize);
    
    return productApi.getProducts(params).then(res => {
      if (res.success && res.data && res.data.list && Array.isArray(res.data.list)) {
        // 减少日志输出：简化商品获取成功日志
        // console.log('获取商品列表成功，页码:', this.data.page, '返回数据:', res.data);
        let productsList = res.data.list;
        
        // 处理商品图片URL，确保格式正确
        productsList = productsList.map(product => {
          // 处理images字段，确保是数组或转为空数组
          if (product.images) {
            if (typeof product.images === 'string') {
              try {
                // 检查字符串是否为有效的JSON格式
                if (product.images.trim().startsWith('[') && product.images.trim().endsWith(']')) {
                  product.images = JSON.parse(product.images);
                } else {
                  // 减少日志输出：只在出现严重问题时警告
                  // console.warn('商品图片数据不是有效的JSON数组格式:', product.images);
                  product.images = [];
                }
              } catch (e) {
                console.error('解析商品图片失败:', e, '原始数据:', product.images);
                product.images = [];
              }
            } else if (!Array.isArray(product.images)) {
              // 减少日志输出：简化警告信息
              // console.warn('商品图片数据不是数组类型:', typeof product.images);
              product.images = [];
            }
          } else {
            product.images = [];
          }
          
          // 确保imageUrl字段存在且为完整URL
          if (!product.imageUrl && product.images && product.images.length > 0) {
            // 确保图片URL是完整路径
            let imageUrl = product.images[0];
            if (typeof imageUrl === 'string') {
              if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('/')) {
                // 如果不是完整路径，添加前缀
                imageUrl = '/' + imageUrl;
              }
              product.imageUrl = imageUrl;
            } else {
              // 减少日志输出：简化警告信息
              // console.warn('商品图片URL不是字符串类型:', imageUrl);
              product.imageUrl = '/images/mo/mogoods.jpg';
            }
          }
          
          // 简化图片处理逻辑 - 如果没有图片或图片路径有问题，直接使用默认图片
          if (!product.imageUrl) {
            product.imageUrl = '/images/mo/mogoods.jpg';
          }
          
          return product;
        });
        
        let products;
        let hasMore = true; // 默认为true，等后面根据数据判断
        
        if (this.data.page === 1) {
          products = productsList;
        } else {
          // 检查是否返回了新数据
          const oldProductsLength = this.data.products.length;
          
          // 合并商品列表
          products = [...this.data.products, ...productsList];
          
          // 减少日志输出：简化合并日志
          // console.log('合并商品列表，本次返回:', res.data.list.length, '原有:', oldProductsLength, '合并后总数:', products.length);
        }
        
        // 判断是否有更多数据的逻辑
        if (res.data.total !== undefined && res.data.total !== null) {
          // 如果后端返回了总数，用总数判断（最准确）
          hasMore = products.length < res.data.total;
          console.log('[首页分页] 使用总数判断 - 当前商品数:', products.length, '总数:', res.data.total, '是否有更多:', hasMore);
        } else {
          // 如果没有总数，用返回的数据量判断
          if (res.data.list.length === 0) {
            // 如果本次没返回任何数据，说明没有更多了
            hasMore = false;
            console.log('[首页分页] 本次返回0个商品，没有更多数据');
          } else if (res.data.list.length < this.data.pageSize) {
            // 如果返回的数据少于页面大小，说明是最后一页
            hasMore = false;
            console.log('[首页分页] 返回数据少于页面大小 - 返回:', res.data.list.length, '页面大小:', this.data.pageSize, '没有更多数据');
          } else {
            // 如果返回的数据等于页面大小，可能还有更多数据
            hasMore = true;
            console.log('[首页分页] 返回数据等于页面大小 - 返回:', res.data.list.length, '页面大小:', this.data.pageSize, '可能有更多数据');
          }
        }
        
        this.setData({
          products: products,
          hasMore: hasMore,
          lastDataLength: res.data.list.length
        });
        
        console.log('[首页商品] 数据更新完成:');
        console.log('  - 当前页码:', this.data.page);
        console.log('  - 本次返回商品数:', res.data.list.length);
        console.log('  - 总商品数:', products.length);
        console.log('  - 是否有更多:', hasMore);
        console.log('  - 后端返回的总数:', res.data.total);
        
        // 减少日志输出：简化更新完成日志
        // console.log('商品列表更新完成，当前页:', this.data.page, '本次返回:', res.data.list.length, '总商品数:', products.length, '是否有更多:', hasMore);
      } else {
        // 减少日志输出：简化失败日志
        // console.log('获取商品列表失败或数据为空');
        this.setData({
          hasMore: false,
          loading: false
        });
        if (this.data.page === 1) {
          this.setData({ products: [] });
        }
      }
    }).catch(err => {
      console.error('获取商品列表失败:', err);
      // 确保即使请求失败也不会影响页面显示
      this.setData({ 
        hasMore: false,
        loading: false 
      });
      
      if (this.data.page === 1) {
        this.setData({ products: [] });
      }
    });
  },

  /**
   * 获取热门商品
   */
  getHotProducts: function() {
    return productApi.getProducts({
      page: 1,
      pageSize: 6,
      sortType: 'sales'
    }).then(res => {
      if (res.success && res.data) {
        console.log('获取热门商品成功:', res.data);
        this.setData({
          hotProducts: res.data.list
        });
      }
    }).catch(err => {
      console.error('获取热门商品失败:', err);
    });
  },

  /**
   * 获取新品推荐数据
   */
  getNewProducts: function() {
    const params = {
      page: 1,
      pageSize: 6,
      isNew: 1, // 明确请求新品数据
      sortType: 'default'
    };
    
    return productApi.getProducts(params).then(res => {
      if (res.success && res.data && res.data.list && Array.isArray(res.data.list)) {
        console.log('[新品上市] 获取新品数据成功，数量:', res.data.list.length);
        
        // 处理商品图片
        const newProducts = res.data.list.map(product => {
          // 处理images字段
          if (product.images && typeof product.images === 'string') {
            try {
              // 检查字符串是否为有效的JSON格式
              if (product.images.trim().startsWith('[') && product.images.trim().endsWith(']')) {
                product.images = JSON.parse(product.images);
              } else {
                // 减少日志输出：简化图片格式警告
                // console.warn('商品图片数据不是有效的JSON数组格式:', product.images);
                product.images = [];
              }
            } catch (e) {
              console.error('解析新品推荐商品图片失败:', e);
              product.images = [];
            }
          }
          
          if (!product.imageUrl && product.images && product.images.length > 0) {
            product.imageUrl = product.images[0];
          }
          
          if (!product.imageUrl) {
            product.imageUrl = '/images/mo/mogoods.jpg';
          }
          
          return product;
        });
        
        // 判断新品数量是否少于5个，如果少于5个则不显示新品上市模块
        const showNewProducts = newProducts.length >= 5;
        console.log('[新品上市] 新品数量:', newProducts.length, '是否显示模块:', showNewProducts);
        
        this.setData({ 
          newProducts,
          showNewProducts
        });
      } else {
        console.log('[新品上市] 获取新品数据失败或数据为空');
        this.setData({ 
          newProducts: [],
          showNewProducts: false
        });
      }
    }).catch(err => {
      console.error('[新品上市] 获取新品数据失败:', err);
      this.setData({ 
        newProducts: [],
        showNewProducts: false
      });
    });
  },

  /**
   * 新品横滑到第10张时自动跳转
   */
  onNewProductsScroll: function(e) {
    // 计算当前滑动到第几个卡片
    const query = wx.createSelectorQuery().in(this);
    query.select('.new-products-scroll').scrollOffset(res => {
      const scrollLeft = res.scrollLeft;
      // 每张卡片宽度约118px（含margin），第10张左边界约为(118*9)
      if (scrollLeft >= 118*9) {
        wx.navigateTo({ url: '/pages/new-products/new-products' });
      }
    }).exec();
  },

  /**
   * 点击新品上市"更多"按钮
   */
  onNewProductsMoreTap: function() {
    console.log('[新品上市] 点击更多按钮，跳转到新品上市页面');
    wx.navigateTo({ 
      url: '/pages/new-products/new-products' 
    });
  },

  /**
   * 加载更多商品
   */
  loadMoreProducts: function() {
    if (this.data.loading || !this.data.hasMore) {
      console.log('[加载更多] 跳过加载 - loading:', this.data.loading, 'hasMore:', this.data.hasMore);
      return;
    }
    
    console.log('[加载更多] 开始加载更多商品，当前页:', this.data.page, '-> 下一页:', this.data.page + 1);
    this.setData({
      page: this.data.page + 1,
      loading: true
    });
    
    this.getProducts().finally(() => {
      console.log('[加载更多] 加载完成，重置loading状态');
      this.setData({ loading: false });
    });
  },

  /**
   * 搜索输入
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 搜索确认
   */
  onSearchConfirm: function() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }
    
    console.log('搜索商品:', keyword);
    
    // 保存搜索历史
    this.saveSearchHistory(keyword);
    
    // 跳转到搜索页面
    wx.navigateTo({
      url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}&type=product`
    });
  },

  /**
   * 保存搜索历史
   */
  saveSearchHistory: function(keyword) {
    if (!keyword) return;

    try {
      let history = wx.getStorageSync('searchHistory') || [];

      // 如果已存在相同关键词，先移除
      history = history.filter(item => item !== keyword);

      // 添加到历史记录开头
      history.unshift(keyword);

      // 限制最多保存10条
      if (history.length > 10) {
        history = history.slice(0, 10);
      }

      // 保存到本地存储
      wx.setStorageSync('searchHistory', history);
    } catch (e) {
      console.error('保存搜索历史失败', e);
    }
  },

  /**
   * 轮播图点击
   */
  onBannerTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const banner = this.data.banners[index];
    
    console.log('点击轮播图:', banner);
    
    if (banner.linkUrl) {
      // 根据链接类型进行跳转
      if (banner.linkUrl.startsWith('/pages/')) {
        wx.navigateTo({
          url: banner.linkUrl
        });
      } else if (banner.linkUrl.startsWith('http')) {
        // 外部链接，可以使用web-view或其他方式处理
        wx.showToast({
          title: '即将跳转到外部链接',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 快捷菜单点击
   */
  onQuickMenuTap: function(e) {
    const menu = e.currentTarget.dataset.menu;
    console.log('点击快捷菜单:', menu);
    
    // 需要登录检查的功能列表
    const loginRequiredFunctions = ['viewPoints', 'goToVipCenter', 'viewFavorites', 'viewAddress', 'shareStore'];
    const loginRequiredPages = ['/pages/points/points', '/pages/vip/center', '/pages/favorites/favorites', '/pages/address/list', '/pages/share/share'];
    
    if (menu.link_type === 'function') {
      // 执行功能
      this.executeFunction(menu.link_url);
    } else if (menu.link_type === 'page') {
      // 页面跳转 - 检查是否需要登录
      if (loginRequiredPages.includes(menu.link_url)) {
        // 需要登录的页面，使用统一的登录检查逻辑
        this.checkLoginAndNavigate(menu.link_url);
      } else {
        // 不需要登录的页面，直接跳转
        wx.navigateTo({
          url: menu.link_url
        }).catch(() => {
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        });
      }
    } else if (menu.link_type === 'external') {
      // 外部链接
      wx.showToast({
        title: '即将跳转到外部链接',
        icon: 'none'
      });
    }
  },

  /**
   * 统一的登录检查和页面跳转方法
   */
  checkLoginAndNavigate: function(url) {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: url
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: url
    });
  },
  
  /**
   * 执行功能方法
   */
  executeFunction: function(functionName) {
    switch (functionName) {
      case 'getRedPack':
        this.getRedPacket();
        break;
      case 'contactService':
        this.contactService();
        break;
      case 'shareStore':
        this.shareStore();
        break;
      case 'viewPoints':
        this.viewPoints();
        break;
      case 'viewFavorites':
        this.viewFavorites();
        break;
      case 'viewAddress':
        this.viewAddress();
        break;
      case 'viewFAQ':
        this.viewFAQ();
        break;
      case 'goToVipCenter':
        this.goToVipCenter();
        break;
      case 'goToSettings':
        this.goToSettings();
        break;
      case 'goToAbout':
        this.goToAbout();
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },
  
  /**
   * 领取红包
   */
  getRedPacket: function() {
    wx.showToast({
      title: '暂无红包',
      icon: 'none'
    });
  },
  
  /**
   * 联系客服
   */
  contactService: function() {
    wx.navigateTo({
      url: '/pages/service/index'
    });
  },
  
  /**
   * 分享本店
   */
  shareStore: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/share/share'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/share/share'
    });
  },

  /**
   * 查看积分
   */
  viewPoints: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/points/points'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/points/points'
    });
  },

  /**
   * 我的收藏
   */
  viewFavorites: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/favorites/favorites'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/favorites/favorites'
    });
  },

  /**
   * 收货地址
   */
  viewAddress: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/address/list'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/address/list'
    });
  },

  /**
   * 常见问题
   */
  viewFAQ: function() {
    wx.navigateTo({
      url: '/pages/faq/faq'
    });
  },

  /**
   * 会员中心
   */
  goToVipCenter: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/vip/center'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/vip/center'
    });
  },

  /**
   * 设置
   */
  goToSettings: function() {
    const app = getApp();
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '请先登录',
        content: '登录后可进入账号设置',
        showCancel: true,
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/auth/auth' });
          }
        }
      });
      return;
    }
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  /**
   * 关于我们
   */
  goToAbout: function() {
    wx.navigateTo({
      url: '/pages/about/index'
    });
  },

  /**
   * 商品点击
   */
  onProductTap: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('点击商品:', productId);
    
    wx.navigateTo({
      url: `/pages/product/detail?id=${productId}`
    });
  },

  /**
   * 新品上市商品点击
   */
  onNewProductTap: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('点击新品商品:', productId);
    
    wx.navigateTo({
      url: `/pages/product/detail?id=${productId}`
    });
  },

  /**
   * 推荐标签切换
   */
  onRecommendTabTap: function(e) {
    const index = e.currentTarget.dataset.index;
    console.log('切换推荐标签:', index);
    
    this.setData({
      currentRecommendTab: index
    });
  },

  /**
   * 商品分类标签切换
   */
  onProductTabTap: function(e) {
    const index = e.currentTarget.dataset.index;
    // 减少日志输出：简化标签切换日志
    // console.log('切换商品标签:', index);
    this.setData({
      currentProductTab: index,
      page: 1,
      products: [],
      loading: true
    });
    this.getProductsByTab(index);
  },

  /**
   * 根据标签获取商品
   */
  getProductsByTab: function(tabIndex) {
    console.log('切换商品标签:', tabIndex);
    // 重置页面状态
    this.setData({
      page: 1,
      hasMore: true,
      products: [],
      currentProductTab: tabIndex,
      loading: false,
      lastDataLength: 0
    });
    
    // 重新获取商品列表
    this.getProducts();
  },

  /**
   * 查看更多商品
   */
  onViewMoreProducts: function() {
    console.log('查看更多商品');
    wx.switchTab({
      url: '/pages/category/category'
    });
  },

  /**
   * 消息图标点击
   */
  onMessageTap: function() {
    console.log('点击消息');
    wx.navigateTo({
      url: '/partner/messages/messages'
    });
  },

  /**
   * 商品图片加载失败兜底
   */
  onGoodsImgError: function(e) {
    const id = e.currentTarget.dataset.id;
    if (!id) {
      return;
    }
    
    // 设置错误标记，使用默认图片
    this.setData({
      [`imgErrorMap.${id}`]: true
    });
    
    // 直接使用默认图片，不再检测旧路径（减少日志输出）
    // const fixedImageUrl = '/images/mo/mogoods.jpg';
    // console.log('将使用默认图片:', fixedImageUrl);
  },

  /**
   * 添加/取消收藏
   */
  onFavoriteTap: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('收藏操作:', productId);
    
    // 检查是否正在操作
    if (this.data.favoriteLoading[productId]) {
      return;
    }
    
    // 检查登录状态
    const app = getApp();
    app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.performFavoriteToggle(productId);
      }
    });
  },

  /**
   * 执行收藏/取消收藏操作
   */
  performFavoriteToggle: function(productId) {
    // 设置加载状态
    this.setData({
      [`favoriteLoading.${productId}`]: true
    });
    
    const isCurrentlyFavorite = this.data.favoriteStatus[productId];
    
    if (isCurrentlyFavorite) {
      // 取消收藏
      favoriteApi.removeFromFavorites(productId)
        .then(res => {
          this.setData({
            [`favoriteLoading.${productId}`]: false
          });
          
          if (res.success) {
            this.setData({
              [`favoriteStatus.${productId}`]: false
            });
            wx.showToast({
              title: '已取消收藏',
              icon: 'success',
              duration: 1500
            });
          } else {
            wx.showToast({
              title: res.message || '操作失败',
              icon: 'none'
            });
          }
        })
        .catch(err => {
          console.error('取消收藏失败', err);
          this.setData({
            [`favoriteLoading.${productId}`]: false
          });
          wx.showToast({
            title: '网络错误，请稍后再试',
            icon: 'none'
          });
        });
    } else {
      // 添加收藏
      favoriteApi.addToFavorites(productId)
        .then(res => {
          this.setData({
            [`favoriteLoading.${productId}`]: false
          });
          
          if (res.success) {
            this.setData({
              [`favoriteStatus.${productId}`]: true
            });
            wx.showToast({
              title: '已加入收藏夹',
              icon: 'success',
              duration: 1500
            });
          } else {
            // 特殊处理：如果商品已在收藏夹中
            if (res.message && res.message.includes('已在收藏夹中')) {
              this.setData({
                [`favoriteStatus.${productId}`]: true
              });
              wx.showToast({
                title: '您已经收藏过该商品',
                icon: 'none',
                duration: 2000
              });
            } else {
              wx.showToast({
                title: res.message || '添加失败',
                icon: 'none'
              });
            }
          }
        })
        .catch(err => {
          console.error('添加到收藏夹失败', err);
          this.setData({
            [`favoriteLoading.${productId}`]: false
          });
          
          // 特殊处理：如果错误信息包含"已在收藏夹中"
          if (err.message && err.message.includes('已在收藏夹中')) {
            this.setData({
              [`favoriteStatus.${productId}`]: true
            });
            wx.showToast({
              title: '您已经收藏过该商品',
              icon: 'none',
              duration: 2000
            });
          } else {
            wx.showToast({
              title: '网络错误，请稍后再试',
              icon: 'none'
            });
          }
        });
    }
  },

  /**
   * 加入购物车
   */
  onAddToCartTap: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('加入购物车:', productId);
    
    // 检查登录状态
    const app = getApp();
    app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.performAddToCart(productId);
      }
    });
  },

  /**
   * 执行添加到购物车操作
   */
  performAddToCart: function(productId) {
    // 显示加载中
    wx.showLoading({
      title: '正在添加到购物车',
      mask: true
    });
    
    // 使用API添加商品到购物车
    cartApi.addToCart(productId, 1)
      .then(res => {
        wx.hideLoading();
        
        if (res.success) {
          // 显示成功提示
          wx.showToast({
            title: '已加入购物车',
            icon: 'success',
            duration: 1500
          });
        } else {
          wx.showToast({
            title: res.message || '添加失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('添加到购物车失败', err);
        wx.hideLoading();
        
        // 显示错误提示
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  },

  /**
   * 分享给朋友
   */
  onShareAppMessage: function() {
    const app = getApp();
    const userId = app.globalData.userInfo ? app.globalData.userInfo._id : '';
    
    // 构建分享路径，包含推荐人ID
    let sharePath = '/pages/home/<USER>';
    if (userId) {
      sharePath += `?referrer=${userId}`;
    }
    
    return {
      title: '陌派 - 购物与创业新范式',
      path: sharePath,
      imageUrl: '/images/share/share.jpg'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline: function() {
    const app = getApp();
    const userId = app.globalData.userInfo ? app.globalData.userInfo._id : '';
    
    // 构建分享路径，包含推荐人ID
    let sharePath = '/pages/home/<USER>';
    if (userId) {
      sharePath += `?referrer=${userId}`;
    }
    
    return {
      title: '陌派 - 购物与创业新范式',
      path: sharePath,
      imageUrl: '/images/share/share.jpg'
    };
  }
});