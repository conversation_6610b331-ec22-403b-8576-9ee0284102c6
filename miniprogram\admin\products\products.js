const { productApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');

Page({
  data: {
    searchValue: '',
    activeTab: 0,
    tabs: [], // 分类列表
    tabIds: [], // 分类ID列表
    products: [],
    selectAll: false,
    page: 1,
    pageSize: 20,
    total: 0,
    selectedCount: 0, // 新增，已选中商品数量
    showEditDrawer: false,
    editProduct: null
  },

  onLoad() {
    // 页面加载时恢复全局登录状态
    loginStateManager.restoreLoginStateToGlobal();
    // 不再require wx-server-sdk，只依赖小程序端wx.cloud
    this.fetchCategories();
  },

  // 获取分类
  fetchCategories() {
    productApi.getCategories().then(res => {
      console.log('分类接口返回：', res);
      if (res.success && Array.isArray(res.data)) {
        const tabs = res.data.map(item => item.name);
        const tabIds = res.data.map(item => item.id);
        this.setData({ tabs, tabIds });
        // 默认加载第一个分类商品
        this.fetchProducts(tabIds[0] || '');
      }
    }).catch(err => {
      console.error('分类接口请求失败', err);
    });
  },

  // 获取商品列表
  fetchProducts(categoryId = '', keyword = '') {
    const params = {
      categoryId,
      keyword,
      page: this.data.page,
      pageSize: this.data.pageSize
    };
    productApi.getProducts(params).then(res => {
      console.log('商品列表接口返回：', res);
      if (res.success && res.data && Array.isArray(res.data.list)) {
        const list = res.data.list;
        // 适配前端展示字段
        let products = list.map(item => {
          // 调试输出图片字段
          console.log('商品图片字段:', item.id, item.image, item.images);
          let mainImage = '';
          if (Array.isArray(item.images) && item.images.length > 0) {
            mainImage = item.images[0];
          } else if (typeof item.images === 'string' && item.images) {
            mainImage = item.images;
          } else if (item.image) {
            mainImage = item.image;
          }
          return {
            id: item.id,
            sku: item.sku,
            name: item.name,
            spec: item.spec || '',
            price: item.price,
            platform_price: item.platform_price,
            store_price: item.store_price,
            retail_price: item.retail_price,
            image: mainImage || '/images/icons2/默认商品.png',
            images: item.images || [],
            status: item.status === 2 ? 'frozen' : (item.status === 1 ? 'on' : 'off'),
            statusText: item.status === 2 ? '冻结' : (item.status === 1 ? '已上架' : '已下架'),
            statusClass: item.status === 2 ? 'frozen' : (item.status === 1 ? 'on' : 'off'),
            checked: false
          };
        });
        // 只在小程序端调用云API
        if (products.length > 0 && typeof wx !== 'undefined' && wx.cloud && wx.cloud.getTempFileURL) {
          const cloudFileIds = products
            .filter(p => p.image && typeof p.image === 'string' && p.image.startsWith('cloud://'))
            .map(p => p.image);
          if (cloudFileIds.length > 0) {
            wx.cloud.getTempFileURL({
              fileList: cloudFileIds
            }).then(res2 => {
              const fileMap = {};
              (res2.fileList || []).forEach(f => {
                if (f.fileID && f.tempFileURL) fileMap[f.fileID] = f.tempFileURL;
              });
              products = products.map(p => {
                if (p.image && fileMap[p.image]) {
                  return { ...p, image: fileMap[p.image] };
                }
                return p;
              });
              this.setData({ products, total: res.data.total, selectedCount: 0, selectAll: false });
            }).catch(err2 => {
              console.error('云文件ID转临时链接失败', err2);
              this.setData({ products, total: res.data.total, selectedCount: 0, selectAll: false });
            });
            return;
          }
        }
        this.setData({ products, total: res.data.total, selectedCount: 0, selectAll: false });
      }
    }).catch(err => {
      console.error('商品列表接口请求失败', err);
    });
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({ searchValue: e.detail.value });
  },
  // 搜索按钮
  onSearch() {
    const { tabIds, activeTab, searchValue } = this.data;
    this.fetchProducts(tabIds[activeTab] || '', searchValue);
  },
  // Tab切换
  onTabChange(e) {
    const idx = e.currentTarget.dataset.index;
    this.setData({ activeTab: idx, searchValue: '' });
    this.fetchProducts(this.data.tabIds[idx] || '', '');
  },
  // 全选/取消
  onSelectAll() {
    const { selectAll, products } = this.data;
    const newSelectAll = !selectAll;
    const newProducts = products.map(item => ({ ...item, checked: newSelectAll }));
    const selectedCount = newSelectAll ? newProducts.length : 0;
    this.setData({
      selectAll: newSelectAll,
      products: newProducts,
      selectedCount
    });
  },
  // 单选
  onCheckProduct(e) {
    const idx = e.currentTarget.dataset.index;
    const products = this.data.products.slice();
    products[idx].checked = !products[idx].checked;
    const selectedCount = products.filter(item => item.checked).length;
    this.setData({
      products,
      selectAll: products.every(item => item.checked),
      selectedCount
    });
  },
  // 批量上架
  async onBatchUp() {
    const ids = this.data.products.filter(item => item.checked).map(item => item.id);
    if (!ids.length) {
      wx.showToast({ title: '请先选择商品', icon: 'none' });
      return;
    }
    wx.showLoading({ title: '批量上架中...' });
    try {
      const res = await productApi.batchUpdateStatus(ids, 1);
      wx.hideLoading();
      if (res.success) {
        wx.showToast({ title: '批量上架成功', icon: 'success' });
        // 刷新商品列表
        this.fetchProducts(this.data.tabIds[this.data.activeTab] || '', this.data.searchValue);
      } else {
        wx.showToast({ title: res.message || '批量上架失败', icon: 'none' });
      }
    } catch (err) {
      wx.hideLoading();
      wx.showToast({ title: '网络异常', icon: 'none' });
    }
  },
  // 批量下架
  async onBatchDown() {
    const ids = this.data.products.filter(item => item.checked).map(item => item.id);
    if (!ids.length) {
      wx.showToast({ title: '请先选择商品', icon: 'none' });
      return;
    }
    wx.showLoading({ title: '批量下架中...' });
    try {
      const res = await productApi.batchUpdateStatus(ids, 0);
      wx.hideLoading();
      if (res.success) {
        wx.showToast({ title: '批量下架成功', icon: 'success' });
        // 刷新商品列表
        this.fetchProducts(this.data.tabIds[this.data.activeTab] || '', this.data.searchValue);
      } else {
        wx.showToast({ title: res.message || '批量下架失败', icon: 'none' });
      }
    } catch (err) {
      wx.hideLoading();
      wx.showToast({ title: '网络异常', icon: 'none' });
    }
  },
  // 批量冻结（如有需求，可扩展）
  onBatchFreeze() {
    let products = this.data.products.map(item => item.checked ? { ...item, status: 'frozen', statusText: '冻结', statusClass: 'frozen' } : item);
    this.setData({ products });
    wx.showToast({ title: '批量冻结成功', icon: 'success' });
  },
  // 编辑商品
  onEditProduct(e) {
    const idx = e.currentTarget.dataset.index;
    const product = this.data.products[idx];
    this.setData({
      showEditDrawer: true,
      editProduct: { ...product }
    });
  },
  onEditDrawerCancel() {
    this.setData({ showEditDrawer: false, editProduct: null });
  },
  async onEditDrawerConfirm(e) {
    const { editProduct } = this.data;
    const updateData = { ...e.detail };
    console.log('商品编辑确认，商品ID:', editProduct.id, '更新数据:', updateData);
    wx.showLoading({ title: '保存中' });
    try {
      const res = await productApi.updateProduct(editProduct.id, updateData);
      console.log('商品更新API响应:', res);
      wx.hideLoading();
      // 增强响应判断逻辑，支持多种成功标识
      const isSuccess = res.success === true || res.success === 'true' || 
                       res.code === 200 || res.statusCode === 200;
      if (isSuccess) {
        wx.showToast({ title: '保存成功', icon: 'success' });
        this.setData({ showEditDrawer: false, editProduct: null });
        // 刷新商品列表
        this.fetchProducts(this.data.tabIds[this.data.activeTab] || '', this.data.searchValue);
      } else {
        console.error('商品更新失败:', res);
        wx.showToast({ title: res.message || '保存失败', icon: 'none' });
      }
    } catch (err) {
      console.error('商品更新异常:', err);
      wx.hideLoading();
      wx.showToast({ title: '网络异常: ' + (err.message || '未知错误'), icon: 'none' });
    }
  }
});