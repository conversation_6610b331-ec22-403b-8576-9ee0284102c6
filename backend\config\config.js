module.exports = {
  port: process.env.PORT || 3001,
  db: {
    // 支持环境变量配置，兼容云托管和本地开发
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || '3306',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'molI2505$',
    database: process.env.DB_NAME || 'morebuy'
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'morebuy-secret-key',
    expiresIn: '24h'
  },
  jwtSecret: process.env.JWT_SECRET || 'morebuy-secret-key',
  apiPrefix: '/api',
  // 微信小程序配置
  wechat: {
    appId: process.env.WX_APPID || process.env.WECHAT_APPID || 'wx1234567890abcdef',
    appSecret: process.env.WX_SECRET || process.env.WECHAT_SECRET || '请替换为正确的AppSecret'
  }
};