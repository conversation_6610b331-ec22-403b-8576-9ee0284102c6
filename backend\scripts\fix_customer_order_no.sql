-- 仅为顾客订单生成订单号的脚本
-- 请在数据库管理工具中执行此脚本

-- 1. 检查当前订单类型分布
SELECT 
    type,
    COUNT(*) as count,
    COUNT(order_no) as has_order_no,
    COUNT(*) - COUNT(order_no) as missing_order_no
FROM orders 
GROUP BY type;

-- 2. 查看现有的顾客订单
SELECT 
    id, 
    type, 
    order_no, 
    status, 
    created_at 
FROM orders 
WHERE (type = 'cart' OR type = 'normal' OR type IS NULL)
ORDER BY created_at DESC 
LIMIT 10;

-- 3. 仅为顾客订单生成订单号
-- 顾客订单编号规则：XS + 年月日时分(12位) + 序号(4位)
UPDATE orders 
SET order_no = CONCAT(
    'XS',
    DATE_FORMAT(FROM_UNIXTIME(created_at/1000), '%Y%m%d%H%i'),
    LPAD(id % 10000, 4, '0')
)
WHERE (type = 'cart' OR type = 'normal' OR type IS NULL) 
    AND (order_no IS NULL OR order_no = '');

-- 4. 验证顾客订单的订单号生成结果
SELECT 
    type,
    COUNT(*) as total_orders,
    COUNT(order_no) as orders_with_no,
    COUNT(*) - COUNT(order_no) as orders_without_no
FROM orders 
WHERE (type = 'cart' OR type = 'normal' OR type IS NULL)
GROUP BY type;

-- 5. 显示生成的顾客订单号示例
SELECT 
    id,
    type,
    order_no,
    status,
    created_at 
FROM orders 
WHERE (type = 'cart' OR type = 'normal' OR type IS NULL)
    AND order_no IS NOT NULL
ORDER BY created_at DESC 
LIMIT 5;

-- 6. 检查是否还有顾客订单缺少订单号
SELECT 
    COUNT(*) as missing_order_no_count
FROM orders 
WHERE (type = 'cart' OR type = 'normal' OR type IS NULL)
    AND (order_no IS NULL OR order_no = '');
