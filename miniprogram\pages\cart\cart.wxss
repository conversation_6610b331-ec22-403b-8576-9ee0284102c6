/* pages/cart/cart.wxss */
.cart-container {
  min-height: 100vh;
  background-color: #F7F7F7;
  padding-bottom: 110px; /* 减小15px，从125px到110px */
  padding-top: 0;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 收藏夹和编辑按钮两端对齐 */
  height: 44px;
  padding: 0 16px;
  background-color: #FFFFFF;
  position: sticky;
  top: 0;
  z-index: 100;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

/* 收藏夹按钮样式 */
.favorites-btn {
  font-size: 14px;
  color: #666666;
  padding: 8px 12px; /* 增加点击区域 */
  border-radius: 4px;
  transition: all 0.2s ease;
}

.favorites-btn:active {
  background-color: #F5F5F5;
  transform: scale(0.95);
}

/* 编辑按钮样式 */
.edit-btn {
  font-size: 14px;
  color: #666666;
  padding: 8px 12px; /* 增加点击区域，与收藏夹按钮保持一致 */
  border-radius: 4px;
  transition: all 0.2s ease;
}

.edit-btn:active {
  background-color: #F5F5F5;
  transform: scale(0.95);
}

/* 登录提示 */
.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.login-icon {
  width: 100px;
  height: 100px;
  margin-bottom: 20px;
  opacity: 0.8;
  filter: grayscale(100%) brightness(0.6); /* 将图标设置为灰色 */
}

.login-text {
  font-size: 18px;
  color: #333333;
  margin-bottom: 8px;
  font-weight: 500;
}

.login-desc {
  font-size: 14px;
  color: #999999;
  margin-bottom: 30px;
  text-align: center;
  line-height: 1.4;
}

.login-btn {
  padding: 6px 20px; /* 调整为与我的页面相同的尺寸 */
  background-color: #FF4D4F;
  color: #FFFFFF;
  border-radius: 18px; /* 调整圆角与我的页面保持一致 */
  font-size: 14px; /* 调整字体大小与我的页面保持一致 */
  margin-bottom: 15px;
  font-weight: bold; /* 调整字体粗细与我的页面保持一致 */
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
  transition: all 0.2s ease;
}

.login-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1px 4px rgba(255, 77, 79, 0.2);
}

.browse-btn {
  padding: 6px 20px; /* 调整为与立即登录按钮相同的尺寸 */
  background-color: transparent;
  color: #666666;
  border: 1px solid #DDDDDD;
  border-radius: 18px; /* 调整圆角与立即登录按钮保持一致 */
  font-size: 14px;
  font-weight: bold; /* 调整字体粗细与立即登录按钮保持一致 */
  transition: all 0.2s ease;
}

.browse-btn:active {
  transform: scale(0.95);
  background-color: #F5F5F5;
}

/* 购物车列表 */
.cart-list {
  padding: 12px 16px;
}

.cart-item {
  display: flex;
  align-items: flex-start; /* 改为顶部对齐，便于右侧控件布局 */
  padding: 12px;
  background-color: #FFFFFF;
  border-radius: 8px;
  margin-bottom: 12px;
  position: relative;
  min-height: 80px; /* 确保最小高度与商品图片一致 */
}

.select-box {
  width: 22px;
  height: 22px;
  margin-right: 10px;
  margin-top: 4px; /* 与商品信息对齐 */
  border-radius: 50%;
  border: 1.5px solid #333333; /* 加粗黑色边框 */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF; /* 确保背景为白色 */
  box-sizing: border-box; /* 确保边框不会增加元素尺寸 */
  position: relative; /* 为选中状态做准备 */
  padding: 10px; /* 增加内边距，扩大点击区域 */
  margin-left: -10px; /* 使用负边距抵消内边距对布局的影响 */
  transition: all 0.2s ease; /* 添加过渡效果 */
  flex-shrink: 0; /* 防止收缩 */
}

.select-box.selected {
  border-color: #07C160; /* 选中时边框变为绿色 */
  background-color: #E6F7EF; /* 选中时背景色变为浅绿色 */
  transform: scale(1.05); /* 选中时稍微放大 */
}

.select-box image {
  width: 16px; /* 进一步缩小图片尺寸，留出更多边框空间 */
  height: 16px;
  opacity: 0.8; /* 未选中状态下稍微透明 */
  position: absolute; /* 确保图片居中 */
  top: 50%; /* 垂直居中 */
  left: 50%; /* 水平居中 */
  transform: translate(-50%, -50%); /* 精确居中 */
}

.select-box.selected image {
  opacity: 1; /* 选中状态下完全不透明 */
  transform: translate(-50%, -50%) scale(1.1); /* 选中状态下图片稍微放大 */
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  margin-right: 10px;
  margin-top: 4px; /* 与选择框对齐 */
  flex-shrink: 0; /* 防止收缩 */
}

.product-info {
  flex: 1;
  overflow: hidden;
  margin-right: 10px;
  margin-top: 4px; /* 与选择框和商品图片顶部对齐 */
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* 让内容分布在顶部和底部 */
  height: 80px; /* 与商品图片高度一致 */
}

.product-name {
  font-size: 14px;
  color: #333333;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-spec {
  font-size: 12px;
  color: #999999;
  margin-bottom: 6px;
}

.product-price {
  font-size: 16px;
  color: #FF4D4F;
  font-weight: bold;
  align-self: flex-start; /* 单价与商品图片底部对齐 */
}

/* 右侧控件区域 */
.right-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between; /* 数量控件在顶部，小计金额在底部 */
  height: 80px; /* 与商品图片高度一致 */
  padding-top: 4px; /* 与商品图片顶部对齐 */
}

/* 缩小的数量控件 */
.quantity-control {
  display: flex;
  align-items: center;
  height: 24px; /* 从28px缩小到24px */
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0; /* 移除下方间距，让数量控件与商品图片顶部对齐 */
}

/* 小计金额样式 */
.subtotal-price {
  font-size: 20px; /* 从18px增加到20px，进一步提升视觉效果 */
  color: #FF4D4F;
  font-weight: bold;
}

.quantity-btn {
  width: 24px; /* 从28px缩小到24px */
  height: 24px; /* 从28px缩小到24px */
  line-height: 24px; /* 从28px缩小到24px */
  text-align: center;
  font-size: 14px; /* 从16px缩小到14px */
  color: #666666;
  background-color: #F5F5F5;
}

.quantity-input {
  width: 32px; /* 从40px缩小到32px */
  height: 24px; /* 从28px缩小到24px */
  line-height: 24px; /* 从28px缩小到24px */
  text-align: center;
  font-size: 12px; /* 从14px缩小到12px */
  color: #333333;
  border-left: 1px solid #EEEEEE;
  border-right: 1px solid #EEEEEE;
}



.global-delete-btn {
  position: fixed; /* 固定定位，相对于屏幕 */
  left: 50%; /* 水平居中 */
  bottom: 110px; /* 减小15px，从125px到110px */
  transform: translateX(-50%); /* 精确水平居中 */
  width: 120px; /* 增加宽度 */
  height: 44px; /* 增加高度 */
  border-radius: 22px; /* 圆角矩形，与高度匹配 */
  text-align: center; /* 文字居中 */
  line-height: 44px; /* 行高与高度一致，确保垂直居中 */
  font-size: 17px; /* 增大字体大小 */
  font-weight: 500; /* 稍微加粗 */
  box-sizing: border-box;
  transition: all 0.2s ease;
  z-index: 100; /* 确保按钮在最上层 */
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25); /* 增强阴影效果 */
}

/* 激活状态 */
.global-delete-btn.active {
  background-color: rgba(255, 77, 79, 0.95); /* 红色背景 */
  color: #FFFFFF; /* 白色文字 */
  cursor: pointer;
}

/* 禁用状态 */
.global-delete-btn.disabled {
  background-color: #CCCCCC; /* 灰色背景 */
  color: #999999; /* 灰色文字 */
  box-shadow: none; /* 移除阴影 */
  cursor: not-allowed;
}

.global-delete-btn.active:active {
  transform: translateX(-50%) scale(0.97); /* 点击时稍微缩小，保持水平居中 */
  background-color: rgba(255, 77, 79, 1); /* 点击时背景色完全不透明 */
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3); /* 点击时阴影减弱 */
  opacity: 0.95; /* 点击时稍微透明 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.loading-icon {
  width: 20px;
  height: 20px;
  border: 2px solid #EEEEEE;
  border-top: 2px solid #FF4D4F;
  border-radius: 50%;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 空购物车 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-icon {
  width: 100px;
  height: 100px;
  margin-bottom: 16px;
  opacity: 0.8;
  filter: grayscale(100%) brightness(0.6); /* 将图标设置为灰色 */
}

.empty-text {
  font-size: 16px;
  color: #999999;
  margin-bottom: 20px;
}

.go-shop-btn {
  padding: 8px 30px;
  background-color: #FF4D4F;
  color: #FFFFFF;
  border-radius: 20px;
  font-size: 14px;
}

/* 底部结算栏 */
.checkout-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 60px; /* 减小15px，从75px到60px */
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  padding: 0 16px 10px 16px; /* 减小底边距10px，从20px到10px */
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.select-all {
  display: flex;
  align-items: center;
}

.select-all .select-box {
  width: 22px;
  height: 22px;
  margin: 0; /* 重置所有边距 */
  margin-right: 0; /* 覆盖上面的样式 */
  padding: 10px; /* 保持扩大的点击区域 */
  margin-left: -10px; /* 左侧使用负边距 */
}

.select-all text {
  font-size: 14px;
  color: #333333;
  margin-left: 6px;
}

.total-info {
  flex: 1;
  margin-left: 20px;
}

.total-price {
  font-size: 14px;
  color: #333333;
}

.price {
  font-size: 18px;
  color: #FF4D4F;
  font-weight: bold;
}

.total-desc {
  font-size: 12px;
  color: #999999;
}

.checkout-btn {
  width: 120px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  background-color: #CCCCCC;
  color: #FFFFFF;
  border-radius: 18px;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.checkout-btn.active {
  background-color: #FF4D4F;
  box-shadow: 0 4px 8px rgba(255, 77, 79, 0.3);
}

.checkout-btn.active:active {
  transform: scale(0.95);
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 34px;
}
