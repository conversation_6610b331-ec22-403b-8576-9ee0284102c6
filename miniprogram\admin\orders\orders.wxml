<view class="orders-container">
  <!-- 一级分类卡片 -->
  <view class="first-level-tabs">
    <block wx:for="{{firstLevelTabs}}" wx:key="key">
      <view class="first-tab {{currentFirstTab === item.key ? 'active' : ''}}" data-key="{{item.key}}" bindtap="onFirstTabChange">
        {{item.label}}
      </view>
    </block>
  </view>

  <!-- 二级分类卡片 -->
  <view class="second-level-tabs">
    <block wx:for="{{secondLevelTabsMap[currentFirstTab]}}" wx:key="key">
      <view class="second-tab {{currentSecondTab === item.key ? 'active' : ''}}" data-key="{{item.key}}" bindtap="onSecondTabChange">
        {{item.label}}
      </view>
    </block>
  </view>

  <!-- 订单列表区域（后续对接接口） -->
  <view class="order-list-placeholder">
    <text>订单列表（{{currentFirstTabLabel}} - {{currentSecondTabLabel}}）</text>
    <view style="color:#bbb;font-size:24rpx;margin-top:16rpx;">（后续可对接后端接口，当前为占位）</view>
  </view>
</view> 