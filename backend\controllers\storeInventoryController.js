/**
 * 门店库存控制器
 */
const db = require('../config/db');

// 获取门店库存商品列表
exports.getInventoryProducts = async (req, res) => {
  try {
    const { storeNo, page = 1, pageSize = 10, keyword, sortBy, minInventory, maxInventory } = req.query;
    
    if (!storeNo) {
      return res.status(400).json({ success: false, message: '缺少门店编号参数' });
    }

    // 构建基础SQL查询
    let sql = `
      SELECT p.*, 
             COALESCE(si.cloud_quantity, 0) as cloudInventory,
             COALESCE(si.offline_quantity, 0) as localInventory
      FROM products p
      LEFT JOIN store_inventory si ON p.id = si.product_id AND si.store_no = ?
      WHERE 1=1
      AND (COALESCE(si.cloud_quantity, 0) > 0 OR COALESCE(si.offline_quantity, 0) > 0)
    `;
    
    const params = [storeNo];

    // 添加筛选条件
    if (keyword) {
      sql += ' AND (p.name LIKE ? OR p.sku LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 库存范围筛选（基于云仓库存和线下库存的总和）
    if (minInventory !== undefined && minInventory !== null) {
      sql += ' AND (COALESCE(si.cloud_quantity, 0) + COALESCE(si.offline_quantity, 0)) >= ?';
      params.push(Number(minInventory));
    }

    if (maxInventory !== undefined && maxInventory !== null) {
      sql += ' AND (COALESCE(si.cloud_quantity, 0) + COALESCE(si.offline_quantity, 0)) <= ?';
      params.push(Number(maxInventory));
    }

    // 排序
    if (sortBy === 'inventory_asc') {
      sql += ' ORDER BY (cloudInventory + localInventory) ASC';
    } else if (sortBy === 'inventory_desc') {
      sql += ' ORDER BY (cloudInventory + localInventory) DESC';
    } else if (sortBy === 'price_asc') {
      sql += ' ORDER BY p.store_price ASC';
    } else if (sortBy === 'price_desc') {
      sql += ' ORDER BY p.store_price DESC';
    } else {
      sql += ' ORDER BY p.id DESC';
    }

    // 分页
    const offset = (Number(page) - 1) * Number(pageSize);
    sql += ' LIMIT ? OFFSET ?';
    params.push(Number(pageSize), offset);

    // 执行查询
    const products = await db.query(sql, params);

    // 获取总数
    let countSql = `
      SELECT COUNT(*) as total
      FROM products p
      LEFT JOIN store_inventory si ON p.id = si.product_id AND si.store_no = ?
      WHERE 1=1
      AND (COALESCE(si.cloud_quantity, 0) > 0 OR COALESCE(si.offline_quantity, 0) > 0)
    `;
    
    const countParams = [storeNo];

    if (keyword) {
      countSql += ' AND (p.name LIKE ? OR p.sku LIKE ?)';
      countParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    if (minInventory !== undefined && minInventory !== null) {
      countSql += ' AND (COALESCE(si.cloud_quantity, 0) + COALESCE(si.offline_quantity, 0)) >= ?';
      countParams.push(Number(minInventory));
    }

    if (maxInventory !== undefined && maxInventory !== null) {
      countSql += ' AND (COALESCE(si.cloud_quantity, 0) + COALESCE(si.offline_quantity, 0)) <= ?';
      countParams.push(Number(maxInventory));
    }

    const countResult = await db.query(countSql, countParams);
    const total = countResult[0].total;

    res.json({
      success: true,
      data: {
        list: products,
        total,
        page: Number(page),
        pageSize: Number(pageSize),
        storeNo
      },
      message: '获取门店库存商品列表成功'
    });

  } catch (error) {
    console.error('获取门店库存商品列表失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '获取门店库存商品列表失败', 
      error: error.message 
    });
  }
};

// 更新门店商品库存
exports.updateInventory = async (req, res) => {
  try {
    const { storeNo, productId, cloudQuantity, offlineQuantity, inventoryType } = req.body;
    
    // 检查参数
    if (!storeNo || !productId) {
      return res.status(400).json({ 
        success: false, 
        message: '缺少必要参数：门店编号和商品ID' 
      });
    }
    
    // 如果指定了库存类型，则必须提供对应的库存数量
    if (inventoryType === 'cloud' && cloudQuantity === undefined) {
      return res.status(400).json({ 
        success: false, 
        message: '更新云仓库存时，必须提供云仓库存数量' 
      });
    }
    
    if (inventoryType === 'offline' && offlineQuantity === undefined) {
      return res.status(400).json({ 
        success: false, 
        message: '更新线下库存时，必须提供线下库存数量' 
      });
    }
    
    // 如果未指定库存类型，则必须至少提供一种库存数量
    if (!inventoryType && cloudQuantity === undefined && offlineQuantity === undefined) {
      return res.status(400).json({ 
        success: false, 
        message: '必须提供云仓库存数量或线下库存数量' 
      });
    }

    // 检查门店是否存在
    const storeCheckSql = 'SELECT id FROM stores WHERE store_no = ?';
    const storeResult = await db.query(storeCheckSql, [storeNo]);
    
    if (storeResult.length === 0) {
      return res.status(404).json({ 
        success: false, 
        message: '门店不存在' 
      });
    }

    // 检查商品是否存在
    const productCheckSql = 'SELECT id FROM products WHERE id = ?';
    const productResult = await db.query(productCheckSql, [productId]);
    
    if (productResult.length === 0) {
      return res.status(404).json({ 
        success: false, 
        message: '商品不存在' 
      });
    }

    // 检查库存记录是否已存在
    const checkSql = 'SELECT id, cloud_quantity, offline_quantity FROM store_inventory WHERE store_no = ? AND product_id = ?';
    const checkResult = await db.query(checkSql, [storeNo, productId]);
    
    const now = new Date().getTime();
    let result;
    let finalCloudQuantity, finalOfflineQuantity;

    if (checkResult.length > 0) {
      // 获取当前库存值
      const currentCloudQuantity = checkResult[0].cloud_quantity || 0;
      const currentOfflineQuantity = checkResult[0].offline_quantity || 0;
      
      // 根据请求类型确定最终库存值
      if (inventoryType === 'cloud') {
        finalCloudQuantity = cloudQuantity;
        finalOfflineQuantity = currentOfflineQuantity;
      } else if (inventoryType === 'offline') {
        finalCloudQuantity = currentCloudQuantity;
        finalOfflineQuantity = offlineQuantity;
      } else {
        // 未指定类型，更新提供的任何值
        finalCloudQuantity = cloudQuantity !== undefined ? cloudQuantity : currentCloudQuantity;
        finalOfflineQuantity = offlineQuantity !== undefined ? offlineQuantity : currentOfflineQuantity;
      }
      
      // 更新现有库存记录
      const updateSql = `
        UPDATE store_inventory 
        SET cloud_quantity = ?, offline_quantity = ?, update_time = ? 
        WHERE store_no = ? AND product_id = ?
      `;
      result = await db.query(updateSql, [finalCloudQuantity, finalOfflineQuantity, now, storeNo, productId]);
    } else {
      // 确定初始库存值
      finalCloudQuantity = cloudQuantity !== undefined ? cloudQuantity : 0;
      finalOfflineQuantity = offlineQuantity !== undefined ? offlineQuantity : 0;
      
      // 创建新的库存记录
      const insertSql = `
        INSERT INTO store_inventory (store_no, product_id, cloud_quantity, offline_quantity, create_time, update_time)
        VALUES (?, ?, ?, ?, ?, ?)
      `;
      result = await db.query(insertSql, [storeNo, productId, finalCloudQuantity, finalOfflineQuantity, now, now]);
    }

    res.json({
      success: true,
      data: {
        storeNo,
        productId,
        cloudQuantity: finalCloudQuantity,
        offlineQuantity: finalOfflineQuantity,
        totalQuantity: finalCloudQuantity + finalOfflineQuantity,
        updateTime: now
      },
      message: '更新门店商品库存成功'
    });

  } catch (error) {
    console.error('更新门店商品库存失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '更新门店商品库存失败', 
      error: error.message 
    });
  }
};

// 批量更新门店商品库存
exports.batchUpdateInventory = async (req, res) => {
  try {
    const { storeNo, items, inventoryType } = req.body;
    
    if (!storeNo || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ 
        success: false, 
        message: '缺少必要参数：门店编号和库存项目数组' 
      });
    }
    
    // 验证库存类型
    if (inventoryType && !['cloud', 'offline', 'both'].includes(inventoryType)) {
      return res.status(400).json({ 
        success: false, 
        message: '库存类型参数无效，必须是 cloud、offline 或 both' 
      });
    }

    // 检查门店是否存在
    const storeCheckSql = 'SELECT id FROM stores WHERE store_no = ?';
    const storeResult = await db.query(storeCheckSql, [storeNo]);
    
    if (storeResult.length === 0) {
      return res.status(404).json({ 
        success: false, 
        message: '门店不存在' 
      });
    }

    // 开始事务
    await db.beginTransaction();

    const now = Date.now();
    const results = [];

    try {
      // 批量处理每个商品的库存更新
      for (const item of items) {
        const { productId, cloudQuantity, offlineQuantity } = item;
        
        if (!productId) {
          throw new Error('商品ID不能为空');
        }
        
        // 根据库存类型检查必要参数
        if (inventoryType === 'cloud' && cloudQuantity === undefined) {
          throw new Error(`商品ID ${productId} 的云仓库存数量不能为空`);
        }
        
        if (inventoryType === 'offline' && offlineQuantity === undefined) {
          throw new Error(`商品ID ${productId} 的线下库存数量不能为空`);
        }
        
        if (inventoryType === 'both' && (cloudQuantity === undefined || offlineQuantity === undefined)) {
          throw new Error(`商品ID ${productId} 的云仓库存和线下库存数量都不能为空`);
        }
        
        if (!inventoryType && cloudQuantity === undefined && offlineQuantity === undefined) {
          throw new Error(`商品ID ${productId} 必须提供云仓库存或线下库存数量`);
        }

        // 检查商品是否存在
        const productCheckSql = 'SELECT id FROM products WHERE id = ?';
        const productResult = await db.query(productCheckSql, [productId]);
        
        if (productResult.length === 0) {
          throw new Error(`商品ID ${productId} 不存在`);
        }

        // 检查库存记录是否已存在
        const checkSql = 'SELECT id, cloud_quantity, offline_quantity FROM store_inventory WHERE store_no = ? AND product_id = ?';
        const checkResult = await db.query(checkSql, [storeNo, productId]);
        
        let result;
        let finalCloudQuantity, finalOfflineQuantity;

        if (checkResult.length > 0) {
          // 获取当前库存值
          const currentCloudQuantity = checkResult[0].cloud_quantity || 0;
          const currentOfflineQuantity = checkResult[0].offline_quantity || 0;
          
          // 根据请求类型确定最终库存值
          if (inventoryType === 'cloud') {
            finalCloudQuantity = cloudQuantity;
            finalOfflineQuantity = currentOfflineQuantity;
          } else if (inventoryType === 'offline') {
            finalCloudQuantity = currentCloudQuantity;
            finalOfflineQuantity = offlineQuantity;
          } else if (inventoryType === 'both') {
            finalCloudQuantity = cloudQuantity;
            finalOfflineQuantity = offlineQuantity;
          } else {
            // 未指定类型，更新提供的任何值
            finalCloudQuantity = cloudQuantity !== undefined ? cloudQuantity : currentCloudQuantity;
            finalOfflineQuantity = offlineQuantity !== undefined ? offlineQuantity : currentOfflineQuantity;
          }
          
          // 更新现有库存记录
          const updateSql = `
            UPDATE store_inventory 
            SET cloud_quantity = ?, offline_quantity = ?, update_time = ? 
            WHERE store_no = ? AND product_id = ?
          `;
          result = await db.query(updateSql, [finalCloudQuantity, finalOfflineQuantity, now, storeNo, productId]);
        } else {
          // 确定初始库存值
          finalCloudQuantity = cloudQuantity !== undefined ? cloudQuantity : 0;
          finalOfflineQuantity = offlineQuantity !== undefined ? offlineQuantity : 0;
          
          // 创建新的库存记录
          const insertSql = `
            INSERT INTO store_inventory (store_no, product_id, cloud_quantity, offline_quantity, create_time, update_time)
            VALUES (?, ?, ?, ?, ?, ?)
          `;
          result = await db.query(insertSql, [storeNo, productId, finalCloudQuantity, finalOfflineQuantity, now, now]);
        }

        results.push({
          productId,
          cloudQuantity: finalCloudQuantity,
          offlineQuantity: finalOfflineQuantity,
          totalQuantity: finalCloudQuantity + finalOfflineQuantity,
          success: true
        });
      }

      // 提交事务
      await db.commit();

      res.json({
        success: true,
        data: {
          storeNo,
          items: results,
          updateTime: now
        },
        message: '批量更新门店商品库存成功'
      });

    } catch (error) {
      // 回滚事务
      await db.rollback();
      throw error;
    }

  } catch (error) {
    console.error('批量更新门店商品库存失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '批量更新门店商品库存失败', 
      error: error.message 
    });
  }
};