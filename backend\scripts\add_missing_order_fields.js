/**
 * 数据库迁移脚本：为orders表添加缺失的字段
 * 
 * 使用方法：
 * node backend/scripts/add_missing_order_fields.js
 */

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'morebuy',
  charset: 'utf8mb4'
};

async function migrateDatabase() {
  let connection;
  
  try {
    console.log('🔄 开始数据库迁移：添加orders表缺失字段...');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 添加product_id字段
    console.log('➕ 添加product_id字段...');
    try {
      await connection.execute(`
        ALTER TABLE orders 
        ADD COLUMN product_id VARCHAR(50) NULL COMMENT '商品ID' AFTER parent_order_id
      `);
      console.log('✅ product_id字段添加成功');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  product_id字段已存在，跳过添加');
      } else {
        throw error;
      }
    }
    
    // 2. 添加quantity字段
    console.log('➕ 添加quantity字段...');
    try {
      await connection.execute(`
        ALTER TABLE orders 
        ADD COLUMN quantity INT DEFAULT 1 COMMENT '商品数量' AFTER product_id
      `);
      console.log('✅ quantity字段添加成功');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  quantity字段已存在，跳过添加');
      } else {
        throw error;
      }
    }
    
    // 3. 为新字段添加索引
    console.log('➕ 添加索引...');
    try {
      await connection.execute(`CREATE INDEX idx_product_id ON orders (product_id)`);
      console.log('✅ 索引 idx_product_id 添加成功');
    } catch (error) {
      if (error.code === 'ER_DUP_KEYNAME') {
        console.log('ℹ️  索引 idx_product_id 已存在，跳过添加');
      } else {
        throw error;
      }
    }
    
    // 4. 显示表结构
    console.log('🔍 当前表结构:');
    const [columns] = await connection.execute('DESCRIBE orders');
    console.table(columns);
    
    console.log('🎉 数据库迁移完成！');
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行迁移
migrateDatabase();
