<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <image src="/images/icons/loading.gif" mode="aspectFit"></image>
      <text>加载中...</text>
    </view>
  </view>

  <!-- 商品详情 -->
  <view class="product-detail" wx:if="{{!loading && product}}">

    <!-- 轮播图 -->
    <swiper class="product-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}" bindchange="onSwiperChange">
      <swiper-item wx:for="{{product.images}}" wx:key="*this" wx:for-index="idx">
        <image src="{{imgErrorMap[idx] ? '/images/mo/mogoods.jpg' : item}}" mode="aspectFill" class="swiper-image" binderror="onImageError" data-index="{{idx}}"></image>
      </swiper-item>
    </swiper>

    <!-- 商品信息 -->
    <view class="product-info">
      <view class="product-price-row">
        <view class="product-price">¥{{product.price}}</view>
        <view class="product-original-price" wx:if="{{product.originalPrice}}">¥{{product.originalPrice}}</view>
      </view>
      <view class="product-name">{{product.name}}</view>
      <view class="product-sales">已售 {{product.salesCount || 0}}</view>
    </view>

    <!-- 商品描述 -->
    <view class="product-description" wx:if="{{product.description}}">
      <view class="section-title">商品描述</view>
      <view class="description-content">{{product.description}}</view>
    </view>

    <!-- 店铺信息 -->
    <view class="shop-info" wx:if="{{product.shopName}}">
      <view class="section-title">店铺信息</view>
      <view class="shop-row">
        <image class="shop-avatar" src="/images/icons2/店铺.png" mode="aspectFill"></image>
        <view class="shop-name">{{product.shopName}}</view>
        <view class="shop-btn">进店</view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="cart-btn" bindtap="goToCart">
        <image src="/images/icons/cart.png" mode="aspectFit"></image>
        <view class="cart-badge" wx:if="{{cartCount > 0}}">{{cartCount}}</view>
      </view>
      <view class="action-btn add-to-cart" bindtap="addToCart">加入购物车</view>
      <view class="action-btn buy-now" bindtap="buyNow">立即购买</view>
    </view>
  </view>

  <!-- 商品不存在 -->
  <view class="not-found" wx:if="{{!loading && !product}}">
    <text>商品不存在或已下架</text>
    <button bindtap="goBack" class="back-button">返回</button>
  </view>
</view>
