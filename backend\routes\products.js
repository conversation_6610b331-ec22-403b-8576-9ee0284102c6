/**
 * 商品路由
 */
const express = require('express');
const { query, param, body } = require('express-validator');
const productController = require('../controllers/productController');
const validate = require('../middleware/validation');

const router = express.Router();

// 获取分类列表 - 必须在 /:id 之前
router.get('/categories', productController.getCategories);

// 获取商城分类列表 - 必须在 /:id 之前
router.get('/shop/categories', productController.getShopCategories);

// 获取商城子分类列表 - 必须在 /:id 之前
router.get('/shop/subcategories', productController.getShopSubCategories);

// 获取轮播图列表 - 必须在 /:id 之前
router.get('/banners', productController.getBanners);

// 获取商品列表
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
  query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须是1-100之间的整数'),
  query('categoryId').optional().isString().withMessage('分类ID必须是字符串'),
  query('subCategoryId').optional().isString().withMessage('子分类ID必须是字符串'),
  query('keyword').optional().isString().withMessage('关键词必须是字符串'),
  query('sortType').optional().isIn(['default', 'price_asc', 'price-asc', 'price_desc', 'price-desc', 'sales']).withMessage('排序类型不正确'),
  query('isNew').optional().isInt({ min: 0, max: 1 }).withMessage('新品标识必须是0或1'),
  validate
], productController.getProducts);

// 获取商品详情 - 必须在具体路径之后
router.get('/:id', [
  param('id').isString().withMessage('商品ID必须是字符串'),
  validate
], productController.getProductById);

// 更新商品信息
router.put('/:id', [
  param('id').isString().withMessage('商品ID必须是字符串'),
  body('sku').optional().isString().withMessage('SKU号必须是字符串'),
  body('name').optional().isString().withMessage('品名必须是字符串'),
  body('image').optional().isString().withMessage('主图必须是字符串'),
  body('spec').optional().isString().withMessage('规格必须是字符串'),
  body('platform_price').optional().isNumeric().withMessage('平台成本价必须为数字'),
  body('store_price').optional().isNumeric().withMessage('门店基准价必须为数字'),
  body('retail_price').optional().isNumeric().withMessage('零售基准价必须为数字'),
  validate
], productController.updateProduct);

// 批量修改商品状态
router.post('/batch-status', [
  body('ids').isArray({ min: 1 }).withMessage('商品ID列表必须是数组'),
  body('status').isInt({ min: 0, max: 2 }).withMessage('状态值必须为0/1/2'),
  validate
], productController.batchUpdateStatus);

module.exports = router;
