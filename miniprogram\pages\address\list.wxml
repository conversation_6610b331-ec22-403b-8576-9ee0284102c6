<view class="container">
  <!-- 地址列表 -->
  <view class="address-list" wx:if="{{addressList.length > 0}}">
    <view class="address-item" wx:for="{{addressList}}" wx:key="id" bindtap="selectAddress" data-address="{{item}}">
      <view class="address-info">
        <view class="name-phone">
          <text class="name">{{item.name}}</text>
          <text class="phone">{{item.phone}}</text>
          <view class="default-tag" wx:if="{{item.isDefault}}">默认</view>
        </view>
        <view class="address">{{item.province}} {{item.city}} {{item.district}} {{item.detail}}</view>
      </view>
      <view class="actions">
        <view class="edit-btn" catchtap="editAddress" data-id="{{item.id}}">编辑</view>
        <view class="delete-btn" catchtap="deleteAddress" data-id="{{item.id}}">删除</view>
      </view>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{addressList.length === 0}}">
    <image class="empty-icon" src="/images/icons/empty.png"></image>
    <view class="empty-text">暂无收货地址</view>
    <view class="empty-desc">添加收货地址，享受便捷购物</view>
  </view>
  
  <!-- 底部按钮 -->
  <view class="bottom-section">
    <view class="add-btn" bindtap="addAddress">+ 新增收货地址</view>
  </view>
</view> 