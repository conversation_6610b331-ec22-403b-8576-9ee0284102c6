-- 回滚脚本：将store_orders表改回orders表
-- 仅在出现问题时使用，执行前请确保已备份数据库

-- 1. 重命名store_orders表为orders
RENAME TABLE store_orders TO orders;

-- 2. 验证表重命名是否成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'orders';

-- 3. 显示表结构
DESCRIBE orders;

-- 4. 检查表中的数据
SELECT COUNT(*) as total_records FROM orders;

-- 5. 显示最近的几条记录
SELECT id, order_no, type, status, created_at 
FROM orders 
ORDER BY created_at DESC 
LIMIT 5;

-- 6. 检查索引是否正常
SHOW INDEX FROM orders;