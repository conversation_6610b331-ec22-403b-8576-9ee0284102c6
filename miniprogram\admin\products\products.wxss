/* 保留主容器和flex布局 */
.product-admin-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100vh;
  background: #fff;
}
.product-mgr-header,
.product-mgr-actions,
.product-mgr-tabs {
  flex-shrink: 0;
}
.product-list {
  flex: 1;
  overflow-y: auto;
}

/* 搜索栏相关 */
.user-mgr-header {
  background: #fff;
  padding: 10rpx 24rpx 0 24rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: center;
}
.user-mgr-search-bar {
  display: flex;
  align-items: center;
  width: 90%;
  max-width: 700rpx;
  min-width: 240rpx;
  padding: 0;
  justify-content: center;
  background: none;
  box-shadow: none;
  margin-bottom: 32rpx;
}
.user-mgr-search-input-container {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  padding: 0 8rpx;
  position: relative;
  flex: 1;
}
.user-mgr-search-input {
  flex: 1;
  height: 100%;
  font-size: 34rpx;
  padding: 0 80rpx 0 32rpx;
  border: none;
  background: transparent;
  outline: none;
}
.user-mgr-search-btn {
  width: 58rpx;
  height: 58rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  box-shadow: none;
  padding: 0;
}
.user-mgr-search-btn image {
  width: 48rpx;
  height: 48rpx;
}

/* 顶部操作按钮栏 */
.user-mgr-actions {
  display: flex;
  gap: 16rpx;
  padding: 0 24rpx;
  background: #fff;
  margin-bottom: 28rpx;
}
.user-mgr-btn {
  flex: 1;
  background: #ff4d4f;
  color: #fff;
  border-radius: 16rpx;
  font-size: 26rpx;
  padding: 16rpx 0;
  border: none;
}
.user-mgr-btn-disabled {
  background: #e0e0e0 !important;
  color: transparent !important;
  pointer-events: none;
  border: none;
}

/* 分类标签栏 */
.user-mgr-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0;
  padding: 0 24rpx;
  background: #fff;
  height: 70rpx;
  margin-bottom: 10rpx;
  margin-top: 6rpx;
  border-top: none;
  border-bottom: none;
  box-shadow: none;
}
.user-mgr-tab {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  color: #888;
  padding-bottom: 8rpx;
  border-bottom: 4rpx solid transparent;
  background: none;
}
.user-mgr-tab.active {
  color: #ff4d4f;
  border-bottom: 4rpx solid #ff4d4f;
  background: none;
}
.user-mgr-table-header {
  display: flex;
  justify-content: space-between;
  padding: 0 40rpx 0 40rpx;
  font-size: 26rpx;
  color: #888;
  background: #fff;
  margin-bottom: 18rpx;
  height: 64rpx;
  align-items: center;
  border-radius: 16rpx;
}
.sortable-header {
  flex: 1;
  text-align: center;
}

/* 商品卡片相关样式 */
.product-mgr-item {
  display: flex;
  align-items: center;
  background: #fff;
  margin: 16rpx 24rpx 0 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx #eee;
  padding: 18rpx 18rpx;
  position: relative;
}
.product-mgr-checkbox {
  margin-right: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  cursor: pointer;
  background-color: transparent;
  transition: all 0.2s ease;
}
.product-mgr-checkbox image {
  width: 48rpx;
  height: 48rpx;
}
.product-mgr-checkbox.selected {
  background-color: transparent;
}
.product-mgr-img {
  width: 86rpx;
  height: 86rpx;
  border-radius: 12rpx;
  margin-right: 18rpx;
  border: 2rpx solid #f0f0f0;
  background: #fafafa;
}
.product-mgr-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}
.product-mgr-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.product-mgr-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.product-mgr-status {
  position: absolute;
  top: 14rpx;
  right: 18rpx;
  font-size: 24rpx;
  font-weight: bold;
  padding: 4rpx 18rpx;
  border-radius: 16rpx;
  min-width: 60rpx;
  text-align: center;
  z-index: 2;
}
.product-mgr-status-on {
  background: #e6f9ed;
  color: #1bc47d;
  border: 1rpx solid #1bc47d;
}
.product-mgr-status-off {
  background: #f5f5f5;
  color: #aaa;
  border: 1rpx solid #ddd;
}
.product-mgr-status-frozen {
  background: #fff1f0;
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}
.product-mgr-spec {
  font-size: 24rpx;
  color: #888;
}
.product-mgr-price {
  font-size: 30rpx;
  color: #e60012;
  font-weight: bold;
  margin-top: 4rpx;
}
.product-mgr-edit-btn {
  position: absolute;
  right: 24rpx;
  top: auto;
  bottom: 18rpx;
  transform: none;
  font-size: 26rpx;
  color: #222;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: #fff;
  padding: 0 18rpx;
  height: 48rpx;
  line-height: 48rpx;
}
.user-mgr-footer {
  text-align: center;
  font-size: 28rpx;
  color: #888;
  background: #fff;
  padding: 24rpx 0 24rpx 0;
  margin-top: 24rpx;
}
.selected-count {
  color: #ff4d4f;
  font-size: 28rpx;
  margin-left: 8rpx;
}
.safe-area {
  height: 120rpx;
  background: transparent;
} 