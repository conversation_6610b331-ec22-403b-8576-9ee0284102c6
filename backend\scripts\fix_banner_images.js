/**
 * 修复banner表中错误的图片路径
 */
const db = require('../config/db');

async function fixBannerImages() {
  try {
    console.log('开始检查banner表中的图片路径...');
    
    // 查询所有banner数据
    const banners = await db.query('SELECT * FROM banners');
    console.log(`找到 ${banners.length} 条banner记录`);
    
    for (const banner of banners) {
      console.log(`Banner ID: ${banner.id}`);
      console.log(`标题: ${banner.title}`);
      console.log(`图片路径: ${banner.image_url}`);
      console.log(`链接: ${banner.link_url}`);
      console.log('---');
      
      // 检查是否有包含错误编码的图片路径
      if (banner.image_url && banner.image_url.includes('XE7XOF')) {
        console.log(`发现错误的图片路径: ${banner.image_url}`);
        
        // 尝试修复路径 - 将错误的路径替换为正确的企业服务图片
        const correctPath = '/images/xinxi/企业服务.jpg';
        
        await db.query(
          'UPDATE banners SET image_url = ? WHERE id = ?',
          [correctPath, banner.id]
        );
        
        console.log(`已修复banner ${banner.id}的图片路径为: ${correctPath}`);
      }
    }
    
    console.log('banner图片路径检查完成');
    
  } catch (error) {
    console.error('修复banner图片路径失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixBannerImages().then(() => {
    console.log('脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { fixBannerImages };