-- 为orders表添加order_no字段
-- 这个字段用于存储订单号，是业务中的重要标识

-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'orders'
    AND COLUMN_NAME = 'order_no'
);

-- 如果字段不存在，则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE orders ADD COLUMN order_no VARCHAR(50) NULL COMMENT "订单号" AFTER id',
    'SELECT "order_no字段已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为order_no字段添加索引（如果不存在）
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'orders'
    AND INDEX_NAME = 'idx_order_no'
);

SET @sql = IF(@index_exists = 0,
    'ALTER TABLE orders ADD INDEX idx_order_no (order_no)',
    'SELECT "order_no索引已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有订单生成订单号（如果order_no为空）
-- 顾客订单编号规则：字母"XS"+年月日时分（12位数）+顺序号（4位数）
UPDATE orders
SET order_no = CONCAT(
    'XS',
    DATE_FORMAT(FROM_UNIXTIME(created_at/1000), '%Y%m%d%H%i'),
    LPAD(id % 10000, 4, '0')
)
WHERE order_no IS NULL OR order_no = '';

-- 验证结果
SELECT 
    COUNT(*) as total_orders,
    COUNT(order_no) as orders_with_no,
    COUNT(*) - COUNT(order_no) as orders_without_no
FROM orders;

-- 显示最近的几个订单号
SELECT id, order_no, status, created_at 
FROM orders 
ORDER BY created_at DESC 
LIMIT 5;
