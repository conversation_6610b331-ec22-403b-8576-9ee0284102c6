/**
 * 用户控制器
 */
const userService = require('../services/userService');

exports.login = async (req, res, next) => {
  try {
    const { username, password } = req.body;
    const result = await userService.login(username, password);

    if (!result.success) {
      return res.status(401).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.loginByPhone = async (req, res, next) => {
  try {
    const { phone, code, referrerId } = req.body;
    const result = await userService.loginByPhone(phone, code, referrerId);

    if (!result.success) {
      return res.status(401).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.loginByWechat = async (req, res, next) => {
  try {
    const { code } = req.body;
    const result = await userService.loginByWechat(code);

    if (!result.success) {
      return res.status(401).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.register = async (req, res, next) => {
  try {
    const result = await userService.register(req.body);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.status(201).json(result);
  } catch (error) {
    next(error);
  }
};

exports.getUserInfo = async (req, res, next) => {
  try {
    // 优先使用url参数id或userId，否则用当前登录用户
    const userId = req.query.id || req.query.userId || (req.userData && req.userData.userId);
    const result = await userService.getUserInfo(userId);

    if (!result.success) {
      return res.status(404).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.updateUserInfo = async (req, res, next) => {
  try {
    console.log('更新用户信息请求:', {
      userData: req.userData,
      body: req.body,
      headers: req.headers
    });

    const userId = req.userData.userId;
    console.log('用户ID:', userId);

    const result = await userService.updateUserInfo(userId, req.body);
    console.log('更新用户信息结果:', result);

    if (!result.success) {
      console.log('更新失败:', result.message);
      return res.status(400).json(result);
    }

    console.log('更新成功');
    res.json(result);
  } catch (error) {
    console.error('更新用户信息异常:', error);
    next(error);
  }
};

// 管理员更新用户信息（包括余额调整）
exports.adminUpdateUser = async (req, res, next) => {
  try {
    console.log('管理员更新用户信息请求:', {
      userData: req.userData,
      body: req.body,
      headers: req.headers
    });

    const { userId } = req.params;
    console.log('目标用户ID:', userId);

    const result = await userService.adminUpdateUser(userId, req.body);
    console.log('管理员更新用户信息结果:', result);

    if (!result.success) {
      console.log('更新失败:', result.message);
      return res.status(400).json(result);
    }

    console.log('更新成功');
    res.json(result);
  } catch (error) {
    console.error('管理员更新用户信息异常:', error);
    next(error);
  }
};

exports.sendVerificationCode = async (req, res, next) => {
  try {
    const { phone } = req.body;
    const result = await userService.sendVerificationCode(phone);

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.updatePassword = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const { oldPassword, newPassword } = req.body;
    const result = await userService.updatePassword(userId, oldPassword, newPassword);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.bindWechat = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const { code, userInfo } = req.body;
    const result = await userService.bindWechat(userId, code, userInfo);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.unbindWechat = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const result = await userService.unbindWechat(userId);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.sendVerifyCode = async (req, res, next) => {
  try {
    const { phone } = req.body;
    console.log('发送验证码请求:', { phone });

    const result = await userService.sendVerifyCode(phone);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('发送验证码异常:', error);
    next(error);
  }
};

exports.resetPasswordByPhone = async (req, res, next) => {
  try {
    const { phone, code, newPassword } = req.body;
    console.log('重置密码请求:', { phone, code: '***', newPassword: '***' });

    const result = await userService.resetPasswordByPhone(phone, code, newPassword);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('重置密码异常:', error);
    next(error);
  }
};

exports.followUser = async (req, res) => {
  try {
    const { followingId } = req.body;
    const followerId = req.userData.userId;
    const result = await userService.followUser(followerId, followingId);
    res.json(result);
  } catch (error) {
    console.error('关注用户失败:', error);
    res.status(500).json({
      success: false,
      message: '关注用户失败'
    });
  }
};

exports.unfollowUser = async (req, res) => {
  try {
    const { followingId } = req.body;
    const followerId = req.userData.userId;
    const result = await userService.unfollowUser(followerId, followingId);
    res.json(result);
  } catch (error) {
    console.error('取消关注失败:', error);
    res.status(500).json({
      success: false,
      message: '取消关注失败'
    });
  }
};

exports.checkFollowStatus = async (req, res) => {
  try {
    // 增强：未登录时直接返回未关注
    if (!req.userData || !req.userData.userId) {
      return res.json({
        success: true,
        data: { isFollowing: false },
        message: '未登录，默认未关注'
      });
    }
    const { followingId } = req.query;
    const followerId = req.userData.userId;

    const result = await userService.checkFollowStatus(followerId, followingId);
    res.json(result);
  } catch (error) {
    console.error('获取关注状态失败:', error);
    res.status(200).json({
      success: true,
      data: { isFollowing: false },
      message: '获取关注状态异常，默认未关注'
    });
  }
};

exports.getMyFollowList = async (req, res) => {
  try {
    const userId = req.userData.userId;
    const result = await userService.getMyFollowList(userId);
    res.json(result);
  } catch (error) {
    console.error('获取我的关注列表失败:', error);
    res.status(500).json({ success: false, message: '获取我的关注列表失败' });
  }
};

exports.getMyFansList = async (req, res) => {
  try {
    const userId = req.userData.userId;
    const result = await userService.getMyFansList(userId);
    res.json(result);
  } catch (error) {
    console.error('获取我的粉丝列表失败:', error);
    res.status(500).json({ success: false, message: '获取我的粉丝列表失败' });
  }
};

exports.getMyPromotionList = async (req, res) => {
  try {
    const userId = req.userData.userId;
    const result = await userService.getMyPromotionList(userId);
    res.json(result);
  } catch (error) {
    console.error('获取我的推广用户列表失败:', error);
    res.status(500).json({ success: false, message: '获取我的推广用户列表失败' });
  }
};







exports.getUserRoles = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    console.log('[控制器调试] getUserRoles 被调用，用户ID:', userId);
    
    if (!userId) {
      console.log('[控制器调试] 用户未登录');
      return res.status(401).json({ success: false, message: '用户未登录' });
    }
    
    console.log('[控制器调试] 调用 userService.getUserRoles');
    const result = await userService.getUserRoles(userId);
    console.log('[控制器调试] userService.getUserRoles 返回结果:', result);
    
    res.json(result);
  } catch (error) {
    console.error('[控制器调试] 获取用户多重身份失败:', error);
    console.error('[控制器调试] 错误堆栈:', error.stack);
    res.status(500).json({ success: false, message: '获取用户多重身份失败' });
  }
};

/**
 * 获取用户钱包信息
 * GET /api/users/wallet
 */
exports.getUserWallet = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const result = await userService.getUserWallet(userId);
    res.json(result);
  } catch (error) {
    console.error('获取用户钱包信息失败:', error);
    res.status(500).json({ success: false, message: '获取用户钱包信息失败' });
  }
};

/**
 * 获取所有用户列表（管理端）
 * GET /api/users/list
 * 支持搜索参数: ?search=关键词
 * 支持标签筛选: ?tab=all|customer|vip|partner
 */
exports.getAllUsers = async (req, res, next) => {
  try {
    const db = require('../config/db');
    const { search, tab } = req.query;
    
    console.log('获取用户列表请求参数:', { search, tab });
    
    // 查询所有用户基本信息
    let sql = `
      SELECT u.id, u.user_id, u.nickname, u.avatar, u.phone, u.createTime, u.status, u.role_type
      FROM users u
    `;
    let whereConditions = [];
    let params = [];
    
    // 添加搜索条件
    if (search && search.trim()) {
      const searchTerm = `%${search.trim()}%`;
      whereConditions.push('(u.nickname LIKE ? OR u.phone LIKE ? OR CAST(u.user_id AS CHAR) LIKE ?)');
      params.push(searchTerm, searchTerm, searchTerm);
    }
    // 添加标签筛选条件
    if (tab && tab !== 'all') {
      switch (tab) {
        case 'customer':
          whereConditions.push('(u.role_type IS NULL OR u.role_type != "partner")');
          break;
        case 'vip':
          console.log('VIP筛选暂时跳过');
          break;
        case 'partner':
          whereConditions.push('u.role_type = "partner"');
          break;
      }
    }
    if (whereConditions.length > 0) {
      sql += ' WHERE ' + whereConditions.join(' AND ');
    }
    sql += ' ORDER BY u.createTime DESC';
    console.log('执行SQL查询:', sql);
    console.log('查询参数:', params);
    // 查询用户
    const users = await db.query(sql, params);
    console.log('查询到用户数量:', users.length);
    // 查询所有用户的多重身份
    const userIds = users.map(u => u.user_id);
    let userRolesMap = {};
    if (userIds.length > 0) {
      const rolesRows = await db.query(
        `SELECT user_id, role_type FROM user_roles WHERE user_id IN (${userIds.map(() => '?').join(',')})`,
        userIds
      );
      // 构建 user_id -> [role_type] 映射
      rolesRows.forEach(row => {
        if (!userRolesMap[row.user_id]) userRolesMap[row.user_id] = [];
        userRolesMap[row.user_id].push(row.role_type);
      });
    }
    // 处理结果，优先级：管理员>合伙人>顾客
    const rolePriority = ['admin', 'partner', 'customer'];
    const roleNameMap = { admin: '管理员', partner: '合伙人', customer: '顾客' };
    const result = users.map(u => {
      let roles = userRolesMap[u.user_id] || [];
      // 兼容users表的role_type
      if (u.role_type && !roles.includes(u.role_type)) roles.push(u.role_type);
      // 统一去重
      roles = Array.from(new Set(roles));
      // 计算最高身份
      let highestRole = '顾客';
      for (const r of rolePriority) {
        if (roles.includes(r)) {
          highestRole = roleNameMap[r];
          break;
        }
      }
      return {
        id: u.user_id, // 返回业务ID
        _id: u.id, // 返回自增ID
        nickname: u.nickname || u.username || '',  // 增加username作为备选，确保昵称不为空
        avatar: u.avatar || '/images/icons2/默认头像.png',
        phone: u.phone || '',
        regDate: u.createTime ? new Date(u.createTime).toISOString().slice(0, 10) : '',
        status: u.status === 1 || u.status === '正常' ? '正常' : '冻结',
        highestRole: highestRole,
        roles: roles.map(r => roleNameMap[r] || r),
        userType: highestRole,
        vipLevel: null
      };
    });
    console.log('返回用户数据，总数:', result.length);
    res.json({ success: true, data: result });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      sqlState: error.sqlState,
      sql: error.sql
    });
    res.status(500).json({ 
      success: false, 
      message: '获取用户列表失败: ' + error.message 
    });
  }
};

/**
 * 批量修改用户状态
 * POST /api/users/batch-status
 * body: { ids: [id1, id2, ...], status: '正常'|'冻结' }
 */
exports.batchUpdateStatus = async (req, res) => {
  try {
    const { ids, status } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ success: false, message: '请选择要操作的用户' });
    }
    
    if (!status || !['正常', '冻结'].includes(status)) {
      return res.status(400).json({ success: false, message: '状态参数无效' });
    }
    
    console.log('批量更新用户状态:', { ids, status });
    
    const db = require('../config/db');
    
    // 构建占位符
    const placeholders = ids.map(() => '?').join(',');
    
    // 执行批量更新
    const sql = `UPDATE users SET status = ? WHERE user_id IN (${placeholders})`;
    const params = [status, ...ids];
    
    console.log('执行SQL:', sql);
    console.log('参数:', params);
    
    const result = await db.query(sql, params);
    
    console.log('批量更新结果:', result);
    
    res.json({
      success: true,
      message: `成功${status === '正常' ? '解冻' : '冻结'} ${result.affectedRows} 个用户`,
      affectedRows: result.affectedRows
    });
    
  } catch (error) {
    console.error('批量更新用户状态失败:', error);
    res.status(500).json({ success: false, message: '批量更新用户状态失败' });
  }
};

/**
 * 管理端：变更用户销售人并同步订阅门店
 * POST /api/users/change-salesman
 * body: { userId, newSalesmanId }
 */
exports.changeSalesmanAndStore = async (req, res) => {
  try {
    const { userId, newSalesmanId } = req.body;
    if (!userId || !newSalesmanId) {
      return res.status(400).json({ success: false, message: '缺少参数' });
    }
    const result = await userService.changeSalesmanAndStore(userId, newSalesmanId);
    res.json(result);
  } catch (error) {
    res.status(500).json({ success: false, message: '变更销售人失败', error: error.message });
  }
};

/**
 * 获取用户资金变动记录
 * GET /api/users/fund-records
 */
exports.getUserFundRecords = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const { limit = 20, offset = 0 } = req.query;
    const records = await userService.getUserFundRecords(userId, parseInt(limit), parseInt(offset));

    res.json({
      success: true,
      data: records
    });
  } catch (error) {
    console.error('获取用户资金记录失败:', error);
    res.status(500).json({ success: false, message: '获取资金记录失败' });
  }
};
