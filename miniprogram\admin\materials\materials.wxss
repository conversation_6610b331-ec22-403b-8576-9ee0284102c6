/* 素材管理页面样式 */
.materials-admin-page {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 顶部标签切换 */
.tabs-container {
  display: flex;
  background: #fff;
  margin: 24rpx;
  border-radius: 12rpx;
  padding: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s;
  font-weight: 500;
}

.tab-item.active {
  color: #ff4d4f;
  font-weight: 600;
  border-bottom: 4rpx solid #ff4d4f;
}

/* 操作按钮区域 */
.section-actions {
  display: flex;
  gap: 24rpx;
  padding: 24rpx;
  padding-bottom: 0;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  background: #fff;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #fff;
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

/* 公司信息预览 */
.company-section {
  margin-top: 24rpx;
}

.company-preview {
  padding: 0 24rpx;
}

.preview-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.card-header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.company-logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
  border: 2rpx solid #f0f0f0;
}

.company-info {
  flex: 1;
}

.company-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.company-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.card-actions {
  display: flex;
  gap: 16rpx;
}

.edit-btn {
  padding: 12rpx 24rpx;
  background: #ff4d4f;
  color: #fff;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
}

/* 公司信息编辑 */
.company-edit-card {
  background: #fff;
  margin: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.card-content {
  padding: 32rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

/* 修改输入框样式，使其为浅灰色背景 */
.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  box-sizing: border-box;
  min-height: 80rpx;
  line-height: 1.5;
}

.form-input:focus {
  border-color: #ff4d4f;
  background: #fff;
}

.form-textarea {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  box-sizing: border-box;
  min-height: 120rpx;
  line-height: 1.5;
  resize: none;
  overflow-y: auto;
}

.form-textarea:focus {
  border-color: #ff4d4f;
  background: #fff;
}

.logo-edit-area {
  display: flex;
  justify-content: center;
}

.logo-upload-area {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #d9d9d9;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.logo-upload-area:hover {
  border-color: #ff4d4f;
  background: #fff5f5;
}

.company-logo-preview {
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  object-fit: cover;
}

.logo-upload-hint {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.save-button-area {
  padding: 24rpx 32rpx;
  background: #fafafa;
  border-top: 1rpx solid #e8e8e8;
}

.save-btn {
  width: 100%;
  padding: 24rpx;
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
}

/* 轮播图管理 */
.banners-section {
  margin-top: 24rpx;
}

.page-type-section {
  padding: 0 24rpx;
  margin-bottom: 24rpx;
}

.page-type-tabs {
  background: #fff;
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.page-type-list {
  display: flex;
  white-space: nowrap;
}

.page-type-tab {
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 8rpx;
  margin-right: 12rpx;
  white-space: nowrap;
  transition: all 0.3s;
}

.page-type-tab.active {
  background: #ff4d4f;
  color: #fff;
}

.actions-row {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24rpx;
}

.add-btn {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
}

.refresh-btn {
  padding: 16rpx 32rpx;
  background: #fff;
  color: #666;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.banners-content {
  padding: 0 24rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 24rpx;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.empty-hint {
  display: block;
  font-size: 26rpx;
  color: #ccc;
}

.banner-edit-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
  overflow: hidden;
  position: relative;
}

.floating-actions {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  gap: 12rpx;
  z-index: 10;
}

.floating-btn {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
  font-weight: 500;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-btn.save {
  background: #52c41a;
  color: #fff;
}

.floating-btn.delete {
  background: #ff4d4f;
  color: #fff;
}

.image-edit-area {
  display: flex;
  justify-content: center;
}

.image-upload-area {
  position: relative;
  width: 300rpx;
  height: 200rpx;
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
}

.image-upload-area:hover {
  border-color: #ff4d4f;
  background: #fff5f5;
}

.banner-preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-upload-hint {
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 22rpx;
  color: #fff;
  background: rgba(0,0,0,0.6);
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.switch-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 消息管理样式 */
.messages-section {
  margin-top: 24rpx;
}

.message-filter-section {
  padding: 0 24rpx;
  margin-bottom: 24rpx;
}

.filter-tabs {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s;
}

.filter-tab.active {
  background: #ff4d4f;
  color: #fff;
}

.messages-content {
  padding: 0 24rpx;
}

.message-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.message-info {
  flex: 1;
}

.sender-info {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.sender-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.sender-details {
  flex: 1;
}

.sender-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.message-time {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.receiver-info {
  display: flex;
  align-items: center;
}

.receiver-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.receiver-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.message-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.block-label {
  font-size: 24rpx;
  color: #666;
}

.message-content {
  padding: 24rpx;
  padding-bottom: 16rpx;
}

.message-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  word-break: break-all;
}

.message-footer {
  padding: 0 24rpx 24rpx;
}

.message-type {
  display: inline-block;
  padding: 4rpx 12rpx;
  background: #f0f0f0;
  color: #666;
  font-size: 22rpx;
  border-radius: 4rpx;
}

/* 常见问题管理样式 */
.faq-section {
  margin-top: 24rpx;
}

.actions-row {
  padding: 0 24rpx 24rpx;
}

.add-btn {
  width: 100%;
  padding: 24rpx;
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
}

.faq-content {
  padding: 0 24rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 24rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.empty-hint {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.faq-edit-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
  overflow: hidden;
  position: relative;
}

.card-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #e8e8e8;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 40rpx;
  height: 40rpx;
  cursor: pointer;
}

.modal-body {
  flex: 1;
  padding: 32rpx;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  border-top: 1rpx solid #e8e8e8;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
}

.modal-btn.cancel {
  background: #f5f5f5;
  color: #666;
}

.modal-btn.confirm {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #fff;
}

/* 页面类型选择器样式 */
.page-type-mode-switch {
  display: flex;
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 4rpx;
  margin-bottom: 16rpx;
}

.mode-tab {
  flex: 1;
  text-align: center;
  padding: 12rpx;
  font-size: 24rpx;
  color: #666;
  border-radius: 6rpx;
  transition: all 0.3s;
}

.mode-tab.active {
  background: #fff;
  color: #ff4d4f;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}

.page-type-select {
  margin-bottom: 16rpx;
}

.form-picker {
  padding: 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
}

.page-type-input {
  margin-bottom: 16rpx;
}

.input-tip {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 图片上传样式 */
.image-upload {
  margin-bottom: 16rpx;
}

.uploaded-image {
  position: relative;
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: rgba(0,0,0,0.7);
  padding: 16rpx;
}

.img-btn {
  flex: 1;
  padding: 8rpx 16rpx;
  background: transparent;
  color: #fff;
  border: 1rpx solid #fff;
  border-radius: 6rpx;
  font-size: 22rpx;
  margin: 0 4rpx;
}

.img-btn.delete {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.upload-placeholder {
  width: 100%;
  height: 300rpx;
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-placeholder:hover {
  border-color: #ff4d4f;
  background: #fff5f5;
}

.upload-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.upload-text {
  font-size: 28rpx;
  color: #999;
}

/* 安全区域 */
.safe-area {
  height: 120rpx;
} 