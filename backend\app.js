require('dotenv').config();
/**
 * 应用入口
 */
const express = require('express');
const cors = require('cors');
const config = require('./config');
const configMiddleware = require('./config/middleware');
const routes = require('./routes');
const errorMiddleware = require('./middleware/error');
const db = require('./config/db');
const path = require('path');
const uploadRoutes = require('./routes/upload');

// 创建Express应用
const app = express();

// 启用 CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// 解析 JSON 请求体
app.use(express.json());



// 静态文件服务
app.use('/uploads', express.static(path.resolve(__dirname, '../uploads'), {
  setHeaders: (res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
    res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
  }
}));
app.use(express.static('public', {
  setHeaders: (res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
  }
}));
// 图片静态文件服务 - 添加URL解码和错误处理
app.use('/images', (req, res, next) => {
  try {
    // 记录原始请求URL
    const originalUrl = req.url;
    console.log('图片请求:', originalUrl);
    
    // 检查是否包含错误的编码字符
    if (originalUrl.includes('XE7XOF') || originalUrl.includes('%A5%E8')) {
      console.error('检测到错误的图片路径编码:', originalUrl);
      // 返回默认图片或404
      return res.status(404).json({ 
        error: '图片路径编码错误', 
        originalPath: originalUrl,
        message: '请检查图片路径是否正确编码'
      });
    }
    
    // 尝试解码URL中的中文字符
    try {
      req.url = decodeURIComponent(req.url);
      console.log('解码后路径:', req.url);
    } catch (decodeErr) {
      console.error('URL解码失败:', originalUrl, decodeErr.message);
      // 如果解码失败，使用原始URL继续
      req.url = originalUrl;
    }
  } catch (err) {
    console.error('图片请求处理错误:', err.message);
  }
  next();
}, express.static(path.resolve(__dirname, '../miniprogram/images'), {
  setHeaders: (res, path) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
    console.log('提供静态文件:', path);
  },
  index: false,
  redirect: false,
  dotfiles: 'allow'
}));

// 图片文件404错误处理
app.use('/images', (req, res) => {
  console.error('图片文件未找到:', req.url);
  res.status(404).json({ 
    error: '图片文件不存在', 
    path: req.url,
    message: '请检查文件路径是否正确',
    suggestion: '可能的原因：1.文件不存在 2.路径编码错误 3.文件名包含特殊字符'
  });
});
app.use('/public/qrcode', require('express').static(__dirname + '/public/qrcode'));

// 配置中间件
configMiddleware(app);

// API路由
app.use(config.apiPrefix, routes);
// 直接挂载/upload，兼容小程序直传
app.use('/upload', uploadRoutes);

// 健康检查路由 - 添加两个路径以确保兼容性
app.get(`${config.apiPrefix}/health`, (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// 微信配置检查端点
app.get('/api/system/config-check', (req, res) => {
  const config = {
    wxAppId: process.env.WX_APPID ? '已配置' : '未配置',
    wxSecret: process.env.WX_SECRET ? '已配置' : '未配置',
    nodeEnv: process.env.NODE_ENV || 'development',
    dbHost: process.env.DB_HOST || 'localhost'
  };
  
  res.json({
    success: true,
    config: config,
    hasValidConfig: !!(process.env.WX_APPID && process.env.WX_SECRET && process.env.WX_SECRET !== '请替换为正确的AppSecret')
  });
});

// 额外的健康检查路由，不依赖于apiPrefix
app.get('/health', async (req, res) => {
  try {
    let dbStatus = 'unknown';
    let dbError = null;

    // 尝试测试数据库连接，但不让数据库错误影响基本健康检查
    try {
      await db.query('SELECT 1');
      dbStatus = 'connected';
    } catch (error) {
      console.warn('Database health check failed:', error.message);
      dbStatus = 'disconnected';
      dbError = error.message;
    }

    // 即使数据库连接失败，应用仍然可以响应健康检查
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      checks: {
        database: dbStatus,
        ...(dbError && { databaseError: dbError })
      }
    });
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(503).json({
      status: 'error',
      message: '服务异常',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 首页路由
app.get('/', (req, res) => {
  res.json({
    message: '陌派小程序API服务',
    version: '1.0.0',
    status: 'running'
  });
});

// 错误处理中间件
app.use(errorMiddleware);

module.exports = app;
