-- =====================================================
-- users表结构重构SQL脚本 - 远程数据库版本
-- 将现有id字段重命名为user_id，并添加新的自增id字段
-- =====================================================

-- 第一步：备份当前users表（强烈建议）
-- CREATE TABLE users_backup AS SELECT * FROM users;

-- 第二步：将现有id字段重命名为user_id（保持原有数据不变）
ALTER TABLE users CHANGE COLUMN id user_id VARCHAR(50) NOT NULL;

-- 第三步：删除主键约束
ALTER TABLE users DROP PRIMARY KEY;

-- 第四步：添加新的自增id字段作为主键
ALTER TABLE users ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST;

-- 第五步：为user_id字段添加唯一索引
ALTER TABLE users ADD UNIQUE INDEX idx_user_id (user_id);

-- =====================================================
-- 验证迁移结果（可选执行）
-- =====================================================

-- 检查表结构
-- DESCRIBE users;

-- 检查索引
-- SHOW INDEX FROM users;

-- 验证数据完整性
-- SELECT COUNT(*) as total_users FROM users;
-- SELECT COUNT(*) as users_with_user_id FROM users WHERE user_id IS NOT NULL;
-- SELECT COUNT(*) as users_with_id FROM users WHERE id IS NOT NULL;

-- 检查user_id唯一性
-- SELECT user_id, COUNT(*) as count 
-- FROM users 
-- GROUP BY user_id 
-- HAVING count > 1;

-- 查看示例数据
-- SELECT id, user_id, nickname, phone FROM users LIMIT 10;

-- =====================================================
-- 注意事项：
-- 1. 执行前请务必备份数据库
-- 2. 建议在测试环境先执行验证
-- 3. 执行过程中可能需要较长时间，请耐心等待
-- 4. 如果表中有大量数据，建议在低峰期执行
-- 5. 确保有足够的磁盘空间
-- ===================================================== 