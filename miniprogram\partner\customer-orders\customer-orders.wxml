<!-- 合伙人端顾客订单页面 -->
<view class="customer-orders-page">
  <!-- 头部导航 -->
  <!-- <view class="header">
    <view class="header-left" bindtap="goBack">
      <image class="back-icon" src="/images/icons2/返回.png" mode="aspectFit"></image>
    </view>
    <view class="header-right">
      <image class="more-icon" src="/images/icons2/更多.png" mode="aspectFit"></image>
      <image class="target-icon" src="/images/icons2/目标.png" mode="aspectFit"></image>
    </view>
  </view> -->

  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-container">
      <image class="search-icon" src="/images/icons2/搜索.png" mode="aspectFit"></image>
      <input 
        class="search-input" 
        placeholder="搜索订单" 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
    </view>
    <view class="search-btn" bindtap="onSearch">搜索</view>
  </view>

  <!-- 筛选器 -->
  <view class="filter-container">
    <!-- 门店选择 -->
    <picker 
      class="filter-picker" 
      mode="selector" 
      range="{{storeList}}" 
      range-key="label"
      value="{{selectedStore}}"
      bindchange="onStoreChange"
    >
      <view class="picker-content">
        <text>{{selectedStore === 'all' ? '所有门店' : selectedStoreLabel}}</text>
        <image class="picker-arrow" src="/images/icons2/下拉箭头.png" mode="aspectFit"></image>
      </view>
    </picker>

    <!-- 配送方式选择 -->
    <picker 
      class="filter-picker" 
      mode="selector" 
      range="{{['所有订单', '快递订单', '自提订单']}}" 
      value="{{deliveryMethodIndex}}"
      bindchange="onDeliveryMethodChange"
    >
      <view class="picker-content">
        <text>{{deliveryMethodLabel}}</text>
        <image class="picker-arrow" src="/images/icons2/下拉箭头.png" mode="aspectFit"></image>
      </view>
    </picker>

    <!-- 日期范围选择 -->
    <picker 
      class="filter-picker" 
      mode="selector" 
      range="{{['所有日期', '今天', '昨天', '本周', '本月']}}" 
      value="{{dateRangeIndex}}"
      bindchange="onDateRangeChange"
    >
      <view class="picker-content">
        <text>{{dateRangeLabel}}</text>
        <image class="calendar-icon" src="/images/icons2/日历.png" mode="aspectFit"></image>
      </view>
    </picker>
  </view>

  <!-- 状态标签栏 -->
  <view class="status-tabs">
    <scroll-view scroll-x class="tabs-scroll">
      <view class="tabs-container">
        <block wx:for="{{statusTabs}}" wx:key="key">
          <view 
            class="tab-item {{currentStatus === item.key ? 'active' : ''}}" 
            bindtap="switchStatusTab" 
            data-status="{{item.key}}"
          >
            <text>{{item.name}}</text>
            <view class="tab-line" wx:if="{{currentStatus === item.key}}"></view>
          </view>
        </block>
      </view>
    </scroll-view>
  </view>

  <!-- 订单列表 -->
  <view class="order-list-container">
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{loading && orderList.length === 0}}">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{!loading && isEmpty}}">
      <image class="empty-icon" src="/images/icons2/空订单.png" mode="aspectFit"></image>
      <view class="empty-text">暂无订单</view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list" wx:else>
      <block wx:for="{{orderList}}" wx:key="id">
        <view class="order-item {{item.isSalesmanOrder ? 'my-sales-order' : ''}}" bindtap="viewOrderDetail" data-id="{{item.id}}">
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="store-info">
              <text class="store-name">{{item.store_name}}</text>
              <text class="order-no">订单号{{item.order_no}}</text>
            </view>
            <view class="order-status">{{item.statusText}}</view>
          </view>

          <!-- 订单日期和销售人 -->
          <view class="order-info-row">
            <view class="order-date">{{item.created_at}}</view>
            <view class="salesman-info {{item.isSalesmanOrder ? 'my-sales' : ''}}">
              <text>销售人: {{item.salesman_name}}</text>
              <text wx:if="{{item.isSalesmanOrder}}" class="my-sales-tag">我的销售</text>
            </view>
          </view>

          <!-- 订单状态和配送方式 -->
          <view class="order-status-row">
            <view class="delivery-tag {{item.delivery_method === 'express' ? 'express' : 'pickup'}}">
              {{item.deliveryText}}
            </view>
            <text class="customer-name">顾客: {{item.customer_name}}</text>
          </view>

          <!-- 子订单信息 -->
          <view class="sub-orders-container" wx:if="{{item.sub_orders && item.sub_orders.length > 0}}">
            <view class="sub-orders-title">拆分订单:</view>
            <view class="sub-orders-list">
              <block wx:for="{{item.sub_orders}}" wx:for-item="subOrder" wx:key="id">
                <view class="sub-order-item">
                  <text class="sub-order-store">{{subOrder.store_name || '未知门店'}}</text>
                  <text class="sub-order-no">{{subOrder.sub_order_no}}</text>
                  <text class="sub-order-status">{{subOrder.status}}</text>
                </view>
              </block>
            </view>
          </view>

          <!-- 商品列表 -->
          <view class="product-list">
            <block wx:for="{{item.items}}" wx:for-item="product" wx:key="id">
              <view class="product-item">
                <image class="product-image" src="{{product.product_image || '/images/icons2/默认商品.png'}}" mode="aspectFit"></image>
                <view class="product-info">
                  <view class="product-name">{{product.product_name}}</view>
                  <view class="product-specs" wx:if="{{product.specs}}">{{product.specs}}</view>
                  <view class="product-price-qty">
                    <text class="product-price">¥{{product.price}}</text>
                    <text class="product-qty">x{{product.quantity}}</text>
                  </view>
                  <view class="product-total">共计{{product.total_quantity}}件</view>
                </view>
              </view>
            </block>
          </view>

          <!-- 订单金额 -->
          <view class="order-total">
            <view class="total-row">
              <text class="total-label">总价</text>
              <text class="total-amount">¥{{item.total_amount}}</text>
            </view>
            <view class="discount-row" wx:if="{{item.discount_amount > 0}}">
              <text class="discount-label">红包优惠</text>
              <text class="discount-amount">¥{{item.discount_amount}}</text>
            </view>
            <view class="actual-row">
              <text class="actual-label">实付</text>
              <text class="actual-amount">¥{{item.actual_amount}}</text>
            </view>
          </view>
        </view>
      </block>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{loading && orderList.length > 0}}">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && orderList.length > 0}}">
      <text>没有更多订单了</text>
    </view>
  </view>
</view> 