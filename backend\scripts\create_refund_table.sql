-- 创建退款表
CREATE TABLE IF NOT EXISTS `order_refunds` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `order_id` VARCHAR(50) NOT NULL COMMENT '订单ID',
  `user_id` INT NOT NULL COMMENT '用户ID',
  `reason` VARCHAR(100) NOT NULL COMMENT '退款原因',
  `description` TEXT COMMENT '详细描述',
  `images` TEXT COMMENT '图片JSON数组',
  `status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '退款状态：PENDING(处理中)、REJECTED(已拒绝)、REFUNDED(已退款)',
  `admin_remark` TEXT COMMENT '管理员备注',
  `created_at` DATETIME NOT NULL COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_order_id` (`order_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单退款表';