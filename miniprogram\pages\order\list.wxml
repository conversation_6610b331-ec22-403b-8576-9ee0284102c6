<view class="container">
  <!-- 顶部标签栏 -->
  <scroll-view scroll-x class="tabs-container">
    <view class="tabs">
      <block wx:for="{{tabs}}" wx:key="key">
        <view class="tab-item {{currentTab === item.key ? 'active' : ''}}" 
              bindtap="switchTab" 
              data-tab="{{item.key}}">
          <text>{{item.name}}</text>
          <view class="tab-line" wx:if="{{currentTab === item.key}}"></view>
        </view>
      </block>
    </view>
  </scroll-view>

  <!-- 订单列表 -->
  <view class="order-list" wx:if="{{!loading && orderList.length > 0}}">
    <block wx:for="{{orderList}}" wx:key="id">
      <view class="order-item" bindtap="viewOrderDetail" data-id="{{item.id}}">
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="order-no">订单号: {{item.order_no}}</view>
          <view class="order-status">{{item.status_text}}</view>
        </view>
        
        <!-- 订单商品列表 -->
        <view class="order-products">
          <block wx:for="{{item.items}}" wx:for-item="product" wx:key="id">
            <view class="product-item">
              <image class="product-image" src="{{product.image || '/images/icons2/默认商品.png'}}"></image>
              <view class="product-info">
                <view class="product-name">{{product.name}}</view>
                <view class="product-specs" wx:if="{{product.specs}}">{{product.specs}}</view>
                <view class="product-price-qty">
                  <text class="product-price">¥{{product.price}}</text>
                  <text class="product-qty">x{{product.quantity}}</text>
                </view>
              </view>
            </view>
          </block>
        </view>
        
        <!-- 订单金额 -->
        <view class="order-total">
          <text>共{{item.total_quantity}}件商品</text>
          <text>合计: ¥{{item.total_amount}}</text>
        </view>
        
        <!-- 订单操作按钮 -->
        <view class="order-actions">
          <!-- 待付款状态 -->
          <block wx:if="{{item.status === 'pending_payment'}}">
            <view class="action-btn cancel" catchtap="cancelOrder" data-id="{{item.id}}">取消订单</view>
            <view class="action-btn primary" catchtap="goToPay" data-id="{{item.id}}">去支付</view>
          </block>

          <!-- 待发货状态 -->
          <block wx:elif="{{item.status === 'pending_shipment'}}">
            <view class="action-btn" catchtap="viewOrderDetail" data-id="{{item.id}}">查看详情</view>
            <view class="action-btn" catchtap="applyRefund" data-id="{{item.id}}">申请退款</view>
          </block>

          <!-- 待收货状态 -->
          <block wx:elif="{{item.status === 'shipped'}}">
            <view class="action-btn" catchtap="viewOrderDetail" data-id="{{item.id}}">查看详情</view>
            <view class="action-btn" catchtap="viewLogistics" data-id="{{item.id}}">查看物流</view>
            <view class="action-btn primary" catchtap="confirmReceipt" data-id="{{item.id}}">确认收货</view>
          </block>

          <!-- 已完成状态 -->
          <block wx:elif="{{item.status === 'completed'}}">
            <view class="action-btn" catchtap="viewOrderDetail" data-id="{{item.id}}">查看详情</view>
            <view class="action-btn" catchtap="viewLogistics" data-id="{{item.id}}">查看物流</view>
            <view wx:if="{{!item.is_rated}}" class="action-btn primary" catchtap="goToRate" data-id="{{item.id}}">去评价</view>
          </block>

          <!-- 已取消状态 -->
          <block wx:elif="{{item.status === 'cancelled'}}">
            <view class="action-btn" catchtap="viewOrderDetail" data-id="{{item.id}}">查看详情</view>
            <view class="action-btn" catchtap="deleteOrder" data-id="{{item.id}}">删除订单</view>
          </block>

          <!-- 退款/售后状态 -->
          <block wx:elif="{{item.status === 'refund'}}">
            <view class="action-btn" catchtap="viewOrderDetail" data-id="{{item.id}}">查看详情</view>
            <view class="action-btn" catchtap="viewRefundDetail" data-id="{{item.id}}">查看进度</view>
          </block>

          <!-- 默认状态（兜底） -->
          <block wx:else>
            <view class="action-btn" catchtap="viewOrderDetail" data-id="{{item.id}}">查看详情</view>
          </block>
        </view>
      </view>
    </block>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && isEmpty}}">
    <image class="empty-icon" src="/images/icons/empty-order.svg"></image>
    <text class="empty-text">暂无相关订单</text>
  </view>
  
  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{!loading && !isEmpty && !hasMore}}">
    <text>没有更多订单了</text>
  </view>
</view>