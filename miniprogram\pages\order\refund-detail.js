// pages/order/refund-detail.js
const { orderApi } = require('../../utils/api');

Page({
  data: {
    orderId: null,
    refundDetail: null,
    loading: true,
    statusMap: {
      '处理中': '退款申请处理中',
      '已拒绝': '退款申请被拒绝',
      '已退款': '退款已完成'
    }
  },

  onLoad: function(options) {
    console.log('退款详情页面加载，参数:', options);
    if (options && options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadRefundDetail(options.id);
    } else {
      wx.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载退款详情
  loadRefundDetail: function(orderId) {
    this.setData({ loading: true });
    
    orderApi.getRefundDetail(orderId).then(res => {
      if (res.success && res.data) {
        // 处理退款详情数据
        const refundDetail = res.data;
        
        // 处理图片数据，确保是数组格式
        if (typeof refundDetail.images === 'string') {
          try {
            refundDetail.images = JSON.parse(refundDetail.images);
          } catch (e) {
            refundDetail.images = [];
          }
        }
        
        this.setData({
          refundDetail,
          loading: false
        });
      } else {
        this.setData({ loading: false });
        wx.showToast({
          title: res.message || '获取退款详情失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取退款详情失败:', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '获取退款详情失败',
        icon: 'none'
      });
    });
  },

  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.refundDetail.images;
    
    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  // 返回订单详情页
  goToOrderDetail: function() {
    wx.navigateBack();
  },

  // 刷新退款详情
  refreshRefundDetail: function() {
    this.loadRefundDetail(this.data.orderId);
  }
});