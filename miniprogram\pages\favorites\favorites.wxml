<view class="container">
  <!-- 顶部操作按钮 -->
  <view class="top-actions" wx:if="{{!loading && favorites.length > 0}}">
    <view class="action-btn select-all-btn" bindtap="toggleSelectAll">
      <text>全选/取消</text>
    </view>
    <view class="action-btn remove-btn {{selectedCount > 0 ? 'active' : 'disabled'}}" bindtap="removeSelected">
      <text>移出收藏</text>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs" wx:if="{{!loading && favorites.length > 0}}">
    <scroll-view class="tabs-scroll" scroll-x="true" enhanced="true" show-scrollbar="false">
      <view class="tab-item {{currentCategory === 'all' ? 'active' : ''}}" 
            bindtap="switchCategory" 
            data-category="all">
        全部
      </view>
      <view class="tab-item {{currentCategory === item.id ? 'active' : ''}}" 
            wx:for="{{categories}}" 
            wx:key="id"
            bindtap="switchCategory" 
            data-category="{{item.id}}">
        {{item.name}}
      </view>
    </scroll-view>
  </view>

  <view class="content">
    <view class="loading" wx:if="{{loading}}">加载中...</view>
    <view class="empty" wx:elif="{{!loading && favorites.length === 0}}">
      <text>还没有收藏任何商品</text>
      <button bindtap="goToShop">去逛逛</button>
    </view>
    <view class="list" wx:else>
      <view class="item" wx:for="{{filteredFavorites}}" wx:key="id">
        <!-- 复选框 -->
        <view class="select-box {{item.selected ? 'selected' : ''}}" bindtap="toggleSelectItem" data-index="{{index}}">
          <image src="{{item.selected ? '/images/icons2/勾选.png' : '/images/icons/checkbox.png'}}"></image>
        </view>

        <!-- 商品图片 -->
        <image class="image" src="{{item.image}}" mode="aspectFill"></image>
        
        <!-- 商品信息 -->
        <view class="info">
          <text class="name">{{item.name}}</text>
          <text class="price">¥{{item.price}}</text>
        </view>

        <!-- 加入购物车按钮 -->
        <view class="add-to-cart-btn" bindtap="addToCart" data-product="{{item}}">
          <image src="/images/icons2/添加购物车.png" class="plus-icon"></image>
        </view>
      </view>
    </view>
  </view>
</view> 