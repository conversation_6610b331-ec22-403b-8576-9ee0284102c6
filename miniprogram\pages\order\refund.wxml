<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>

  <block wx:if="{{!loading && orderDetail}}">
    <!-- 订单信息 -->
    <view class="order-section">
      <view class="order-title">订单信息</view>
      <view class="order-info">
        <view class="order-no">订单号：{{orderDetail.order_no}}</view>
        <view class="order-amount">订单金额：<text class="amount">¥{{orderDetail.total_amount}}</text></view>
      </view>
    </view>

    <!-- 退款表单 -->
    <view class="refund-form">
      <!-- 退款类型 -->
      <view class="form-item">
        <view class="form-label">退款类型</view>
        <view class="refund-type-options">
          <view class="type-option {{refundType === 'full' ? 'selected' : ''}}" 
                bindtap="selectRefundType" 
                data-type="full">
            <view class="radio-circle {{refundType === 'full' ? 'checked' : ''}}"></view>
            <text>全额退款</text>
          </view>
          <view class="type-option {{refundType === 'partial' ? 'selected' : ''}}" 
                bindtap="selectRefundType" 
                data-type="partial">
            <view class="radio-circle {{refundType === 'partial' ? 'checked' : ''}}"></view>
            <text>部分退款</text>
          </view>
        </view>
      </view>

      <!-- 退款金额 -->
      <view class="form-item" wx:if="{{refundType === 'partial'}}">
        <view class="form-label">退款金额</view>
        <view class="form-input-container">
          <text class="input-prefix">¥</text>
          <input class="form-input" 
                 type="digit" 
                 value="{{refundAmount}}" 
                 bindinput="inputRefundAmount" 
                 placeholder="请输入退款金额" />
        </view>
        <view class="form-tip">最多可退 ¥{{orderDetail.total_amount}}</view>
      </view>

      <!-- 退款原因 -->
      <view class="form-item">
        <view class="form-label">退款原因</view>
        <view class="reason-options">
          <block wx:for="{{refundReasons}}" wx:key="*this">
            <view class="reason-option {{selectedReason === item ? 'selected' : ''}}" 
                  bindtap="selectReason" 
                  data-reason="{{item}}">
              <view class="radio-circle {{selectedReason === item ? 'checked' : ''}}"></view>
              <text>{{item}}</text>
            </view>
          </block>
        </view>
      </view>

      <!-- 自定义原因 -->
      <view class="form-item" wx:if="{{selectedReason === '其他'}}">
        <view class="form-label">详细原因</view>
        <textarea class="form-textarea" 
                  value="{{customReason}}" 
                  bindinput="inputCustomReason" 
                  placeholder="请输入详细退款原因" 
                  maxlength="200" />
        <view class="textarea-counter">{{customReason.length}}/200</view>
      </view>

      <!-- 上传凭证 -->
      <view class="form-item">
        <view class="form-label">上传凭证</view>
        <view class="upload-container">
          <view class="image-list">
            <block wx:for="{{images}}" wx:key="*this">
              <view class="image-item">
                <image class="uploaded-image" src="{{item}}" bindtap="previewImage" data-index="{{index}}"></image>
                <view class="delete-btn" catchtap="deleteImage" data-index="{{index}}">×</view>
              </view>
            </block>
            <view class="upload-btn" bindtap="chooseImage" wx:if="{{images.length < maxImageCount}}">
              <view class="upload-icon">+</view>
              <text>上传图片</text>
            </view>
          </view>
          <view class="upload-tip">最多上传{{maxImageCount}}张图片</view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-btn" bindtap="submitRefund">提交申请</view>
  </block>

  <!-- 订单不存在 -->
  <view class="empty-container" wx:if="{{!loading && !orderDetail}}">
    <image class="empty-icon" src="/images/icons/empty-order.svg"></image>
    <text class="empty-text">订单不存在或已被删除</text>
  </view>
</view>