-- ========================================
-- 合伙人端快捷菜单表扩展脚本
-- 执行前请备份数据库！
-- ========================================

-- 第一步：检查当前表结构
DESCRIBE quick_menus;

-- 第二步：检查是否已经存在target_platform字段
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'quick_menus' 
AND COLUMN_NAME = 'target_platform';

-- ========================================
-- 第三步：扩展表结构（仅在字段不存在时执行）
-- ========================================

-- 添加平台标识字段
ALTER TABLE `quick_menus` 
ADD COLUMN IF NOT EXISTS `target_platform` ENUM('customer', 'partner', 'admin', 'all') NOT NULL DEFAULT 'customer' COMMENT '目标平台：customer=顾客端，partner=合伙人端，admin=管理端，all=全平台' AFTER `is_active`;

-- 添加角色权限字段
ALTER TABLE `quick_menus` 
ADD COLUMN IF NOT EXISTS `required_roles` JSON NULL COMMENT '所需角色权限，JSON数组格式' AFTER `target_platform`;

-- 添加时间戳字段
ALTER TABLE `quick_menus` 
ADD COLUMN IF NOT EXISTS `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `required_roles`;

ALTER TABLE `quick_menus` 
ADD COLUMN IF NOT EXISTS `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `created_at`;

-- ========================================
-- 第四步：添加索引优化查询性能
-- ========================================

-- 添加平台索引
ALTER TABLE `quick_menus` 
ADD INDEX IF NOT EXISTS `idx_target_platform` (`target_platform`);

-- 添加组合索引（状态+平台）
ALTER TABLE `quick_menus` 
ADD INDEX IF NOT EXISTS `idx_is_active_platform` (`is_active`, `target_platform`);

-- ========================================
-- 第五步：插入合伙人端默认菜单数据
-- ========================================

-- 插入合伙人端快捷菜单（避免重复插入）
INSERT IGNORE INTO `quick_menus` (`name`, `icon`, `link_type`, `link_url`, `sort_order`, `is_active`, `target_platform`, `required_roles`) VALUES
('门店合伙人', '/images/icons2/门店合伙人.png', 'function', 'viewStorePartners', 1, 1, 'partner', '["partner"]'),
('门店库存', '/images/icons2/门店库存.png', 'function', 'viewStoreInventory', 2, 1, 'partner', '["partner"]'),
('在线客服', '/images/icons2/在线客服.png', 'function', 'contactService', 3, 1, 'partner', '["partner"]'),
('分享门店', '/images/icons2/分享门店.png', 'function', 'shareStore', 4, 1, 'partner', '["partner"]');

-- ========================================
-- 第六步：验证扩展结果
-- ========================================

-- 查看更新后的表结构
DESCRIBE quick_menus;

-- 查看插入的合伙人端菜单
SELECT * FROM quick_menus WHERE target_platform = 'partner';

-- 查看所有菜单按平台分类
SELECT target_platform, COUNT(*) as menu_count 
FROM quick_menus 
WHERE is_active = 1 
GROUP BY target_platform;

-- ========================================
-- 回滚脚本（如果需要）
-- ========================================

/*
-- 如果需要回滚，可以执行以下命令：

-- 删除合伙人端菜单
DELETE FROM quick_menus WHERE target_platform = 'partner';

-- 删除添加的字段
ALTER TABLE quick_menus DROP COLUMN IF EXISTS updated_at;
ALTER TABLE quick_menus DROP COLUMN IF EXISTS created_at;
ALTER TABLE quick_menus DROP COLUMN IF EXISTS required_roles;
ALTER TABLE quick_menus DROP COLUMN IF EXISTS target_platform;

-- 删除添加的索引
ALTER TABLE quick_menus DROP INDEX IF EXISTS idx_is_active_platform;
ALTER TABLE quick_menus DROP INDEX IF EXISTS idx_target_platform;
*/ 