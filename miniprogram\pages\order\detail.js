// pages/order/detail.js
const { orderApi } = require('../../utils/api');

Page({
  data: {
    orderId: null,
    orderDetail: null,
    loading: true,
    statusSteps: [],
    statusMap: {
      'pending_payment': { text: '待付款', step: 0 },
      'pending_shipment': { text: '待发货', step: 1 },
      'shipped': { text: '待收货', step: 2 },
      'completed': { text: '已完成', step: 3 },
      'cancelled': { text: '已取消', step: -1 },
      'refund': { text: '退款/售后', step: -1 }
    }
  },

  onLoad: function(options) {
    console.log('订单详情页面加载，参数:', options);

    // 检查登录状态和token
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    console.log('当前登录状态 - token存在:', !!token);
    console.log('当前登录状态 - userInfo存在:', !!userInfo);
    if (token) {
      console.log('token前10位:', token.substring(0, 10));
    }

    if (options && options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail(options.id);
    } else {
      wx.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载订单详情
  loadOrderDetail: function(orderId) {
    this.setData({ loading: true });

    console.log('开始加载订单详情, orderId:', orderId);

    orderApi.getOrderById(orderId).then(res => {
      console.log('订单详情API响应:', res);
      console.log('响应类型:', typeof res);
      console.log('响应结构:', JSON.stringify(res, null, 2));

      if (res && res.success && res.data) {
        const orderDetail = res.data;
        console.log('订单详情数据:', orderDetail);

        // 处理收货地址信息
        if (orderDetail.address_info) {
          orderDetail.receiver_name = orderDetail.address_info.name;
          orderDetail.receiver_phone = orderDetail.address_info.phone;
          orderDetail.receiver_address = `${orderDetail.address_info.province} ${orderDetail.address_info.city} ${orderDetail.address_info.district} ${orderDetail.address_info.detail}`;
        }

        // 处理门店信息
        if (orderDetail.store_info) {
          orderDetail.store_name = orderDetail.store_info.name;
          orderDetail.store_address = orderDetail.store_info.address;
          orderDetail.store_phone = orderDetail.store_info.phone;
        }

        // 确保必要字段存在
        orderDetail.goods_amount = orderDetail.goods_amount || orderDetail.total_amount || 0;
        orderDetail.shipping_fee = orderDetail.shipping_fee || 0;
        orderDetail.discount_amount = orderDetail.discount_amount || 0;

        // 构建订单状态步骤
        const statusSteps = this.buildStatusSteps(orderDetail.status);

        console.log('处理后的订单详情:', orderDetail);
        console.log('状态步骤:', statusSteps);

        this.setData({
          orderDetail,
          statusSteps,
          loading: false
        });
      } else {
        console.error('订单详情API返回失败:', res);
        console.error('失败原因 - success:', res ? res.success : 'undefined');
        console.error('失败原因 - data:', res ? res.data : 'undefined');
        console.error('失败原因 - message:', res ? res.message : 'undefined');

        this.setData({ loading: false });

        let errorMessage = '获取订单详情失败';
        if (res && res.message) {
          errorMessage = res.message;
        } else if (res && typeof res === 'string') {
          errorMessage = res;
        }

        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      }
    }).catch(err => {
      console.error('获取订单详情失败 - catch:', err);
      console.error('错误类型:', typeof err);
      console.error('错误详情:', JSON.stringify(err, null, 2));

      this.setData({ loading: false });

      let errorMessage = '获取订单详情失败';
      if (err && err.message) {
        errorMessage = err.message;
      } else if (err && typeof err === 'string') {
        errorMessage = err;
      } else if (err && err.errMsg) {
        errorMessage = err.errMsg;
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });
    });
  },

  // 构建订单状态步骤
  buildStatusSteps: function(status) {
    console.log('构建状态步骤，当前状态:', status);

    const steps = [
      { title: '待付款', desc: '' },
      { title: '待发货', desc: '' },
      { title: '待收货', desc: '' },
      { title: '已完成', desc: '' }
    ];

    const statusInfo = this.data.statusMap[status];
    console.log('状态信息:', statusInfo);

    if (!statusInfo) {
      console.warn('未找到状态映射:', status);
      return steps;
    }

    // 特殊状态处理
    if (statusInfo.step === -1) {
      if (status === 'cancelled') {
        return [{ title: '已取消', desc: '订单已取消' }];
      } else if (status === 'refund') {
        return [{ title: '退款/售后', desc: '订单正在处理退款或售后' }];
      }
      return steps;
    }

    // 正常状态流程
    const currentStep = statusInfo.step;
    for (let i = 0; i <= currentStep; i++) {
      steps[i].active = true;
    }
    steps[currentStep].current = true;

    console.log('构建的状态步骤:', steps);
    return steps;
  },

  // 复制订单号
  copyOrderNo: function() {
    const { orderDetail } = this.data;
    if (!orderDetail || !orderDetail.order_no) return;
    
    wx.setClipboardData({
      data: orderDetail.order_no,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        });
      }
    });
  },

  // 取消订单
  cancelOrder: function() {
    const { orderId } = this.data;
    wx.showModal({
      title: '提示',
      content: '确定要取消该订单吗？',
      success: (res) => {
        if (res.confirm) {
          orderApi.cancelOrder(orderId).then(res => {
            if (res.success) {
              wx.showToast({
                title: '订单已取消',
                icon: 'success'
              });
              // 刷新订单详情
              this.loadOrderDetail(orderId);
            } else {
              wx.showToast({
                title: res.message || '取消失败',
                icon: 'none'
              });
            }
          }).catch(() => {
            wx.showToast({
              title: '取消失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 去支付
  goToPay: function() {
    const { orderId } = this.data;
    wx.navigateTo({
      url: `/pages/order/pay?id=${orderId}`
    });
  },

  // 确认收货
  confirmReceipt: function() {
    const { orderId } = this.data;
    wx.showModal({
      title: '提示',
      content: '确认已收到商品吗？',
      success: (res) => {
        if (res.confirm) {
          orderApi.confirmReceipt(orderId).then(res => {
            if (res.success) {
              wx.showToast({
                title: '确认收货成功',
                icon: 'success'
              });
              // 刷新订单详情
              this.loadOrderDetail(orderId);
            } else {
              wx.showToast({
                title: res.message || '操作失败',
                icon: 'none'
              });
            }
          }).catch(() => {
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 申请退款
  applyRefund: function() {
    const { orderId } = this.data;
    wx.navigateTo({
      url: `/pages/order/refund?id=${orderId}`
    });
  },

  // 查看退款详情
  viewRefundDetail: function() {
    const { orderId } = this.data;
    wx.navigateTo({
      url: `/pages/order/refund-detail?id=${orderId}`
    });
  },

  // 查看物流
  viewLogistics: function() {
    const { orderId } = this.data;
    wx.navigateTo({
      url: `/pages/order/logistics?id=${orderId}`
    });
  },

  // 去评价
  goToRate: function() {
    const { orderId } = this.data;
    wx.navigateTo({
      url: `/pages/order/rate?id=${orderId}`
    });
  },

  // 联系客服
  contactService: function() {
    wx.navigateTo({
      url: '/pages/service/index'
    });
  }
});