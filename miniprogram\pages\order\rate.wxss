/* pages/order/rate.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 加载中 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  height: 300rpx;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 26rpx;
  color: #999;
}

/* 订单信息 */
.order-info {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.order-no {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.order-time {
  font-size: 26rpx;
  color: #999;
}

/* 评分区域通用样式 */
.rate-section,
.product-rate-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 24rpx;
  background-color: #ff6b00;
  border-radius: 3rpx;
}

/* 评分行 */
.rating-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.rating-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #666;
}

.rating-stars {
  flex: 1;
  display: flex;
  align-items: center;
}

.star-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.rating-text {
  font-size: 26rpx;
  color: #ff6b00;
  margin-left: 10rpx;
}

/* 评价输入区域 */
.comment-area {
  margin-top: 30rpx;
}

.comment-input {
  width: 100%;
  height: 200rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.comment-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 商品评价 */
.product-rate-item {
  margin-bottom: 40rpx;
  padding-bottom: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.product-rate-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.product-info {
  display: flex;
  margin-bottom: 20rpx;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.product-detail {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-specs {
  font-size: 24rpx;
  color: #999;
}

.product-rating {
  margin-bottom: 20rpx;
}

.product-comment {
  margin-bottom: 20rpx;
}

/* 上传图片区域 */
.upload-section {
  margin-top: 20rpx;
}

.upload-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  position: relative;
  width: 140rpx;
  height: 140rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-icon {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  z-index: 1;
}

.upload-btn {
  width: 140rpx;
  height: 140rpx;
  background-color: #f9f9f9;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.upload-icon {
  font-size: 40rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 匿名评价 */
.anonymous-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.anonymous-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.anonymous-label {
  font-size: 28rpx;
  color: #333;
}

.anonymous-switch {
  width: 80rpx;
  height: 40rpx;
  background-color: #e0e0e0;
  border-radius: 20rpx;
  position: relative;
  transition: all 0.3s;
}

.anonymous-switch.active {
  background-color: #ff6b00;
}

.switch-slider {
  width: 36rpx;
  height: 36rpx;
  background-color: #fff;
  border-radius: 50%;
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  transition: all 0.3s;
}

.anonymous-switch.active .switch-slider {
  left: 42rpx;
}

.anonymous-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 提交按钮 */
.submit-btn {
  position: fixed;
  bottom: 30rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #ff6b00;
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 0, 0.3);
}

.submit-btn.disabled {
  background-color: #ccc;
  box-shadow: none;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}