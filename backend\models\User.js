/**
 * 用户模型
 */
const db = require('../config/db');
const crypto = require('crypto');
const Follow = require('./Follow');
const Favorite = require('./Favorite');

class User {
  static async findByUsername(username) {
    // 先尝试查找username字段
    let result = await db.query('SELECT * FROM users WHERE username = ?', [username]);

    // 如果没找到，尝试查找account_username字段
    if (result.length === 0) {
      result = await db.query('SELECT * FROM users WHERE account_username = ?', [username]);
    }

    console.log('查找用户结果:', result);
    return result.length > 0 ? result[0] : null;
  }

  static async findByPhone(phone) {
    const result = await db.query('SELECT * FROM users WHERE phone = ?', [phone]);
    return result.length > 0 ? result[0] : null;
  }

  static async findByOpenid(openid) {
    const result = await db.query('SELECT * FROM users WHERE openid = ?', [openid]);
    return result.length > 0 ? result[0] : null;
  }

  // 通过user_id查找用户（业务ID）
  static async findByUserId(userId) {
    const result = await db.query('SELECT * FROM users WHERE user_id = ?', [userId]);
    return result.length > 0 ? result[0] : null;
  }

  // 通过自增id查找用户（保持向后兼容）
  static async findById(id) {
    const result = await db.query('SELECT * FROM users WHERE id = ?', [id]);
    return result.length > 0 ? result[0] : null;
  }

  static async create(userData) {
    // 如果没有密码，自动生成一个随机密码
    if (!userData.password) {
      // 生成8位随机字符串
      const randomPwd = Math.random().toString(36).slice(-8);
      userData.password = crypto.createHash('md5').update(randomPwd).digest('hex');
    } else {
      userData.password = crypto.createHash('md5').update(userData.password).digest('hex');
    }
    
    // 生成用户ID（10位数字，以8开头）
    if (!userData.user_id) {
      userData.user_id = '8' + Math.floor(Math.random() * 9000000000 + 1000000000).toString().substring(1);
    }
    
    // 设置默认值
    userData.createTime = userData.createTime || Date.now();
    userData.updateTime = userData.updateTime || Date.now();
    
    // 构建插入SQL
    const keys = Object.keys(userData);
    const values = keys.map(k => userData[k]);
    const placeholders = keys.map(() => '?').join(', ');
    const sql = `INSERT INTO users (${keys.join(', ')}) VALUES (${placeholders})`;
    
    await db.query(sql, values);
    
    // 返回新用户（通过user_id查找）
    return await this.findByUserId(userData.user_id);
  }

  static async update(userId, userData) {
    try {
      console.log('更新用户信息:', userId, userData);

      // 特殊处理密码字段 - 不在这里处理，由userService处理
      // 因为密码更新需要同时更新password和account_password两个字段
      // 以及password_salt字段

      // 注释掉更新时间，因为数据库中没有这个字段
      // userData.updateTime = Date.now();

      // 确保数据库字段名称正确
      // 如果传入的是nickname，确保它被正确映射到数据库字段
      if (userData.nickname !== undefined) {
        console.log('更新昵称:', userData.nickname);
      }

      // 如果传入的是avatar，确保它被正确映射到数据库字段
      if (userData.avatar !== undefined) {
        console.log('更新头像:', userData.avatar);
      }

      // 直接使用SQL更新，确保更新成功
      const updateFields = [];
      const updateValues = [];

      for (const [key, value] of Object.entries(userData)) {
        updateFields.push(`${key} = ?`);
        updateValues.push(value);
      }

      // 添加user_id作为条件
      updateValues.push(userId);

      const sql = `UPDATE users SET ${updateFields.join(', ')} WHERE user_id = ?`;
      console.log('执行SQL更新:', sql);
      console.log('更新参数:', updateValues);

      await db.query(sql, updateValues);

      // 获取更新后的用户信息
      const updatedUser = await this.findByUserId(userId);
      console.log('更新后的用户信息:', updatedUser);
      return updatedUser;
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }

  static async getUserStats(userId) {
    // 获取用户统计信息
    const followCount = await Follow.getFollowingCount(userId);
    const followerCount = await Follow.getFollowerCount(userId);
    const favoriteCount = await Favorite.count(userId);

    // 获取订单统计
    const orderStats = await this.getOrderStats(userId);

    // 获取积分
    const pointsResult = await db.query('SELECT points FROM user_points WHERE user_id = ?', [userId]);
    const points = pointsResult.length > 0 ? pointsResult[0].points : 0;

    // 获取余额
    const balanceResult = await db.query('SELECT account_balance FROM user_fund_accounts WHERE user_id = ?', [userId]);
    const balance = balanceResult.length > 0 ? balanceResult[0].account_balance : 0;

    return {
      followCount,
      followerCount,
      favoriteCount,
      points,
      balance,
      ...orderStats
    };
  }

  static async getOrderStats(userId) {
    // 获取订单统计信息
    const stats = {
      unpaidCount: 0,
      unshippedCount: 0,
      unreceivedCount: 0,
      unratedCount: 0
    };

    try {
      // 查询各状态订单数量
      const unpaidResult = await db.query('SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = "待付款"', [userId]);
      const unshippedResult = await db.query('SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = "待发货"', [userId]);
      const unreceivedResult = await db.query('SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = "待收货"', [userId]);
      const unratedResult = await db.query('SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = "待评价"', [userId]);

      stats.unpaidCount = unpaidResult[0]?.count || 0;
      stats.unshippedCount = unshippedResult[0]?.count || 0;
      stats.unreceivedCount = unreceivedResult[0]?.count || 0;
      stats.unratedCount = unratedResult[0]?.count || 0;
    } catch (error) {
      console.error('获取订单统计失败:', error);
    }

    return stats;
  }
}

module.exports = User;
