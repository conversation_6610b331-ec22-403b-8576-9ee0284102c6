/**
 * 执行更新门店库存表结构的SQL脚本
 */
const fs = require('fs');
const path = require('path');
const db = require('../config/db');

async function executeSQL() {
  try {
    // 读取SQL文件内容
    const sqlFilePath = path.join(__dirname, 'update_store_inventory_table.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // 将SQL语句按分号分割成多个语句
    const sqlStatements = sqlContent
      .split(';')
      .filter(statement => statement.trim() !== '');
    
    // 开始事务
    const connection = await db.getConnection();
    await connection.beginTransaction();
    
    try {
      // 依次执行每个SQL语句
      for (const statement of sqlStatements) {
        await connection.query(statement);
      }
      
      // 提交事务
      await connection.commit();
      console.log('门店库存表结构更新成功！');
    } catch (error) {
      // 回滚事务
      await connection.rollback();
      throw error;
    } finally {
      // 释放连接
      connection.release();
    }
    
    process.exit(0);
  } catch (error) {
    console.error('执行SQL脚本失败:', error);
    process.exit(1);
  }
}

// 执行脚本
executeSQL();