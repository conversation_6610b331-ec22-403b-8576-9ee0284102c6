.user-mgr-container {
  background: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 合并并精简顶部四栏相关样式，确保每个类只保留一份定义 */
.user-mgr-header {
  background: #fff;
  padding: 10rpx 24rpx 0 24rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: center;
  position: sticky;
  top: 0;
  z-index: 20;
  height: auto;
  box-sizing: border-box;
}
.user-mgr-search-bar {
  display: flex;
  align-items: center;
  width: 90%;
  max-width: 700rpx;
  min-width: 240rpx;
  padding: 0;
  justify-content: center;
  background: none;
  box-shadow: none;
  margin-bottom: 32rpx;
}
.user-mgr-search-input-container {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  padding: 0 8rpx;
  position: relative;
  flex: 1;
}
.user-mgr-search-input {
  flex: 1;
  height: 100%;
  font-size: 34rpx;
  padding: 0 80rpx 0 32rpx;
  border: none;
  background: transparent;
  outline: none;
}
.user-mgr-search-btn {
  width: 58rpx;
  height: 58rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  box-shadow: none;
  padding: 0;
}
.user-mgr-search-btn image {
  width: 48rpx;
  height: 48rpx;
}
.user-mgr-actions {
  display: flex;
  gap: 16rpx;
  padding: 0 24rpx;
  background: #fff;
  margin-bottom: 28rpx;
  position: sticky;
  top: 70rpx;
  z-index: 19;
  height: auto;
  box-sizing: border-box;
}
.user-mgr-btn {
  flex: 1;
  background: #ff4d4f;
  color: #fff;
  border-radius: 16rpx;
  font-size: 26rpx;
  padding: 16rpx 0;
  border: none;
}
.user-mgr-btn-disabled {
  background: #e0e0e0 !important;
  color: transparent !important;
  pointer-events: none;
  border: none;
}
.user-mgr-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0;
  padding: 0 24rpx;
  background: #fff;
  height: 70rpx;
  margin-bottom: 10rpx;
  margin-top: 6rpx;
  border-top: none;
  border-bottom: none;
  box-shadow: none;
  position: sticky;
  top: 140rpx;
  z-index: 18;
  box-sizing: border-box;
}
.user-mgr-tab {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  color: #888;
  padding-bottom: 8rpx;
  border-bottom: 4rpx solid transparent;
  background: none;
}
.user-mgr-tab.active {
  color: #ff4d4f;
  border-bottom: 4rpx solid #ff4d4f;
  background: none;
}
.user-mgr-table-header {
  display: flex;
  justify-content: space-between;
  padding: 0 40rpx 0 40rpx;
  font-size: 26rpx;
  color: #888;
  background: #fff;
  margin-bottom: 18rpx;
  height: 64rpx;
  align-items: center;
  border-radius: 16rpx;
}
.sortable-header {
  flex: 1;
  text-align: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  user-select: none;
}
.sortable-header:hover {
  background: #f5f5f5;
  color: #1765d5;
}
.sortable-header:active {
  transform: scale(0.98);
}
.sortable-header::after {
  content: '';
  position: absolute;
  right: -8rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 6rpx solid transparent;
  border-right: 6rpx solid transparent;
  opacity: 0.3;
}
.sortable-header::before {
  content: '⇅';
  position: absolute;
  right: -16rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20rpx;
  color: #ccc;
  opacity: 0.6;
}
.sortable-header.sorted-asc {
  color: #1765d5;
  font-weight: 500;
}
.sortable-header.sorted-asc::before {
  content: '↑';
  color: #1765d5;
  opacity: 1;
  font-weight: bold;
}
.sortable-header.sorted-desc {
  color: #1765d5;
  font-weight: 500;
}
.sortable-header.sorted-desc::before {
  content: '↓';
  color: #1765d5;
  opacity: 1;
  font-weight: bold;
}
.sortable-header.sorted-asc::after,
.sortable-header.sorted-desc::after {
  display: none;
}

.user-mgr-list {
  margin: 0 0 24rpx 0;
  background: #f7f7f7;
}
.user-mgr-item {
  display: flex;
  align-items: center;
  background: #fff;
  margin: 16rpx 24rpx 0 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx #eee;
  padding: 18rpx 18rpx;
  position: relative;
}
.user-mgr-checkbox {
  margin-right: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  cursor: pointer;
  background-color: transparent;
  transition: all 0.2s ease;
}

.user-mgr-checkbox image {
  width: 48rpx;
  height: 48rpx;
}

.user-mgr-checkbox.selected {
  background-color: transparent;
}

.user-mgr-checkbox:hover {
  transform: scale(1.05);
}
.user-mgr-avatar {
  width: 82.8rpx;
  height: 82.8rpx;
  border-radius: 50%;
  margin-right: 18rpx;
  border: 2rpx solid #f0f0f0;
  background: #fafafa;
}
.user-mgr-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}
.user-mgr-nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.user-mgr-id,
.user-mgr-phone,
.user-mgr-date {
  font-size: 24rpx;
  color: #888;
}
.user-mgr-status-top {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 24rpx;
  padding: 4rpx 18rpx;
  border-radius: 16rpx 0 0 16rpx;
  font-weight: bold;
  z-index: 2;
  border: 2rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx #eee;
  background: #fff;
}
.user-mgr-edit-btn {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  background: #f5f5f5;
  color: #1765d5;
  border-radius: 12rpx;
  padding: 8rpx 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  cursor: pointer;
  z-index: 2;
}
.user-mgr-status-edit, .user-mgr-edit, .user-mgr-edit-icon {
  display: none !important;
}
.user-mgr-footer {
  text-align: center;
  font-size: 28rpx;
  color: #888;
  background: #fff;
  padding: 24rpx 0 24rpx 0;
  margin-top: 24rpx;
}

/* 选中数量样式 */
.selected-count {
  color: #ff4d4f;
  font-weight: bold;
  margin-left: 16rpx;
}
.user-mgr-nickname-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.user-mgr-role {
  font-size: 22rpx;
  padding: 2rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
  margin-left: 8rpx;
  line-height: 28rpx;
}
.user-mgr-role-customer {
  background: #f5f5f5;
  color: #888;
  border: 1rpx solid #e0e0e0;
}
.user-mgr-role-partner {
  background: #e6f0ff;
  color: #1765d5;
  border: 1rpx solid #b3d1ff;
}
.user-mgr-role-admin {
  background: #fff7e6;
  color: #fa8c16;
  border: 1rpx solid #ffe1b8;
}
.user-mgr-checkbox-custom {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #bbb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 18rpx;
  background: #fff;
  cursor: pointer;
}
.user-mgr-checkbox-custom.checked {
  border-color: #ff4d4f;
  background: #ffeded;
}
.checkbox-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 搜索状态样式 */
.user-mgr-searching {
  text-align: center;
  padding: 40rpx 0;
  background: #fff;
  margin: 16rpx 24rpx;
  border-radius: 16rpx;
  color: #666;
  font-size: 28rpx;
}

/* 搜索结果提示样式 */
.user-mgr-search-result {
  text-align: center;
  padding: 20rpx 0;
  background: #f0f9ff;
  margin: 16rpx 24rpx;
  border-radius: 16rpx;
  color: #1765d5;
  font-size: 26rpx;
  border: 1rpx solid #b3d1ff;
}

.user-mgr-top-sticky {
  position: sticky;
  top: 0;
  z-index: 30;
  background: #fff;
  box-shadow: 0 2rpx 12rpx #eee;
}

/* 用户调整半屏弹窗 */
.user-edit-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.user-edit-drawer.show {
  opacity: 1;
  visibility: visible;
}

.user-edit-content {
  width: 100%;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.user-edit-drawer.show .user-edit-content {
  transform: translateY(0);
}

.user-edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.user-edit-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.user-edit-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.user-edit-body {
  padding: 30rpx;
}

.user-edit-section {
  margin-bottom: 40rpx;
}

.user-edit-section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #eee;
}

.user-edit-field {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-edit-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.user-edit-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.user-edit-input {
  flex: 1;
  height: 60rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
}

.user-edit-picker {
  flex: 1;
}

.user-edit-picker-text {
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
}

.user-edit-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.user-edit-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.user-edit-btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.user-edit-btn-save {
  background: #007aff;
  color: #fff;
}