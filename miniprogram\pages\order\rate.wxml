<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>

  <block wx:if="{{!loading && orderDetail}}">
    <!-- 订单信息 -->
    <view class="order-info">
      <view class="order-no">订单号: {{orderDetail.order_no}}</view>
      <view class="order-time">下单时间: {{orderDetail.created_at}}</view>
    </view>

    <!-- 整体评分 -->
    <view class="rate-section">
      <view class="section-title">整体评分</view>
      <view class="rating-row">
        <view class="rating-label">商品评分</view>
        <view class="rating-stars">
          <block wx:for="{{5}}" wx:key="overall-star-{{index}}">
            <image 
              class="star-icon" 
              src="{{index < overallRating ? '/images/icons/star-active.svg' : '/images/icons/star-inactive.svg'}}"
              bindtap="setOverallRating"
              data-rating="{{index + 1}}"
            ></image>
          </block>
          <text class="rating-text">{{['很差', '较差', '一般', '不错', '很好'][overallRating - 1]}}</text>
        </view>
      </view>

      <view class="rating-row">
          <view class="rating-label">配送评分</view>
          <view class="rating-stars">
            <block wx:for="{{5}}" wx:key="delivery-star-{{index}}">
              <image 
                class="star-icon" 
                src="{{index < deliveryRating ? '/images/icons/star-active.svg' : '/images/icons/star-inactive.svg'}}"
                bindtap="setDeliveryRating"
                data-rating="{{index + 1}}"
              ></image>
            </block>
            <text class="rating-text">{{['很差', '较差', '一般', '不错', '很好'][deliveryRating - 1]}}</text>
          </view>
        </view>
        
        <view class="rating-row">
          <view class="rating-label">服务评分</view>
          <view class="rating-stars">
            <block wx:for="{{5}}" wx:key="service-star-{{index}}">
              <image 
                class="star-icon" 
                src="{{index < serviceRating ? '/images/icons/star-active.svg' : '/images/icons/star-inactive.svg'}}"
                bindtap="setServiceRating"
                data-rating="{{index + 1}}"
              ></image>
            </block>
            <text class="rating-text">{{['很差', '较差', '一般', '不错', '很好'][serviceRating - 1]}}</text>
          </view>
        </view>

      <view class="comment-area">
        <textarea 
          class="comment-input" 
          placeholder="请输入您的整体评价内容" 
          maxlength="{{maxCommentLength}}" 
          bindinput="inputComment"
          value="{{comment}}"
        ></textarea>
        <view class="comment-counter">{{comment.length}}/{{maxCommentLength}}</view>
      </view>
    </view>

    <!-- 商品评价 -->
    <view class="product-rate-section">
      <view class="section-title">商品评价</view>
      
      <block wx:for="{{rateItems}}" wx:key="id" wx:for-index="productIndex">
        <view class="product-rate-item">
          <view class="product-info">
            <image class="product-image" src="{{item.image || '/images/icons2/默认商品.png'}}"></image>
            <view class="product-detail">
              <view class="product-name">{{item.name}}</view>
              <view class="product-specs" wx:if="{{item.specs}}">{{item.specs}}</view>
            </view>
          </view>

          <view class="product-rating">
            <view class="rating-label">商品评分</view>
            <view class="rating-stars">
              <block wx:for="{{5}}" wx:key="product-star-{{productIndex}}-{{starIndex}}" wx:for-item="star" wx:for-index="starIndex">
                <image 
                  class="star-icon" 
                  src="{{starIndex < item.rating ? '/images/icons/star-active.svg' : '/images/icons/star-inactive.svg'}}"
                  bindtap="setProductRating"
                  data-index="{{productIndex}}"
                  data-rating="{{starIndex + 1}}"
                ></image>
              </block>
              <text class="rating-text">{{['很差', '较差', '一般', '不错', '很好'][item.rating - 1]}}</text>
            </view>
          </view>

          <view class="product-comment">
            <textarea 
              class="comment-input" 
              placeholder="请输入对该商品的评价" 
              maxlength="{{maxCommentLength}}" 
              bindinput="inputProductComment"
              data-index="{{productIndex}}"
              value="{{item.comment}}"
            ></textarea>
            <view class="comment-counter">{{item.comment.length}}/{{maxCommentLength}}</view>
          </view>

          <view class="upload-section">
            <view class="upload-title">上传图片(选填)</view>
            <view class="image-list">
              <block wx:for="{{item.images}}" wx:for-item="image" wx:for-index="imageIndex" wx:key="image-{{productIndex}}-{{imageIndex}}">
                <view class="image-item">
                  <image 
                    class="uploaded-image" 
                    src="{{image}}" 
                    mode="aspectFill"
                    bindtap="previewImage"
                    data-index="{{productIndex}}"
                    data-image-index="{{imageIndex}}"
                  ></image>
                  <view 
                    class="delete-icon" 
                    bindtap="deleteImage"
                    data-index="{{productIndex}}"
                    data-image-index="{{imageIndex}}"
                  >×</view>
                </view>
              </block>
              
              <view 
                class="upload-btn" 
                bindtap="chooseImage"
                data-index="{{productIndex}}"
                wx:if="{{item.images.length < maxImageCount}}"
              >
                <view class="upload-icon">+</view>
                <view class="upload-text">添加图片</view>
              </view>
            </view>
            <view class="upload-tip">最多上传{{maxImageCount}}张图片</view>
          </view>
        </view>
      </block>
    </view>

    <!-- 匿名评价 -->
    <view class="anonymous-section">
      <view class="anonymous-row" bindtap="toggleAnonymous">
        <view class="anonymous-label">匿名评价</view>
        <view class="anonymous-switch {{anonymousRate ? 'active' : ''}}">
          <view class="switch-slider"></view>
        </view>
      </view>
      <view class="anonymous-tip">开启后，您的评价将以匿名形式展示</view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-btn {{submitting ? 'disabled' : ''}}" bindtap="submitRate">
      {{submitting ? '提交中...' : '提交评价'}}
    </view>
  </block>

  <!-- 订单不存在 -->
  <view class="empty-container" wx:if="{{!loading && !orderDetail}}">
    <image class="empty-icon" src="/images/icons/empty-order.svg"></image>
    <text class="empty-text">订单不存在或已被删除</text>
  </view>
</view>