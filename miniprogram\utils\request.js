// miniprogram/utils/request.js

const { getApiEnv, getBaseUrl } = require('./api');

function request({ url, method = 'GET', data = {}, header = {}, requireAuth = true }) {
  // 在函数内部获取app实例，避免在模块顶层调用getApp()
  const env = getApiEnv && getApiEnv();
  const baseUrl = getBaseUrl && getBaseUrl();

  // 获取 token
  let token = wx.getStorageSync('token');

  // 如果本地存储没有token，尝试从全局数据获取
  if (!token && getApp && getApp().globalData) {
    token = getApp().globalData.token;
    if (token) {
      console.log('从全局数据恢复token成功');
      // 同步回本地存储
      wx.setStorageSync('token', token);
    }
  }

  // 如果仍然没有token，尝试重新获取登录状态
  if (!token) {
    try {
      const loginStateManager = require('./login-state-manager');
      const loginState = loginStateManager.getLoginState();
      if (loginState && loginState.token) {
        token = loginState.token;
        console.log('从登录状态管理器恢复token成功');
        
        // 更新本地存储
        wx.setStorageSync('token', token);
        
        // 同步到全局数据
        if (getApp && getApp().globalData) {
          getApp().globalData.token = token;
          getApp().globalData.isLogin = true;
          
          // 如果有时间戳，也同步
          if (loginState.timestamp) {
            wx.setStorageSync('tokenTimestamp', loginState.timestamp);
            getApp().globalData.tokenTimestamp = loginState.timestamp;
          }
        }
      }
    } catch (e) {
      console.error('尝试恢复token失败:', e);
    }
  }
  
  // 只有在需要鉴权时才检查token
  if (requireAuth && !token) {
    console.warn('需要鉴权但没有token，请求将失败:', url);
    return Promise.reject({ success: false, code: 401, message: '请先登录' });
  }
  
  // 如果不需要鉴权，静默处理（减少日志）
  if (!requireAuth) {
    // 不需要鉴权的请求，无论是否有token都继续执行（减少日志输出）
  }

  // 准备请求参数

  // 确保header对象存在
  header = header || {};

  // 只有需要鉴权时才加token
  if (requireAuth && token) {
    // 使用大写的 Authorization 和正确的 Bearer 格式（注意空格）
    header['Authorization'] = `Bearer ${token}`;
    // 添加认证头

    // 同时添加小写的authorization，以防后端区分大小写
    header['authorization'] = `Bearer ${token}`;
  } else if (!requireAuth) {
    // 确保不需要鉴权的请求不会添加任何Authorization头
    delete header['Authorization'];
    delete header['authorization'];
  }
  // 减少调试日志：只在错误时输出详细信息
  // console.log('[API请求调试] url:', url, 'method:', method, 'header:', header, 'data:', data);

  // 添加内容类型
  if (!header['content-type']) {
    header['content-type'] = 'application/json';
  }

  // 请求头准备完成

  // 如果是POST请求，确保data不是undefined
  if (method === 'POST' && data === undefined) {
    data = {};
  }

  // 如果是对象，确保正确序列化
  // 准备请求数据

  if (env === 'dev') {
    // 本地开发环境，使用wx.request
    
    // 处理GET请求的查询参数（开发环境调试）
    let requestUrl = baseUrl + url;
    if (method.toUpperCase() === 'GET' && data && Object.keys(data).length > 0) {
      console.log('[开发环境GET请求] URL:', url, '参数:', data);
    }
    
    return new Promise((resolve, reject) => {
      wx.request({
        url: requestUrl,
        method,
        data,
        header,
        success: res => {
          // 只有在需要鉴权的请求收到401时才处理登录失效
          if (res.statusCode === 401 && requireAuth) {
            console.error('[API请求401] url:', url, 'header:', header, 'response:', res);
            
            // 检查当前页面路径，避免在订单创建页面清除登录状态
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            const currentRoute = currentPage ? currentPage.route : '';
            
            // 使用一个标记避免重复弹窗
            const loginModalShown = wx.getStorageSync('loginModalShown');
            
            // 如果是订单创建页面，不清除登录状态，直接返回错误
            if (currentRoute === 'pages/order/create') {
              console.log('订单创建页面API请求401，不清除登录状态，直接返回错误');
              reject({ success: false, code: 401, message: '登录已失效，请返回购物车重新结算' });
              return;
            }
            
            // 对于非订单创建页面，清除登录状态
            try {
              const loginStateManager = require('./login-state-manager');
              loginStateManager.clearLoginState(); // 这会同时清除token、userInfo和tokenTimestamp
              console.log('已通过登录状态管理器清除登录状态');
            } catch (e) {
              console.error('通过登录状态管理器清除登录状态失败:', e);
              // 如果调用失败，手动清除
              wx.removeStorageSync('token');
              wx.removeStorageSync('userInfo');
              wx.removeStorageSync('tokenTimestamp');
              wx.removeStorageSync('login_state');
            }
            
            // 更新全局数据
            if (getApp && getApp().globalData) {
              getApp().globalData.isLogin = false;
              getApp().globalData.userInfo = null;
              getApp().globalData.token = null;
              getApp().globalData.tokenTimestamp = null;
            }
            
            // 如果是订单支付页面，也不显示弹窗，直接返回错误
            if (currentRoute === 'pages/order/pay') {
              console.log('订单支付页面API请求401，不显示弹窗，直接返回错误');
              reject({ success: false, code: 401, message: '登录已失效，请重新登录' });
              return;
            }
            
            // 其他页面正常显示弹窗
            if (!loginModalShown) {
              // 设置标记，避免重复弹窗
              wx.setStorageSync('loginModalShown', true);
              // 5分钟后清除标记
              setTimeout(() => {
                wx.removeStorageSync('loginModalShown');
              }, 5 * 60 * 1000);
              
              wx.showModal({
                title: '登录失效',
                content: '请重新登录',
                showCancel: false,
                success: () => {
                  // 使用redirectTo而不是navigateTo，避免返回到需要登录的页面
                  wx.redirectTo({ url: '/pages/auth/auth' });
                }
              });
            }
            
            reject({ success: false, code: 401, message: res.data && res.data.message ? res.data.message : '未授权' });
            return;
          }
          if (res.statusCode >= 400) {
            let errorMessage = '请求失败';
            if (res.data && res.data.message) { errorMessage = res.data.message; } else if (typeof res.data === 'string') { errorMessage = res.data; }
            console.error('[API请求失败] url:', url, 'header:', header, 'response:', res);
            reject({ success: false, code: res.statusCode, message: errorMessage });
            return;
          }
          resolve(res.data);
        },
        fail: err => {
          const errorMessage = err.errMsg || '网络请求失败';
          console.error('[API请求fail] url:', url, 'header:', header, 'error:', err);
          reject({ success: false, code: -1, message: errorMessage });
        }
      });
    });
  } else {
    // 云托管环境
    const app = getApp();
    const envId = app && app.globalData && app.globalData.cloudEnvId ? app.globalData.cloudEnvId : 'prod-4g3qet1k59f2d66f';
    const serviceName = 'morebuy25';

    // 减少日志输出：只在开发调试时输出
    // console.log('[云托管请求] url:', url, 'method:', method, 'envId:', envId, 'serviceName:', serviceName);
    // console.log('[云托管请求] header:', header);
    // console.log('[云托管请求] data:', data);

    return new Promise((resolve, reject) => {
      // 确保method是大写格式，这是云托管的要求
      const upperMethod = method.toUpperCase();

      // 处理GET请求的查询参数
      let requestUrl = url;
      if (upperMethod === 'GET' && data && Object.keys(data).length > 0) {
        // 手动构建查询字符串，避免使用微信小程序不支持的URLSearchParams
        const queryParts = [];
        Object.keys(data).forEach(key => {
          if (data[key] !== undefined && data[key] !== null) {
            queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`);
          }
        });
        const queryString = queryParts.join('&');
        if (queryString) {
          requestUrl = url + (url.includes('?') ? '&' : '?') + queryString;
        }
        console.log('[GET请求] 原始URL:', url, '查询参数:', data, '最终URL:', requestUrl);
      }

      // 对于GET请求，不应该传递data参数
      const requestParams = {
        config: { env: envId },
        path: requestUrl,
        method: upperMethod,
        header: { 'X-WX-SERVICE': serviceName, ...header }
      };

      // 只有非GET请求才添加data参数
      if (upperMethod !== 'GET' && data) {
        requestParams.data = data;
      }

      // 减少日志输出：只在开发调试时输出
      // console.log('[云托管请求参数]', requestParams);

      wx.cloud.callContainer({
        ...requestParams,
        success: res => {
          // 减少日志输出：只在错误时输出
          // console.log('[云托管响应] statusCode:', res.statusCode);
          // console.log('[云托管响应] data:', res.data);

          // 只有在需要鉴权的请求收到401时才处理登录失效
          if (res.statusCode === 401 && requireAuth) {
            console.error('[API请求401] url:', url, 'header:', header, 'response:', res);
            
            // 检查当前页面路径，避免在订单创建页面清除登录状态
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            const currentRoute = currentPage ? currentPage.route : '';
            
            // 使用一个标记避免重复弹窗
            const loginModalShown = wx.getStorageSync('loginModalShown');
            
            // 如果是订单创建页面，不清除登录状态，直接返回错误
            if (currentRoute === 'pages/order/create') {
              console.log('订单创建页面API请求401，不清除登录状态，直接返回错误');
              reject({ success: false, code: 401, message: '登录已失效，请返回购物车重新结算' });
              return;
            }
            
            // 对于非订单创建页面，清除登录状态
            try {
              const loginStateManager = require('./login-state-manager');
              loginStateManager.clearLoginState();
              console.log('已通过登录状态管理器清除登录状态');
            } catch (e) {
              console.error('通过登录状态管理器清除登录状态失败:', e);
              // 如果登录状态管理器调用失败，手动清除
              wx.removeStorageSync('token');
              wx.removeStorageSync('userInfo');
              wx.removeStorageSync('tokenTimestamp'); // 同时清除token时间戳
              wx.removeStorageSync('login_state');
            }
            
            // 确保全局数据也被清除
            if (app && app.globalData) {
              app.globalData.isLogin = false;
              app.globalData.userInfo = null;
              app.globalData.token = null;
              app.globalData.tokenTimestamp = 0; // 同时清除全局token时间戳
            }
            
            // 其他页面正常显示弹窗
            if (!loginModalShown) {
              // 设置标记，避免重复弹窗
              wx.setStorageSync('loginModalShown', true);
              // 5分钟后清除标记
              setTimeout(() => {
                wx.removeStorageSync('loginModalShown');
              }, 5 * 60 * 1000);
              
              wx.showModal({
                title: '登录失效',
                content: '请重新登录',
                showCancel: false,
                success: () => {
                  // 使用redirectTo而不是navigateTo，避免返回到需要登录的页面
                  wx.redirectTo({ url: '/pages/auth/auth' });
                }
              });
            }
            
            reject({ success: false, code: 401, message: res.data && res.data.message ? res.data.message : '未授权' });
            return;
          }
          if (res.statusCode >= 400) {
            let errorMessage = '请求失败';
            if (res.data && res.data.message) { errorMessage = res.data.message; } else if (typeof res.data === 'string') { errorMessage = res.data; }
            console.error('[API请求失败] url:', url, 'header:', header, 'response:', res);
            reject({ success: false, code: res.statusCode, message: errorMessage });
            return;
          }
          resolve(res.data);
        },
        fail: err => {
          const errorMessage = err.errMsg || '网络请求失败';
          console.error('[API请求fail] url:', url, 'header:', header, 'error:', err);
          reject({ success: false, code: -1, message: errorMessage });
        }
      });
    });
  }
}

module.exports = request;