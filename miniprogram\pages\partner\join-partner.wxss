/* pages/partner/join-partner.wxss */
.join-partner-container {
  min-height: 100vh;
  background: #fff;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* 顶部标语区域 */
.slogan-section {
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  background: #fff;
  margin-bottom: 20rpx;
}

.slogan-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  letter-spacing: 4rpx;
}

/* 权益区域 */
.benefits-section {
  flex: 1;
  padding: 0 30rpx 0;
  margin-bottom: 0;
}

.benefits-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
  background: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  position: relative;
}

.benefits-title::after {
  content: '';
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 4rpx;
  background: #ff4d4f;
  border-radius: 2rpx;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  padding: 0 10rpx 0;
  margin-bottom: 0;
}

.benefit-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 2rpx solid #ff4d4f;
}

.benefit-item:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.benefit-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  display: block;
}

.benefit-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.2;
}

/* 合约提示语 */
.contract-notice {
  margin-top: 30rpx;
  padding: 20rpx 0;
  text-align: center;
}

.contract-notice .notice-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

.contract-notice .contract-link {
  color: #ff4d4f;
  text-decoration: underline;
}

/* 底部提示区域 */
.notice-section {
  padding: 0 30rpx 60rpx;
  background: #fff;
  margin-top: 0;
}

.notice-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.contract-link {
  color: #ff4d4f;
  text-decoration: underline;
}

.notice-actions {
  display: flex;
  justify-content: center;
  gap: 30rpx;
}

.action-btn {
  max-width: 300rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  padding: 0 60rpx;
}

.apply-btn {
  background: #ff4d4f;
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
}

.apply-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
}

.status-btn {
  background: #fff;
  color: #ff4d4f;
  border: 2rpx solid #ff4d4f;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.1);
}

.status-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.1);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.modal-content {
  width: 100%;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

.modal-body {
  flex: 1;
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: bold;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #ff4d4f;
}

.form-picker {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.form-picker:active {
  border-color: #ff4d4f;
}

.modal-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #ff4d4f;
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
}

.submit-btn:active {
  transform: scale(0.98);
}

.submit-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  line-height: 1.4;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .benefits-grid {
    gap: 15rpx;
  }
  
  .benefit-item {
    padding: 25rpx 15rpx;
  }
  
  .benefit-icon {
    font-size: 50rpx;
  }
  
  .benefit-title {
    font-size: 26rpx;
  }
}