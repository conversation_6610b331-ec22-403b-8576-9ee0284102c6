-- 将orders表改名为store_orders的SQL脚本
-- 执行前请确保已备份数据库

-- 1. 重命名orders表为store_orders
RENAME TABLE orders TO store_orders;

-- 2. 验证表重命名是否成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'store_orders';

-- 3. 显示新表结构
DESCRIBE store_orders;

-- 4. 检查表中的数据
SELECT COUNT(*) as total_records FROM store_orders;

-- 5. 显示最近的几条记录
SELECT id, order_no, type, status, created_at 
FROM store_orders 
ORDER BY created_at DESC 
LIMIT 5;

-- 6. 检查索引是否正常
SHOW INDEX FROM store_orders;