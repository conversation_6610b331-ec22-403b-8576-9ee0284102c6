/**
 * 数据库迁移脚本：为orders表添加订单拆分支持字段
 * 
 * 使用方法：
 * node backend/scripts/migrate_order_split.js
 */

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'morebuy',
  charset: 'utf8mb4'
};

async function migrateDatabase() {
  let connection;
  
  try {
    console.log('🔄 开始数据库迁移：添加订单拆分支持字段...');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 添加主订单号字段
    console.log('➕ 添加main_order_no字段...');
    try {
      await connection.execute(`
        ALTER TABLE orders 
        ADD COLUMN main_order_no VARCHAR(50) NULL COMMENT '主订单号（用于关联拆分订单）' AFTER order_no
      `);
      console.log('✅ main_order_no字段添加成功');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  main_order_no字段已存在，跳过添加');
      } else {
        throw error;
      }
    }
    
    // 2. 添加子订单序号字段
    console.log('➕ 添加sub_order_seq字段...');
    try {
      await connection.execute(`
        ALTER TABLE orders 
        ADD COLUMN sub_order_seq INT DEFAULT 1 COMMENT '子订单序号（同一主订单下的序号）' AFTER main_order_no
      `);
      console.log('✅ sub_order_seq字段添加成功');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  sub_order_seq字段已存在，跳过添加');
      } else {
        throw error;
      }
    }
    
    // 3. 添加订单拆分类型字段
    console.log('➕ 添加split_type字段...');
    try {
      await connection.execute(`
        ALTER TABLE orders 
        ADD COLUMN split_type ENUM('main', 'sub') DEFAULT 'main' COMMENT '订单拆分类型：main=主订单，sub=子订单' AFTER sub_order_seq
      `);
      console.log('✅ split_type字段添加成功');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  split_type字段已存在，跳过添加');
      } else {
        throw error;
      }
    }
    
    // 4. 添加父订单ID字段
    console.log('➕ 添加parent_order_id字段...');
    try {
      await connection.execute(`
        ALTER TABLE orders 
        ADD COLUMN parent_order_id INT NULL COMMENT '父订单ID（子订单关联主订单）' AFTER split_type
      `);
      console.log('✅ parent_order_id字段添加成功');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  parent_order_id字段已存在，跳过添加');
      } else {
        throw error;
      }
    }
    
    // 5. 添加索引
    console.log('➕ 添加索引...');
    const indexes = [
      { name: 'idx_main_order_no', column: 'main_order_no' },
      { name: 'idx_parent_order_id', column: 'parent_order_id' },
      { name: 'idx_split_type', column: 'split_type' }
    ];
    
    for (const index of indexes) {
      try {
        await connection.execute(`CREATE INDEX ${index.name} ON orders (${index.column})`);
        console.log(`✅ 索引 ${index.name} 添加成功`);
      } catch (error) {
        if (error.code === 'ER_DUP_KEYNAME') {
          console.log(`ℹ️  索引 ${index.name} 已存在，跳过添加`);
        } else {
          throw error;
        }
      }
    }
    
    // 6. 为现有订单设置默认值
    console.log('🔄 为现有订单设置默认值...');
    const [updateResult] = await connection.execute(`
      UPDATE orders 
      SET main_order_no = order_no, 
          sub_order_seq = 1, 
          split_type = 'main',
          parent_order_id = NULL
      WHERE main_order_no IS NULL
    `);
    console.log(`✅ 已为 ${updateResult.affectedRows} 个订单设置默认值`);
    
    // 7. 验证结果
    console.log('🔍 验证迁移结果...');
    const [stats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_orders,
        COUNT(main_order_no) as orders_with_main_no,
        COUNT(*) - COUNT(main_order_no) as orders_without_main_no
      FROM orders
    `);
    
    console.log('📊 迁移统计:');
    console.log(`   总订单数: ${stats[0].total_orders}`);
    console.log(`   有主订单号: ${stats[0].orders_with_main_no}`);
    console.log(`   缺少主订单号: ${stats[0].orders_without_main_no}`);
    
    // 8. 显示表结构
    console.log('🔍 当前表结构:');
    const [columns] = await connection.execute('DESCRIBE orders');
    console.table(columns);
    
    console.log('🎉 数据库迁移完成！');
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行迁移
migrateDatabase();
