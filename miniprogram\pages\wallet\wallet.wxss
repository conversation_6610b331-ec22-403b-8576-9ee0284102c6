/* pages/wallet/wallet.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 20rpx;
}

/* 加载中 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #e64340;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 余额卡片 */
.balance-card {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.balance-header {
  margin-bottom: 20rpx;
}

.balance-title {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

.balance-amount {
  margin-bottom: 30rpx;
}

.amount-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.balance-actions {
  margin-bottom: 40rpx;
}

.bind-card {
  display: inline-block;
  color: #e64340;
  font-size: 28rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recharge-btn {
  background: #e64340;
  color: #fff;
}

.withdraw-btn {
  background: #e64340;
  color: #fff;
}

/* 钱包明细 */
.wallet-details {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.details-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

/* 标签栏 */
.tabs-container {
  margin-bottom: 30rpx;
}

.tabs-bar {
  display: flex;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 4rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #e64340;
  color: #fff;
}

.tab-value {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.tab-name {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 资金记录列表 */
.records-container {
  min-height: 200rpx;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-left {
  flex: 1;
  margin-right: 20rpx;
}

.record-desc {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.record-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.record-balance {
  font-size: 24rpx;
  color: #666;
}

.record-right {
  text-align: right;
}

.record-amount {
  font-size: 32rpx;
  font-weight: bold;
}

.record-amount.positive {
  color: #e64340;
}

.record-amount.negative {
  color: #333;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

.load-more .loading-spinner {
  width: 30rpx;
  height: 30rpx;
  margin-right: 20rpx;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
} 