-- 为orders表添加订单拆分支持字段
-- 支持顾客订单按库存拆分成多个子订单

-- 1. 添加主订单号字段（用于关联拆分的子订单）
ALTER TABLE orders ADD COLUMN IF NOT EXISTS main_order_no VARCHAR(50) NULL COMMENT '主订单号（用于关联拆分订单）' AFTER order_no;

-- 2. 添加子订单序号字段
ALTER TABLE orders ADD COLUMN IF NOT EXISTS sub_order_seq INT DEFAULT 1 COMMENT '子订单序号（同一主订单下的序号）' AFTER main_order_no;

-- 3. 添加订单拆分类型字段
ALTER TABLE orders ADD COLUMN IF NOT EXISTS split_type ENUM('main', 'sub') DEFAULT 'main' COMMENT '订单拆分类型：main=主订单，sub=子订单' AFTER sub_order_seq;

-- 4. 添加父订单ID字段（用于子订单关联父订单）
ALTER TABLE orders ADD COLUMN IF NOT EXISTS parent_order_id INT NULL COMMENT '父订单ID（子订单关联主订单）' AFTER split_type;

-- 5. 为新字段添加索引
CREATE INDEX IF NOT EXISTS idx_main_order_no ON orders (main_order_no);
CREATE INDEX IF NOT EXISTS idx_parent_order_id ON orders (parent_order_id);
CREATE INDEX IF NOT EXISTS idx_split_type ON orders (split_type);

-- 6. 为现有订单设置默认值
UPDATE orders 
SET main_order_no = order_no, 
    sub_order_seq = 1, 
    split_type = 'main',
    parent_order_id = NULL
WHERE main_order_no IS NULL;

-- 7. 验证结果
SELECT 
    COUNT(*) as total_orders,
    COUNT(main_order_no) as orders_with_main_no,
    COUNT(*) - COUNT(main_order_no) as orders_without_main_no
FROM orders;

-- 8. 显示表结构
DESCRIBE orders;
