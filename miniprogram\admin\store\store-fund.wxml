<view class="store-fund-page">
  <!-- 门店搜索选择栏 -->
  <view class="form-group">
    <view class="form-label">操作门店</view>
    <input class="form-input" placeholder="输入门店名称/编号" value="{{storeSearchValue}}" bindinput="onStoreSearchInput" />
    <view wx:if="{{showStoreDropdown}}" class="dropdown-list">
      <block wx:for="{{filteredStoreList}}" wx:key="id">
        <view class="dropdown-item" bindtap="selectStore" data-index="{{index}}">{{item.name}}（编号:{{item.code}}）</view>
      </block>
      <view wx:if="{{filteredStoreList.length === 0}}" class="dropdown-empty">无匹配门店</view>
    </view>
    <view wx:if="{{showStoreDropdown}}" class="dropdown-mask" catchtap="onCloseDropdown"></view>
  </view>
  <!-- 操作按钮栏 -->
  <view class="partner-actions">
    <button class="partner-action-btn" bindtap="openCapitalDrawer" wx:if="selectedStore" >{{fundActionBtns[0]}}</button>
    <button class="partner-action-btn partner-action-btn-disabled" disabled wx:else>{{fundActionBtns[0]}}</button>
    <button class="partner-action-btn partner-action-btn-disabled" disabled></button>
    <button class="partner-action-btn">{{fundActionBtns[2]}}</button>
    <button class="partner-action-btn">{{fundActionBtns[3]}}</button>
  </view>

  <!-- 股本金增减半屏弹窗 -->
  <view wx:if="{{showCapitalDrawer}}" class="capital-drawer-mask" bindtap="closeCapitalDrawer"></view>
  <view wx:if="{{showCapitalDrawer}}" class="capital-drawer">
    <view class="capital-drawer-header">增减股本金</view>
    <view class="capital-drawer-body">
      <view class="capital-form-row">
        <view class="capital-form-label">增资金额</view>
        <input class="capital-form-input" type="number" placeholder="输入负数为减资" value="{{capitalForm.amount}}" bindinput="onCapitalAmountInput" />
      </view>
      <view class="capital-form-row">
        <view class="capital-form-label">资金事由</view>
        <picker mode="selector" range="{{capitalForm.reasonList}}" range-key="label" value="{{capitalForm.reasonIndex}}" bindchange="onCapitalReasonChange">
          <view class="capital-form-input">{{capitalForm.reasonIndex === -1 ? '请选择' : capitalForm.reasonList[capitalForm.reasonIndex].label}}</view>
        </picker>
      </view>
      <view class="capital-form-row">
        <view class="capital-form-label">事由描述</view>
        <input class="capital-form-input" placeholder="输入详细事由" value="{{capitalForm.desc}}" bindinput="onCapitalDescInput" />
      </view>
      <view class="capital-form-row">
        <view class="capital-form-label">付款凭证</view>
        <view class="voucher-image-list">
          <block wx:for="{{capitalForm.voucherImages}}" wx:key="*this">
            <view class="voucher-image-item">
              <image class="voucher-image" src="{{item}}" mode="aspectFill" bindtap="previewVoucherImage" data-index="{{index}}"></image>
              <view class="delete-icon" catchtap="deleteVoucherImage" data-index="{{index}}">
                <image src="/images/icons2/关闭.png"></image>
              </view>
            </view>
          </block>
          <view class="add-voucher-image-btn" bindtap="chooseVoucherImage" wx:if="{{capitalForm.voucherImages.length < 3}}">
            <image src="/images/icons2/添加图片.png"></image>
          </view>
        </view>
      </view>
      <view class="capital-form-row capital-form-switch-row">
        <switch checked="{{capitalForm.useFund}}" bindchange="onUseFundChange" />
        <text class="capital-form-switch-label">公积金转增股本</text>
        <text class="capital-form-switch-tip">（打开时，即全额由公积金转增）</text>
      </view>
    </view>
    <view class="capital-drawer-footer">
      <button class="capital-cancel-btn" bindtap="closeCapitalDrawer">取消</button>
      <button class="capital-confirm-btn" bindtap="onCapitalConfirm">确定</button>
    </view>
  </view>
  <!-- 分类tab栏 -->
  <view class="fund-tabs">
    <block wx:for="{{tabs}}" wx:key="*this">
      <view class="fund-tab {{tabIndex === index ? 'active' : ''}}" data-index="{{index}}" bindtap="onTabChange">{{item}}</view>
    </block>
  </view>
  <!-- 排序栏 -->
  <view class="fund-sort-bar">
    <view class="sort-btn {{sortType === 'time' ? 'active' : ''}}" data-type="time" bindtap="onSortChange">时间排序<text wx:if="{{sortType === 'time'}}">{{sortOrder === 'desc' ? '↓' : '↑'}}</text></view>
    <view class="sort-btn {{sortType === 'amount' ? 'active' : ''}}" data-type="amount" bindtap="onSortChange">金额排序<text wx:if="{{sortType === 'amount'}}">{{sortOrder === 'desc' ? '↓' : '↑'}}</text></view>
    <view class="filter-btn" bindtap="onFilterToggle">筛选</view>
  </view>
  <!-- 资金记录列表 -->
  <view class="fund-record-list">
    <block wx:for="{{fundRecords}}" wx:key="id">
      <view class="fund-record-item">
        <!-- 顶部：凭证号 + 类型标签 + 金额 -->
        <view class="fund-record-row">
          <view class="fund-record-voucher">
            <text class="voucher-no">{{item.voucher_no}}</text>
            <text class="fund-type-tag">{{item.type}}</text>
          </view>
          <text class="fund-record-amount">{{item.amount > 0 ? '+' : ''}}{{item.amount}}</text>
        </view>
        <!-- 时间 -->
        <view class="fund-record-meta">
          <text>{{item.created_at}}</text>
        </view>
        <!-- 事由 -->
        <view class="fund-record-desc">{{item.description}}</view>
      </view>
    </block>
  </view>
</view> 