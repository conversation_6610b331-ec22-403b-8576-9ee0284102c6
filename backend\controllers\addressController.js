/**
 * 地址控制器
 */
const db = require('../config/db');

// 获取默认收货地址
exports.getDefaultAddress = async (req, res) => {
  try {
    const userId = req.userData.userId;
    
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    // 查询用户的默认地址
    const result = await db.query(
      'SELECT * FROM user_addresses WHERE user_id = ? AND is_default = 1 LIMIT 1',
      [userId]
    );

    if (result.length === 0) {
      return res.json({
        success: true,
        data: null,
        message: '暂无默认地址'
      });
    }

    res.json({
      success: true,
      data: result[0],
      message: '获取默认地址成功'
    });
  } catch (error) {
    console.error('获取默认地址失败:', error);
    res.status(500).json({
      success: false,
      message: '获取地址失败',
      error: error.message
    });
  }
};

// 获取地址列表
exports.getAddressList = async (req, res) => {
  try {
    const userId = req.userData.userId;
    
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const result = await db.query(
      'SELECT * FROM user_addresses WHERE user_id = ? ORDER BY is_default DESC, created_at DESC',
      [userId]
    );

    res.json({
      success: true,
      data: result,
      message: '获取地址列表成功'
    });
  } catch (error) {
    console.error('获取地址列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取地址列表失败',
      error: error.message
    });
  }
};

// 添加地址
exports.addAddress = async (req, res) => {
  try {
    const userId = req.userData.userId;
    const { name, phone, province, city, district, detail, isDefault = false } = req.body;
    
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    if (!name || !phone || !province || !city || !district || !detail) {
      return res.status(400).json({ success: false, message: '请填写完整的地址信息' });
    }

    // 如果设置为默认地址，先取消其他默认地址
    if (isDefault) {
      await db.query(
        'UPDATE user_addresses SET is_default = 0 WHERE user_id = ?',
        [userId]
      );
    }

    const now = Date.now();
    const result = await db.query(
      'INSERT INTO user_addresses (user_id, name, phone, province, city, district, detail, is_default, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [userId, name, phone, province, city, district, detail, isDefault ? 1 : 0, now, now]
    );

    res.json({
      success: true,
      data: { id: result.insertId },
      message: '添加地址成功'
    });
  } catch (error) {
    console.error('添加地址失败:', error);
    res.status(500).json({
      success: false,
      message: '添加地址失败',
      error: error.message
    });
  }
};

// 更新地址
exports.updateAddress = async (req, res) => {
  try {
    const userId = req.userData.userId;
    const addressId = req.params.id;
    const { name, phone, province, city, district, detail, isDefault = false } = req.body;
    
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    // 检查地址是否存在且属于当前用户
    const existingAddress = await db.query(
      'SELECT * FROM user_addresses WHERE id = ? AND user_id = ?',
      [addressId, userId]
    );

    if (existingAddress.length === 0) {
      return res.status(404).json({ success: false, message: '地址不存在' });
    }

    // 如果设置为默认地址，先取消其他默认地址
    if (isDefault) {
      await db.query(
        'UPDATE user_addresses SET is_default = 0 WHERE user_id = ?',
        [userId]
      );
    }

    const now = Date.now();
    await db.query(
      'UPDATE user_addresses SET name = ?, phone = ?, province = ?, city = ?, district = ?, detail = ?, is_default = ?, updated_at = ? WHERE id = ? AND user_id = ?',
      [name, phone, province, city, district, detail, isDefault ? 1 : 0, now, addressId, userId]
    );

    res.json({
      success: true,
      message: '更新地址成功'
    });
  } catch (error) {
    console.error('更新地址失败:', error);
    res.status(500).json({
      success: false,
      message: '更新地址失败',
      error: error.message
    });
  }
};

// 删除地址
exports.deleteAddress = async (req, res) => {
  try {
    const userId = req.userData.userId;
    const addressId = req.params.id;
    
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const result = await db.query(
      'DELETE FROM user_addresses WHERE id = ? AND user_id = ?',
      [addressId, userId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: '地址不存在' });
    }

    res.json({
      success: true,
      message: '删除地址成功'
    });
  } catch (error) {
    console.error('删除地址失败:', error);
    res.status(500).json({
      success: false,
      message: '删除地址失败',
      error: error.message
    });
  }
};

// 设置默认地址
exports.setDefaultAddress = async (req, res) => {
  try {
    const userId = req.userData.userId;
    const addressId = req.params.id;
    
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    // 检查地址是否存在且属于当前用户
    const existingAddress = await db.query(
      'SELECT * FROM user_addresses WHERE id = ? AND user_id = ?',
      [addressId, userId]
    );

    if (existingAddress.length === 0) {
      return res.status(404).json({ success: false, message: '地址不存在' });
    }

    // 先取消所有默认地址
    await db.query(
      'UPDATE user_addresses SET is_default = 0 WHERE user_id = ?',
      [userId]
    );

    // 设置新的默认地址
    await db.query(
      'UPDATE user_addresses SET is_default = 1 WHERE id = ? AND user_id = ?',
      [addressId, userId]
    );

    res.json({
      success: true,
      message: '设置默认地址成功'
    });
  } catch (error) {
    console.error('设置默认地址失败:', error);
    res.status(500).json({
      success: false,
      message: '设置默认地址失败',
      error: error.message
    });
  }
}; 