/* pages/order/logistics.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 加载中 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  height: 300rpx;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 26rpx;
  color: #999;
}

/* 物流状态 */
.logistics-status {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-left {
  flex: 1;
}

.express-company {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.tracking-number-container {
  display: flex;
  align-items: center;
}

.tracking-number {
  font-size: 26rpx;
  color: #666;
}

.copy-btn {
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #ff6b00;
  padding: 4rpx 12rpx;
  border: 1rpx solid #ff6b00;
  border-radius: 20rpx;
}

.status-right {
  margin-left: 20rpx;
}

.express-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}

.status-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f5f5f5;
}

.status-text {
  font-size: 28rpx;
  color: #ff6b00;
  font-weight: 500;
}

.refresh-btn {
  font-size: 26rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.refresh-btn::before {
  content: '';
  display: inline-block;
  width: 28rpx;
  height: 28rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iIzk5OTk5OSI+PHBhdGggZD0iTTEyIDVWMUw3IDZsNSA1VjdjMy4zMSAwIDYgMi42OSA2IDZzLTIuNjkgNi02IDYtNi0yLjY5LTYtNkg0YzAgNC40MiAzLjU4IDggOCA4czgtMy41OCA4LTgtMy41OC04LTgtOHoiLz48L3N2Zz4=');
  background-size: cover;
  margin-right: 6rpx;
}

/* 收货信息 */
.address-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: flex-start;
}

.address-icon {
  margin-right: 20rpx;
  margin-top: 6rpx;
}

.address-icon image {
  width: 40rpx;
  height: 40rpx;
}

.address-content {
  flex: 1;
}

.address-user {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.address-user .name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 20rpx;
}

.address-user .phone {
  font-size: 26rpx;
  color: #666;
}

.address-detail {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 物流轨迹 */
.logistics-timeline {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.timeline-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 20rpx;
}

.timeline-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 24rpx;
  background-color: #ff6b00;
  border-radius: 3rpx;
}

.timeline-list {
  padding: 10rpx 0 10rpx 30rpx;
}

.timeline-item {
  position: relative;
  padding-bottom: 40rpx;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -30rpx;
  top: 10rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ddd;
  z-index: 2;
}

.timeline-line {
  position: absolute;
  left: -23rpx;
  top: 20rpx;
  width: 2rpx;
  height: calc(100% - 10rpx);
  background-color: #eee;
  z-index: 1;
}

.timeline-content {
  padding-left: 20rpx;
}

.timeline-info {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
}

.timeline-item.active .timeline-dot {
  background-color: #ff6b00;
  width: 20rpx;
  height: 20rpx;
  left: -32rpx;
  top: 8rpx;
}

.timeline-item.active .timeline-info {
  color: #ff6b00;
  font-weight: 500;
}

.empty-timeline {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.empty-timeline .empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.empty-timeline .empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部操作按钮 */
.footer-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 0;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.action-btn text {
  font-size: 26rpx;
  color: #666;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}