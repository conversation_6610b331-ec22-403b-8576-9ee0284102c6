/* pages/new-products/new-products.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* ==================== 搜索栏 ==================== */
.search-bar {
  background-color: #fff;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 12px;
}

.search-icon {
  margin-right: 8px;
}

.search-input {
  flex: 1;
  font-size: 14px;
  color: #333;
}

/* ==================== 筛选栏 ==================== */
.filter-bar {
  display: flex;
  background-color: #fff;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-right: 24px;
  padding: 4px 8px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.filter-item.active {
  background-color: #007aff;
  color: #fff;
}

.filter-item text {
  font-size: 14px;
  color: #666;
}

.filter-item.active text {
  color: #fff;
}

.sort-icon {
  margin-left: 4px;
  font-size: 12px;
  opacity: 0.6;
}

.sort-icon.active {
  opacity: 1;
}

/* ==================== 商品列表 ==================== */
.product-list {
  padding: 0 16px;
}

.product-item {
  display: flex;
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}

.product-item:active {
  transform: scale(0.98);
}

/* 商品图片 */
.product-image-wrapper {
  position: relative;
  width: 100px;
  height: 100px;
  margin-right: 12px;
  flex-shrink: 0;
}

.product-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background-color: #f8f8f8;
}

.new-tag {
  position: absolute;
  top: 4px;
  left: 4px;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: bold;
  box-shadow: 0 1px 3px rgba(255, 107, 107, 0.3);
}

/* 商品信息 */
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.3;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 价格信息 */
.price-wrapper {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
}

.current-price {
  font-size: 18px;
  font-weight: bold;
  color: #ff4757;
  margin-right: 8px;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

/* 商品标签 */
.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  font-size: 10px;
  color: #666;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 8px;
  line-height: 1;
}

/* ==================== 加载状态 ==================== */
.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-text {
  font-size: 14px;
  color: #999;
}

/* ==================== 没有更多 ==================== */
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: #999;
  font-size: 12px;
}

/* ==================== 空状态 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.empty-icon {
  width: 80px;
  height: 80px;
  opacity: 0.3;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}