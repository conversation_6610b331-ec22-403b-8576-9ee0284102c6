const loginStateManager = require('../../utils/login-state-manager');
const { dashboardApi } = require('../../utils/api');

Page({
  data: {
    userInfo: null,
    identityLabel: '',
    showRoleSwitch: false,
    adminLevel: '',
    // 新增统计数据
    storeCount: 0,
    partnerCount: 0,
    userCount: 0,
    totalSales: 0,
    // 徽标数据
    kuaidiPending: 0,
    partnerApplicationPending: 0,
    userWithdrawPending: 0
  },
  onLoad() {
    // 热重载或页面加载时恢复全局登录状态
    loginStateManager.restoreLoginStateToGlobal();
    this.loadUserInfo();
    this.loadDashboardStats();
  },
  onShow() {
    // 页面显示时也恢复全局登录状态，防止切换tab后丢失
    loginStateManager.restoreLoginStateToGlobal();
    this.loadUserInfo();
    this.loadDashboardStats();
    this.loadDashboardBadges();
  },
  loadUserInfo() {
    const app = getApp();
    const globalData = app.globalData || {};
    let adminLevel = '';
    let identityLabel = '';
    let showRoleSwitch = false;
    // 优先用全局
    if (globalData.isLogin && globalData.userInfo) {
      // 只取管理员身份
      if (globalData.userRoles && Array.isArray(globalData.userRoles)) {
        const adminRole = globalData.userRoles.find(r => r.role_type === 'admin');
        if (adminRole) {
          adminLevel = adminRole.role_name || '管理员';
          identityLabel = adminRole.role_name || '管理员';
        }
        showRoleSwitch = globalData.userRoles.length > 1;
      } else if (globalData.currentRole && globalData.currentRole.role_type === 'admin') {
        adminLevel = globalData.currentRole.role_name || '管理员';
        identityLabel = globalData.currentRole.role_name || '管理员';
      } else {
        adminLevel = globalData.userInfo.role_name || '管理员';
        identityLabel = globalData.userInfo.role_name || '管理员';
      }
      this.setData({
        userInfo: globalData.userInfo,
        identityLabel,
        showRoleSwitch,
        adminLevel
      });
      return;
    }
    // 本地存储
    const loginState = loginStateManager.getLoginState();
    const userInfo = wx.getStorageSync('userInfo');
    if (loginState && loginState.isLogin && userInfo) {
      let adminLevel = '';
      let identityLabel = '';
      let showRoleSwitch = false;
      if (userInfo.roles && Array.isArray(userInfo.roles)) {
        const adminRole = userInfo.roles.find(r => r.role_type === 'admin');
        if (adminRole) {
          adminLevel = adminRole.role_name || '管理员';
          identityLabel = adminRole.role_name || '管理员';
        }
        showRoleSwitch = userInfo.roles.length > 1;
      } else {
        adminLevel = userInfo.role_name || '管理员';
        identityLabel = userInfo.role_name || '管理员';
      }
      this.setData({
        userInfo,
        identityLabel,
        showRoleSwitch,
        adminLevel
      });
      return;
    }
    // 未登录
    this.setData({ userInfo: null, identityLabel: '', showRoleSwitch: false, adminLevel: '' });
  },
  // 新增：加载统计数据
  loadDashboardStats() {
    dashboardApi.getDashboardSummary().then(res => {
      if (res.success && res.data) {
        this.setData({
          storeCount: res.data.storeCount || 0,
          partnerCount: res.data.partnerCount || 0,
          userCount: res.data.userCount || 0,
          totalSales: res.data.totalSales || 0
        });
      } else {
        wx.showToast({ title: '统计数据获取失败', icon: 'none' });
      }
    }).catch(() => {
      wx.showToast({ title: '统计数据获取失败', icon: 'none' });
    });
  },
  
  // 新增：加载徽标数据
  loadDashboardBadges() {
    dashboardApi.getDashboardBadges().then(res => {
      if (res.success && res.data) {
        this.setData({
          kuaidiPending: res.data.kuaidiPending || 0,
          partnerApplicationPending: res.data.partnerApplicationPending || 0,
          userWithdrawPending: res.data.userWithdrawPending || 0
        });
      } else {
        console.log('徽标数据获取失败');
      }
    }).catch(() => {
      console.log('徽标数据获取失败');
    });
  },
  onGoSwitchLoginPage() {
    wx.navigateTo({ url: '/pages/switch-login/switch-login' });
  },

  goToProducts() {
    wx.redirectTo({ url: '/admin/products/products' });
  },
  goToUsers() {
    wx.redirectTo({ url: '/admin/users/users' });
  },
  // 订单管理模块跳转
  goToOrders(e) {
    const key = e.currentTarget.dataset.key;
    wx.navigateTo({ url: `/admin/orders/orders?tab=${key}` });
  },
  
  // 跳转到合伙人申请管理页面
  goToPartnerApplications() {
    wx.navigateTo({ url: '/admin/partner-applications/partner-applications' });
  }
});