/**
 * 统一登录状态检查模块
 * 用于处理各种需要登录的场景，包括状态检查、提示、跳转、登录后返回等功能
 */

const { validateLoginState } = require('./login-state-manager');

// 需要登录的页面路径映射
const LOGIN_REQUIRED_PAGES = {

  'pages/points-center/index': '积分中心',
  'pages/member-center/index': '会员中心',
  'pages/customer-service/index': '在线客服',
  'pages/share/index': '立即分享'
};

// 需要登录的操作映射
const LOGIN_REQUIRED_ACTIONS = {
  'follow': '关注'
};

/**
 * 获取当前页面对象
 * @returns {Object|null}
 */
function getCurrentPage() {
  const pages = getCurrentPages();
  return pages.length ? pages[pages.length - 1] : null;
}

/**
 * 检查登录状态并处理
 * @param {Object} options 配置选项
 * @param {String} options.type 检查类型：'page' 或 'action'
 * @param {String} options.target 目标页面路径或操作类型
 * @param {Object} options.params 跳转参数
 * @param {Function} options.success 登录成功后的回调
 * @param {Function} options.fail 登录失败后的回调
 * @returns {Promise} 包含检查结果的Promise
 */
function checkLogin(options = {}) {
  return new Promise((resolve, reject) => {
    const { type, target, params = {}, success, fail } = options;
    
    // 验证参数
    if (!type || !target) {
      console.error('检查登录状态失败: 缺少必要参数');
      reject(new Error('缺少必要参数'));
      return;
    }

    // 获取操作或页面的中文名称
    let targetName = '';
    if (type === 'page') {
      targetName = LOGIN_REQUIRED_PAGES[target] || target;
    } else if (type === 'action') {
      targetName = LOGIN_REQUIRED_ACTIONS[target] || target;
    }

    // 验证登录状态
    validateLoginState().then(result => {
      if (result.isValid) {
        // 登录状态有效，执行成功回调
        if (typeof success === 'function') {
          success(result);
        }
        resolve(result);
      } else {
        // 登录状态无效，显示提示并跳转到登录页
        wx.showModal({
          title: '提示',
          content: `请先登录后再${type === 'page' ? '进入' : ''}${targetName}`,
          confirmText: '去登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 获取当前页面对象
              const currentPage = getCurrentPage();
              const currentPath = currentPage ? currentPage.route : '';
              const currentParams = currentPage ? currentPage.options : {};
              
              // 构建登录后返回的路径
              const returnPath = type === 'page' ? target : currentPath;
              let returnParams = type === 'page' ? params : currentParams;
              

              
              // 保存返回信息
              wx.setStorageSync('login_return_info', {
                path: returnPath,
                params: returnParams,
                type: type,
                target: target
              });

              // 跳转到登录页
              wx.navigateTo({
                url: '/pages/auth/auth',
                fail: (err) => {
                  console.error('跳转到登录页失败:', err);
                  if (typeof fail === 'function') {
                    fail(err);
                  }
                  reject(err);
                }
              });
            } else {
              if (typeof fail === 'function') {
                fail(new Error('用户取消登录'));
              }
              reject(new Error('用户取消登录'));
            }
          }
        });
      }
    }).catch(err => {
      console.error('检查登录状态出错:', err);
      if (typeof fail === 'function') {
        fail(err);
      }
      reject(err);
    });
  });
}

/**
 * 处理登录后的返回
 * @param {Object} loginResult 登录结果
 */
function handleLoginReturn(loginResult) {
  try {
    const returnInfo = wx.getStorageSync('login_return_info');
    if (!returnInfo) {
      console.log('没有需要返回的页面信息');
      return;
    }

    // 清除返回信息
    wx.removeStorageSync('login_return_info');

    const { path, params, type, target } = returnInfo;
    
    // 构建跳转参数
    let url = `/${path}`;
    if (Object.keys(params).length > 0) {
      const queryString = Object.entries(params)
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&');
      url += `?${queryString}`;
    }

    // 执行跳转
    wx.navigateTo({
      url: url,
      fail: (err) => {
        console.error('返回目标页面失败:', err);
        // 如果跳转失败，尝试使用redirectTo
        wx.redirectTo({
          url: url,
          fail: (redirectErr) => {
            console.error('重定向到目标页面也失败:', redirectErr);
          }
        });
      }
    });
  } catch (error) {
    console.error('处理登录返回出错:', error);
  }
}

module.exports = {
  checkLogin,
  handleLoginReturn,
  LOGIN_REQUIRED_PAGES,
  LOGIN_REQUIRED_ACTIONS
};