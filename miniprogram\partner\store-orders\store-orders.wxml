<!-- partner/store-orders/store-orders.wxml -->
<!-- 合伙人端门店订单页面 -->
<view class="store-orders-page">
  <!-- 门店选择区域 -->
  <view class="store-select-section">
    <view class="store-select-header">
      <view class="section-title">当前门店</view>
      <view class="store-select-container">
        <picker class="store-picker" 
                mode="selector" 
                range="{{storeList}}" 
                range-key="name" 
                bindchange="onStoreChange" 
                wx:if="{{storeList.length > 0}}">
          <view class="store-selector">
            <text class="store-name">{{selectedStore.name}}（{{selectedStore.store_no}}）</text>
            <view class="dropdown-arrow">▼</view>
          </view>
        </picker>
        <view class="store-selector" wx:else>
          <text class="store-name">暂无门店数据</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-input-wrapper">
      <image class="search-icon" src="/images/icons2/搜索.png"></image>
      <input class="search-input" 
             placeholder="搜索订单" 
             placeholder-class="search-placeholder"
             value="{{searchKeyword}}"
             bindinput="onSearchInput"
             bindconfirm="onSearchConfirm" />
    </view>
  </view>

  <!-- 排序筛选栏 -->
  <view class="sort-filter-bar">
    <!-- 日期排序 -->
    <view class="sort-item {{currentSort === 'date' ? 'active' : ''}}" 
          bindtap="onSortChange" 
          data-sort="date">
      <text>日期</text>
      <image wx:if="{{currentSort === 'date'}}" class="sort-icon" src="/images/icons2/{{currentSortDirection === 'desc' ? '降序' : '升序'}}.svg"></image>
    </view>
    
    <!-- 金额排序 -->
    <view class="sort-item {{currentSort === 'amount' ? 'active' : ''}}" 
          bindtap="onSortChange" 
          data-sort="amount">
      <text>金额</text>
      <image wx:if="{{currentSort === 'amount'}}" class="sort-icon" src="/images/icons2/{{currentSortDirection === 'desc' ? '降序' : '升序'}}.svg"></image>
    </view>
    
    <!-- 筛选按钮 -->
    <view class="filter-btn" bindtap="onShowFilter">
      <text>筛选</text>
      <image class="filter-icon" src="/images/icons2/筛选.svg"></image>
    </view>
  </view>

  <!-- 订单分类标签栏 -->
  <view class="order-type-tabs">
    <view class="tabs-container">
      <block wx:for="{{orderTypes}}" wx:key="key">
        <view 
          class="tab-item {{currentOrderType === item.key ? 'active' : ''}}" 
          bindtap="switchOrderType" 
          data-type="{{item.key}}"
        >
          <text>{{item.name}}</text>
          <view class="tab-line" wx:if="{{currentOrderType === item.key}}"></view>
        </view>
      </block>
    </view>
  </view>

  <!-- 二级分类标签栏 -->
  <view class="sub-type-tabs" wx:if="{{currentOrderType === 'purchase'}}">
    <view class="tabs-container">
      <block wx:for="{{purchaseSubTypes}}" wx:key="key">
        <view 
          class="sub-tab-item {{currentSubType === item.key ? 'active' : ''}}" 
          bindtap="switchSubType" 
          data-subtype="{{item.key}}"
        >
          <text>{{item.name}}</text>
          <view class="tab-line" wx:if="{{currentSubType === item.key}}"></view>
        </view>
      </block>
    </view>
  </view>
  
  <view class="sub-type-tabs" wx:if="{{currentOrderType === 'transfer'}}">
    <view class="tabs-container">
      <block wx:for="{{transferSubTypes}}" wx:key="key">
        <view 
          class="sub-tab-item {{currentSubType === item.key ? 'active' : ''}}" 
          bindtap="switchSubType" 
          data-subtype="{{item.key}}"
        >
          <text>{{item.name}}</text>
          <view class="tab-line" wx:if="{{currentSubType === item.key}}"></view>
        </view>
      </block>
    </view>
  </view>

  <!-- 订单列表 -->
  <view class="order-list-container">
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{loading && orderList.length === 0}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{!loading && isEmpty}}">
      <image class="empty-icon" src="/images/icons2/空订单.png" mode="aspectFit"></image>
      <view class="empty-text">暂无订单</view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list" wx:else>
      <block wx:for="{{orderList}}" wx:key="id">
        <view class="order-item" bindtap="viewOrderDetail" data-id="{{item.id}}" data-type="{{item.order_type}}">
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-type-tag {{item.order_type === 'purchase' ? 'purchase' : 'transfer'}}">
              {{item.order_type === 'purchase' ? '采购订单' : '移库订单'}}
            </view>
            <view class="order-info">
              <view class="order-no">订单号: {{item.order_no}}</view>
              <view class="order-date">下单时间: {{item.created_at}}</view>
              <view class="order-store">门店: {{item.store_name}}({{item.store_no}})</view>
              <view class="order-user">操作人: {{item.operator_name}}</view>
            </view>
            <view class="order-status">
              <!-- 采购订单状态 -->
              <block wx:if="{{item.order_type === 'purchase'}}">
                <text class="status-text {{item.status}}">{{item.status_text}}</text>
                <view class="status-time" wx:if="{{item.status === 'reviewed' && item.review_time}}">
                  审核时间: {{item.review_time}}
                </view>
              </block>
              <!-- 移库订单状态 -->
              <block wx:else>
                <text class="status-text {{item.status}}">{{item.status_text}}</text>
                <view class="logistics-status" wx:if="{{item.logistics_status}}">
                  物流状态: {{item.logistics_status}}
                </view>
              </block>
            </view>
          </view>

          <!-- 商品列表 -->
          <view class="product-list">
            <block wx:for="{{item.items}}" wx:for-item="product" wx:key="id">
              <view class="product-item">
                <image class="product-image" src="{{product.product_image || '/images/icons2/默认商品.png'}}" mode="aspectFit"></image>
                <view class="product-info">
                  <view class="product-name">{{product.product_name}}</view>
                  <view class="product-specs" wx:if="{{product.specs}}">规格: {{product.specs}}</view>
                  <view class="product-price-qty">
                    <view class="product-price-info">
                      <text class="product-price-label">采购价:</text>
                      <text class="product-price">¥{{product.price}}</text>
                    </view>
                    <view class="product-qty-info">
                      <text class="product-qty-label">数量:</text>
                      <text class="product-qty">{{product.quantity}}</text>
                    </view>
                    <view class="product-amount-info">
                      <text class="product-amount-label">金额:</text>
                      <text class="product-amount">¥{{product.amount || '0.00'}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </block>
            
            <!-- 更多商品提示 -->
            <view class="more-products" wx:if="{{item.items_count > item.items.length}}">
              <text>共{{item.items_count}}件商品</text>
            </view>
          </view>

          <!-- 订单金额 -->
          <view class="order-total">
            <view class="total-row">
              <text class="total-label">总金额</text>
              <text class="total-amount">¥{{item.total_amount}}</text>
            </view>
          </view>
          
          <!-- 订单操作按钮 -->
          <view class="order-actions">
            <block wx:if="{{item.order_type === 'purchase'}}">
              <button class="action-btn" wx:if="{{item.status === 'pending_review'}}">审核订单</button>
              <button class="action-btn" wx:if="{{item.status === 'reviewed'}}">查看详情</button>
            </block>
            <block wx:else>
              <button class="action-btn" wx:if="{{item.status === 'pending_shipment'}}">发货</button>
              <button class="action-btn" wx:if="{{item.status === 'shipped'}}">确认到店</button>
              <button class="action-btn" wx:if="{{item.status === 'received'}}">查看详情</button>
            </block>
          </view>
        </view>
      </block>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{loading && orderList.length > 0}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && orderList.length > 0}}">
      <text>没有更多订单了</text>
    </view>
  </view>

  <!-- 筛选抽屉 -->
  <view class="filter-drawer {{showFilterDrawer ? 'show' : ''}}">
    <view class="filter-drawer-mask" bindtap="onHideFilter"></view>
    <view class="filter-drawer-content">
      <view class="filter-drawer-header">
        <text class="filter-drawer-title">筛选</text>
        <view class="filter-drawer-close" bindtap="onHideFilter">
          <image class="close-icon" src="/images/icons2/关闭.svg"></image>
        </view>
      </view>
      
      <view class="filter-drawer-body">
        <!-- 日期筛选 -->
        <view class="filter-section">
          <view class="filter-section-title">日期范围</view>
          <view class="date-range-inputs">
            <picker mode="date" bindchange="onDateChange" data-field="start">
              <view class="date-input">
                <text wx:if="{{!filterOptions.dateRange[0]}}">开始日期</text>
                <text wx:else>{{filterOptions.dateRange[0]}}</text>
              </view>
            </picker>
            <text class="date-range-separator">-</text>
            <picker mode="date" bindchange="onDateChange" data-field="end">
              <view class="date-input">
                <text wx:if="{{!filterOptions.dateRange[1]}}">结束日期</text>
                <text wx:else>{{filterOptions.dateRange[1]}}</text>
              </view>
            </picker>
          </view>
        </view>
        
        <!-- 商品筛选 -->
        <view class="filter-section">
          <view class="filter-section-title">商品名称</view>
          <view class="product-keyword-input">
            <input 
              placeholder="输入商品名称关键词" 
              value="{{filterOptions.productKeyword}}"
              bindinput="onProductKeywordInput"
            />
          </view>
        </view>
      </view>
      
      <view class="filter-drawer-footer">
        <button class="reset-btn" bindtap="onResetFilter">重置</button>
        <button class="apply-btn" bindtap="onApplyFilter">确定</button>
      </view>
    </view>
  </view>
</view> 