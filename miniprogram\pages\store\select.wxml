<view class="container">
  <!-- 门店列表 -->
  <view class="store-list">
    <view class="store-item" 
          wx:for="{{storeList}}" 
          wx:key="id"
          bindtap="selectStore"
          data-store="{{item}}">
      <!-- 门店形象照 -->
      <view class="store-image">
        <image src="{{item.image || '/images/icons/location.png'}}" mode="aspectFill"></image>
      </view>
      
      <!-- 门店信息 -->
      <view class="store-info">
        <view class="store-name">{{item.name}}</view>
        <view class="store-no" wx:if="{{item.store_no}}">
          <text class="no-icon">🏪</text>
          <text>{{item.store_no}}</text>
        </view>
        <view class="store-address">
          <text class="address-icon">📍</text>
          <text>{{item.province}}{{item.city}}{{item.district}}{{item.address || ''}}</text>
        </view>
        <view class="store-hours" wx:if="{{item.business_hours}}">
          <text class="time-icon">🕒</text>
          <text>{{item.business_hours}}</text>
        </view>
        <view class="store-phone" wx:if="{{item.phone}}">
          <text class="phone-icon">📞</text>
          <text>{{item.phone}}</text>
        </view>
      </view>
      
      <!-- 营业状态 -->
      <view class="store-status">
        <view class="status-icon {{item.status === 'open' ? 'open' : 'closed'}}">
          {{item.status === 'open' ? '营业中' : '已关闭'}}
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{storeList.length === 0}}">
    <image class="empty-icon" src="/images/icons/empty.png"></image>
    <view class="empty-text">暂无可用门店</view>
    <view class="empty-desc">请联系您的销售人添加门店</view>
  </view>
</view>