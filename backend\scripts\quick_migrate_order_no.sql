-- 快速迁移脚本：为orders表添加order_no字段
-- 适用于微信云托管环境

-- 1. 添加order_no字段（如果不存在）
ALTER TABLE orders ADD COLUMN IF NOT EXISTS order_no VARCHAR(50) NULL COMMENT '订单号' AFTER id;

-- 2. 添加索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_order_no ON orders (order_no);

-- 3. 为现有订单生成订单号
-- 顾客订单编号规则：字母"XS"+年月日时分（12位数）+顺序号（4位数）
UPDATE orders 
SET order_no = CONCAT(
    'XS',
    DATE_FORMAT(FROM_UNIXTIME(created_at/1000), '%Y%m%d%H%i'),
    LPAD(id % 10000, 4, '0')
)
WHERE order_no IS NULL OR order_no = '';

-- 4. 验证结果
SELECT 
    COUNT(*) as total_orders,
    COUNT(order_no) as orders_with_no,
    COUNT(*) - COUNT(order_no) as orders_without_no
FROM orders;

-- 5. 显示最近的订单号示例
SELECT id, order_no, status, created_at 
FROM orders 
ORDER BY created_at DESC 
LIMIT 5;
