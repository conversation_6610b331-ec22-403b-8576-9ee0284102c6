<view class="create-group-container">
  <view class="cover-upload">
    <image class="cover-image" src="{{coverUrl}}" mode="aspectFill" bindtap="onChooseCover" />
    <view class="cover-tip">点击更换群封面</view>
  </view>
  <view class="form-item">
    <text class="form-label">群名称</text>
    <input class="form-input" placeholder="请输入群名称" maxlength="20" value="{{groupName}}" bindinput="onGroupNameInput" />
  </view>
  <view class="form-item">
    <text class="form-label">群简介</text>
    <textarea class="form-textarea" placeholder="请输入群简介（可选）" maxlength="100" value="{{groupDesc}}" bindinput="onGroupDescInput" />
  </view>
  <view class="form-item switch-item">
    <text class="form-label">公开可见</text>
    <switch checked="{{visible}}" bindchange="onVisibleChange" />
    <text class="switch-desc">打开后，任何用户可在全部群聊页看到</text>
  </view>
  <view class="form-item switch-item">
    <text class="form-label">加入审批</text>
    <switch checked="{{needApprove}}" bindchange="onApproveChange" />
    <text class="switch-desc">打开后，进群需群主审批</text>
  </view>
  <button class="submit-btn" bindtap="onSubmit">创建群聊</button>
</view> 