/**
 * 购物车控制器
 */
const cartService = require('../services/cartService');

exports.getCart = async (req, res, next) => {
  try {
    const userId = req.userData && req.userData.userId;
    console.log('【购物车控制器】获取购物车 - userId:', userId, 'userId类型:', typeof userId);
    console.log('【购物车控制器】req.userData:', req.userData);
    
    if (!userId) {
      console.error('【购物车控制器】userId为空，token解析失败或未登录');
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }
    
    // 确保userId是字符串格式，与数据库中的格式保持一致
    const normalizedUserId = String(userId);
    console.log('【购物车控制器】标准化后的userId:', normalizedUserId);
    
    const result = await cartService.getCart(normalizedUserId);
    console.log('【购物车控制器】购物车服务返回结果:', result);
    if (!result.success) {
      console.error('【购物车控制器】购物车服务返回错误:', result.message, result.error);
      return res.status(400).json(result);
    }
    res.json(result);
  } catch (error) {
    console.error('【购物车控制器】获取购物车控制器错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
};

exports.getCartItemsByIds = async (req, res, next) => {
  try {
    const userId = req.userData && req.userData.userId;
    const { ids } = req.query;
    
    console.log('【购物车控制器】根据ID获取购物车商品 - userId:', userId, 'ids:', ids);
    
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录或身份失效，请重新登录' });
    }
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ success: false, message: '请提供有效的商品ID列表' });
    }
    
    // 确保userId是字符串格式
    const normalizedUserId = String(userId);
    
    const result = await cartService.getCartItemsByIds(normalizedUserId, ids);
    
    if (!result.success) {
      return res.status(400).json(result);
    }
    
    res.json(result);
  } catch (error) {
    console.error('【购物车控制器】根据ID获取购物车商品失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
};

exports.addToCart = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const { productId, quantity } = req.body;
    
    console.log('添加到购物车 - 用户ID:', userId, '商品ID:', productId, '数量:', quantity);
    
    // 参数验证
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }

    if (quantity && (isNaN(quantity) || quantity < 1)) {
      return res.status(400).json({
        success: false,
        message: '商品数量必须大于0'
      });
    }
    
    // 确保userId是字符串格式
    const normalizedUserId = String(userId);
    
    const result = await cartService.addToCart(normalizedUserId, productId, quantity);
    console.log('添加到购物车结果:', result);
    
    if (!result.success) {
      return res.status(400).json(result);
    }
    
    res.status(201).json(result);
  } catch (error) {
    console.error('添加到购物车失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试',
      error: error.message
    });
  }
};

exports.updateCart = async (req, res, next) => {
  try {
    const { quantity } = req.body;
    const cartId = req.params.id;
    
    console.log('更新购物车 - 购物车ID:', cartId, '数量:', quantity);
    
    const result = await cartService.updateCart(cartId, quantity);
    
    if (!result.success) {
      return res.status(404).json(result);
    }
    
    res.json(result);
  } catch (error) {
    console.error('更新购物车失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试',
      error: error.message
    });
  }
};

exports.removeFromCart = async (req, res, next) => {
  try {
    const cartId = req.params.id;
    console.log('删除购物车商品 - 购物车ID:', cartId);
    
    const result = await cartService.removeFromCart(cartId);
    
    if (!result.success) {
      return res.status(404).json(result);
    }
    
    res.json(result);
  } catch (error) {
    console.error('删除购物车商品失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试',
      error: error.message
    });
  }
};

exports.clearCart = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    console.log('清空购物车 - 用户ID:', userId);
    
    // 确保userId是字符串格式
    const normalizedUserId = String(userId);
    
    const result = await cartService.clearCart(normalizedUserId);
    res.json(result);
  } catch (error) {
    console.error('清空购物车失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试',
      error: error.message
    });
  }
};

exports.getCartCount = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    console.log('获取购物车数量 - 用户ID:', userId);
    
    // 确保userId是字符串格式
    const normalizedUserId = String(userId);
    
    const result = await cartService.getCartCount(normalizedUserId);
    res.json(result);
  } catch (error) {
    console.error('获取购物车数量失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试',
      error: error.message
    });
  }
};

exports.batchRemoveFromCart = async (req, res, next) => {
  try {
    const { ids } = req.body;
    console.log('批量删除购物车商品 - 商品ID列表:', ids);
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的商品ID列表'
      });
    }
    
    const result = await cartService.batchRemoveFromCart(ids);
    
    if (!result.success) {
      return res.status(400).json(result);
    }
    
    res.json(result);
  } catch (error) {
    console.error('批量删除购物车商品失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试',
      error: error.message
    });
  }
};
