.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 表单区域 */
.form-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  z-index: 10;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 160rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.input {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.textarea {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  min-height: 80rpx;
  line-height: 1.5;
}

/* 地区选择器样式 */
.picker-view {
  flex: 1;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.region-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  flex: 1;
  word-break: break-all;
}

.region-placeholder {
  font-size: 30rpx;
  color: #999;
  flex: 1;
}

.arrow {
  font-size: 24rpx;
  color: #ccc;
  margin-left: 20rpx;
}

/* 设置选项 */
.options-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
}

.option-label {
  font-size: 30rpx;
  color: #333;
}

.switch {
  transform: scale(0.8);
}

/* 删除按钮 */
.delete-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.delete-btn {
  text-align: center;
  padding: 30rpx;
  font-size: 30rpx;
  color: #e74c3c;
}

/* 保存按钮 */
.save-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.save-btn {
  background: #e74c3c;
  color: #fff;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
} 