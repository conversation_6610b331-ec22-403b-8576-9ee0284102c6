/* 合伙人端合伙人页面样式 */
.page-content {
  padding-bottom: 100rpx;
  background: #f7f7f7;
  min-height: 100vh;
}

.profile-header {
  position: relative;
  padding: 32rpx 24rpx 24rpx 24rpx;
  background: #fff;
  color: #333;
  border-bottom: 1rpx solid #eee;
}
.user-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}
.avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 2px solid #fff;
  background: #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
}
.user-info-content {
  flex: 1;
  margin-left: 15px;
}
.user-info-flex {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.user-info-main-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.user-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}
.user-role-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  gap: 4px;
}
.user-role-label {
  font-size: 14px;
  color: #22a2c3;
  margin-bottom: 2px;
}
.switch-role-btn {
  margin-left: 0;
  font-size: 14px;
  padding: 0 16rpx;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 24rpx;
  background: #f5f5f5;
  color: #22a2c3;
  border: 1px solid #22a2c3;
}
.user-id {
  font-size: 14px;
  color: #666;
}
.user-id-bottom {
  margin-top: 4px;
}
.user-info-align-center {
  align-items: center;
}
.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}
.login-text {
  font-size: 16px;
  margin: 10px 0;
  color: #333;
}
.login-btn {
  padding: 6px 20px;
  background-color: #22a2c3;
  color: #fff;
  border-radius: 18px;
  font-size: 14px;
  font-weight: bold;
}

/* 账户数据卡片、门店推荐、订单管理、门店管理、其他功能等原有样式保留 */
.account-data.float-card {
  position: relative;
  z-index: 10;
  margin: -20rpx 24rpx 16rpx 24rpx;
  box-shadow: 0 8rpx 32rpx #0002;
  border-radius: 24rpx;
  background: #fff;
}
.account-data {
  display: flex;
  overflow: hidden;
}
.data-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  border-right: 1rpx solid #f0f0f0;
}
.data-item:last-child {
  border-right: none;
}
.value {
  font-size: 32rpx;
  font-weight: bold;
  color: #22a2c3;
}
.label {
  font-size: 22rpx;
  color: #888;
  margin-top: 8rpx;
}

.store-recommend {
  display: flex;
  justify-content: space-between;
  margin: 0 24rpx 16rpx 24rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx #0001;
  padding: 20rpx 0;
}
.store, .recommend {
  flex: 1;
  text-align: center;
}
.store .value, .recommend .value {
  color: #22a2c3;
  font-size: 28rpx;
  font-weight: bold;
  margin-top: 8rpx;
}

.order-manage {
  background: #fff;
  border-radius: 24rpx;
  margin: 0 24rpx 16rpx 24rpx;
  box-shadow: 0 2rpx 8rpx #0001;
  padding: 24rpx 0 8rpx 0;
}
.order-title {
  font-size: 26rpx;
  font-weight: bold;
  margin-left: 32rpx;
  margin-bottom: 12rpx;
}
.order-list {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
  padding: 0 12rpx;
}
.order-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-width: 60rpx;
}
.order-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 6rpx;
  display: block;
}
.order-label {
  font-size: 22rpx;
  color: #333;
  text-align: center;
  margin-top: 0;
}
.order-all { display: none; }

.store-manage {
  background: #fff;
  border-radius: 24rpx;
  margin: 0 24rpx 16rpx 24rpx;
  box-shadow: 0 2rpx 8rpx #0001;
  padding: 24rpx 0 8rpx 0;
}

.store-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 16rpx;
}

.store-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.store-picker {
  flex: 0 0 auto;
  margin-left: auto;
}

.store-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f8f8;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  min-width: 200rpx;
  border: 1rpx solid #e0e0e0;
}

.store-name {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  text-align: right;
  margin-right: 8rpx;
}

.dropdown-arrow {
  font-size: 20rpx;
  color: #999;
  font-weight: bold;
}

.store-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  padding: 0 24rpx 16rpx 24rpx;
}

.store-grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 8rpx;
  border-radius: 12rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.store-grid-item:active {
  background: #f0f0f0;
  transform: scale(0.95);
}

.store-grid-icon {
  width: 66rpx;
  height: 66rpx;
  margin-bottom: 8rpx;
  display: block;
}

.store-grid-label {
  font-size: 22rpx;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

.other-func {
  background: #fff;
  border-radius: 24rpx;
  margin: 0 24rpx 24rpx 24rpx;
  box-shadow: 0 2rpx 8rpx #0001;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  padding: 16rpx 0;
}
.func-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #888;
  font-size: 22rpx;
  margin-bottom: 12rpx;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}
.func-item:active {
  transform: scale(0.95);
}
.func-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 6rpx;
  display: block;
}
.func-text {
  font-size: 20rpx;
  color: #333;
  text-align: center;
  line-height: 1.2;
}
.switch-role-icon {
  width: 20px;
  height: 20px;
  margin-left: 6px;
  vertical-align: middle;
  display: inline-block;
} 