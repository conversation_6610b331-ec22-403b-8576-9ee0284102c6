顾客订单的详细逻辑

一、顾客用户所有的订单，业绩都归属其“销售人”（写入此订单的销售人字段）；
二、顾客用户提交订单时，门店的归属是动态的，按以下顺序逐个拆分：
1、首先划到用户的【订阅门店】，如果【订阅门店】库存充足（与订单库存逐个商品对比），则有库存的部分归属【订阅门店】，超过库存的部分，继续按以下顺序拆分：
2、然后找到”销售人“名下的其他门店，逐一按照库存对比，有库存的部分归属该门店，超出的，再次拆分订单，以此类推
3、当”销售人“名下的所有门店库存都匹配之后，订单量还超出的，全部归属到平台总部（平台总部没有设置库存的，则当作无限量；平台设置了库存的，超过的则无法下单）

举例：
顾客"用户A"的销售人是"用户B"，订阅门店是"门店1#"，此时用户A提交订单为商品P，数量为5件，此时：
1、门店1#的商品P库存为1件，则门店1#拆分一个订单（数量为1件）；
2、销售人“用户B”名下另一个门店2#的商品P库存为2件，则门店2#拆分一个订单（数量为2件）；
3、销售人“用户B”没有其他门店，或者其他门店没有商品P的库存，则不能拆分，此时订单数量还差2件，全部归属平台总部，平台总部拆分一个订单（数量为2件），至此，顾客订单5件拆分完毕。
（总结，顾客订单拆分成了3个分订单，门店1#为1件，门店2#为2件，平台总部为2件；这三个订单的业绩都归属销售人“用户B”）