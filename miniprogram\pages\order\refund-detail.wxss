/* pages/order/refund-detail.wxss */
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 状态区域 */
.status-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.status-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-icon.success {
  background-color: #52c41a;
}

.status-icon.failed {
  background-color: #f5222d;
}

.status-icon.pending {
  background-color: #faad14;
}

.icon-inner {
  color: white;
  font-size: 60rpx;
  font-weight: bold;
}

.status-text {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

/* 信息区域 */
.refund-section, .images-section, .order-status-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  border-left: 8rpx solid #1890ff;
  padding-left: 20rpx;
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.item-label {
  width: 180rpx;
  color: #666;
  font-size: 28rpx;
}

.item-value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
  word-break: break-all;
}

/* 图片区域 */
.image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.evidence-image {
  width: 210rpx;
  height: 210rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}

/* 订单状态 */
.order-status {
  font-size: 30rpx;
  color: #1890ff;
  font-weight: bold;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.action-btn {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.action-btn.refresh {
  background-color: #1890ff;
  color: white;
}

.action-btn.back {
  background-color: #f5f5f5;
  color: #666;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.back-btn {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #1890ff;
  color: white;
  border-radius: 40rpx;
  font-size: 30rpx;
}