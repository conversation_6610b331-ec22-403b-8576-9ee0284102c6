/**
 * 管理员路由
 */
const express = require('express');
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const db = require('../config/db');
const { checkAuth, verifyAdmin } = require('../middleware/auth');
const storeController = require('../controllers/storeController');
const fundRoutes = require('./fund');
const dashboardController = require('../controllers/dashboardController');
const adminController = require('../controllers/adminController');
const bannerController = require('../controllers/bannerController');
const adminMessageController = require('../controllers/adminMessageController');
const adminFaqController = require('../controllers/adminFaqController');

const router = express.Router();

// 微信云托管环境变量优先
const dbConfig = {
  host: process.env.MYSQL_IP || process.env.DB_HOST,
  port: process.env.MYSQL_PORT || 3306,
  user: process.env.MYSQL_USERNAME || process.env.DB_USER,
  database: process.env.MYSQL_DATABASE || process.env.DB_NAME,
  password: (process.env.MYSQL_PASSWORD || process.env.DB_PASSWORD || '').replace(/"/g, '')
};





router.post('/store/create', storeController.createStore);
router.put('/store/update/:id', storeController.updateStore);

// 新增门店列表接口
router.get('/store/list', async (req, res) => {
  try {
    const db = require('../config/db');
    const { search } = req.query;
    let sql = 'SELECT id, store_no, name, level, level_title, province, city, district, address, image, contact_person, phone, create_time, update_time, capital FROM stores';
    let params = [];
    if (search && search.trim()) {
      sql += ' WHERE name LIKE ? OR store_no LIKE ?';
      params.push(`%${search.trim()}%`, `%${search.trim()}%`);
    }
    sql += ' ORDER BY create_time DESC';
    const rows = await db.query(sql, params);
    res.json({ success: true, data: rows });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// 数据统计接口 - 需要管理员权限
router.get('/dashboard/summary', verifyAdmin, dashboardController.getSummary);

// 徽标统计接口 - 需要管理员权限
router.get('/dashboard/badges', verifyAdmin, dashboardController.getBadges);

// 获取退款申请列表
router.get('/refunds', verifyAdmin, adminController.getRefundList);

// 获取退款申请详情
router.get('/refunds/:id', verifyAdmin, adminController.getRefundDetail);

// 处理退款申请
router.post('/refunds/:id/process', verifyAdmin, adminController.processRefund);

router.use('/fund', fundRoutes);

// ==================== 轮播图管理路由 ====================
// 获取轮播图列表
router.get('/banners', bannerController.getBanners);

// 获取单个轮播图
router.get('/banners/:id', bannerController.getBannerById);

// 创建轮播图
router.post('/banners', bannerController.createBanner);

// 更新轮播图
router.put('/banners/:id', bannerController.updateBanner);

// 删除轮播图
router.delete('/banners/:id', bannerController.deleteBanner);

// 更新轮播图状态
router.patch('/banners/:id/status', bannerController.updateBannerStatus);

// 批量更新轮播图排序
router.post('/banners/batch-sort', bannerController.batchUpdateSort);

// 消息管理
router.get('/messages', adminMessageController.getMessages);
router.patch('/messages/:id/block', adminMessageController.toggleMessageBlock);
router.delete('/messages/:id', adminMessageController.deleteMessage);
router.get('/messages/stats', adminMessageController.getMessageStats);

// 常见问题管理
router.get('/faq', adminFaqController.getFaqList);
router.post('/faq', adminFaqController.createFaq);
router.put('/faq/:id', adminFaqController.updateFaq);
router.delete('/faq/:id', adminFaqController.deleteFaq);
router.patch('/faq/:id/status', adminFaqController.updateFaqStatus);
router.get('/faq/stats', adminFaqController.getFaqStats);

module.exports = router;
