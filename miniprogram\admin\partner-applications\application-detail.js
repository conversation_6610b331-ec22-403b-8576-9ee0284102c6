// admin/partner-applications/application-detail.js
const { partnerApi } = require('../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: null,
    application: null,
    loading: true,
    submitting: false,
    remark: '',
    showRemarkInput: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options.id) {
      this.setData({ id: options.id });
      this.loadApplicationDetail();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载申请详情
   */
  loadApplicationDetail: function () {
    const { id } = this.data;
    this.setData({ loading: true });
    
    partnerApi.getApplicationDetail(id)
      .then(res => {
        if (res.success && res.data) {
          this.setData({
            application: res.data,
            loading: false,
            remark: res.data.admin_remark || ''
          });
        } else {
          wx.showToast({
            title: res.message || '加载失败',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      })
      .catch(err => {
        console.error('加载申请详情失败:', err);
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      });
  },

  /**
   * 审核申请
   */
  reviewApplication: function (e) {
    const status = e.currentTarget.dataset.status;
    const { id, remark } = this.data;
    
    // 如果是拒绝且没有填写备注，提示用户填写
    if (status === 'rejected' && !remark.trim()) {
      this.setData({ showRemarkInput: true });
      return;
    }
    
    wx.showModal({
      title: status === 'approved' ? '通过申请' : '拒绝申请',
      content: status === 'approved' ? '确定通过该合伙人申请吗？' : '确定拒绝该合伙人申请吗？',
      success: (res) => {
        if (res.confirm) {
          this.submitReview(status);
        }
      }
    });
  },

  /**
   * 提交审核结果
   */
  submitReview: function (status) {
    const { id, remark } = this.data;
    
    this.setData({ submitting: true });
    
    partnerApi.reviewApplication(id, { status, admin_remark: remark })
      .then(res => {
        if (res.success) {
          wx.showToast({
            title: '审核成功',
            icon: 'success'
          });
          
          // 更新本地数据
          const application = { ...this.data.application, status, admin_remark: remark };
          this.setData({ 
            application,
            submitting: false,
            showRemarkInput: false
          });
          
          // 返回上一页并刷新列表
          setTimeout(() => {
            const pages = getCurrentPages();
            const prevPage = pages[pages.length - 2];
            if (prevPage && prevPage.loadApplications) {
              prevPage.loadApplications();
            }
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.message || '审核失败',
            icon: 'none'
          });
          this.setData({ submitting: false });
        }
      })
      .catch(err => {
        console.error('审核申请失败:', err);
        wx.showToast({
          title: '审核失败，请重试',
          icon: 'none'
        });
        this.setData({ submitting: false });
      });
  },

  /**
   * 备注输入变化
   */
  onRemarkInput: function (e) {
    this.setData({ remark: e.detail.value });
  },

  /**
   * 取消备注输入
   */
  cancelRemark: function () {
    this.setData({ showRemarkInput: false });
  },

  /**
   * 确认备注输入
   */
  confirmRemark: function () {
    const { remark } = this.data;
    if (!remark.trim()) {
      wx.showToast({
        title: '请输入拒绝理由',
        icon: 'none'
      });
      return;
    }
    
    this.submitReview('rejected');
  },

  /**
   * 拨打电话
   */
  makePhoneCall: function () {
    const { application } = this.data;
    if (application && application.phone) {
      wx.makePhoneCall({
        phoneNumber: application.phone
      });
    }
  },

  /**
   * 格式化时间戳
   */
  formatDate: function (timestamp) {
    if (!timestamp) return '';
    const date = new Date(parseInt(timestamp));
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  }
});