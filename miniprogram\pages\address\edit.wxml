<view class="container">
  <!-- 地址信息表单 -->
  <view class="form-section">
    <view class="form-item">
      <view class="label">收货人</view>
      <input class="input" placeholder="请输入收货人姓名" value="{{address.name}}" bindinput="onNameInput" />
    </view>
    
    <view class="form-item">
      <view class="label">手机号码</view>
      <input class="input" type="number" placeholder="请输入手机号码" value="{{address.phone}}" bindinput="onPhoneInput" />
    </view>
    
    <view class="form-item">
      <view class="label">所在地区</view>
      <picker mode="region" bindchange="onRegionChange" value="{{regionArray}}">
        <view class="picker-view">
          <text wx:if="{{regionArray[0]}}">{{regionArray[0]}} {{regionArray[1]}} {{regionArray[2]}}</text>
          <text wx:else style="color: #999;">请选择所在地区</text>
        </view>
      </picker>
    </view>
    
    <view class="form-item">
      <view class="label">详细地址</view>
      <textarea class="textarea" placeholder="请输入详细地址，如街道、门牌号等" value="{{address.detail}}" bindinput="onDetailInput" />
    </view>
  </view>
  
  <!-- 设置选项 -->
  <view class="options-section">
    <view class="option-item">
      <view class="option-label">设为默认地址</view>
      <switch class="switch" checked="{{address.isDefault}}" bindchange="onDefaultChange" />
    </view>
  </view>
  
  <!-- 删除按钮 -->
  <view class="delete-section" wx:if="{{address.id}}">
    <view class="delete-btn" bindtap="deleteAddress">删除收货地址</view>
  </view>
  
  <!-- 保存按钮 -->
  <view class="save-section">
    <view class="save-btn" bindtap="saveAddress">保存</view>
  </view>
</view> 