-- 更新门店库存表结构，将quantity字段拆分为cloud_quantity和offline_quantity字段，并将store_id字段改为store_no字段

-- 1. 添加新字段
ALTER TABLE `store_inventory` 
ADD COLUMN `cloud_quantity` INT NOT NULL DEFAULT 0 COMMENT '云仓库存数量' AFTER `product_id`,
ADD COLUMN `offline_quantity` INT NOT NULL DEFAULT 0 COMMENT '线下库存数量' AFTER `cloud_quantity`;

-- 2. 将现有quantity字段的值复制到cloud_quantity字段
UPDATE `store_inventory` SET `cloud_quantity` = `quantity`;

-- 3. 删除原quantity字段
ALTER TABLE `store_inventory` DROP COLUMN `quantity`;

-- 4. 将store_id字段改为store_no字段
ALTER TABLE `store_inventory` 
CHANGE COLUMN `store_id` `store_no` VARCHAR(32) NOT NULL COMMENT '门店编号';

-- 5. 更新索引
ALTER TABLE `store_inventory` 
DROP INDEX `idx_store_product`,
DROP INDEX `idx_store_id`,
ADD UNIQUE INDEX `idx_store_product` (`store_no`, `product_id`),
ADD INDEX `idx_store_no` (`store_no`);