/**
 * 轮播图管理控制器
 */
const Banner = require('../models/Banner');

/**
 * 获取轮播图列表
 * GET /api/admin/banners
 */
exports.getBanners = async (req, res) => {
  try {
    const { is_active, page_type } = req.query;
    
    const options = {};
    if (is_active !== undefined) {
      options.is_active = is_active === 'true' ? 1 : 0;
    }
    if (page_type !== undefined) {
      options.page_type = page_type;
    }

    const banners = await Banner.findAll(options);
    
    res.json({
      success: true,
      data: banners,
      message: '获取轮播图列表成功'
    });
  } catch (error) {
    console.error('获取轮播图列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取轮播图列表失败',
      error: error.message
    });
  }
};

/**
 * 获取单个轮播图
 * GET /api/admin/banners/:id
 */
exports.getBannerById = async (req, res) => {
  try {
    const { id } = req.params;
    const banner = await Banner.findById(id);

    if (banner) {
      res.json({
        success: true,
        data: banner,
        message: '获取轮播图成功'
      });
    } else {
      res.status(404).json({
        success: false,
        message: '轮播图不存在'
      });
    }
  } catch (error) {
    console.error('获取轮播图失败:', error);
    res.status(500).json({
      success: false,
      message: '获取轮播图失败',
      error: error.message
    });
  }
};

/**
 * 创建轮播图
 * POST /api/admin/banners
 */
exports.createBanner = async (req, res) => {
  try {
    const bannerData = req.body;

    // 验证必填字段
    if (!bannerData.image_url) {
      return res.status(400).json({
        success: false,
        message: '轮播图片不能为空'
      });
    }

    const result = await Banner.create(bannerData);
    
    res.json({
      success: true,
      data: { id: result.id },
      message: '轮播图创建成功'
    });
  } catch (error) {
    console.error('创建轮播图失败:', error);
    res.status(500).json({
      success: false,
      message: '创建轮播图失败',
      error: error.message
    });
  }
};

/**
 * 更新轮播图
 * PUT /api/admin/banners/:id
 */
exports.updateBanner = async (req, res) => {
  try {
    const { id } = req.params;
    const bannerData = req.body;

    const result = await Banner.update(id, bannerData);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(404).json(result);
    }
  } catch (error) {
    console.error('更新轮播图失败:', error);
    res.status(500).json({
      success: false,
      message: '更新轮播图失败',
      error: error.message
    });
  }
};

/**
 * 删除轮播图
 * DELETE /api/admin/banners/:id
 */
exports.deleteBanner = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await Banner.delete(id);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(404).json(result);
    }
  } catch (error) {
    console.error('删除轮播图失败:', error);
    res.status(500).json({
      success: false,
      message: '删除轮播图失败',
      error: error.message
    });
  }
};

/**
 * 更新轮播图状态
 * PATCH /api/admin/banners/:id/status
 */
exports.updateBannerStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;

    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: '缺少状态参数'
      });
    }

    const result = await Banner.updateStatus(id, is_active);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(404).json(result);
    }
  } catch (error) {
    console.error('更新轮播图状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新轮播图状态失败',
      error: error.message
    });
  }
};

/**
 * 批量更新轮播图排序
 * POST /api/admin/banners/batch-sort
 */
exports.batchUpdateSort = async (req, res) => {
  try {
    const { sortData } = req.body;

    if (!Array.isArray(sortData) || sortData.length === 0) {
      return res.status(400).json({
        success: false,
        message: '排序数据不能为空'
      });
    }

    const result = await Banner.batchUpdateSort(sortData);
    res.json(result);
  } catch (error) {
    console.error('批量更新排序失败:', error);
    res.status(500).json({
      success: false,
      message: '批量更新排序失败',
      error: error.message
    });
  }
};

/**
 * 获取前端展示用的轮播图列表（仅启用的）
 * GET /api/banners
 */
exports.getActiveBanners = async (req, res) => {
  try {
    const { page_type = 'customer_home' } = req.query;
    
    const banners = await Banner.findAll({ 
      is_active: 1,
      page_type: page_type
    });
    
    // 只返回前端需要的字段
    const frontendBanners = banners.map(banner => ({
      id: banner.id,
      title: banner.title,
      imageUrl: banner.image_url,
      linkUrl: banner.link_url,
      sort_order: banner.sort_order,
      page_type: banner.page_type
    }));

    res.json({
      success: true,
      data: frontendBanners,
      message: '获取轮播图成功'
    });
  } catch (error) {
    console.error('获取前端轮播图失败:', error);
    res.status(500).json({
      success: false,
      message: '获取轮播图失败',
      error: error.message
    });
  }
}; 