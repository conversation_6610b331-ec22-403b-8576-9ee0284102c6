/* pages/shop/shop.wxss */
.shop-container {
  min-height: 100vh;
  background-color: #F7F7F7;
  padding-bottom: 20px;
}



/* 搜索栏区域 */
.search-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding-top: 5px; /* 减小顶部内边距，确保在原生导航栏下方有适当间距 */
  background-color: #F7F7F7; /* 与页面背景色一致 */
  border-radius: 18px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  padding: 2px;
  margin: 5px 16px 5px; /* 减小上下边距 */
  z-index: 1000; /* 确保搜索栏在最上层 */
}

.search-input-wrapper {
  background-color: #f8f8f8;
  border-radius: 14px;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  height: 28px;
  width: 100%;
  position: relative;
}

.search-icon {
  width: 22px;
  height: 22px;
  position: absolute;
  right: 28px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0.7;
  cursor: pointer;
  z-index: 1;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 14px;
  color: #333;
  padding-right: 35px;
  min-width: 0;
}

.search-placeholder {
  color: #999;
  font-size: 14px;
}

/* 搜索栏占位元素 */
.search-placeholder-space {
  height: 50px; /* 搜索栏高度 + 上下边距 + 顶部内边距（减小高度） */
  width: 100%;
}



/* 分类主容器 */
.category-main-container {
  margin-top: 0;
  display: flex;
  min-height: calc(100vh - 50px); /* 调整高度，考虑原生导航栏 */
  /* 移除 overflow: hidden 以允许页面滚动 */
}

/* 轮播图 */
.banner {
  width: 100%;
  height: 140px;
  border-radius: 10px;
  margin: 0;
  overflow: hidden;
  background: #f5f5f5;
}
.banner-image {
  width: 100%;
  height: 140px;
  object-fit: cover;
  border-radius: 10px;
}

.banner-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #FFFFFF;
  font-size: 14px;
  font-weight: bold;
}

.debug-info {
  padding: 10rpx;
  margin: 0 16px;
  background-color: #ffeeee;
  color: #ff0000;
  font-size: 24rpx;
  text-align: center;
  border-radius: 8rpx;
}



/* 分类导航 */
.category-container {
  background-color: #FFFFFF;
  position: sticky;
  z-index: 99;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 添加阴影效果 */
}

.category-scroll {
  height: 40px;
  white-space: nowrap;
  border-bottom: 1px solid #F5F5F5; /* 添加底部边框 */
  margin: 0 20px; /* 增加左右外边距，确保与屏幕边缘有更大距离 */
  width: calc(100% - 40px); /* 调整宽度，考虑左右外边距 */
}

.category-item {
  position: relative;
  display: inline-block;
  height: 40px;
  line-height: 40px;
  padding: 0 15px; /* 减小左右内边距，缩小卡片宽度 */
  font-size: 15px; /* 稍微减小字体大小 */
  color: #666666;
  transition: all 0.3s ease;
  border-right: 1px solid #EEEEEE; /* 添加右侧分界线 */
}

.category-item.active {
  color: #333333;
  font-weight: bold;
}

.category-item.last-item {
  border-right: none; /* 最后一个分类项不显示分界线 */
  padding-right: 0; /* 最后一个分类项右侧不需要内边距，因为已经有滚动区域的内边距 */
}

.category-item.first-item {
  padding-left: 0; /* 第一个分类项左侧不需要内边距，因为已经有滚动区域的内边距 */
}

.category-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px; /* 减小底部指示线宽度，与卡片宽度保持协调 */
  height: 3px;
  background-color: #FF4D4F;
  border-radius: 1.5px;
  transition: all 0.3s ease;
}

/* 统一.subcategory-scroll样式，只保留一处，margin:0 0 8px 0，避免不同分类下间距不一致 */
.subcategory-scroll {
  margin: 0 0 4px 0; /* 缩小下方间距 */
  padding-left: 0; /* 移除左侧内边距，让白色背景连为一体 */
  white-space: normal;
  height: auto;
  border-top: none;
  width: 100%;
  background: #fff; /* 白色背景 */
}

.subcategory-item {
  display: inline-block;
  padding: 4px 12px; /* 增加内边距，让标签更美观 */
  margin: 8px 8px 4px 0; /* 调整外边距，左侧为0让白色背景连为一体 */
  font-size: 12px; /* 减小字体大小 */
  color: #666;
  background: #F5F5F5; /* 未选中状态的浅灰色底纹 */
  border-radius: 12px; /* 圆角标签 */
  transition: all 0.2s ease; /* 过渡效果包含背景色变化 */
}

.subcategory-item.active {
  color: #ff4d4f;
  font-weight: bold;
  background: #FFF2F0; /* 选中状态的浅红色底纹 */
  border: 1px solid #FFD6CC; /* 选中状态的浅红色边框 */
}

/* 筛选栏 */
/* 彻底删除259行开始的.filter-bar及其相关旧样式，避免与新版冲突，确保所有分类下统一美观 */

/* 商品列表 */
.product-list {
  display: flex;
  flex-wrap: wrap;
  padding: 12px 12px 12px 2px; /* 左侧内边距减小到2像素 */
}

.product-item {
  width: calc((100% - 12px) / 2);
  margin-bottom: 12px;
  background-color: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.product-item:nth-child(odd) {
  margin-right: 12px;
}

.product-image {
  width: 100%;
  height: 160px; /* 使用固定高度代替 padding-bottom */
  object-fit: cover; /* 确保图片填充整个容器 */
}

.product-info {
  padding: 8px;
  position: relative;
}

.product-name {
  font-size: 14px;
  color: #333333;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  height: 40px;
}

.product-price-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 4px;
}

.product-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 14px;
  color: #FF4D4F;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.price-value {
  font-size: 18px;
  color: #FF4D4F;
  font-weight: 600;
  letter-spacing: 1px;
}

.product-original-price {
  font-size: 14px;
  color: #999999;
  text-decoration: line-through;
  margin-left: 6px;
  margin-bottom: 1px;
}

.product-sales {
  font-size: 12px;
  color: #999999;
  margin-bottom: 4px;
}

.product-shop {
  font-size: 12px;
  color: #666666;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}



/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.loading-icon {
  width: 20px;
  height: 20px;
  border: 2px solid #EEEEEE;
  border-top: 2px solid #FF4D4F;
  border-radius: 50%;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 20px 0;
  font-size: 14px;
  color: #999999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999999;
}

/* 筛选面板 */
.filter-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.filter-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 80%;
  background-color: #FFFFFF;
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
}

.filter-panel.show {
  transform: translateX(0);
}

.filter-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}

.filter-panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.filter-panel-close {
  width: 24px;
  height: 24px;
}

.filter-panel-close image {
  width: 100%;
  height: 100%;
}

.filter-section {
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}

.filter-section-title {
  font-size: 15px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12px;
}

.price-range {
  display: flex;
  align-items: center;
}

.price-input {
  flex: 1;
  height: 36px;
  background-color: #F5F5F5;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
}

.price-separator {
  margin: 0 8px;
  color: #999999;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
}

.filter-option {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 10px;
}

.filter-checkbox {
  width: 18px;
  height: 18px;
  margin-right: 6px;
}

.filter-checkbox image {
  width: 100%;
  height: 100%;
}

.filter-option text {
  font-size: 14px;
  color: #666666;
}

.filter-option.active text {
  color: #FF4D4F;
}

.filter-panel-footer {
  display: flex;
  padding: 16px;
  margin-top: auto;
}

.filter-reset-btn {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #F5F5F5;
  color: #666666;
  border-radius: 22px;
  margin-right: 12px;
}

.filter-apply-btn {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #FF4D4F;
  color: #FFFFFF;
  border-radius: 22px;
}

/* 分类页主容器：左右结构 */
.category-main-container {
  display: flex;
  flex-direction: row;
  width: 100vw;
  height: calc(100vh - 50px); /* 减去搜索栏占位高度 */
  background: #f7f7f7;
}

/* 左侧一级分类 */
.category-left {
  width: 90px;
  height: 100%;
  background: #fff;
  border-right: 1px solid #f0f0f0;
  overflow-y: auto;
  position: sticky;
  top: 50px; /* 与搜索栏占位高度一致 */
  z-index: 10;
}
.category-left-item {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  color: #666;
  border-left: 4px solid transparent;
  background: #fff;
  transition: background 0.2s;
}
.category-left-item.active {
  color: #ff4d4f;
  font-weight: bold;
  background: #f9f9f9;
  border-left: 4px solid #ff4d4f;
}

/* 右侧内容区 */
.category-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0; /* 移除左侧内边距，让白色背景连为一体 */
  background: #f7f7f7;
  min-width: 0;
  /* 移除 min-height: 0 限制，允许内容自然扩展 */
  overflow-y: auto; /* 添加垂直滚动 */
}

.banner {
  width: 100%;
  height: 120px;
  border-radius: 10px;
  margin: 0 0 8px 0; /* 移除上边距，让白色背景连为一体 */
  overflow: hidden;
  flex-shrink: 0; /* 防止被flex挤压塌陷 */
}

.subcategory-item {
  display: inline-block;
  padding: 4px 12px; /* 增加内边距，让标签更美观 */
  margin: 8px 8px 4px 0; /* 调整外边距，左侧为0让白色背景连为一体 */
  font-size: 12px;
  color: #666;
  background: #F5F5F5; /* 未选中状态的浅灰色底纹 */
  border-radius: 12px; /* 圆角标签 */
  transition: all 0.2s ease; /* 过渡效果包含背景色变化 */
}
.subcategory-item.active {
  color: #ff4d4f;
  font-weight: bold;
  background: #FFF2F0; /* 选中状态的浅红色底纹 */
  border: 1px solid #FFD6CC; /* 选中状态的浅红色边框 */
}

.product-list {
  margin-top: 4px; /* 与排序筛选栏的边距调整为4像素 */
  padding-left: 8px; /* 左侧内边距调整为8像素 */
}

/* 兼容原有商品、加载、空等样式 */

/* 商品卡片一行一个，图片左、信息右 */
.product-card {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  margin-bottom: 14px;
  padding: 10px 2px 10px 8px; /* 左侧边距调整为8像素 */
  min-height: 100px;
}
.product-card-image {
  width: 90px;
  height: 90px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 12px;
  background: #f5f5f5;
}
.product-card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
}
.product-card-title {
  font-size: 16px;
  color: #222;
  font-weight: 500;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  line-height: 1.3;
  max-height: 2.6em;
}

/* 删除旧的价格行样式，因为已改为新的布局 */

.product-card-price {
  display: flex;
  align-items: baseline;
}

.product-card-price .price-symbol {
  font-size: 14px;
  color: #ff4d4f;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.product-card-price .price-value {
  font-size: 20px;
  color: #ff4d4f;
  font-weight: 600;
  letter-spacing: 1px;
}
/* 划线价单独一行 */
.product-card-original-price {
  color: #bbb;
  font-size: 16px;
  text-decoration: line-through;
  margin-bottom: 6px;
  display: block;
}

/* 零售价与按钮的底部行 */
.product-card-bottom-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.product-card-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* 右对齐按钮组 */
  gap: 12px; /* 增加50%：从8px调整到12px */
  flex-shrink: 0; /* 防止按钮被压缩 */
  margin-right: 0; /* 重置右边距，因为在新容器中 */
}

.action-btn {
  width: 26px; /* 减小15%：从30px调整到26px */
  height: 26px; /* 减小15%：从30px调整到26px */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: transparent;
  border: none;
  cursor: pointer;
}

.favorite-btn {
  background: transparent;
  border: none;
}

.favorite-btn:active {
  transform: scale(0.9);
}

.cart-btn {
  background: transparent;
  border: none;
}

.cart-btn:active {
  transform: scale(0.9);
}

.action-icon {
  width: 21px; /* 减小15%：从25px调整到21px */
  height: 21px; /* 减小15%：从25px调整到21px */
}

.favorite-btn .action-icon {
  /* 收藏图标使用红色调 */
  filter: hue-rotate(-10deg) saturate(1.5) brightness(1.1);
  color: #FF3333;
}

.favorite-btn:active .action-icon {
  opacity: 0.8;
}

.cart-btn .action-icon {
  /* 购物车图标使用红色调 */
  filter: hue-rotate(10deg) saturate(1.3) brightness(1.2);
  color: #FF0000;
}

.cart-btn:active .action-icon {
  opacity: 0.8;
}

/* 二级分类横向滚动优化 - 样式已在上方统一定义 */

/* 排序及筛选栏 */
.filter-bar {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 0; /* 取消圆角 */
  margin: 0 0 4px 0; /* 调整下边距为4像素 */
  padding: 6px 12px;
  min-height: 44px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.filter-item {
  flex: 1;
  margin-right: 0;
  font-size: 15px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.filter-item.active {
  color: #ff4d4f;
  font-weight: bold;
}
.filter-icon {
  width: 18px;
  height: 18px;
  margin-left: 4px;
}
.sort-icon {
  display: flex;
  flex-direction: column;
  margin-left: 2px;
}
.sort-icon image {
  width: 12px;
  height: 12px;
}
