// pages/faq/faq.js
const { settingsApi } = require('../../utils/api');

Page({
  data: {
    faqList: [],
    activeIndex: -1,
    loading: true
  },

  onLoad: function (options) {
    this.loadFaqList();
  },

  onShow: function () {
    // 页面显示时的逻辑
  },

  // 加载常见问题列表
  async loadFaqList() {
    try {
      this.setData({ loading: true });
      
      // 从后端API获取常见问题列表
      const res = await settingsApi.getFaqList();
      
      if (res.success && res.data) {
        this.setData({ 
          faqList: res.data,
          loading: false
        });
      } else {
        // 如果API失败，使用默认数据
        this.setDefaultFaqList();
      }
    } catch (error) {
      console.error('加载常见问题失败:', error);
      // 使用默认数据
      this.setDefaultFaqList();
    }
  },

  // 设置默认的常见问题列表
  setDefaultFaqList() {
    const defaultFaqList = [
      {
        question: '如何注册账号？',
        answer: '点击首页的"登录"按钮，使用微信授权即可快速注册账号。'
      },
      {
        question: '如何修改个人信息？',
        answer: '进入"我的"页面，点击"账号设置"即可修改头像、昵称等个人信息。'
      },
      {
        question: '如何查看订单？',
        answer: '在"我的"页面的"我的订单"区域可以查看所有订单状态。'
      },
      {
        question: '如何申请退款？',
        answer: '在订单详情页面点击"申请退款"按钮，填写退款原因即可。'
      },
      {
        question: '如何联系客服？',
        answer: '在"我的"页面点击"在线客服"即可联系客服人员。'
      },
      {
        question: '如何成为VIP会员？',
        answer: '在"我的"页面点击"会员中心"，选择相应的会员等级进行开通。'
      },
      {
        question: '如何查看物流信息？',
        answer: '在订单详情页面点击"查看物流"即可查看最新的物流信息。'
      },
      {
        question: '如何添加收货地址？',
        answer: '在"我的"页面点击"收货地址"，然后点击"添加新地址"即可。'
      }
    ];
    
    this.setData({ 
      faqList: defaultFaqList,
      loading: false
    });
  },

  // 切换FAQ展开状态
  toggleFaq: function(e) {
    const index = e.currentTarget.dataset.index;
    if (this.data.activeIndex === index) {
      this.setData({
        activeIndex: -1
      });
    } else {
      this.setData({
        activeIndex: index
      });
    }
  },

  // 联系客服
  contactService: function() {
    wx.navigateTo({
      url: '/pages/service/index'
    });
  }
}); 