const fs = require('fs');
const path = require('path');
const db = require('../config/db');

async function addWechatFields() {
  try {
    console.log('开始添加微信登录相关字段...');
    
    // 读取SQL文件
    const sqlPath = path.join(__dirname, 'add_wechat_fields.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    // 分割SQL语句 - 改进分割逻辑
    const sqlStatements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => {
        // 过滤掉注释和空语句
        const cleanStmt = stmt.replace(/--.*$/gm, '').trim();
        return cleanStmt.length > 0 && !cleanStmt.startsWith('--');
      })
      .map(stmt => stmt.replace(/--.*$/gm, '').trim()); // 移除行内注释
    
    console.log('找到SQL语句数量:', sqlStatements.length);
    console.log('SQL语句列表:');
    sqlStatements.forEach((stmt, index) => {
      console.log(`${index + 1}. ${stmt.substring(0, 50)}...`);
    });
    
    // 执行每个SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const sql = sqlStatements[i];
      console.log(`\n执行第${i + 1}条SQL:`, sql.substring(0, 50) + '...');
      
      try {
        await db.query(sql);
        console.log(`✅ 第${i + 1}条SQL执行成功`);
      } catch (error) {
        // 检查是否是字段已存在的错误
        if (error.message.includes('Duplicate column name') || 
            error.message.includes('Duplicate key name') ||
            error.message.includes('already exists')) {
          console.log(`⚠️ 第${i + 1}条SQL跳过（字段已存在）:`, error.message);
        } else {
          console.error(`❌ 第${i + 1}条SQL执行失败:`, error.message);
        }
      }
    }
    
    console.log('\n微信字段添加完成！');
    
    // 验证字段是否添加成功
    console.log('验证字段添加结果...');
    const result = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'users' 
      AND COLUMN_NAME IN ('openid', 'unionid')
      AND TABLE_SCHEMA = DATABASE()
    `);
    
    console.log('字段验证结果:', result);
    
    // 检查是否两个字段都存在
    const hasOpenid = result.some(row => row.COLUMN_NAME === 'openid');
    const hasUnionid = result.some(row => row.COLUMN_NAME === 'unionid');
    
    if (hasOpenid && hasUnionid) {
      console.log('✅ 微信登录字段添加成功！');
    } else {
      console.log('⚠️ 部分字段可能未添加成功');
      if (!hasOpenid) console.log('❌ openid字段缺失');
      if (!hasUnionid) console.log('❌ unionid字段缺失');
    }
    
  } catch (error) {
    console.error('添加微信字段失败:', error);
  } finally {
    process.exit(0);
  }
}

addWechatFields();