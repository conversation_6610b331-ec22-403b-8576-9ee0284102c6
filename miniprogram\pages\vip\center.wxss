.vip-center-container {
  display: flex;
  flex-direction: column;
  background: linear-gradient(to bottom, #fff, #f8f9fa);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.vip-center-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(to right, #FF6B35, #FF6B35, #FF6B35); /* 改为橙红色调 */
  z-index: 10;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx 28rpx 24rpx;
  background: linear-gradient(135deg, #f5f7f9, #fff);
  border-bottom: 1rpx solid rgba(229, 229, 229, 0.8);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.03);
  position: relative;
}

.user-info::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 40rpx;
  right: 40rpx;
  height: 1rpx;
  background: linear-gradient(to right, transparent, rgba(255, 107, 53, 0.1), transparent); /* 改为橙红色调 */
}

.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  background: #FF6B35; /* 改为橙红色调 */
  margin-right: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 3rpx solid rgba(255, 255, 255, 0.8);
}

.user-details {
  flex: 1;
}

.nickname {
  font-size: 38rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  color: #333;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.user-id {
  font-size: 26rpx;
  color: #666;
  background: rgba(0, 0, 0, 0.04);
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.vip-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.vip-label {
  font-size: 36rpx;
  color: #FF6B35; /* 改为橙红色调 */
  font-weight: bold;
  position: relative;
  padding-left: 40rpx;
}

.vip-label::before {
  content: '★';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  color: #FF6B35; /* 改为橙红色调 */
  font-size: 32rpx;
}

.vip-expire {
  font-size: 24rpx;
  color: #888;
  margin-top: 10rpx;
  background: rgba(255, 107, 53, 0.05); /* 改为橙红色调 */
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

/* VIP权益展示区 */
.vip-benefits-display {
  margin: 20rpx 24rpx;
  background: linear-gradient(135deg, #fff, #f8f9fa);
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.1);
  border: 1rpx solid rgba(255, 107, 53, 0.1);
  position: relative;
  overflow: hidden;
}

.vip-benefits-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(to right, #FF6B35, #FF6B35);
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
}

.benefits-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.benefits-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.benefits-subtitle {
  font-size: 28rpx;
  color: #FF6B35;
  font-weight: 500;
}

.benefits-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.benefits-content .benefit-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid rgba(255, 107, 53, 0.1);
}

.benefits-content .benefit-item:last-child {
  border-bottom: none;
}

.benefit-icon {
  width: 40rpx;
  height: 40rpx;
  background: #FF6B35;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.benefit-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  flex: 1;
}

/* 非VIP用户提示区 */
.non-vip-section {
  margin: 20rpx 24rpx;
  background: linear-gradient(135deg, #fff, #f8f9fa);
  border-radius: 24rpx;
  padding: 40rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 107, 53, 0.1);
  text-align: center;
}

.non-vip-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.non-vip-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.non-vip-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.non-vip-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.vip-benefit-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20rpx 0 0 0;
  position: relative;
  padding: 15rpx 0;
  height: 650rpx; /* 进一步减小高度 */
  background-color: #f5f5f7;
  border-top: 1rpx solid #e8e8e8;
  border-bottom: 1rpx solid #e8e8e8;
  position: relative;
  overflow: hidden;
}

.vip-benefit-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 10rpx;
  background: linear-gradient(to right, #FF6B35, #FF6B35, #FF6B35); /* 改为橙红色调 */
  opacity: 0.15;
}
.benefit-swiper {
  width: 100%;
  height: 630rpx;
  min-height: 630rpx;
  background: transparent;
  display: flex;
  align-items: center;
  border: none;
  border-radius: 24rpx;
  box-sizing: border-box;
  position: relative;
  padding: 15rpx 0; /* 增加上下内边距 */
  overflow: visible; /* 确保缩放后的内容不被裁剪 */
}

/* 自定义轮播指示器样式 */
.benefit-swiper .wx-swiper-dots {
  margin-bottom: -10rpx !important;
}

.benefit-swiper .wx-swiper-dot {
  width: 16rpx;
  height: 16rpx;
  background: rgba(255, 107, 53, 0.2) !important; /* 改为橙红色调 */
}

.benefit-swiper .wx-swiper-dot-active {
  width: 24rpx;
  background: rgba(255, 107, 53, 0.8) !important; /* 改为橙红色调 */
  border-radius: 8rpx;
}
.benefit-swiper-item {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  height: 630rpx;
  padding: 20rpx 0; /* 增加上下内边距，为缩放后的卡片留出空间 */
  overflow: visible; /* 确保缩放后的内容不被裁剪 */
}
.benefit-card {
  width: 85%;
  height: 95%; /* 增加卡片高度以容纳更多内容 */
  background: #fff;
  border-radius: 32rpx; /* 增大圆角 */
  box-shadow: 0 10rpx 30rpx rgba(255, 107, 53, 0.15), 0 4rpx 10rpx rgba(0,0,0,0.08); /* 改为橙红色调 */
  border: none; /* 去掉边框 */
  padding: 20rpx 28rpx 15rpx; /* 优化内边距 */
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  position: relative;
  min-height: 580rpx; /* 增加最小高度以确保按钮可见 */
  overflow: visible; /* 确保内容不被裁剪 */
  backdrop-filter: blur(8px);
  background-image: linear-gradient(to bottom right, rgba(255,255,255,0.9), rgba(255,255,255,1));
}
.benefit-card:not(.active) {
  opacity: 0.7;
  transform: scale(0.85);
  background-color: #f9f9f9;
  height: 90%; /* 与基础卡片保持一致 */
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow: visible; /* 确保缩放后的内容不被裁剪 */
}

/* 确保非激活状态下的标题样式一致 */
.benefit-card:not(.active) .benefit-title {
  font-size: 36rpx; /* 稍微小一点 */
  margin-bottom: 16rpx;
  padding-bottom: 10rpx;
}

/* 确保非激活状态下的价格样式一致 */
.benefit-card:not(.active) .benefit-price {
  font-size: 26rpx; /* 稍微小一点 */
  margin: 6rpx 0 10rpx 0;
}

/* 确保非激活状态下的权益列表样式一致 */
.benefit-card:not(.active) .benefit-list {
  margin: 4rpx 0 12rpx; /* 进一步减少间距 */
}

/* 确保非激活状态下的权益项样式一致 */
.benefit-card:not(.active) .benefit-item {
  font-size: 26rpx; /* 稍微小一点 */
  margin-bottom: 10rpx; /* 减少间距 */
  line-height: 1.2; /* 减少行高 */
}

/* 确保非激活状态下的按钮样式一致 */
.benefit-card:not(.active) .renew-btn {
  font-size: 26rpx; /* 稍微小一点 */
  padding: 10rpx 0; /* 减少内边距 */
  margin-top: 8rpx; /* 减少上边距 */
  margin-bottom: 3rpx; /* 减少下边距 */
}

.benefit-title {
  font-size: 38rpx;
  font-weight: 600;
  margin-bottom: 18rpx;
  color: #FF6B35; /* 改为橙红色调 */
  text-align: center;
  position: relative;
  padding-bottom: 12rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.05);
}

.benefit-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(to right, rgba(255,107,53,0.6), rgba(255,107,53,0.8), rgba(255,107,53,0.6)); /* 改为橙红色调 */
  border-radius: 2rpx;
}
.benefit-list {
  width: 100%;
  margin: 4rpx 0 10rpx; /* 进一步减少上下边距 */
  flex: 1;
  overflow: visible;
  max-height: 300rpx; /* 限制权益列表最大高度 */
}
.benefit-item {
  font-size: 26rpx; /* 稍微减小字体 */
  color: #333;
  margin-bottom: 8rpx; /* 减少权益项间距 */
  position: relative;
  padding-left: 40rpx; /* 减少左边距 */
  line-height: 1.2; /* 减少行高 */
  display: flex;
  align-items: center;
}

.benefit-item::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #FF6B35; /* 改为橙红色调 */
  font-weight: bold;
  width: 36rpx;
  height: 36rpx;
  background: rgba(255, 107, 53, 0.1); /* 改为橙红色调 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}
.renew-btn {
  width: 75%;
  margin-top: 8rpx; /* 减少上边距 */
  margin-bottom: 8rpx; /* 确保下边距 */
  background: #FF6B35; /* 改为橙红色调 */
  color: #fff;
  font-size: 26rpx; /* 稍微减小字体 */
  border-radius: 30rpx;
  padding: 10rpx 0; /* 减少内边距 */
  transition: all 0.3s;
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  box-shadow: 0 6rpx 12rpx rgba(255,107,53,0.2); /* 改为橙红色调 */
  letter-spacing: 2rpx;
  font-weight: 500;
  overflow: hidden;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.renew-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, transparent, rgba(255,255,255,0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.benefit-card.active .renew-btn {
  background: #FF6B35; /* 改为橙红色调 */
  box-shadow: 0 8rpx 16rpx rgba(255, 107, 53, 0.35); /* 改为橙红色调 */
}

.benefit-card.active .renew-btn::after {
  transform: translateX(100%);
}

.benefit-card.active .benefit-title {
  color: #FF6B35; /* 改为橙红色调 */
}


.benefit-card.active {
  transform: scale(0.98);
  height: 90%; /* 与基础卡片保持一致 */
  box-shadow: 0 16rpx 36rpx rgba(255, 107, 53, 0.25), 0 6rpx 16rpx rgba(255, 107, 53, 0.15); /* 改为橙红色调 */
  border: none;
  background: #fff;
  z-index: 5;
  position: relative;
  background-image: linear-gradient(135deg, #fff, #f9fcff);
  overflow: visible; /* 确保内容不被裁剪 */
}

.benefit-card.active::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  border-radius: 34rpx;
  box-shadow: 0 0 0 8rpx rgba(255, 107, 53, 0.08); /* 改为橙红色调 */
  pointer-events: none;
}

.benefit-card.active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(to right, #FF6B35, #FF6B35); /* 改为橙红色调 */
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
}
.more-vip-btn {
  width: 80%;
  margin: 30rpx auto 40rpx auto;
  background: rgba(255, 255, 255, 0.9);
  color: #FF6B35; /* 改为橙红色调 */
  font-size: 32rpx;
  border: 2rpx solid rgba(255,107,53,0.6); /* 改为橙红色调 */
  border-radius: 36rpx;
  padding: 18rpx 0;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(255,107,53,0.08); /* 改为橙红色调 */
  font-weight: 500;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  letter-spacing: 2rpx;
}

.more-vip-btn:active {
  transform: scale(0.98);
  background: rgba(255,107,53,0.05); /* 改为橙红色调 */
}

/* 加载状态样式 */
.vip-loading {
  font-size: 24rpx;
  color: #888;
  margin-top: 10rpx;
  padding: 4rpx 12rpx;
  animation: pulse 1.5s infinite ease-in-out;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600rpx;
  color: #888;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 107, 53, 0.1); /* 改为橙红色调 */
  border-radius: 50%;
  border-top-color: #FF6B35; /* 改为橙红色调 */
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* 价格样式 */
.benefit-price {
  font-size: 28rpx;
  color: #e74c3c;
  margin: 8rpx 0 12rpx 0; /* 减少上下间距 */
  font-weight: bold;
  text-align: center;
}