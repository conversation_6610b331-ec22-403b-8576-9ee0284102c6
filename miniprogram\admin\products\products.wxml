<view class="product-admin-page">
  <!-- 搜索栏 -->
  <view class="user-mgr-header">
    <view class="user-mgr-search-bar">
      <view class="user-mgr-search-input-container">
        <input class="user-mgr-search-input" placeholder="请输入搜索内容" value="{{searchValue}}" bindinput="onSearchInput" confirm-type="search" bindconfirm="onSearch" />
        <view class="user-mgr-search-btn" bindtap="onSearch">
          <image src="/images/icons2/搜索.png"></image>
        </view>
      </view>
    </view>
  </view>
  <!-- 操作按钮栏 -->
  <view class="user-mgr-actions">
    <button class="user-mgr-btn" bindtap="onSelectAll">全选/取消</button>
    <button class="user-mgr-btn user-mgr-btn-disabled"></button>
    <button class="user-mgr-btn" bindtap="onBatchUp">批量上架</button>
    <button class="user-mgr-btn" bindtap="onBatchDown">批量下架</button>
  </view>
  <!-- 分类标签栏 -->
  <view class="user-mgr-tabs">
    <block wx:for="{{tabs}}" wx:key="*this">
      <text class="user-mgr-tab {{activeTab === index ? 'active' : ''}}" data-index="{{index}}" bindtap="onTabChange">{{item}}</text>
    </block>
  </view>
  <!-- 排序栏（表头） -->
  <view class="user-mgr-table-header">
    <text class="sortable-header">创建时间</text>
    <text class="sortable-header">名称</text>
    <text class="sortable-header">筛选</text>
  </view>

  <!-- 商品列表 -->
  <view class="product-list">
    <block wx:for="{{products}}" wx:key="id">
      <view class="product-mgr-item">
        <view class="product-mgr-status product-mgr-status-{{item.statusClass}}">{{item.statusText}}</view>
        <view class="product-mgr-checkbox {{item.checked ? 'selected' : ''}}" bindtap="onCheckProduct" data-index="{{index}}">
          <image src="{{item.checked ? '/images/icons/checked-box.svg' : '/images/icons/unchecked-box.svg'}}"></image>
        </view>
        <image class="product-mgr-img" src="{{item.image}}" />
        <view class="product-mgr-info">
          <view class="product-mgr-title-row">
            <view class="product-mgr-title">{{item.name}}</view>
          </view>
          <view class="product-mgr-spec">{{item.spec}}</view>
          <view class="product-mgr-price">￥{{item.price}}</view>
        </view>
        <view class="product-mgr-edit-btn" bindtap="onEditProduct" data-index="{{index}}">编辑</view>
      </view>
    </block>
    <!-- 底部统计区，移到商品列表末尾 -->
    <view class="user-mgr-footer">
      共计：{{products.length}}个商品
      <text wx:if="{{selectedCount > 0}}" class="selected-count">（已选中{{selectedCount}}个）</text>
    </view>
    <view class="safe-area"></view>
  </view>

  <admin-tabbar current="products" />
  <product-edit-drawer visible="{{showEditDrawer}}" product="{{editProduct}}" bind:cancel="onEditDrawerCancel" bind:confirm="onEditDrawerConfirm" />
</view> 