<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>

  <block wx:if="{{!loading && logisticsInfo}}">
    <!-- 物流状态 -->
    <view class="logistics-status">
      <view class="status-header">
        <view class="status-left">
          <view class="express-company">{{logisticsInfo.express_company}}</view>
          <view class="tracking-number-container">
            <text class="tracking-number">{{logisticsInfo.tracking_number}}</text>
            <view class="copy-btn" bindtap="copyTrackingNumber">复制</view>
          </view>
        </view>
        <view class="status-right">
          <image class="express-logo" src="{{logisticsInfo.express_logo || '/images/icons/express-default.svg'}}"></image>
        </view>
      </view>
      <view class="status-footer">
        <view class="status-text">{{logisticsInfo.status_text}}</view>
        <view class="refresh-btn" bindtap="refreshLogistics">刷新</view>
      </view>
    </view>

    <!-- 收货信息 -->
    <view class="address-section" wx:if="{{orderDetail}}">
      <view class="address-icon">
        <image src="/images/icons/location.svg"></image>
      </view>
      <view class="address-content">
        <view class="address-user">
          <text class="name">{{orderDetail.receiver_name}}</text>
          <text class="phone">{{orderDetail.receiver_phone}}</text>
        </view>
        <view class="address-detail">{{orderDetail.receiver_address}}</view>
      </view>
    </view>

    <!-- 物流轨迹 -->
    <view class="logistics-timeline">
      <view class="timeline-title">物流轨迹</view>
      <view class="timeline-list">
        <block wx:if="{{logisticsInfo.traces && logisticsInfo.traces.length > 0}}">
          <block wx:for="{{logisticsInfo.traces}}" wx:key="*this">
            <view class="timeline-item {{index === 0 ? 'active' : ''}}">
              <view class="timeline-dot"></view>
              <view class="timeline-line" wx:if="{{index < logisticsInfo.traces.length - 1}}"></view>
              <view class="timeline-content">
                <view class="timeline-info">{{item.content}}</view>
                <view class="timeline-time">{{item.time}}</view>
              </view>
            </view>
          </block>
        </block>
        <view class="empty-timeline" wx:else>
          <image class="empty-icon" src="/images/icons/empty-logistics.svg"></image>
          <text class="empty-text">暂无物流信息</text>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="footer-actions">
      <view class="action-btn" bindtap="contactExpress">
        <image class="action-icon" src="/images/icons/phone.svg"></image>
        <text>联系快递</text>
      </view>
      <view class="action-btn" bindtap="contactService">
        <image class="action-icon" src="/images/icons/service.svg"></image>
        <text>联系客服</text>
      </view>
    </view>
  </block>

  <!-- 物流信息不存在 -->
  <view class="empty-container" wx:if="{{!loading && !logisticsInfo}}">
    <image class="empty-icon" src="/images/icons/empty-logistics.svg"></image>
    <text class="empty-text">暂无物流信息</text>
  </view>
</view>