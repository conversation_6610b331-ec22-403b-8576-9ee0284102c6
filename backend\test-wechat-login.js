const axios = require('axios');

async function testWechatLogin() {
  try {
    console.log('开始测试微信登录API...');
    
    // 测试配置检查
    console.log('\n1. 测试配置检查...');
    const configResponse = await axios.get('https://morebuy25-172172-8-1368182116.sh.run.tcloudbase.com/api/system/config-check');
    console.log('配置检查结果:', configResponse.data);
    
    // 测试微信登录API
    console.log('\n2. 测试微信登录API...');
    const loginResponse = await axios.post('https://morebuy25-172172-8-1368182116.sh.run.tcloudbase.com/api/users/login/wechat', {
      code: 'test_code_123'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('微信登录API响应:', loginResponse.data);
    
  } catch (error) {
    console.error('测试失败:', error.response ? error.response.data : error.message);
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应头:', error.response.headers);
    }
  }
}

testWechatLogin();