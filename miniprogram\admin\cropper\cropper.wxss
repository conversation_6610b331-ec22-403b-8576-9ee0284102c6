/* admin/cropper/cropper.wxss - 完全复制顾客端成功实现 */
.cropper-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 40rpx;
  box-sizing: border-box;
}

.cropper-box {
  /* 尺寸由JavaScript动态设置 */
  background: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: relative;
  /* 默认尺寸，会被动态样式覆盖 */
  width: 90vw;
  height: 90vw;
}

.cropper-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.cropper-btn {
  margin-top: 48rpx;
  width: 60vw;
  background: #1E6A9E;
  color: #fff;
  border-radius: 24rpx;
  font-size: 16px;
  font-weight: bold;
  padding: 16rpx 0;
}