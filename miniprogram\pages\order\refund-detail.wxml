<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>

  <block wx:if="{{!loading && refundDetail}}">
    <!-- 退款状态 -->
    <view class="status-section">
      <view class="status-icon {{refundDetail.status === '已退款' ? 'success' : refundDetail.status === '已拒绝' ? 'failed' : 'pending'}}">
        <view class="icon-inner">
          <text wx:if="{{refundDetail.status === '已退款'}}">✓</text>
          <text wx:elif="{{refundDetail.status === '已拒绝'}}">!</text>
          <text wx:else>?</text>
        </view>
      </view>
      <view class="status-text">{{statusMap[refundDetail.status] || refundDetail.status}}</view>
      <view class="status-desc" wx:if="{{refundDetail.status === '已拒绝' && refundDetail.admin_remark}}">
        拒绝原因: {{refundDetail.admin_remark}}
      </view>
    </view>

    <!-- 退款信息 -->
    <view class="refund-section">
      <view class="section-title">退款信息</view>
      <view class="info-item">
        <text class="item-label">订单编号</text>
        <text class="item-value">{{refundDetail.order_id}}</text>
      </view>
      <view class="info-item">
        <text class="item-label">申请时间</text>
        <text class="item-value">{{refundDetail.created_at}}</text>
      </view>
      <view class="info-item">
        <text class="item-label">更新时间</text>
        <text class="item-value">{{refundDetail.updated_at}}</text>
      </view>
      <view class="info-item">
        <text class="item-label">退款原因</text>
        <text class="item-value">{{refundDetail.reason}}</text>
      </view>
      <view class="info-item" wx:if="{{refundDetail.description}}">
        <text class="item-label">详细描述</text>
        <text class="item-value">{{refundDetail.description}}</text>
      </view>
    </view>

    <!-- 凭证图片 -->
    <view class="images-section" wx:if="{{refundDetail.images && refundDetail.images.length > 0}}">
      <view class="section-title">凭证图片</view>
      <view class="image-list">
        <block wx:for="{{refundDetail.images}}" wx:key="*this">
          <image class="evidence-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
        </block>
      </view>
    </view>

    <!-- 订单状态 -->
    <view class="order-status-section">
      <view class="section-title">订单状态</view>
      <view class="order-status">{{refundDetail.order_status}}</view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn refresh" bindtap="refreshRefundDetail">刷新状态</button>
      <button class="action-btn back" bindtap="goToOrderDetail">返回订单</button>
    </view>
  </block>

  <!-- 退款记录不存在 -->
  <view class="empty-container" wx:if="{{!loading && !refundDetail}}">
    <image class="empty-icon" src="/images/icons/empty-order.svg"></image>
    <text class="empty-text">退款记录不存在或已被删除</text>
    <button class="back-btn" bindtap="goToOrderDetail">返回订单列表</button>
  </view>
</view>