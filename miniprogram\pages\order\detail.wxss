/* pages/order/detail.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 加载中 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  height: 300rpx;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 26rpx;
  color: #999;
}

/* 订单状态 */
.status-section {
  background-color: #ff6b00;
  color: #fff;
  padding: 40rpx 30rpx;
  position: relative;
}

.status-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 40rpx;
}

.status-text {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 订单进度条 */
.status-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding: 20rpx 0;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
}

.step-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  margin-bottom: 16rpx;
  position: relative;
  z-index: 2;
}

.step-line {
  position: absolute;
  top: 10rpx;
  left: 50%;
  width: 100%;
  height: 2rpx;
  background-color: rgba(255, 255, 255, 0.3);
  z-index: 1;
}

.step-title {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.step-desc {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 6rpx;
  text-align: center;
}

.step-item.active .step-dot {
  background-color: #fff;
  box-shadow: 0 0 6rpx rgba(255, 255, 255, 0.8);
}

.step-item.active .step-line {
  background-color: #fff;
}

.step-item.active .step-title {
  color: #fff;
}

.step-item.current .step-dot {
  width: 24rpx;
  height: 24rpx;
  background-color: #fff;
  box-shadow: 0 0 10rpx rgba(255, 255, 255, 1);
}

.step-item.current .step-title {
  font-weight: 500;
  color: #fff;
}

/* 信息区块通用样式 */
.info-section {
  background-color: #fff;
  margin: 20rpx 20rpx 0;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 24rpx;
  background-color: #ff6b00;
  border-radius: 3rpx;
}

/* 收货信息 */
.address-info {
  padding: 10rpx 0;
}

.address-user {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.address-user .name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 20rpx;
}

.address-user .phone {
  font-size: 26rpx;
  color: #666;
}

.address-detail {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 自提信息 */
.store-info {
  padding: 10rpx 0;
}

.store-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.store-address {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

.store-contact {
  font-size: 26rpx;
  color: #666;
}

/* 订单信息 */
.order-info {
  padding: 10rpx 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 26rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #999;
}

.info-value {
  color: #333;
}

.info-value-copy {
  display: flex;
  align-items: center;
  color: #333;
}

.copy-btn {
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #ff6b00;
  padding: 4rpx 12rpx;
  border: 1rpx solid #ff6b00;
  border-radius: 20rpx;
}

/* 商品信息 */
.products-list {
  padding: 10rpx 0;
}

.product-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-specs {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.product-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.product-price {
  font-size: 28rpx;
  color: #ff6b00;
  font-weight: 500;
}

.product-qty {
  font-size: 24rpx;
  color: #999;
}

/* 金额信息 */
.price-info {
  padding: 10rpx 0;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 26rpx;
}

.price-item:last-child {
  margin-bottom: 0;
}

.price-label {
  color: #999;
}

.price-value {
  color: #333;
}

.price-item.total {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx dashed #eee;
}

.price-item.total .price-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.price-item.total .price-value {
  font-size: 32rpx;
  color: #ff6b00;
  font-weight: 500;
}

/* 底部操作按钮 */
.footer-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-btn {
  padding: 0 30rpx;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 35rpx;
  font-size: 28rpx;
  margin-left: 20rpx;
  background-color: #f5f5f5;
  color: #666;
  text-align: center;
  border: 1rpx solid #e0e0e0;
}

.action-btn.primary {
  background-color: #ff6b00;
  color: #fff;
  border: none;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}