const loginStateManager = require('../../../utils/login-state-manager');

Page({
  data: {},

  onLoad() {
    console.log('账户设置页面 onLoad');
  },

  onShow() {
    console.log('账户设置页面 onShow');
  },

  // 修改密码
  goToPassword() {
    wx.navigateTo({ url: '/pages/settings/password/password' });
  },

  // 绑定手机
  goToPhone() {
    wx.navigateTo({ url: '/pages/settings/phone/phone' });
  },

  // 微信绑定
  goToWechat() {
    wx.navigateTo({ url: '/pages/settings/wechat/wechat' });
  },

  // 安全验证
  goToSecurity() {
    wx.showToast({
      title: '安全验证功能开发中',
      icon: 'none'
    });
  },

  // 隐私设置
  goToPrivacy() {
    wx.showToast({
      title: '隐私设置功能开发中',
      icon: 'none'
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录状态
          loginStateManager.clearLoginState();
          wx.removeStorageSync('userInfo');
          
          // 清除全局数据
          const app = getApp();
          if (app.globalData) {
            app.globalData.userInfo = null;
            app.globalData.isLogin = false;
          }
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
          
          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      }
    });
  }
}); 