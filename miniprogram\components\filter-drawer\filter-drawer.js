// components/filter-drawer/filter-drawer.js
Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    filterOptions: {
      type: Object,
      value: {
        priceRange: [0, 1000],
        sortBy: 'default',
        onlyInStock: false
      }
    }
  },

  data: {
    // 价格范围
    priceRange: [0, 1000],
    minPrice: 0,
    maxPrice: 1000,
    
    // 排序方式
    sortOptions: [
      { value: 'default', label: '默认排序' },
      { value: 'price_asc', label: '价格从低到高' },
      { value: 'price_desc', label: '价格从高到低' },
      { value: 'sales_desc', label: '销量优先' },
      { value: 'newest', label: '最新上架' }
    ],
    selectedSort: 'default',
    
    // 库存状态
    onlyInStock: false,
    
    // 商品分类
    categories: [],
    selectedCategories: []
  },

  lifetimes: {
    attached: function() {
      this.loadCategories();
    }
  },

  observers: {
    'filterOptions': function(filterOptions) {
      if (filterOptions) {
        this.setData({
          priceRange: filterOptions.priceRange || [0, 1000],
          minPrice: filterOptions.priceRange ? filterOptions.priceRange[0] : 0,
          maxPrice: filterOptions.priceRange ? filterOptions.priceRange[1] : 1000,
          selectedSort: filterOptions.sortBy || 'default',
          onlyInStock: filterOptions.onlyInStock || false
        });
      }
    }
  },

  methods: {
    // 加载分类数据
    loadCategories: function() {
      const api = require('../../utils/api');
      api.productApi.getCategories().then(res => {
        if (res.success && Array.isArray(res.data)) {
          this.setData({ categories: res.data });
        }
      }).catch(err => {
        console.error('获取分类失败:', err);
      });
    },

    // 防止滑动穿透
    preventTouchMove: function() {
      return false;
    },

    // 价格范围变化
    onPriceRangeChange: function(e) {
      this.setData({
        priceRange: e.detail.value
      });
    },

    // 最小价格输入
    onMinPriceInput: function(e) {
      const value = parseInt(e.detail.value) || 0;
      this.setData({
        minPrice: value,
        priceRange: [value, this.data.priceRange[1]]
      });
    },

    // 最大价格输入
    onMaxPriceInput: function(e) {
      const value = parseInt(e.detail.value) || 1000;
      this.setData({
        maxPrice: value,
        priceRange: [this.data.priceRange[0], value]
      });
    },

    // 排序方式选择
    onSortChange: function(e) {
      this.setData({
        selectedSort: e.currentTarget.dataset.value
      });
    },

    // 库存状态切换
    onStockToggle: function() {
      this.setData({
        onlyInStock: !this.data.onlyInStock
      });
    },

    // 分类选择
    onCategoryToggle: function(e) {
      const categoryId = e.currentTarget.dataset.id;
      const selectedCategories = [...this.data.selectedCategories];
      const index = selectedCategories.indexOf(categoryId);
      
      if (index > -1) {
        selectedCategories.splice(index, 1);
      } else {
        selectedCategories.push(categoryId);
      }
      
      this.setData({ selectedCategories });
    },

    // 重置筛选
    resetFilter: function() {
      this.setData({
        priceRange: [0, 1000],
        minPrice: 0,
        maxPrice: 1000,
        selectedSort: 'default',
        onlyInStock: false,
        selectedCategories: []
      });
      
      wx.vibrateShort({ type: 'light' });
    },

    // 取消筛选
    cancelFilter: function() {
      this.triggerEvent('close');
    },

    // 应用筛选
    applyFilter: function() {
      const filterOptions = {
        priceRange: this.data.priceRange,
        sortBy: this.data.selectedSort,
        onlyInStock: this.data.onlyInStock,
        selectedCategories: this.data.selectedCategories
      };
      
      console.log('应用筛选:', filterOptions);
      
      // 保存筛选参数
      wx.setStorageSync('productFilterOptions', filterOptions);
      
      this.triggerEvent('apply', filterOptions);
      wx.vibrateShort({ type: 'light' });
    }
  }
});
