/**
 * 数据库迁移脚本：为orders表添加order_no字段
 * 
 * 使用方法：
 * node backend/scripts/migrate_add_order_no.js
 */

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'morebuy',
  charset: 'utf8mb4'
};

async function migrateDatabase() {
  let connection;
  
  try {
    console.log('🔄 开始数据库迁移：添加order_no字段...');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 检查order_no字段是否已存在
    console.log('🔍 检查order_no字段是否存在...');
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'orders' AND COLUMN_NAME = 'order_no'
    `, [dbConfig.database]);
    
    if (columns.length > 0) {
      console.log('ℹ️  order_no字段已存在，跳过添加步骤');
    } else {
      // 2. 添加order_no字段
      console.log('➕ 添加order_no字段...');
      await connection.execute(`
        ALTER TABLE orders 
        ADD COLUMN order_no VARCHAR(50) NULL COMMENT '订单号' AFTER id
      `);
      console.log('✅ order_no字段添加成功');
    }
    
    // 3. 检查索引是否存在
    console.log('🔍 检查order_no索引是否存在...');
    const [indexes] = await connection.execute(`
      SELECT INDEX_NAME 
      FROM INFORMATION_SCHEMA.STATISTICS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'orders' AND INDEX_NAME = 'idx_order_no'
    `, [dbConfig.database]);
    
    if (indexes.length > 0) {
      console.log('ℹ️  order_no索引已存在，跳过添加步骤');
    } else {
      // 4. 添加索引
      console.log('➕ 为order_no字段添加索引...');
      await connection.execute(`
        ALTER TABLE orders ADD INDEX idx_order_no (order_no)
      `);
      console.log('✅ order_no索引添加成功');
    }
    
    // 5. 为现有订单生成订单号
    // 顾客订单编号规则：字母"XS"+年月日时分（12位数）+顺序号（4位数）
    console.log('🔄 为现有订单生成订单号...');
    const [updateResult] = await connection.execute(`
      UPDATE orders
      SET order_no = CONCAT(
        'XS',
        DATE_FORMAT(FROM_UNIXTIME(created_at/1000), '%Y%m%d%H%i'),
        LPAD(id % 10000, 4, '0')
      )
      WHERE order_no IS NULL OR order_no = ''
    `);
    console.log(`✅ 已为 ${updateResult.affectedRows} 个订单生成订单号`);
    
    // 6. 验证结果
    console.log('🔍 验证迁移结果...');
    const [stats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_orders,
        COUNT(order_no) as orders_with_no,
        COUNT(*) - COUNT(order_no) as orders_without_no
      FROM orders
    `);
    
    const stat = stats[0];
    console.log(`📊 统计结果：`);
    console.log(`   总订单数：${stat.total_orders}`);
    console.log(`   有订单号：${stat.orders_with_no}`);
    console.log(`   无订单号：${stat.orders_without_no}`);
    
    if (stat.orders_without_no === 0) {
      console.log('✅ 所有订单都已有订单号');
    } else {
      console.log('⚠️  仍有订单缺少订单号');
    }
    
    // 7. 显示最近的订单号示例
    console.log('📋 最近的订单号示例：');
    const [recentOrders] = await connection.execute(`
      SELECT id, order_no, status, created_at 
      FROM orders 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    recentOrders.forEach(order => {
      console.log(`   ID: ${order.id}, 订单号: ${order.order_no}, 状态: ${order.status}`);
    });
    
    console.log('🎉 数据库迁移完成！');
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error.message);
    console.error('详细错误:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行迁移
if (require.main === module) {
  migrateDatabase();
}

module.exports = migrateDatabase;
