Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },
  data: {
    storeName: '',
    levels: ['L1', 'L2', 'L3', 'L4', 'L5'],
    levelTitles: ['一星门店', '二星门店', '三星门店', '四星门店', '五星门店'], // 对应的level_title值
    levelIndex: 2, // 默认选中“三星门店”
    region: ['', '', ''], // 微信原生地区选择器数据
    regionText: '选择省市区',
    storeImage: '', // 门店形象照URL
    storeImageTemp: '', // 门店形象照临时路径
    customImageMode: false // 标记是否使用自定义图片
  },
  lifetimes: {
    // 使用微信原生picker，无需数据加载
  },
  
  pageLifetimes: {
    show() {
      console.log('创建门店抽屉组件 show 生命周期触发');
      // 从裁剪页面返回时处理门店图片（参考公司Logo成功实现）
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      // 处理门店图片裁剪后的结果
      if (currentPage.data.croppedStoreImage) {
        console.log('检测到门店图片裁剪结果:', currentPage.data.croppedStoreImage);
        console.log('当前storeImageTemp值:', this.data.storeImageTemp);

        // 立即设置裁剪后的图片，确保页面能够显示
        this.setData({
          storeImageTemp: currentPage.data.croppedStoreImage,
          customImageMode: true
        }, () => {
          // 设置完成后的回调，确保数据已更新
          console.log('门店图片设置完成，当前storeImageTemp值:', this.data.storeImageTemp);
        });

        // 清除页面临时数据
        currentPage.setData({
          croppedStoreImage: '',
          selectedStoreImage: '',
          customStoreImageMode: false
        });
      }
    }
  },
  
  observers: {
    'visible': function(visible) {
      if (visible) {
        // 重置表单数据，但不重置图片相关字段（参考公司Logo实现）
        this.setData({
          storeName: '',
          levelIndex: 2,
          region: ['', '', ''],
          regionText: '选择省市区'
          // 不重置 storeImage, storeImageTemp, customImageMode
        });
      }
    }
  },
  methods: {
    preventTouchMove() { return false; },
    catchTouchMove() {},
    

    
    // 使用微信原生地区选择器，无需复杂的数据加载
    onNameInput(e) {
      this.setData({ storeName: e.detail.value });
    },
    onLevelChange(e) {
      this.setData({ levelIndex: e.detail.value });
    },
    // 微信原生地区选择器事件处理
    onRegionChange(e) {
      const region = e.detail.value;
      const regionText = region.join(' ');
      this.setData({
        region: region,
        regionText: regionText
      });
    },
    onCancel() {
      this.triggerEvent('cancel');
    },
    // 选择门店形象照
    chooseStoreImage() {
      console.log('开始选择门店形象照');
      // 先尝试使用chooseMedia API
      try {
        wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: ['album', 'camera'],
          success: res => {
            console.log('选择门店图片成功:', res);
            const file = res.tempFiles[0];
            if (file.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
              wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
              return;
            }
            // 跳转到裁剪页面 - 门店形象照使用16:9比例
            wx.navigateTo({
              url: `/admin/cropper/cropper?src=${encodeURIComponent(file.tempFilePath)}&type=storeImage`
            });
          },
          fail: err => {
            console.error('chooseMedia 失败:', err);
            // 检查是否是用户取消操作
            if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
              console.log('用户取消选择门店图片');
              return; // 用户取消，直接返回
            }
            // 其他错误情况才使用备选方案
            this.chooseStoreImageFallback();
          }
        });
      } catch (error) {
        console.error('chooseMedia 异常:', error);
        // 如果chooseMedia出现异常，尝试使用chooseImage作为备选方案
        this.chooseStoreImageFallback();
      }
    },

    // 选择门店形象照备选方案
    chooseStoreImageFallback() {
      console.log('使用chooseImage作为备选方案选择门店图片');
      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: res => {
          console.log('chooseImage选择门店图片成功:', res);
          const tempFilePath = res.tempFilePaths[0];
          const tempFile = res.tempFiles[0];
          if (tempFile && tempFile.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
            wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
            return;
          }
          // 跳转到裁剪页面 - 门店形象照使用16:9比例
          wx.navigateTo({
            url: `/admin/cropper/cropper?src=${encodeURIComponent(tempFilePath)}&type=storeImage`
          });
        },
        fail: err => {
          console.error('chooseImage 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消选择门店图片');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才显示提示
          wx.showToast({
            title: '选择图片失败，请检查相关权限',
            icon: 'none'
          });
        }
      });
    },

    // 删除门店形象照
    deleteStoreImage() {
      console.log('删除门店图片');
      this.setData({ 
        storeImageTemp: '',
        storeImage: ''
      });
    },

    // 预览门店形象照
    previewStoreImage() {
      const imageUrl = this.data.storeImageTemp || this.data.storeImage;
      if (imageUrl) {
        wx.previewImage({
          urls: [imageUrl]
        });
      }
    },
    async onConfirm() {
      console.log('开始创建门店');
      console.log('当前组件数据:', {
        storeImageTemp: this.data.storeImageTemp,
        storeImage: this.data.storeImage,
        customImageMode: this.data.customImageMode,
        storeName: this.data.storeName
      });

      // 校验
      if (!this.data.storeName) {
        wx.showToast({ title: '请输入门店名称', icon: 'none' });
        return;
      }
      if (!this.data.region[0] || !this.data.region[1] || !this.data.region[2]) {
        wx.showToast({ title: '请选择完整的省市区', icon: 'none' });
        return;
      }
      
      const level = this.data.levels[this.data.levelIndex];
      const level_title = this.data.levelTitles[this.data.levelIndex];

      // 1. 上传图片（参考公司Logo的成功实现）
      let imageUrl = this.data.storeImageTemp || this.data.storeImage;
      console.log('=== 门店图片上传调试信息 ===');
      console.log('storeImageTemp值:', this.data.storeImageTemp);
      console.log('storeImageTemp类型:', typeof this.data.storeImageTemp);
      console.log('storeImage值:', this.data.storeImage);
      console.log('初始imageUrl:', imageUrl);
      console.log('自定义图片模式:', this.data.customImageMode);

      // 判断是否需要上传新图片 - 检查是否为临时文件路径（参考公司Logo实现）
      const isTempFilePath = this.data.storeImageTemp &&
                            (this.data.storeImageTemp.startsWith('wxfile://') ||
                             this.data.storeImageTemp.startsWith('http://tmp') ||
                             this.data.storeImageTemp.includes('tmp'));

      const needUploadImage = isTempFilePath;

      if (needUploadImage) {
        console.log('检测到新门店图片需要上传:', this.data.storeImageTemp);
        console.log('上传前图片URL:', imageUrl);
        try {
          // 使用store_images目录上传门店图片文件（参考公司Logo实现）
          wx.showLoading({ title: '正在上传图片...', mask: true });
          const { uploadFile } = require('../../../utils/api');
          imageUrl = await uploadFile(this.data.storeImageTemp, 'store_images');
          wx.hideLoading();
          console.log('门店图片上传成功，新URL:', imageUrl);
          console.log('上传前后URL对比:', {
            before: this.data.storeImageTemp,
            after: imageUrl
          });
        } catch (uploadError) {
          console.error('门店图片上传失败:', uploadError);
          wx.hideLoading();
          wx.showToast({ title: '门店图片上传失败，请重试', icon: 'none' });
          return;
        }
      } else {
        console.log('无需上传门店图片，使用现有URL:', imageUrl);
        console.log('判断条件:', {
          hasStoreImageTemp: !!this.data.storeImageTemp,
          storeImageTemp: this.data.storeImageTemp,
          currentImage: this.data.storeImage,
          isTempFilePath: isTempFilePath,
          needUploadImage: needUploadImage
        });
        if (!imageUrl) {
          imageUrl = '/images/icons2/店铺.png';
        }
      }

      console.log('=== 最终门店创建数据检查 ===');
      console.log('imageUrl值:', imageUrl);
      console.log('storeImage字段值:', imageUrl);
      console.log('准备传递的数据:', {
        storeName: this.data.storeName,
        level,
        level_title,
        province: this.data.region[0],
        city: this.data.region[1],
        district: this.data.region[2],
        storeImage: imageUrl
      });
      console.log('storeImage字段是否存在:', !!imageUrl);
      console.log('storeImage字段值类型:', typeof imageUrl);

      // 传递省市区名称给后端，由后端处理行政代码转换
      this.triggerEvent('confirm', {
        storeName: this.data.storeName,
        level,
        level_title,
        province: this.data.region[0],
        city: this.data.region[1],
        district: this.data.region[2],
        storeImage: imageUrl // 确保门店图片URL被正确传递
      });
    },
    // 使用微信原生picker，无需开发者工具调试方法
  }
});