/**
 * 快捷菜单路由
 * 处理快捷菜单相关的API请求
 */

const express = require('express');
const router = express.Router();
const db = require('../utils/db');
const authMiddleware = require('../middleware/auth');

/**
 * 获取快捷菜单列表（支持平台筛选）
 * GET /api/quick-menus?platform=customer|partner|admin
 */
router.get('/', async (req, res) => {
  try {
    console.log('获取快捷菜单列表');
    
    const { platform = 'customer' } = req.query;
    console.log('请求平台:', platform);
    
    // 构建查询条件
    let whereClause = 'WHERE is_active = 1';
    let queryParams = [];
    
    // 检查表结构是否已升级（是否有target_platform字段）
    const checkColumnQuery = `
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'quick_menus' 
      AND COLUMN_NAME = 'target_platform'
    `;
    
    const columnExists = await db.query(checkColumnQuery);
    
    if (columnExists.length > 0) {
      // 表已升级，支持平台筛选
      whereClause += ' AND (target_platform = ? OR target_platform = "all")';
      queryParams.push(platform);
    }
    
    // 查询启用的快捷菜单，按排序顺序排列
    const query = `
      SELECT id, name, icon, link_type, link_url, sort_order, target_platform
      FROM quick_menus 
      ${whereClause}
      ORDER BY sort_order ASC
    `;
    
    const rows = await db.query(query, queryParams);
    
    console.log(`查询到 ${rows.length} 个快捷菜单（平台：${platform}）`);
    
    res.json({
      success: true,
      data: rows,
      message: '获取快捷菜单成功'
    });
    
  } catch (error) {
    console.error('获取快捷菜单失败:', error);
    res.status(500).json({
      success: false,
      message: '获取快捷菜单失败',
      error: error.message
    });
  }
});

/**
 * 获取合伙人端专用菜单（带权限验证）
 * GET /api/quick-menus/partner
 */
router.get('/partner', authMiddleware.required, async (req, res) => { // 改回required，因为进入合伙人端必然是已登录状态
  try {
    console.log('获取合伙人端快捷菜单');
    console.log('用户信息:', req.user);
    
    // 用户已登录，检查权限
    if (req.user && req.user.id) {
      const userId = req.user.id;
      console.log('当前用户ID:', userId);
      
      // 检查用户是否有门店关联
      const userQuery = 'SELECT subscribe_store_no FROM users WHERE user_id = ?';
      const userResult = await db.query(userQuery, [userId]);
      
      if (userResult.length > 0 && userResult[0].subscribe_store_no) {
        console.log('用户有关联门店，允许访问合伙人功能');
      } else {
        console.log('用户没有门店关联，但允许访问基础菜单');
      }
    } else {
      console.log('用户信息不完整，但允许访问基础菜单');
    }
    
    // 检查是否存在 partner_quick_menus 表
    const checkTableQuery = `
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'partner_quick_menus'
    `;
    
    const tableExists = await db.query(checkTableQuery);
    
    let rows;
    
    if (tableExists.length > 0) {
      // 使用独立的合伙人菜单表
      console.log('使用独立的合伙人菜单表');
      
      const query = `
        SELECT 
          id, name, icon, link_type, link_url, 
          function_params, required_permissions, 
          display_condition, sort_order, description
        FROM partner_quick_menus 
        WHERE is_active = 1 
        ORDER BY sort_order ASC
      `;
      
      rows = await db.query(query);
      
      // TODO: 根据 required_permissions 和 display_condition 进行权限和条件过滤
      // 这里可以添加更复杂的权限逻辑
      
    } else {
      // 使用扩展的通用菜单表
      console.log('使用扩展的通用菜单表');
      
      // 检查是否有 target_platform 字段
      const checkColumnQuery = `
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'quick_menus' 
        AND COLUMN_NAME = 'target_platform'
      `;
      
      const columnExists = await db.query(checkColumnQuery);
      
      if (columnExists.length > 0) {
        const query = `
          SELECT id, name, icon, link_type, link_url, sort_order
          FROM quick_menus 
          WHERE is_active = 1 
          AND (target_platform = 'partner' OR target_platform = 'all')
          ORDER BY sort_order ASC
        `;
        rows = await db.query(query);
      } else {
        // 表结构未升级，返回默认菜单
        rows = [];
      }
    }
    
    console.log(`查询到 ${rows.length} 个合伙人端快捷菜单`);
    
    res.json({
      success: true,
      data: rows,
      message: '获取合伙人端快捷菜单成功'
    });
    
  } catch (error) {
    console.error('获取合伙人端快捷菜单失败:', error);
    res.status(500).json({
      success: false,
      message: '获取合伙人端快捷菜单失败',
      error: error.message
    });
  }
});

/**
 * 获取门店级别的个性化菜单配置
 * GET /api/quick-menus/partner/store/:storeId
 */
router.get('/partner/store/:storeId', authMiddleware.required, async (req, res) => {
  try {
    const { storeId } = req.params;
    console.log(`获取门店 ${storeId} 的个性化菜单配置`);
    
    // 检查是否存在门店配置表
    const checkTableQuery = `
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'store_quick_menu_config'
    `;
    
    const tableExists = await db.query(checkTableQuery);
    
    if (tableExists.length === 0) {
      // 表不存在，返回默认配置
      return this.getPartnerMenus(req, res);
    }
    
    // 使用视图查询门店个性化配置
    const query = `
      SELECT 
        id, name, icon, link_type, link_url, 
        function_params, required_permissions, 
        display_condition, sort_order, is_enabled
      FROM v_partner_menus 
      WHERE (store_id = ? OR store_id IS NULL)
      AND is_enabled = 1
      ORDER BY sort_order ASC
    `;
    
    const rows = await db.query(query, [storeId]);
    
    console.log(`查询到 ${rows.length} 个门店个性化快捷菜单`);
    
    res.json({
      success: true,
      data: rows,
      message: '获取门店快捷菜单成功'
    });
    
  } catch (error) {
    console.error('获取门店快捷菜单失败:', error);
    res.status(500).json({
      success: false,
      message: '获取门店快捷菜单失败',
      error: error.message
    });
  }
});

/**
 * 根据ID获取单个快捷菜单
 * GET /api/quick-menus/:id
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`获取快捷菜单详情，ID: ${id}`);
    
    const query = `
      SELECT id, name, icon, link_type, link_url, sort_order, is_active
      FROM quick_menus 
      WHERE id = ?
    `;
    
    const rows = await db.query(query, [id]);
    
    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '快捷菜单不存在'
      });
    }
    
    res.json({
      success: true,
      data: rows[0],
      message: '获取快捷菜单详情成功'
    });
    
  } catch (error) {
    console.error('获取快捷菜单详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取快捷菜单详情失败',
      error: error.message
    });
  }
});

/**
 * 创建快捷菜单（管理员功能）
 * POST /api/quick-menus
 */
router.post('/', authMiddleware.verifyAdmin, async (req, res) => {
  try {
    const { name, icon, link_type, link_url, sort_order = 0, target_platform = 'customer', required_roles } = req.body;
    
    // 检查管理员权限
    const userRoles = req.user.roles || [];
    const hasAdminRole = userRoles.some(role => ['admin', 'super_admin'].includes(role.role_type));
    
    if (!hasAdminRole) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }
    
    // 验证必填字段
    if (!name || !icon || !link_type || !link_url) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段：name, icon, link_type, link_url'
      });
    }
    
    // 检查表结构是否支持平台字段
    const checkColumnQuery = `
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'quick_menus' 
      AND COLUMN_NAME = 'target_platform'
    `;
    
    const columnExists = await db.query(checkColumnQuery);
    
    let insertQuery, insertParams;
    
    if (columnExists.length > 0) {
      // 表已升级，支持平台字段
      insertQuery = `
        INSERT INTO quick_menus (name, icon, link_type, link_url, sort_order, target_platform, required_roles, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, 1)
      `;
      insertParams = [name, icon, link_type, link_url, sort_order, target_platform, JSON.stringify(required_roles)];
    } else {
      // 表未升级，使用基础字段
      insertQuery = `
        INSERT INTO quick_menus (name, icon, link_type, link_url, sort_order, is_active)
        VALUES (?, ?, ?, ?, ?, 1)
      `;
      insertParams = [name, icon, link_type, link_url, sort_order];
    }
    
    const result = await db.query(insertQuery, insertParams);
    
    res.json({
      success: true,
      data: { id: result.insertId },
      message: '创建快捷菜单成功'
    });
    
  } catch (error) {
    console.error('创建快捷菜单失败:', error);
    res.status(500).json({
      success: false,
      message: '创建快捷菜单失败',
      error: error.message
    });
  }
});

module.exports = router;