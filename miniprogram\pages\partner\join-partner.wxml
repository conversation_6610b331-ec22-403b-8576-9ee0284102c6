<!--pages/partner/join-partner.wxml-->
<view class="join-partner-container">
  <!-- 顶部标语 -->
  <view class="slogan-section">
    <view class="slogan-text">{{slogan}}</view>
  </view>

  <!-- 合伙人权益九宫格 -->
  <view class="benefits-section">
    <view class="benefits-title">合伙人权益</view>
    <view class="benefits-grid">
      <view class="benefit-item" 
            wx:for="{{benefits}}" 
            wx:key="title" 
            bindtap="onBenefitTap" 
            data-index="{{index}}">
        <view class="benefit-icon">{{item.icon}}</view>
        <view class="benefit-title">{{item.title}}</view>
      </view>
    </view>
    
    <!-- 合约提示语 -->
    <view class="contract-notice">
      <view class="notice-text">
        更多权益详情见<text class="contract-link" bindtap="viewContract">合伙人协议</text>，公司签约保障权益。
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="notice-section">
    <view class="notice-actions">
      <button class="action-btn apply-btn" bindtap="applyPartner">申请加入</button>
      <button class="action-btn status-btn" bindtap="checkApplicationStatus">查看申请状态</button>
    </view>
  </view>
</view>

<!-- 申请加入弹窗 -->
<view class="modal-overlay" wx:if="{{showApplyModal}}" bindtap="closeApplyModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-title">申请加入合伙人</view>
      <view class="modal-close" bindtap="closeApplyModal">×</view>
    </view>
    
    <view class="modal-body">
      <view class="form-item">
        <view class="form-label">姓名</view>
        <input class="form-input" 
               placeholder="请输入您的姓名" 
               value="{{applyForm.name}}"
               data-field="name"
               bindinput="onInputChange" />
      </view>
      
      <view class="form-item">
        <view class="form-label">电话</view>
        <input class="form-input" 
               type="number"
               placeholder="请输入您的联系电话" 
               value="{{applyForm.phone}}"
               data-field="phone"
               bindinput="onInputChange" />
      </view>
      
      <view class="form-item">
        <view class="form-label">意向区域</view>
        <picker mode="region" bindchange="onRegionChange" value="{{applyForm.region}}">
          <view class="form-picker">
            <text wx:if="{{applyForm.regionText !== '请选择省市区'}}">{{applyForm.regionText}}</text>
            <text wx:else style="color:#bbb">请选择省市区</text>
          </view>
        </picker>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="submit-btn" bindtap="submitApplication">提交申请</button>
      <view class="submit-tip">提交后48小时内，工作人员将与您联系，谢谢等待</view>
    </view>
  </view>
</view>