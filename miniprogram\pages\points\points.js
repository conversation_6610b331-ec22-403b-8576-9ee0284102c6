// pages/points/points.js
const { productApi, pointsApi, userApi } = require('../../utils/api');
const { validateLoginState } = require('../../utils/login-state-manager');

Page({  /**
   * 页面的初始数据
   */  data: {
    userInfo: {},
    banners: [],
    bannerTitles: [],
    activeTab: 'consume', // consume/gain
    consumeRule: '',
    gainWays: '',
    loading: true,
    pointsRecords: [],
    loadingRecords: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.checkLoginAndInit();    // 直接设置本地轮播图
    this.setData({
      banners: [
        '/images/icons2/默认商品.png',
        '/images/icons2/默认商品.png',
        '/images/icons2/默认商品.png',
        '/images/icons2/默认商品.png',
        '/images/icons2/默认商品.png'
      ],
      bannerTitles: [
        '每日签到赚积分',
        '分享好友获积分',
        '积分兑换精美礼品',
        '使用积分查看联系方式',
        '积分可抵扣购物'
      ]
    });
    setTimeout(() => {
      console.log('页面userInfo:', this.data.userInfo);
      console.log('页面banners:', this.data.banners);
    }, 1000);
  },
  // 检查登录并初始化
  checkLoginAndInit() {
    const that = this;
    validateLoginState().then(res => {
      if (!res.isValid) {
        wx.showModal({
          title: '请先登录',
          content: '登录后可查看积分中心',
          showCancel: false,
          success: () => {
            wx.navigateTo({ url: '/pages/auth/auth' });
          }
        });
        return;
      }
      // 已登录，直接用本地全局信息，兼容字段
      const app = getApp();
      let userInfo = (app.globalData && app.globalData.userInfo) || {};

      // 统一字段名，确保显示正确
      userInfo.avatarUrl = userInfo.avatarUrl || userInfo.avatar || '/images/icons2/男头像.png';
      userInfo.avatar = userInfo.avatar || userInfo.avatarUrl || '/images/icons2/男头像.png';
      userInfo.nickName = userInfo.nickName || userInfo.nickname || userInfo.username || '用户昵称';
      userInfo.nickname = userInfo.nickname || userInfo.nickName || userInfo.username || '用户昵称';
      userInfo.id = userInfo.id || userInfo._id || 'XXXXXXXXXX';
      userInfo.points = 0; // 初始化为0，等待从数据库获取

      console.log('积分中心初始用户信息:', userInfo);
      that.setData({ userInfo });

      // 加载积分配置
      that.loadPointsConfig();

      // 如果有有效的用户ID，加载用户最新积分信息和积分记录
      if (userInfo.id && userInfo.id !== 'XXXXXXXXXX') {
        console.log('用户ID有效，开始加载积分数据:', userInfo.id);
        that.loadUserInfoAndStats(userInfo.id);
      } else {
        console.log('用户ID无效，积分显示为0');
        // 没有有效用户ID时，积分保持为0
        that.setData({
          userInfo: { ...userInfo, points: 0 },
          pointsRecords: []
        });
      }
    });
  },
  // 获取最新用户信息和积分
  loadUserInfoAndStats(userId) {
    const that = this;

    // 优先从数据库获取积分信息
    console.log('开始获取用户积分信息，用户ID:', userId);

    // 先获取积分信息，这是最重要的
    pointsApi.getUserPoints(userId)
      .then(pointsRes => {
        console.log('积分API响应:', pointsRes);

        // 获取当前用户信息
        const app = getApp();
        let userInfo = (app.globalData && app.globalData.userInfo) || {};

        // 统一字段名，确保显示正确
        userInfo.avatarUrl = userInfo.avatarUrl || userInfo.avatar || '/images/icons2/男头像.png';
        userInfo.avatar = userInfo.avatar || userInfo.avatarUrl || '/images/icons2/男头像.png';
        userInfo.nickName = userInfo.nickName || userInfo.nickname || userInfo.username || '用户昵称';
        userInfo.nickname = userInfo.nickname || userInfo.nickName || userInfo.username || '用户昵称';
        userInfo.id = userInfo.id || userInfo._id || userId;

        // 优先使用数据库中的积分数据
        if (pointsRes.success && pointsRes.data && typeof pointsRes.data.points === 'number') {
          userInfo.points = pointsRes.data.points;
          console.log('从数据库获取到积分:', pointsRes.data.points);
        } else {
          userInfo.points = 0; // 如果数据库没有数据，默认为0
          console.log('数据库中无积分数据，设置为0');
        }

        console.log('最终用户信息:', userInfo);
        that.setData({ userInfo });

        // 更新全局用户信息中的积分
        if (app.globalData && app.globalData.userInfo) {
          app.globalData.userInfo.points = userInfo.points;
        }

        // 加载积分记录
        that.loadPointsRecords();

        // 然后异步获取最新的用户基本信息（不影响积分显示）
        userApi.getUserInfo(userId)
          .then(infoRes => {
            if (infoRes.success && infoRes.data) {
              let updatedUserInfo = infoRes.data;
              // 保持积分数据不变
              updatedUserInfo.points = userInfo.points;
              // 统一字段名
              updatedUserInfo.avatarUrl = updatedUserInfo.avatarUrl || updatedUserInfo.avatar || '/images/icons2/男头像.png';
              updatedUserInfo.nickName = updatedUserInfo.nickName || updatedUserInfo.nickname || updatedUserInfo.username || '用户昵称';

              console.log('更新后的用户信息:', updatedUserInfo);
              that.setData({ userInfo: updatedUserInfo });
            }
          })
          .catch(err => {
            console.error('获取用户基本信息失败:', err);
          });
      })
      .catch((err) => {
        console.error('获取用户积分失败:', err);
        // 积分获取失败时，使用本地数据但积分设为0
        const app = getApp();
        let fallbackUserInfo = (app.globalData && app.globalData.userInfo) || {};
        // 确保回退数据也有正确的字段
        fallbackUserInfo.avatarUrl = fallbackUserInfo.avatarUrl || fallbackUserInfo.avatar || '/images/icons2/男头像.png';
        fallbackUserInfo.nickName = fallbackUserInfo.nickName || fallbackUserInfo.nickname || '用户昵称';
        fallbackUserInfo.points = 0; // 网络错误时积分显示为0
        fallbackUserInfo.id = fallbackUserInfo.id || userId;

        console.log('积分获取失败，使用回退数据:', fallbackUserInfo);
        that.setData({ userInfo: fallbackUserInfo });
      });
  },

  // 加载积分记录
  loadPointsRecords() {
    this.setData({ loadingRecords: true });
    
    // 获取当前用户ID
    const app = getApp();
    const userInfo = (app.globalData && app.globalData.userInfo) || {};
    const userId = userInfo.id || userInfo._id;
    
    if (!userId || userId === 'XXXXXXXXXX') {
      console.log('用户ID无效，跳过积分记录加载');
      this.setData({ 
        pointsRecords: [],
        loadingRecords: false 
      });
      return;
    }
    
    pointsApi.getUserPointsRecords({ userId, limit: 20, offset: 0 })
      .then(res => {
        if (res.success && Array.isArray(res.data)) {
          // 格式化时间
          const records = res.data.map(record => {
            return {
              id: record.id,
              time: this.formatTime(new Date(record.created_at)),
              event: record.event,
              change: record.change_amount,
              balance: record.balance
            };
          });
          this.setData({ pointsRecords: records });
        }
      })
      .catch(err => {
        console.error('获取积分记录失败:', err);
        this.setData({ pointsRecords: [] });
      })
      .finally(() => {
        this.setData({ loadingRecords: false });
      });
  },

  // 格式化时间
  formatTime(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const second = date.getSeconds();

    return [year, month, day].map(this.formatNumber).join('年') + '月' +
           day + '日' + ' ' + [hour, minute, second].map(this.formatNumber).join(':');
  },

  // 格式化数字
  formatNumber(n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  },
  // 获取积分规则和获取方式
  loadPointsConfig() {
    // 直接设置为"暂无"
    this.setData({
      consumeRule: '暂无',
      gainWays: '暂无',
      loading: false
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次页面显示时同步用户信息和积分
    const app = getApp();
    if (app.globalData.isLogin && app.globalData.userInfo) {
      let userInfo = app.globalData.userInfo;
      // 统一字段名
      userInfo.avatarUrl = userInfo.avatarUrl || userInfo.avatar || '/images/icons2/男头像.png';
      userInfo.nickName = userInfo.nickName || userInfo.nickname || userInfo.username || '用户昵称';
      userInfo.id = userInfo.id || userInfo._id;

      console.log('onShow - 当前用户信息:', userInfo);

      // 如果有有效的用户ID，重新获取最新积分
      if (userInfo.id && userInfo.id !== 'XXXXXXXXXX') {
        console.log('onShow - 重新获取积分数据');
        pointsApi.getUserPoints(userInfo.id)
          .then(pointsRes => {
            if (pointsRes.success && pointsRes.data && typeof pointsRes.data.points === 'number') {
              userInfo.points = pointsRes.data.points;
              console.log('onShow - 获取到最新积分:', pointsRes.data.points);
            } else {
              userInfo.points = 0;
              console.log('onShow - 积分数据无效，设置为0');
            }
            this.setData({ userInfo });

            // 更新全局用户信息中的积分
            if (app.globalData && app.globalData.userInfo) {
              app.globalData.userInfo.points = userInfo.points;
            }
          })
          .catch(err => {
            console.error('onShow - 获取积分失败:', err);
            userInfo.points = 0;
            this.setData({ userInfo });
          });
      } else {
        userInfo.points = 0;
        this.setData({ userInfo });
      }
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.checkLoginAndInit();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
  },

  // 切换卡片
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
  }
})
