// pages/settings/avatar.js
const { userApi } = require('../../utils/api');

Page({
  data: {
    avatarUrl: '',
    defaultAvatars: [
      '/images/icons2/男头像.png',
      '/images/icons2/女头像.png',
      '/images/icons2/我的.png',
      '/images/icons2/用户.png',
      '/images/icons2/头像.png',
      '/images/icons2/客服.png'
    ],
    selectedAvatar: '',
    loading: false,
    customAvatarMode: false // 标记是否使用自定义头像
  },

  onLoad: function (options) {
    console.log('头像页面加载');
    this.loadUserInfo();

    // 添加全局错误处理，忽略图片加载相关的错误
    this.setupErrorHandler();
  },

  // 设置错误处理
  setupErrorHandler: function() {
    // 监听全局错误，过滤掉图片相关的错误
    const originalOnError = wx.onError;
    if (originalOnError) {
      wx.onError = (error) => {
        // 过滤掉图片加载相关的错误
        if (typeof error === 'string' &&
            (error.includes('getImageInfo') ||
             error.includes('fail invalid') ||
             error.includes('image load'))) {
          console.warn('忽略图片加载错误:', error);
          return;
        }
        // 其他错误正常处理
        originalOnError(error);
      };
    }
  },
  
  onShow: function() {
    console.log('头像页面onShow被调用');

    // 从裁剪页面返回时可能会有新的头像
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    console.log('头像页面onShow，当前页面数据:', {
      croppedAvatar: currentPage.data.croppedAvatar,
      selectedAvatar: currentPage.data.selectedAvatar,
      customAvatarMode: currentPage.data.customAvatarMode
    });

    if (currentPage.data.croppedAvatar) {
      console.log('检测到裁剪后的头像:', currentPage.data.croppedAvatar);

      // 强制更新头像显示
      this.setData({
        selectedAvatar: currentPage.data.croppedAvatar,
        customAvatarMode: true,
        // 添加时间戳强制图片重新加载
        imageUpdateTime: Date.now()
      }, () => {
        console.log('头像设置完成，selectedAvatar:', this.data.selectedAvatar);
        console.log('customAvatarMode:', this.data.customAvatarMode);
      });

      // 清除临时数据
      currentPage.setData({
        croppedAvatar: ''
      });
    } else {
      console.log('没有检测到裁剪后的头像数据');
    }
  },

  // 加载用户信息
  loadUserInfo: function() {
    // 先从全局获取
    const app = getApp();
    if (app.globalData.userInfo) {
      this.setData({
        avatarUrl: app.globalData.userInfo.avatar || '',
        selectedAvatar: app.globalData.userInfo.avatar || ''
      });
      return;
    }

    // 如果全局没有，从服务器获取
    const userInfo = wx.getStorageSync('userInfo');
    userApi.getUserInfo(userInfo && userInfo.id)
      .then(res => {
        if (res.success) {
          // 更新全局用户信息
          app.globalData.userInfo = res.data;
          app.globalData.isLogin = true;

          this.setData({
            avatarUrl: res.data.avatar || '',
            selectedAvatar: res.data.avatar || ''
          });
        } else {
          // 如果API调用失败，尝试从本地存储获取
          this.getLocalUserInfo();
        }
      })
      .catch(err => {
        console.error('获取用户信息失败:', err);
        // 如果API调用失败，尝试从本地存储获取
        this.getLocalUserInfo();
      });
  },

  // 从本地存储获取用户信息
  getLocalUserInfo: function() {
    wx.getStorage({
      key: 'userInfo',
      success: (res) => {
        const app = getApp();
        app.globalData.userInfo = res.data;
        app.globalData.isLogin = true;

        this.setData({
          avatarUrl: res.data.avatar || '',
          selectedAvatar: res.data.avatar || ''
        });
      },
      fail: () => {
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 选择默认头像
  selectAvatar: function(e) {
    const avatar = e.currentTarget.dataset.avatar;
    this.setData({
      selectedAvatar: avatar,
      customAvatarMode: false
    });
  },

  // 图片加载成功事件
  onImageLoad: function(e) {
    console.log('头像图片加载成功:', e.detail);
  },

  // 图片加载失败事件
  onImageError: function(e) {
    console.warn('头像图片加载失败，使用默认头像:', e.detail);
    // 不显示错误提示，静默处理
    // 图片组件会自动显示默认头像
  },

  // 从相册选择图片
  chooseFromAlbum: function() {
    console.log('开始从相册选择图片');
    // 先尝试使用chooseMedia API
    try {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album'],
        success: res => {
          console.log('选择图片成功:', res);
          const file = res.tempFiles[0];
          console.log('选择的文件信息:', file);
          console.log('文件路径:', file.tempFilePath);
          console.log('文件大小:', file.size);

          if (file.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
            wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
            return;
          }

          // 跳转到裁剪页面
          const cropperUrl = `./cropper/cropper?src=${encodeURIComponent(file.tempFilePath)}`;
          console.log('跳转到裁剪页面，URL:', cropperUrl);

          wx.navigateTo({
            url: cropperUrl
          });
        },
        fail: err => {
          console.error('chooseMedia 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消选择图片');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才使用备选方案
          this.chooseImageFallback('album');
        }
      });
    } catch (error) {
      console.error('chooseMedia 异常:', error);
      // 如果chooseMedia出现异常，尝试使用chooseImage作为备选方案
      this.chooseImageFallback('album');
    }
  },

  // 从相机拍照
  takePhoto: function() {
    console.log('开始拍照');
    try {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['camera'],
        success: res => {
          console.log('拍照成功:', res);
          const file = res.tempFiles[0];
          if (file.size > 2 * 1024 * 1024) { // 放宽限制到2MB
            wx.showToast({ title: '图片不能大于2MB', icon: 'none' });
            return;
          }
          // 跳转到裁剪页面
          wx.navigateTo({
            url: `./cropper/cropper?src=${encodeURIComponent(file.tempFilePath)}`
          });
        },
        fail: err => {
          console.error('chooseMedia 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消拍照');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才使用备选方案
          this.chooseImageFallback('camera');
        }
      });
    } catch (error) {
      console.error('chooseMedia 异常:', error);
      // 如果chooseMedia出现异常，尝试使用chooseImage作为备选方案
      this.chooseImageFallback('camera');
    }
  },

  // 使用chooseImage作为备选方案
  chooseImageFallback: function(sourceType = 'album') {
    console.log('使用chooseImage作为备选方案, 来源:', sourceType);
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: [sourceType],
      success: res => {
        console.log('chooseImage成功:', res);
        const tempFilePath = res.tempFilePaths[0];
        const tempFile = res.tempFiles[0];
        if (tempFile.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
          wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
          return;
        }
        // 跳转到裁剪页面
        wx.navigateTo({
          url: `/pages/settings/cropper/cropper?src=${encodeURIComponent(tempFilePath)}`
        });
      },
      fail: err => {
        console.error('chooseImage 失败:', err);
        // 检查是否是用户取消操作
        if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
          console.log('用户取消选择图片');
          return; // 用户取消，直接返回
        }
        // 其他错误情况才显示提示
        wx.showToast({
          title: '选择图片失败，请检查相关权限',
          icon: 'none'
        });
      }
    });
  },


  // 保存头像
  saveAvatar: async function() {
    const { selectedAvatar } = this.data;
    
    if (!selectedAvatar) {
      wx.showToast({
        title: '请选择头像',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ loading: true });
    
    try {
      let avatarUrl = selectedAvatar;
      console.log('当前选中的头像 URL:', avatarUrl);
      console.log('自定义头像模式:', this.data.customAvatarMode);
      
      // 判断是否是临时文件路径（需要上传的自定义头像）
      const isTempFilePath = avatarUrl.startsWith('wxfile://') ||
                            avatarUrl.startsWith('http://tmp') ||
                            avatarUrl.startsWith('https://tmp') ||
                            avatarUrl.includes('tmp_') ||
                            avatarUrl.includes('temp');
      
      // 如果是自定义头像且是临时文件路径，需要先上传
      if (this.data.customAvatarMode && isTempFilePath) {
        console.log('检测到自定义头像，需要上传:', {
          customAvatarMode: this.data.customAvatarMode,
          isTempFilePath,
          avatarUrl
        });

        wx.showLoading({ title: '正在上传头像...' });
        try {
          console.log('开始上传头像:', avatarUrl);
          // 使用uploadFile函数
          const { uploadFile } = require('../../utils/api');
          avatarUrl = await uploadFile(avatarUrl, 'avatars');
          console.log('头像上传成功:', avatarUrl);
        } catch (uploadError) {
          console.error('头像上传失败:', uploadError);
          wx.hideLoading();
          wx.showToast({ title: '头像上传失败', icon: 'none' });
          this.setData({ loading: false });
          return;
        }
        wx.hideLoading();
      } else {
        console.log('使用默认头像或已上传的头像:', {
          customAvatarMode: this.data.customAvatarMode,
          isTempFilePath,
          avatarUrl
        });
      }
      
      // 检查token是否存在
      const token = wx.getStorageSync('token');
      console.log('保存头像时的令牌:', token ? token.substring(0, 10) + '...' : '未提供');

      if (!token) {
        wx.showToast({
          title: '未登录，请先登录',
          icon: 'none'
        });
        this.setData({ loading: false });

        // 跳转到登录页
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/auth/auth'
          });
        }, 1500);
        return;
      }
      
      console.log('准备更新用户头像:', avatarUrl);
      // 更新用户信息
      const res = await userApi.updateUserInfo({ avatar: avatarUrl });
      console.log('更新用户头像响应:', res);
      
      if (res.success) {
        // 获取最新的用户信息
        const userInfoRes = await userApi.getUserInfo(wx.getStorageSync('userInfo').id);
        console.log('获取最新用户信息结果:', userInfoRes);
        
        if (userInfoRes.success) {
          // 更新本地存储的用户信息
          wx.setStorageSync('userInfo', userInfoRes.data);
          
          // 更新全局用户信息
          const app = getApp();
          app.globalData.userInfo = userInfoRes.data;
          app.globalData.needRefreshProfile = true; // 标记需要刷新个人信息
          
          // 强制刷新页面上的头像
          this.setData({
            avatarUrl: userInfoRes.data.avatar,
            selectedAvatar: userInfoRes.data.avatar
          });
        } else {
          // 如果获取最新用户信息失败，至少更新本地存储
          const userInfo = wx.getStorageSync('userInfo');
          userInfo.avatar = avatarUrl;
          wx.setStorageSync('userInfo', userInfo);
          
          // 更新全局用户信息
          const app = getApp();
          app.globalData.userInfo = userInfo;
          app.globalData.needRefreshProfile = true;
          // 同步刷新页面头像
          this.setData({
            avatarUrl: avatarUrl,
            selectedAvatar: avatarUrl
          });
        }
        
        wx.showToast({
          title: '头像修改成功',
          icon: 'success'
        });
        
        // 延迟返回，确保用户看到成功提示
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: res.message || '头像修改失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('保存头像失败:', error);
      wx.showToast({
        title: '头像修改失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 执行保存头像操作
  doSaveAvatar: function(selectedAvatar) {
    console.log('开始更新头像:', selectedAvatar);
    userApi.updateUserInfo({ avatar: selectedAvatar })
      .then(res => {
        console.log('更新头像响应:', res);
        this.setData({ loading: false });

        if (res.success) {
          // 获取最新的用户信息
          userApi.getUserInfo(wx.getStorageSync('userInfo').id)
            .then(infoRes => {
              console.log('修改头像后获取最新用户信息结果:', infoRes);
              if (infoRes.success) {
                console.log('修改头像后获取最新用户信息成功:', infoRes.data);
                const app = getApp();

                // 更新全局用户信息
                app.globalData.userInfo = infoRes.data;

                // 更新本地存储
                wx.setStorage({
                  key: 'userInfo',
                  data: infoRes.data
                });

                // 标记需要刷新个人中心页面
                app.globalData.needRefreshProfile = true;

                wx.showToast({
                  title: '头像修改成功',
                  icon: 'success'
                });

                // 返回上一页
                setTimeout(() => {
                  wx.navigateBack();
                }, 1500);
              } else {
                // 如果获取最新信息失败，至少更新本地的头像
                const app = getApp();

                // 直接更新头像
                if (app.globalData.userInfo) {
                  app.globalData.userInfo.avatar = selectedAvatar;
                } else {
                  app.globalData.userInfo = { avatar: selectedAvatar };
                }

                // 更新本地存储
                wx.getStorage({
                  key: 'userInfo',
                  success: (result) => {
                    const userInfo = result.data;
                    userInfo.avatar = selectedAvatar;
                    wx.setStorage({
                      key: 'userInfo',
                      data: userInfo
                    });
                  },
                  fail: () => {
                    // 如果本地没有存储，创建一个新的
                    wx.setStorage({
                      key: 'userInfo',
                      data: { avatar: selectedAvatar }
                    });
                  }
                });

                // 标记需要刷新个人中心页面
                app.globalData.needRefreshProfile = true;

                wx.showToast({
                  title: '头像修改成功',
                  icon: 'success'
                });

                // 返回上一页
                setTimeout(() => {
                  wx.navigateBack();
                }, 1500);
              }
            })
            .catch(err => {
              console.error('修改头像后获取用户信息失败:', err);

              // 如果获取最新信息失败，至少更新本地的头像
              const app = getApp();

              // 直接更新头像
              if (app.globalData.userInfo) {
                app.globalData.userInfo.avatar = selectedAvatar;
              } else {
                app.globalData.userInfo = { avatar: selectedAvatar };
              }

              // 更新本地存储
              wx.getStorage({
                key: 'userInfo',
                success: (result) => {
                  const userInfo = result.data;
                  userInfo.avatar = selectedAvatar;
                  wx.setStorage({
                    key: 'userInfo',
                    data: userInfo
                  });
                },
                fail: () => {
                  // 如果本地没有存储，创建一个新的
                  wx.setStorage({
                    key: 'userInfo',
                    data: { avatar: selectedAvatar }
                  });
                }
              });

              // 标记需要刷新个人中心页面
              app.globalData.needRefreshProfile = true;

              wx.showToast({
                title: '头像修改成功',
                icon: 'success'
              });

              // 返回上一页
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            });
        } else {
          wx.showToast({
            title: res.message || '修改失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('更新头像请求失败:', err);
        this.setData({ loading: false });
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  }
})
