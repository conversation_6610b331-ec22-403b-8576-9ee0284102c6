/* 半屏弹窗样式 */
.edit-drawer-container {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  z-index: 9999;
  display: none;
}
.edit-drawer-container.visible {
  display: block;
}
.edit-drawer-mask {
  position: absolute;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 1;
}
.edit-drawer-panel {
  position: absolute;
  left: 0; right: 0; bottom: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  min-height: 700rpx;
  max-height: 80vh;
  z-index: 2;
  box-shadow: 0 -8rpx 32rpx rgba(0,0,0,0.08);
  padding: 32rpx 32rpx 0 32rpx;
  animation: slideUp .3s;
  display: flex;
  flex-direction: column;
}
@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}
.edit-drawer-header {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 32rpx;
}
.edit-drawer-form {
  flex: 1;
}
.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 16rpx;
  height: 72rpx;
}
.form-label {
  width: 140rpx;
  min-width: 140rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  text-align: left;
  line-height: 72rpx;
}
.form-input {
  flex: 1;
  font-size: 28rpx;
  border: none;
  border-bottom: 1rpx solid #eee;
  padding: 0;
  background: transparent;
  outline: none;
  height: 72rpx;
  line-height: 72rpx;
}
.product-image-upload {
  display: flex;
  align-items: center;
  min-height: 72rpx;
}
.product-image-preview {
  width: 96rpx;
  height: 96rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
  border: 1rpx solid #eee;
  object-fit: cover;
}
.product-image-add-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.add-image-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 8rpx;
}
.add-image-text {
  color: #bbb;
  font-size: 26rpx;
}
.product-image-delete-btn {
  margin-left: 8rpx;
  cursor: pointer;
}
.delete-image-icon {
  width: 32rpx;
  height: 32rpx;
}
.edit-drawer-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 24rpx;
  margin-bottom: 96rpx;
}
.footer-btn {
  flex: 1;
  margin: 0 12rpx;
  height: 72rpx;
  line-height: 72rpx;
  font-size: 30rpx;
  border-radius: 12rpx;
  border: none;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255,77,79,0.08);
  transition: background 0.2s;
}
.footer-btn.cancel {
  background: #fff;
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}
.footer-btn.confirm {
  background: linear-gradient(90deg, #ff4d4f 0%, #ff7a45 100%);
  color: #fff;
  border: none;
} 