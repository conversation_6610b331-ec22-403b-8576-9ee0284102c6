// pages/partner/join-partner.js
Page({
  data: {
    slogan: '购物与创业新范式',
    benefits: [
      {
        icon: '💰',
        title: '现金推荐奖'
      },
      {
        icon: '🎁',
        title: '席位赠送奖'
      },
      {
        icon: '💎',
        title: '销售高分佣'
      },
      {
        icon: '🛒',
        title: '购物专享价'
      },
      {
        icon: '📈',
        title: '个人成长'
      },
      {
        icon: '🏪',
        title: '门店分红'
      },
      {
        icon: '👥',
        title: '顾客助销售'
      },
      {
        icon: '🎯',
        title: '定向福利'
      },
      {
        icon: '🛡️',
        title: '零风险退出'
      }
    ],
    // 申请弹窗相关数据
    showApplyModal: false,
    applyForm: {
      name: '',
      phone: '',
      region: ['', '', ''], // 微信原生地区选择器数据
      regionText: '请选择省市区'
    }
  },

  onLoad: function (options) {
    // 页面加载时的逻辑
  },

  onShow: function () {
    // 页面显示时的逻辑
    // 检查用户登录状态
    const app = getApp();
    if (app.globalData.isLogin) {
      // 已登录状态，可以继续其他操作
    }
  },

  // 点击权益项
  onBenefitTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const benefit = this.data.benefits[index];
    
    wx.showModal({
      title: benefit.title,
      content: '点击查看详情，请联系客服了解更多信息',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 申请加入合伙人
  applyPartner: function() {
    this.setData({
      showApplyModal: true
    });
  },

  // 关闭申请弹窗
  closeApplyModal: function() {
    this.setData({
      showApplyModal: false,
      applyForm: {
        name: '',
        phone: '',
        region: ['', '', ''],
        regionText: '请选择省市区'
      }
    });
  },

  // 输入框内容变化
  onInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`applyForm.${field}`]: value
    });
  },

  // 微信原生地区选择器事件处理
  onRegionChange: function(e) {
    const region = e.detail.value;
    const regionText = region.join(' ');
    this.setData({
      'applyForm.region': region,
      'applyForm.regionText': regionText
    });
  },

  // 提交申请
  submitApplication: function() {
    const { name, phone, region, regionText } = this.data.applyForm;
    
    if (!name.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }
    
    if (!phone.trim() || !/^1\d{10}$/.test(phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    
    if (!region[0] || !region[1] || !region[2]) {
      wx.showToast({
        title: '请选择完整的省市区',
        icon: 'none'
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '提交中...'
    });
    
    // 调用合伙人申请API
    const { partnerApi } = require('../../utils/api');
    partnerApi.applyPartner({
      name: name.trim(),
      phone: phone.trim(),
      province: region[0],
      city: region[1],
      district: region[2] || ''
    }).then(res => {
      wx.hideLoading();
      if (res.success) {
        wx.showToast({
          title: '申请提交成功',
          icon: 'success',
          duration: 2000
        });
        // 关闭弹窗
        this.closeApplyModal();
      } else {
        wx.showToast({
          title: res.message || '申请提交失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '申请提交失败，请稍后重试',
        icon: 'none'
      });
      console.error('合伙人申请提交失败:', err);
    });
  },

  // 查看合伙人合约
  viewContract: function() {
    // 跳转到合约内容页面
    wx.navigateTo({
      url: '/pages/partner/contract'
    });
  },
  
  // 查看申请状态
  checkApplicationStatus: function() {
    const app = getApp();
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再查看申请状态',
        confirmText: '去登录',
        success(res) {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 跳转到申请状态页面
    wx.navigateTo({
      url: '/pages/partner/application-status'
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止事件冒泡
  }
});