<view class="partner-change-page">
  <!-- 门店搜索选择框 -->
  <view class="form-group">
    <view class="form-label">操作门店</view>
    <input class="form-input" placeholder="输入门店名称/编号" value="{{storeSearchValue}}" bindinput="onStoreSearchInput" />
    <view wx:if="{{showStoreDropdown}}" class="dropdown-list">
      <block wx:for="{{filteredStoreList}}" wx:key="id">
        <view class="dropdown-item" bindtap="selectStore" data-index="{{index}}">{{item.name}}（编号:{{item.code}}）</view>
      </block>
      <view wx:if="{{filteredStoreList.length === 0}}" class="dropdown-empty">无匹配门店</view>
    </view>
    <view wx:if="{{showStoreDropdown}}" class="dropdown-mask" catchtap="onCloseDropdown"></view>
  </view>
  <!-- 操作按钮栏 -->
  <view class="partner-actions">
    <button class="partner-action-btn">全选/取消</button>
    <button class="partner-action-btn">冻结</button>
    <button class="partner-action-btn">解冻</button>
    <button class="partner-action-btn">移除</button>
  </view>
  <!-- 合伙人列表 -->
  <view class="partner-list">
    <block wx:if="{{selectedStore}}">
      <block wx:if="{{loadingPartners}}">
        <view>加载中...</view>
      </block>
      <block wx:else>
        <block wx:for="{{partnerList}}" wx:key="id">
          <view class="partner-item">
            <image class="avatar" src="{{item.avatar}}" mode="aspectFill" />
            <view class="info">
              <view class="nickname">{{item.nickname}}</view>
              <view class="phone">{{item.phone}}</view>
              <view class="type">类型: {{item.type}}</view>
              <view class="percent">股份: {{item.percent}}%</view>
              <view class="join-time">加入: {{item.created_at | formatDate}}</view>
              <view class="join-way">方式: {{item.amount > 0 ? '投资加入' : '奖励赠送'}}</view>
              <view class="status {{item.user_status == 1 ? 'status-on' : 'status-frozen'}}">
                {{item.user_status == 1 ? '正常' : '冻结'}}
              </view>
            </view>
          </view>
        </block>
      </block>
    </block>
    <block wx:else>
      <view>请先选择门店</view>
    </block>
  </view>
  <view class="add-partner-btn" bindtap="onAddPartner">+</view>
</view> 