/**
 * 收藏路由
 */
const express = require('express');
const { body, query } = require('express-validator');
const favoriteController = require('../controllers/favoriteController');
const { checkAuth } = require('../middleware/auth');
const validate = require('../middleware/validation');

const router = express.Router();

// 获取收藏列表
router.get('/', checkAuth, favoriteController.getFavorites);

// 添加到收藏夹
router.post('/', [
  checkAuth,
  body('productId').isString().withMessage('商品ID必须是字符串'),
  validate
], favoriteController.addToFavorites);

// 从收藏夹移除
router.delete('/', [
  checkAuth,
  body('productId').isString().withMessage('商品ID必须是字符串'),
  validate
], favoriteController.removeFromFavorites);

// 检查收藏状态
router.get('/status', [
  checkAuth,
  query('productId').isString().withMessage('商品ID必须是字符串'),
  validate
], favoriteController.checkFavoriteStatus);

// 获取收藏数量
router.get('/count', checkAuth, favoriteController.getFavoriteCount);

module.exports = router; 