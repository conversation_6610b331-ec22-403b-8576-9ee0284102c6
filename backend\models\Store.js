const db = require('../config/db');

class Store {
  // 自动生成门店编号：M+行政代码+顺序号
  static async generateStoreNo(regionCode) {
    // 查找该行政区下最大顺序号
    const sql = 'SELECT store_no FROM stores WHERE store_no LIKE ? ORDER BY store_no DESC LIMIT 1';
    const prefix = `M${regionCode}`;
    const rows = await db.query(sql, [`${prefix}%`]);
    let seq = 1;
    if (rows.length > 0) {
      const lastNo = rows[0].store_no;
      const lastSeq = parseInt(lastNo.slice(-3));
      seq = lastSeq + 1;
    }
    return `${prefix}${seq.toString().padStart(3, '0')}`;
  }

  static async getRegionCodeByName(province, city, district) {
    // 优先区县，找不到降级到市、省
    let sql = 'SELECT code FROM regions WHERE name = ? AND level = 3';
    let rows = await db.query(sql, [district]);
    if (rows.length > 0) return rows[0].code;
    sql = 'SELECT code FROM regions WHERE name = ? AND level = 2';
    rows = await db.query(sql, [city]);
    if (rows.length > 0) return rows[0].code;
    sql = 'SELECT code FROM regions WHERE name = ? AND level = 1';
    rows = await db.query(sql, [province]);
    if (rows.length > 0) return rows[0].code;
    return null;
  }

  // 创建门店
  static async create({ name, level, level_title, province, city, district, contact_person, phone, image }) {
    // 查找行政区代码
    const regionCode = await this.getRegionCodeByName(province, city, district);
    if (!regionCode) throw new Error('未找到对应的行政区代码');
    
    // 生成门店编号
    const store_no = await this.generateStoreNo(regionCode);
    
    // 如果没有提供level_title，根据level自动生成
    let finalLevelTitle = level_title;
    if (!finalLevelTitle) {
      const levelTitleMap = {
        'L1': '一星门店',
        'L2': '二星门店',
        'L3': '三星门店',
        'L4': '四星门店',
        'L5': '五星门店'
      };
      finalLevelTitle = levelTitleMap[level] || '三星门店';
    }
    
    const now = Date.now();
    const sql = `INSERT INTO stores (store_no, name, level, level_title, province, city, district, contact_person, phone, image, create_time, update_time)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    const params = [store_no, name, level, finalLevelTitle, province, city, district, contact_person, phone, image, now, now];
    const result = await db.query(sql, params);
    return { success: true, id: result.insertId, store_no };
  }

  // 累加门店股本金
  static async addCapital(store_id, amount) {
    const sql = 'UPDATE stores SET capital = capital + ? WHERE id = ?';
    await db.query(sql, [amount, store_id]);
  }

  // 根据ID获取门店信息
  static async getStoreById(store_id) {
    const sql = 'SELECT * FROM stores WHERE id = ?';
    const rows = await db.query(sql, [store_id]);
    return rows.length > 0 ? rows[0] : null;
  }
}

module.exports = Store; 