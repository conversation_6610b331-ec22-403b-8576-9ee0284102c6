/**
 * 检查远程数据库中的公司信息表
 * 用于诊断公司简介保存失败的问题
 */

const mysql = require('mysql2/promise');

// 远程数据库配置（微信云托管）
// 尝试多个可能的远程主机地址
const possibleRemoteHosts = [
  'mysql',
  'morebuy-mysql',
  '**********',
  '**********'
];

const remoteDbConfig = {
  host: process.env.MYSQL_IP || 'mysql', // 云托管内部主机名
  port: process.env.MYSQL_PORT || 3306,
  user: process.env.MYSQL_USERNAME || 'root',
  password: 'molI2578#', // 远程数据库密码
  database: process.env.MYSQL_DATABASE || 'morebuy',
  charset: 'utf8mb4',
  connectTimeout: 10000,
  acquireTimeout: 10000,
  timeout: 10000
};

// 本地数据库配置（用于对比）
const localDbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'molI2505$', // 本地数据库密码
  database: 'morebuy',
  charset: 'utf8mb4'
};

async function checkCompanyInfo() {
  console.log('🔍 开始检查公司信息表...\n');

  // 检查远程数据库 - 尝试多个主机
  console.log('📡 检查远程数据库（微信云托管）...');
  let remoteSuccess = false;

  for (const host of possibleRemoteHosts) {
    console.log(`\n🔄 尝试连接远程主机: ${host}`);
    const testConfig = { ...remoteDbConfig, host };

    try {
      await checkDatabase(testConfig, `远程(${host})`);
      remoteSuccess = true;
      break;
    } catch (error) {
      console.log(`❌ 连接 ${host} 失败: ${error.message}`);
      continue;
    }
  }

  if (!remoteSuccess) {
    console.log('❌ 所有远程主机连接都失败');
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 检查本地数据库
  console.log('💻 检查本地数据库...');
  try {
    await checkDatabase(localDbConfig, '本地');
  } catch (error) {
    console.log('❌ 本地数据库连接失败:', error.message);
  }
}

async function checkDatabase(dbConfig, label) {
  let connection;

  try {
    console.log(`🔗 连接到${label}数据库...`);
    console.log(`   主机: ${dbConfig.host}:${dbConfig.port}`);
    console.log(`   用户: ${dbConfig.user}`);
    console.log(`   数据库: ${dbConfig.database}`);

    // 设置连接超时
    const connectionPromise = mysql.createConnection(dbConfig);
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('连接超时')), 15000);
    });

    connection = await Promise.race([connectionPromise, timeoutPromise]);
    console.log(`✅ ${label}数据库连接成功`);

    // 1. 检查表是否存在
    console.log(`\n📋 检查${label}数据库中的company_info表...`);
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'company_info'"
    );
    
    if (tables.length === 0) {
      console.log(`❌ ${label}数据库中不存在company_info表`);
      return;
    }
    
    console.log(`✅ ${label}数据库中存在company_info表`);

    // 2. 检查表结构
    console.log(`\n🏗️ ${label}数据库表结构:`);
    const [columns] = await connection.execute('DESCRIBE company_info');
    columns.forEach(col => {
      console.log(`   - ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(可空)' : '(非空)'} ${col.Key ? `[${col.Key}]` : ''}`);
    });

    // 3. 检查数据
    console.log(`\n📊 ${label}数据库中的公司信息数据:`);
    const [rows] = await connection.execute('SELECT * FROM company_info ORDER BY id');
    
    if (rows.length === 0) {
      console.log(`   ⚠️ ${label}数据库中没有公司信息数据`);
    } else {
      console.log(`   📈 ${label}数据库中有 ${rows.length} 条公司信息记录:`);
      rows.forEach((row, index) => {
        console.log(`   ${index + 1}. ID: ${row.id}`);
        console.log(`      公司名称: ${row.company_name || '(空)'}`);
        console.log(`      公司简介: ${row.company_description ? (row.company_description.length > 50 ? row.company_description.substring(0, 50) + '...' : row.company_description) : '(空)'}`);
        console.log(`      创建时间: ${row.createTime ? new Date(row.createTime).toLocaleString() : '(空)'}`);
        console.log(`      更新时间: ${row.updateTime ? new Date(row.updateTime).toLocaleString() : '(空)'}`);
        console.log('');
      });
    }

    // 4. 测试更新操作
    if (rows.length > 0) {
      console.log(`\n🧪 测试${label}数据库更新操作...`);
      const testId = rows[0].id;
      const testDescription = `测试更新 - ${new Date().toLocaleString()}`;
      
      try {
        const [updateResult] = await connection.execute(
          'UPDATE company_info SET company_description = ?, updateTime = ? WHERE id = ?',
          [testDescription, Date.now(), testId]
        );
        
        if (updateResult.affectedRows > 0) {
          console.log(`   ✅ ${label}数据库更新测试成功，影响行数: ${updateResult.affectedRows}`);
          
          // 验证更新结果
          const [verifyRows] = await connection.execute(
            'SELECT company_description, updateTime FROM company_info WHERE id = ?',
            [testId]
          );
          
          if (verifyRows.length > 0) {
            console.log(`   📝 更新后的简介: ${verifyRows[0].company_description}`);
            console.log(`   ⏰ 更新时间: ${new Date(verifyRows[0].updateTime).toLocaleString()}`);
          }
        } else {
          console.log(`   ❌ ${label}数据库更新测试失败，没有行被更新`);
        }
      } catch (updateError) {
        console.log(`   ❌ ${label}数据库更新测试失败:`, updateError.message);
      }
    }

  } catch (error) {
    console.error(`❌ ${label}数据库检查失败:`, error.message);
    if (error.code) {
      console.error(`   错误代码: ${error.code}`);
    }
    if (error.errno) {
      console.error(`   错误号: ${error.errno}`);
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log(`🔌 ${label}数据库连接已关闭`);
    }
  }
}

// 执行检查
checkCompanyInfo()
  .then(() => {
    console.log('\n🎉 检查完成！');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ 检查失败:', error);
    process.exit(1);
  });
